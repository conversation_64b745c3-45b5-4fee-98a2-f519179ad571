# Translation of Themes - Twenty Twenty-Three in Polish
# This file is distributed under the same license as the Themes - Twenty Twenty-Three package.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-11-14 06:20:13+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n == 1) ? 0 : ((n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14)) ? 1 : 2);\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: pl\n"
"Project-Id-Version: Themes - Twenty Twenty-Three\n"

#. Description of the theme
#: style.css
#, gp-priority: high
msgid "Twenty Twenty-Three is designed to take advantage of the new design tools introduced in WordPress 6.1. With a clean, blank base as a starting point, this default theme includes ten diverse style variations created by members of the WordPress community. Whether you want to build a complex or incredibly simple website, you can do it quickly and intuitively through the bundled styles or dive into creation and full customization yourself."
msgstr "Twenty Twenty-Three został zaprojektowany, aby wykorzystać nowe narzędzia do projektowania wprowadzone w WordPress 6.1. Z czystą, pustą bazą jako punktem wyjścia, ten domyślny motyw zawiera dziesięć różnorodnych odmian stylu stworzonych przez członków społeczności WordPress. Niezależnie od tego, czy chcesz zbudować złożoną, czy niesamowicie prostą stronę internetową, możesz to zrobić szybko i intuicyjnie za pomocą dołączonych stylów lub zanurkować w tworzeniu i pełnym dostosowaniu samodzielnie."

#. Theme Name of the theme
#: style.css
#, gp-priority: high
msgid "Twenty Twenty-Three"
msgstr "Twenty Twenty-Three"

#: patterns/hidden-heading.php:9
msgctxt "Main heading for homepage"
msgid "Mindblown: a blog about philosophy."
msgstr "Mindblown: blog o filozofii."

#: patterns/hidden-heading.php
msgctxt "Pattern title"
msgid "Hidden Heading for Homepage"
msgstr "Ukryty nagłówek strony głównej"

#: patterns/post-meta.php
msgctxt "Pattern description"
msgid "Post meta information with separator on the top."
msgstr "Zamieść meta informacje z separatorem na górze."

#: patterns/footer-default.php
msgctxt "Pattern description"
msgid "Footer with site title and powered by WordPress."
msgstr "Stopka z tytułem strony i zasilana przez WordPressa."

#: patterns/call-to-action.php
msgctxt "Pattern description"
msgid "Left-aligned text with a CTA button and a separator."
msgstr "Tekst wyrównany do lewej z przyciskiem wezwania do działania  i separatorem."

#: theme.json
msgctxt "Template part name"
msgid "Comments Template Part"
msgstr "Fragment szablonu komentarzy"

#: theme.json
msgctxt "Template part name"
msgid "Post Meta"
msgstr "Meta dane wpisu"

#: theme.json
msgctxt "Template part name"
msgid "Footer"
msgstr "Stopka"

#: theme.json
msgctxt "Template part name"
msgid "Header"
msgstr "Nagłówek"

#: theme.json
msgctxt "Font family name"
msgid "Source Serif Pro"
msgstr "Source Serif Pro"

#: theme.json
msgctxt "Font family name"
msgid "System Font"
msgstr "Systemowy krój pisma"

#: theme.json
msgctxt "Font family name"
msgid "Inter"
msgstr "Inter"

#: theme.json
msgctxt "Font family name"
msgid "IBM Plex Mono"
msgstr "IBM Plex Mono"

#: theme.json
msgctxt "Font family name"
msgid "DM Sans"
msgstr "DM Sans"

#: theme.json
msgctxt "Custom template name"
msgid "404"
msgstr "404"

#: theme.json
msgctxt "Custom template name"
msgid "Blog (Alternative)"
msgstr "Blog (alternatywnie)"

#: theme.json
msgctxt "Custom template name"
msgid "Blank"
msgstr "Pusto"

#: styles/whisper.json
msgctxt "Style variation name"
msgid "Whisper"
msgstr "Whisper"

#: styles/sherbet.json
msgctxt "Gradient name"
msgid "Tertiary to Secondary to Primary Fixed"
msgstr "Trzeci do drugiego do pierwszego - stałe"

#: styles/sherbet.json
msgctxt "Gradient name"
msgid "Primary to Secondary to Tertiary Fixed"
msgstr "Pierwszy do drugiego do trzeciego - stałe"

#: styles/sherbet.json
msgctxt "Gradient name"
msgid "Primary to Secondary to Tertiary"
msgstr "Pierwszy do drugiego do trzeciego"

#: styles/sherbet.json
msgctxt "Style variation name"
msgid "Sherbet"
msgstr "Sorbet"

#: styles/pitch.json
msgctxt "Font size name"
msgid "2X Large"
msgstr "2X Duży"

#: styles/pitch.json
msgctxt "Font size name"
msgid "Extra Large"
msgstr "Bardzo duży"

#: styles/pitch.json
msgctxt "Font size name"
msgid "Large"
msgstr "Duży"

#: styles/pitch.json
msgctxt "Font size name"
msgid "Medium"
msgstr "Średni"

#: styles/pitch.json
msgctxt "Font size name"
msgid "small"
msgstr "mały"

#: styles/pitch.json
msgctxt "Space size name"
msgid "7"
msgstr "7"

#: styles/pitch.json
msgctxt "Style variation name"
msgid "Pitch"
msgstr "Poziom"

#: styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Dots"
msgstr "Kropkowany"

#: styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Base to Primary"
msgstr "Od bazy do pierwszego"

#: styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Tertiary to Secondary"
msgstr "Trzeci do drugiego"

#: styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Secondary to Primary"
msgstr "Drugi do pierwszego"

#: styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Primary to Secondary"
msgstr "Pierwszy do drugiego"

#: styles/pilgrimage.json
msgctxt "Style variation name"
msgid "Pilgrimage"
msgstr "Pielgrzymka"

#: styles/marigold.json
msgctxt "Font size name"
msgid "Gigantic"
msgstr "Gigantyczny"

#: styles/marigold.json
msgctxt "Font size name"
msgid "Huge"
msgstr "Ogromny"

#: styles/marigold.json
msgctxt "Font size name"
msgid "Normal"
msgstr "Normalny"

#: styles/marigold.json
msgctxt "Font size name"
msgid "Tiny"
msgstr "Bardzo mały"

#: styles/marigold.json styles/pitch.json theme.json
msgctxt "Space size name"
msgid "6"
msgstr "6"

#: styles/marigold.json styles/pitch.json theme.json
msgctxt "Space size name"
msgid "5"
msgstr "5"

#: styles/marigold.json styles/pitch.json theme.json
msgctxt "Space size name"
msgid "4"
msgstr "4"

#: styles/marigold.json styles/pitch.json theme.json
msgctxt "Space size name"
msgid "3"
msgstr "3"

#: styles/marigold.json styles/pitch.json theme.json
msgctxt "Space size name"
msgid "2"
msgstr "2"

#: styles/marigold.json styles/pitch.json theme.json
msgctxt "Space size name"
msgid "1"
msgstr "1"

#: styles/marigold.json
msgctxt "Style variation name"
msgid "Marigold"
msgstr "Aksamitka"

#: styles/grapes.json
msgctxt "Style variation name"
msgid "Grapes"
msgstr "Winogrona"

#: styles/electric.json
msgctxt "Style variation name"
msgid "Electric"
msgstr "Elektryczny"

#: styles/canary.json
msgctxt "Style variation name"
msgid "Canary"
msgstr "Kanarkowy"

#: styles/block-out.json styles/canary.json styles/pilgrimage.json
#: styles/sherbet.json
msgctxt "Duotone name"
msgid "Default filter"
msgstr "Filtr domyślny"

#: styles/block-out.json
msgctxt "Style variation name"
msgid "Block out"
msgstr "Szkicowany"

#: styles/aubergine.json styles/block-out.json styles/canary.json
#: styles/electric.json styles/grapes.json styles/marigold.json
#: styles/pilgrimage.json styles/pitch.json styles/sherbet.json
#: styles/whisper.json theme.json
msgctxt "Color name"
msgid "Tertiary"
msgstr "Trzeci"

#: styles/aubergine.json styles/block-out.json styles/canary.json
#: styles/electric.json styles/grapes.json styles/marigold.json
#: styles/pilgrimage.json styles/pitch.json styles/sherbet.json
#: styles/whisper.json theme.json
msgctxt "Color name"
msgid "Secondary"
msgstr "Drugi"

#: styles/aubergine.json styles/block-out.json styles/canary.json
#: styles/electric.json styles/grapes.json styles/marigold.json
#: styles/pilgrimage.json styles/pitch.json styles/sherbet.json
#: styles/whisper.json theme.json
msgctxt "Color name"
msgid "Primary"
msgstr "Pierwszy"

#: styles/aubergine.json styles/block-out.json styles/canary.json
#: styles/electric.json styles/grapes.json styles/marigold.json
#: styles/pilgrimage.json styles/pitch.json styles/sherbet.json
#: styles/whisper.json theme.json
msgctxt "Color name"
msgid "Contrast"
msgstr "Kontrast"

#: styles/aubergine.json styles/block-out.json styles/canary.json
#: styles/electric.json styles/grapes.json styles/marigold.json
#: styles/pilgrimage.json styles/pitch.json styles/sherbet.json
#: styles/whisper.json theme.json
msgctxt "Color name"
msgid "Base"
msgstr "Bazowy"

#: styles/aubergine.json
msgctxt "Gradient name"
msgid "Primary to Tertiary"
msgstr "Pierwszy do trzeciego"

#: styles/aubergine.json styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Tertiary to Primary"
msgstr "Trzeci do pierwszego"

#: styles/aubergine.json
msgctxt "Gradient name"
msgid "Base to Secondary to Base"
msgstr "Bazowy do drugiego do bazowego"

#: styles/aubergine.json
msgctxt "Gradient name"
msgid "Secondary to Base"
msgstr "Drugi do bazowego"

#: styles/aubergine.json
msgctxt "Style variation name"
msgid "Aubergine"
msgstr "Bakłażanowy"

#: patterns/post-meta.php:65
msgctxt "Label for a list of post tags"
msgid "Tags:"
msgstr "Tagi:"

#: patterns/post-meta.php:49
msgctxt "Preposition to show the relationship between the post and its author"
msgid "by"
msgstr "przez"

#: patterns/post-meta.php:37
msgctxt "Preposition to show the relationship between the post and its categories"
msgid "in"
msgstr "w"

#: patterns/post-meta.php:29
msgctxt "Verb to explain the publication status of a post"
msgid "Posted"
msgstr "Opublikowano"

#: patterns/post-meta.php
msgctxt "Pattern title"
msgid "Post Meta"
msgstr "Meta dane wpisu"

#: patterns/hidden-no-results.php:10
msgctxt "Message explaining that there are no results returned from a search"
msgid "Sorry, but nothing matched your search terms. Please try again with some different keywords."
msgstr "Brak wyników wyszukiwania. Proszę spróbować ponownie z innymi słowami."

#: patterns/hidden-no-results.php
msgctxt "Pattern title"
msgid "Hidden No Results Content"
msgstr "Ukryty brak wyników"

#: patterns/hidden-comments.php:13
msgctxt "Title of comments section"
msgid "Comments"
msgstr "Komentarze"

#: patterns/hidden-comments.php
msgctxt "Pattern title"
msgid "Hidden Comments"
msgstr "Ukryte komentarze"

#: patterns/hidden-404.php:22 patterns/hidden-no-results.php:14
msgid "Search"
msgstr "Szukaj"

#: patterns/hidden-404.php:22 patterns/hidden-no-results.php:14
msgctxt "placeholder for search field"
msgid "Search..."
msgstr "Wyszukiwanie…"

#: patterns/hidden-404.php:22 patterns/hidden-no-results.php:14
msgctxt "label"
msgid "Search"
msgstr "Szukaj"

#: patterns/hidden-404.php:19
msgctxt "Message to convey that a webpage could not be found"
msgid "This page could not be found."
msgstr "Nie można odnaleźć wybranej strony."

#: patterns/hidden-404.php:13
msgctxt "Error code for a webpage that is not found."
msgid "404"
msgstr "404"

#: patterns/hidden-404.php
msgctxt "Pattern title"
msgid "Hidden 404"
msgstr "Ukryty 404"

#. Translators: WordPress link.
#: patterns/footer-default.php:20
msgid "Proudly powered by %s"
msgstr "Dumnie wspierane przez %s"

#: patterns/footer-default.php
msgctxt "Pattern title"
msgid "Default Footer"
msgstr "Stopka domyślna"

#: patterns/call-to-action.php:25
msgctxt "sample content for call to action button"
msgid "Get In Touch"
msgstr "Pozostań w kontakcie"

#: patterns/call-to-action.php:16
msgctxt "sample content for call to action"
msgid "Got any book recommendations?"
msgstr "Czy możesz polecić książkę?"

#: patterns/call-to-action.php
msgctxt "Pattern title"
msgid "Call to action"
msgstr "Wezwanie do działania"

#. Author URI of the theme
#: style.css patterns/footer-default.php:21
#, gp-priority: low
msgid "https://wordpress.org"
msgstr "https://pl.wordpress.org"

#. Author of the theme
#: style.css
#, gp-priority: low
msgid "the WordPress team"
msgstr "zespół WordPressa"

#. Theme URI of the theme
#: style.css
#, gp-priority: low
msgid "https://wordpress.org/themes/twentytwentythree"
msgstr "https://pl.wordpress.org/themes/twentytwentythree/"
