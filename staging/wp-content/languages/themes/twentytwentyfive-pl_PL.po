# Translation of Themes - Twenty Twenty-Five in Polish
# This file is distributed under the same license as the Themes - Twenty Twenty-Five package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-07-09 10:46:29+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n == 1) ? 0 : ((n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14)) ? 1 : 2);\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: pl\n"
"Project-Id-Version: Themes - Twenty Twenty-Five\n"

#. Description of the theme
#: style.css
#, gp-priority: high
msgid "Twenty Twenty-Five emphasizes simplicity and adaptability. It offers flexible design options, supported by a variety of patterns for different page types, such as services and landing pages, making it ideal for building personal blogs, professional portfolios, online magazines, or business websites. Its templates cater to various blog styles, from text-focused to image-heavy layouts. Additionally, it supports international typography and diverse color palettes, ensuring accessibility and customization for users worldwide."
msgstr "Motyw Dwadzieścia Dwadzieścia-Pięć kładzie nacisk na prostotę i adaptacyjność. Oferuje elastyczne opcje wyglądu, obsługiwane przez różnorodne wzorce dla różnych rodzajów stron, takich jak usługi i strony docelowe, co czyni go idealnym do tworzenia osobistych blogów, profesjonalnych portfolio, internetowych czasopism czy witryn biznesowych. Jego szablony dostosowują się do różnych stylów blogów, od skupionych na tekście po układy obfitujące w obrazki. Ponadto obsługuje międzynarodową typografię i różnorodne palety kolorów, zapewniając dostępność i możliwość dostosowania dla użytkowników na całym świecie."

#. Theme Name of the theme
#: style.css patterns/footer-columns.php:66 patterns/footer-newsletter.php:42
#: patterns/footer.php:75 patterns/page-portfolio-home.php:226
#, gp-priority: high
msgid "Twenty Twenty-Five"
msgstr "Dwadzieścia Dwadzieścia-Pięć"

#: patterns/template-search-text-blog.php
msgctxt "Pattern title"
msgid "Text blog search results"
msgstr "Wyniki wyszukiwania bloga"

#: patterns/template-query-loop-vertical-header-blog.php
msgctxt "Pattern title"
msgid "Right-aligned query loop"
msgstr "Pętla zapytań z wyrównaniem do prawej"

#: patterns/template-query-loop-text-blog.php
msgctxt "Pattern title"
msgid "Text blog query loop"
msgstr "Pętla zapytań bloga"

#: patterns/text-faqs.php:35 patterns/text-faqs.php:51
#: patterns/text-faqs.php:71 patterns/text-faqs.php:87
msgctxt "Answer in the FAQs pattern."
msgid "This exquisite compilation showcases a diverse array of photographs that capture the essence of different eras and cultures, reflecting the unique styles and perspectives of each artist."
msgstr "Wyjątkowa kompilacja prezentuje różnorodne zdjęcia, które uchwyciły istotę różnych epok i kultur, odzwierciedlając unikatowe style i perspektywy każdego artysty."

#: theme.json
msgctxt "Custom template name"
msgid "Page No Title"
msgstr "Strona bez tytułu"

#: theme.json
msgctxt "Template part name"
msgid "Sidebar"
msgstr "Panel boczny"

#: theme.json
msgctxt "Template part name"
msgid "Footer Newsletter"
msgstr "Stopka biuletynu"

#: theme.json
msgctxt "Template part name"
msgid "Footer Columns"
msgstr "Kolumny stopki"

#: theme.json
msgctxt "Template part name"
msgid "Footer"
msgstr "Stopka"

#: theme.json
msgctxt "Template part name"
msgid "Header with large title"
msgstr "Nagłówek z dużym tytułem"

#: theme.json
msgctxt "Template part name"
msgid "Vertical site header"
msgstr "Pionowy nagłówek witryny"

#: theme.json
msgctxt "Template part name"
msgid "Header"
msgstr "Nagłówek"

#: styles/02-noon.json styles/typography/typography-preset-1.json
msgctxt "Font family name"
msgid "Beiruti"
msgstr "Beiruti"

#: styles/05-twilight.json styles/typography/typography-preset-4.json
msgctxt "Font family name"
msgid "Roboto Slab"
msgstr "Roboto Slab"

#: styles/04-afternoon.json styles/07-sunrise.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-6.json
msgctxt "Font family name"
msgid "Platypi"
msgstr "Platypi"

#: styles/04-afternoon.json styles/06-morning.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-5.json
msgctxt "Font family name"
msgid "Ysabeau Office"
msgstr "Ysabeau Office"

#: styles/08-midnight.json styles/typography/typography-preset-7.json
msgctxt "Font family name"
msgid "Fira Sans"
msgstr "Fira Sans"

#: theme.json styles/03-dusk.json styles/typography/typography-preset-2.json
msgctxt "Font family name"
msgid "Fira Code"
msgstr "Fira Code"

#: styles/03-dusk.json styles/typography/typography-preset-2.json
msgctxt "Font family name"
msgid "Vollkorn"
msgstr "Vollkorn"

#: styles/02-noon.json styles/06-morning.json styles/07-sunrise.json
#: styles/08-midnight.json styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-6.json
#: styles/typography/typography-preset-7.json
msgctxt "Font family name"
msgid "Literata"
msgstr "Literata"

#: theme.json styles/05-twilight.json
#: styles/typography/typography-preset-4.json
msgctxt "Font family name"
msgid "Manrope"
msgstr "Manrope"

#: theme.json
msgctxt "Space size name"
msgid "XX-Large"
msgstr "Bardzo bardzo duża"

#: theme.json
msgctxt "Space size name"
msgid "X-Large"
msgstr "Bardzo duża"

#: theme.json
msgctxt "Space size name"
msgid "Large"
msgstr "Duża"

#: theme.json
msgctxt "Space size name"
msgid "Regular"
msgstr "Zwykła"

#: theme.json
msgctxt "Space size name"
msgid "Small"
msgstr "Mała"

#: theme.json
msgctxt "Space size name"
msgid "X-Small"
msgstr "Bardzo mała"

#: theme.json
msgctxt "Space size name"
msgid "Tiny"
msgstr "Malutka"

#: styles/typography/typography-preset-7.json
msgctxt "Style variation name"
msgid "Literata & Fira Sans"
msgstr "Literata i Fira Sans"

#: styles/typography/typography-preset-6.json
msgctxt "Style variation name"
msgid "Platypi & Literata"
msgstr "Platypi i Literata"

#: styles/typography/typography-preset-5.json
msgctxt "Style variation name"
msgid "Literata & Ysabeau Office"
msgstr "Literata i Ysabeau Office"

#: styles/typography/typography-preset-4.json
msgctxt "Style variation name"
msgid "Roboto Slab & Manrope"
msgstr "Roboto Slab i Manrope"

#: styles/typography/typography-preset-3.json
msgctxt "Style variation name"
msgid "Platypi & Ysabeau Office"
msgstr "Platypi i Ysabeau Office"

#: styles/typography/typography-preset-2.json
msgctxt "Style variation name"
msgid "Vollkorn & Fira Code"
msgstr "Vollkorn i Fira Code"

#: styles/typography/typography-preset-1.json
msgctxt "Style variation name"
msgid "Beiruti & Literata"
msgstr "Beiruti i Literata"

#: styles/sections/section-5.json
msgctxt "Style variation name"
msgid "Style 5"
msgstr "Styl 5"

#: styles/sections/section-4.json
msgctxt "Style variation name"
msgid "Style 4"
msgstr "Styl 4"

#: styles/sections/section-3.json
msgctxt "Style variation name"
msgid "Style 3"
msgstr "Styl 3"

#: styles/sections/section-2.json
msgctxt "Style variation name"
msgid "Style 2"
msgstr "Styl 2"

#: styles/sections/section-1.json
msgctxt "Style variation name"
msgid "Style 1"
msgstr "Styl 1"

#: styles/blocks/post-terms-1.json
msgctxt "Style variation name"
msgid "Pill shaped"
msgstr "Owalne"

#: styles/blocks/03-annotation.json
msgctxt "Style variation name"
msgid "Annotation"
msgstr "Adnotacja"

#: styles/blocks/02-subtitle.json
msgctxt "Style variation name"
msgid "Subtitle"
msgstr "Podtytuł"

#: styles/blocks/01-display.json
msgctxt "Style variation name"
msgid "Display"
msgstr "Wyświetlacz"

#: styles/08-midnight.json styles/colors/08-midnight.json
msgctxt "Duotone name"
msgid "Midnight filter"
msgstr "Filtr nocny"

#: styles/08-midnight.json styles/colors/08-midnight.json
msgctxt "Style variation name"
msgid "Midnight"
msgstr "Północ"

#: styles/07-sunrise.json styles/colors/07-sunrise.json
msgctxt "Style variation name"
msgid "Sunrise"
msgstr "Brzask"

#: styles/06-morning.json styles/colors/06-morning.json
msgctxt "Style variation name"
msgid "Morning"
msgstr "Rano"

#: styles/05-twilight.json styles/colors/05-twilight.json
msgctxt "Style variation name"
msgid "Twilight"
msgstr "Zmierzch"

#: styles/04-afternoon.json styles/colors/04-afternoon.json
msgctxt "Style variation name"
msgid "Afternoon"
msgstr "Popołudnie"

#: styles/03-dusk.json styles/colors/03-dusk.json
msgctxt "Style variation name"
msgid "Dusk"
msgstr "Świt"

#: theme.json styles/02-noon.json styles/04-afternoon.json
#: styles/05-twilight.json styles/06-morning.json styles/08-midnight.json
#: styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-4.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-7.json
msgctxt "Font size name"
msgid "Extra Extra Large"
msgstr "Bardzo Bardzo Duży"

#: theme.json styles/02-noon.json styles/04-afternoon.json
#: styles/05-twilight.json styles/06-morning.json styles/08-midnight.json
#: styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-4.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-7.json
msgctxt "Font size name"
msgid "Extra Large"
msgstr "Bardzo duży"

#: theme.json styles/02-noon.json styles/04-afternoon.json
#: styles/05-twilight.json styles/06-morning.json styles/08-midnight.json
#: styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-4.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-7.json
msgctxt "Font size name"
msgid "Large"
msgstr "Duży"

#: theme.json styles/02-noon.json styles/04-afternoon.json
#: styles/05-twilight.json styles/06-morning.json styles/08-midnight.json
#: styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-4.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-7.json
msgctxt "Font size name"
msgid "Medium"
msgstr "Średni"

#: theme.json styles/02-noon.json styles/04-afternoon.json
#: styles/05-twilight.json styles/06-morning.json styles/08-midnight.json
#: styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-4.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-7.json
msgctxt "Font size name"
msgid "Small"
msgstr "Małe"

#: styles/02-noon.json styles/colors/02-noon.json
msgctxt "Style variation name"
msgid "Noon"
msgstr "Południe"

#: theme.json styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json
msgctxt "Color name"
msgid "Accent 6"
msgstr "Akcent 6"

#: theme.json styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json
msgctxt "Color name"
msgid "Accent 5"
msgstr "Akcent 5"

#: theme.json styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json
msgctxt "Color name"
msgid "Accent 4"
msgstr "Akcent 4"

#: theme.json styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json
msgctxt "Color name"
msgid "Accent 3"
msgstr "Akcent 3"

#: theme.json styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json
msgctxt "Color name"
msgid "Accent 2"
msgstr "Akcent 2"

#: theme.json styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json
msgctxt "Color name"
msgid "Accent 1"
msgstr "Akcent 1"

#: theme.json styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json
msgctxt "Color name"
msgid "Contrast"
msgstr "Kontrast"

#: theme.json styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json
msgctxt "Color name"
msgid "Base"
msgstr "Bazowy"

#: styles/01-evening.json styles/colors/01-evening.json
msgctxt "Style variation name"
msgid "Evening"
msgstr "Wieczór"

#: patterns/vertical-header.php
msgctxt "Pattern description"
msgid "Vertical site header with site title and navigation."
msgstr "Pionowy nagłówek witryny z tytułem strony i nawigacją"

#: patterns/vertical-header.php
msgctxt "Pattern title"
msgid "Vertical site header"
msgstr "Pionowy nagłówek witryny"

#: patterns/text-faqs.php:83
msgctxt "Question in the FAQs pattern."
msgid "Are signed copies available?"
msgstr "Czy są dostępne podpisane kopie?"

#: patterns/text-faqs.php:67
msgctxt "Question in the FAQs pattern."
msgid "When will The Stories Book be released?"
msgstr "Kiedy zbiór opowiadań zostanie wydany?"

#: patterns/text-faqs.php:47
msgctxt "Question in the FAQs pattern."
msgid "How much does The Stories Book cost?"
msgstr "Ile kosztuje zbiór opowiadań?"

#: patterns/text-faqs.php:31
msgctxt "Question in the FAQs pattern."
msgid "What is The Stories Book about?"
msgstr "O czym są opowiadania?"

#: patterns/text-faqs.php:21
msgctxt "Heading of the FAQs pattern."
msgid "Frequently Asked Questions"
msgstr "Często zadawane pytania"

#: patterns/text-faqs.php
msgctxt "Pattern description"
msgid "A FAQs section with a FAQ heading and list of questions and answers."
msgstr "Sekcja FAQ z nagłówkami i lista pytań i odpowiedzi."

#: patterns/text-faqs.php
msgctxt "Pattern title"
msgid "FAQs"
msgstr "Często zadawane pytania"

#: patterns/testimonials-large.php:47
msgctxt "Alt text for testimonial image."
msgid "Picture of a person typing on a typewriter."
msgstr "Obrazek przedstawiający osobę piszącą na maszynie."

#: patterns/testimonials-large.php:24
msgctxt "Testimonial heading."
msgid "What people are saying"
msgstr "Co mówią ludzie"

#: patterns/testimonials-large.php
msgctxt "Pattern description"
msgid "A testimonial with a large image on the right."
msgstr "Referencja z dużym obrazkiem po prawej stronie."

#: patterns/testimonials-large.php
msgctxt "Pattern title"
msgid "Review with large image on right"
msgstr "Recenzja z dużym obrazkiem po prawej stronie"

#: patterns/testimonials-6-col.php:18
msgctxt "Testimonial section heading."
msgid "What people are saying"
msgstr "Co mówią ludzie"

#: patterns/testimonials-6-col.php
msgctxt "Pattern description"
msgid "A section with three columns and two rows, each containing a testimonial and citation."
msgstr "Sekcja z dwoma rzędami i trzema kolumnami, każdy zawierający opinię i opis"

#: patterns/testimonials-6-col.php
msgctxt "Pattern title"
msgid "3 column layout with 6 testimonials"
msgstr "3 kolumnowy układ z 6 opiniami"

#: patterns/testimonials-2-col.php:67 patterns/testimonials-6-col.php:34
#: patterns/testimonials-6-col.php:51 patterns/testimonials-6-col.php:68
#: patterns/testimonials-6-col.php:89 patterns/testimonials-6-col.php:104
#: patterns/testimonials-6-col.php:119
msgctxt "Sample testimonial citation."
msgid "Otto Reid <br><sub>Springfield, IL</sub>"
msgstr "Otto Reid <br><sub>Springfield, IL</sub>"

#: patterns/testimonials-2-col.php:65 patterns/testimonials-6-col.php:30
#: patterns/testimonials-6-col.php:47 patterns/testimonials-6-col.php:64
#: patterns/testimonials-6-col.php:85 patterns/testimonials-6-col.php:101
#: patterns/testimonials-6-col.php:116
msgctxt "Sample testimonial."
msgid "“Amazing quality and care. I love all your products.”"
msgstr "„Wspaniała jakość i obsługa. Uwielbiam wszystkie twoje produkty.”"

#: patterns/testimonials-2-col.php:38 patterns/testimonials-large.php:36
msgctxt "Sample testimonial citation."
msgid "Jo Mulligan <br /><sub>Atlanta, GA</sub>"
msgstr "Jo Mulligan <br /><sub>Atlanta, GA</sub>"

#: patterns/testimonials-2-col.php:36 patterns/testimonials-large.php:32
msgctxt "Sample testimonial."
msgid "“Superb product and customer service!”"
msgstr "„Doskonały produkt i świetna obsługa klienta!”"

#: patterns/testimonials-2-col.php:26 patterns/testimonials-2-col.php:55
msgctxt "Alt text for testimonial image."
msgid "Picture of a person"
msgstr "Zdjęcie osoby"

#: patterns/testimonials-2-col.php
msgctxt "Pattern description"
msgid "Two columns with testimonials and avatars."
msgstr "Dwie kolumny z referencjami i awatarami."

#: patterns/testimonials-2-col.php
msgctxt "Pattern title"
msgid "2 columns with avatar"
msgstr "2 kolumny z awatarem"

#: patterns/template-single-vertical-header-blog.php
msgctxt "Pattern title"
msgid "Right-aligned single post"
msgstr "Pojedynczy wpis z wyrównaniem do prawej"

#: patterns/template-single-text-blog.php
msgctxt "Pattern title"
msgid "Text blog single post"
msgstr "Pojedynczy wpis na blogu"

#: patterns/template-single-photo-blog.php:79
msgid "Next Photo"
msgstr "Następne zdjęcie"

#: patterns/template-single-photo-blog.php:78
msgid "Previous Photo"
msgstr "Poprzednie zdjęcie"

#: patterns/template-single-photo-blog.php:61
msgctxt "Prefix before one or more tags. The tags are displayed in a separate block on the next line."
msgid "Tagged:"
msgstr "Oznaczony:"

#: patterns/template-single-photo-blog.php:53
msgctxt "Prefix before one or more categories. The categories are displayed in a separate block on the next line."
msgid "Categories:"
msgstr "Kategorie:"

#: patterns/template-single-photo-blog.php:42
msgctxt "Prefix before the author name. The post author name is displayed in a separate block on the next line."
msgid "Posted by"
msgstr "Napisany przez"

#: patterns/template-single-photo-blog.php
msgctxt "Pattern title"
msgid "Photo blog single post"
msgstr "Pojedynczy wpis foto bloga"

#: patterns/template-single-offset.php:40
#: patterns/template-single-photo-blog.php:36
msgctxt "Prefix before the post date block."
msgid "Published on"
msgstr "Opublikowany"

#: patterns/template-single-offset.php
msgctxt "Pattern title"
msgid "Offset post without featured image"
msgstr "Przesunięty wpis bez obrazka wyróżniającego"

#: patterns/template-single-news-blog.php
msgctxt "Pattern title"
msgid "News blog single post with sidebar"
msgstr "Pojedynczy wpis bloga z paskiem bocznym"

#: patterns/template-home-with-sidebar-news-blog.php:88
#: patterns/template-single-left-aligned-content.php:56
#: patterns/template-single-news-blog.php:39
msgctxt "Separator between date and categories."
msgid "·"
msgstr "·"

#: patterns/template-single-left-aligned-content.php:31
msgctxt "Prefix before the author name. The post author name is displayed in a separate block."
msgid "by"
msgstr "przez"

#: patterns/template-single-left-aligned-content.php
msgctxt "Pattern title"
msgid "Post with left-aligned content"
msgstr "Wpis z zawartością wyrównaną do lewej"

#: patterns/template-search-vertical-header-blog.php
msgctxt "Pattern title"
msgid "Right-aligned blog, search"
msgstr "Blog wyrównany do prawej, wyszukiwanie"

#: patterns/template-search-photo-blog.php
msgctxt "Pattern title"
msgid "Photo blog search results"
msgstr "Wyniki wyszukiwania foto bloga"

#: patterns/template-search-news-blog.php
msgctxt "Pattern title"
msgid "News blog search results"
msgstr "Wyniki wyszukiwania bloga"

#: patterns/template-query-loop.php
msgctxt "Pattern description"
msgid "A list of posts, 1 column, with featured image and post date."
msgstr "Lista wpisów, 1 kolumna, z wyróżnionym obrazkiem i datą wpisu."

#: patterns/template-query-loop.php
msgctxt "Pattern title"
msgid "List of posts, 1 column"
msgstr "Lista wpisów, 1 kolumna"

#: patterns/template-page-vertical-header-blog.php
msgctxt "Pattern title"
msgid "Right-aligned page"
msgstr "Strona wyrównana do prawej"

#: patterns/template-query-loop-photo-blog.php
msgctxt "Pattern description"
msgid "A list of posts, 3 columns, with only featured images."
msgstr "Lista wpisów, 3 kolumny z obrazkami wyróżniającymi."

#: patterns/template-query-loop-photo-blog.php
msgctxt "Pattern title"
msgid "Photo blog posts"
msgstr "Wpisy foto bloga"

#: patterns/template-query-loop-news-blog.php:49
msgid "Older Posts"
msgstr "Starsze wpisy"

#: patterns/template-query-loop-news-blog.php:45
msgid "Newer Posts"
msgstr "Nowsze wpisy"

#: patterns/template-query-loop-news-blog.php:30
msgctxt "Prefix before the author name. The post author name is displayed in a separate block."
msgid "Written by"
msgstr "Napisane przez"

#: patterns/template-query-loop-news-blog.php
msgctxt "Pattern title"
msgid "News blog query loop"
msgstr "Pętla zapytania bloga informacyjnego"

#: patterns/template-page-photo-blog.php
msgctxt "Pattern title"
msgid "Photo blog page"
msgstr "Strona bloga fotograficznego"

#: patterns/template-home-with-sidebar-news-blog.php:42
msgid "The Latest"
msgstr "Najnowsze"

#: patterns/template-home-with-sidebar-news-blog.php
msgctxt "Pattern title"
msgid "News blog with sidebar"
msgstr "Blog informacyjny z panelem bocznym"

#: patterns/template-home-text-blog.php
msgctxt "Pattern title"
msgid "Text blog home"
msgstr "Blog tylko z tekstem, strona główna"

#: patterns/template-home-posts-grid-news-blog.php:114
msgid "Architecture"
msgstr "Architektura"

#: patterns/template-home-posts-grid-news-blog.php
msgctxt "Pattern title"
msgid "News blog with featured posts grid"
msgstr "Blog informacyjny z siatką wyróżnionych wpisów"

#: patterns/template-home-photo-blog.php
msgctxt "Pattern title"
msgid "Photo blog home"
msgstr "Strona główna bloga fotograficznego"

#: patterns/template-home-news-blog.php
msgctxt "Pattern title"
msgid "News blog home"
msgstr "Strona główna bloga informacyjnego"

#: patterns/template-archive-text-blog.php
msgctxt "Pattern title"
msgid "Text blog archive"
msgstr "Blog tylko z tekstem, archiwum"

#: patterns/template-archive-photo-blog.php
msgctxt "Pattern title"
msgid "Photo blog archive"
msgstr "Archiwum bloga fotograficznego"

#: patterns/template-archive-news-blog.php
msgctxt "Pattern title"
msgid "News blog archive"
msgstr "Archiwum bloga informacyjnego"

#: patterns/services-team-photos.php:50
msgid "Man in hat, standing in front of a building."
msgstr "Mężczyzna w kapeluszu stojący przed budynkiem."

#: patterns/services-team-photos.php:44
msgid "Picture of a person typing on a typewriter."
msgstr "Obraz osoby piszącej na maszynie."

#: patterns/services-team-photos.php:38
msgid "Portrait of a nurse"
msgstr "Portret pielęgniarki"

#: patterns/services-team-photos.php:21
msgid "Our small team is a group of driven, detail-oriented people who are passionate about their customers."
msgstr "Nasz niewielki zespół składa się z ambitnych, zorientowanych na szczegóły osób, dla których klienci są prawdziwą pasją."

#: patterns/services-team-photos.php
msgctxt "Pattern description"
msgid "Display team photos in a services section with grid layout."
msgstr "Wyświetl zdjęcia zespołu w sekcji usług z układem siatki."

#: patterns/services-team-photos.php
msgctxt "Pattern title"
msgid "Services, team photos"
msgstr "Usługi, zdjęcia zespołu"

#: patterns/services-subscriber-only-section.php:69
msgid "Smartphones capturing a scenic wildflower meadow with trees"
msgstr "Smartfony rejestrujące malowniczą łąkę z dzikimi kwiatami i drzewami"

#: patterns/services-subscriber-only-section.php:55
msgid "View plans"
msgstr "Zobacz plany"

#: patterns/services-subscriber-only-section.php:21
msgid "Subscribe to get unlimited access"
msgstr "Subskrybuj, aby uzyskać nieograniczony dostęp."

#: patterns/services-subscriber-only-section.php
msgctxt "Pattern description"
msgid "A subscriber-only section highlighting exclusive services and offerings."
msgstr "Sekcja tylko dla subskrybentów, prezentująca ekskluzywne usługi i oferty."

#: patterns/services-subscriber-only-section.php
msgctxt "Pattern title"
msgid "Services, subscriber only section"
msgstr "Usługi, sekcja tylko dla subskrybentów"

#: patterns/services-3-col.php:68
msgid "Deliver"
msgstr "Dostarczanie"

#: patterns/services-3-col.php:50
msgid "Assemble"
msgstr "Składanie"

#: patterns/services-3-col.php:36 patterns/services-3-col.php:54
#: patterns/services-3-col.php:72
msgid "Like flowers that bloom in unexpected places, every story unfolds with beauty and resilience"
msgstr "Podobnie jak kwiaty, które rozkwitają w nieoczekiwanych miejscach, każda historia rozwija się pięknie i dynamicznie"

#: patterns/services-3-col.php:32
msgid "Collect"
msgstr "Zbieranie"

#: patterns/services-3-col.php:27 patterns/services-3-col.php:45
#: patterns/services-3-col.php:63
msgid "Image for service"
msgstr "Obrazek usługi"

#: patterns/services-3-col.php:17
msgid "Our services"
msgstr "Nasze usługi"

#: patterns/services-3-col.php
msgctxt "Pattern description"
msgid "Three columns with images and text to showcase services."
msgstr "Trzy kolumny z obrazkami i tekstem prezentujące usługi."

#: patterns/services-3-col.php
msgctxt "Pattern title"
msgid "Services, 3 columns"
msgstr "Usługi, 3 kolumny"

#: patterns/pricing-3-col.php:125
msgid "40€"
msgstr "40 zł"

#: patterns/pricing-3-col.php:117
msgid "Get access to our paid newsletter and an unlimited pass."
msgstr "Uzyskaj dostęp do naszego płatnego newslettera i nielimitowanego karnetu."

#: patterns/pricing-3-col.php:113
msgctxt "Name of membership package."
msgid "Expert"
msgstr "Ekspert"

#: patterns/pricing-3-col.php:89 patterns/pricing-3-col.php:129
msgid "Month"
msgstr "Miesiąc"

#: patterns/pricing-3-col.php:85
msgid "20€"
msgstr "20 zł"

#: patterns/pricing-3-col.php:77
msgid "Get access to our paid newsletter and a limited pass for one event."
msgstr "Uzyskaj dostęp do naszego płatnego newslettera i limitowaną wejściówkę na jedno wydarzenie."

#: patterns/pricing-3-col.php:41
msgid "Get access to our free articles and weekly newsletter."
msgstr "Uzyskaj dostęp do naszych darmowych artykułów i cotygodniowego biuletynu."

#: patterns/pricing-3-col.php:19
msgid "Choose your membership"
msgstr "Wybierz swoje członkostwo"

#: patterns/pricing-3-col.php
msgctxt "Pattern description"
msgid "A three-column boxed pricing table designed to showcase services, descriptions, and pricing options."
msgstr "Tabela cenowa z trzema kolumnami, zaprojektowana do prezentacji usług, opisów i opcji cenowych."

#: patterns/pricing-3-col.php
msgctxt "Pattern title"
msgid "Pricing, 3 columns"
msgstr "Cennik, 3 kolumny"

#: patterns/pricing-2-col.php:82
msgid "20€/month"
msgstr "20 zł/miesiąc"

#: patterns/pricing-2-col.php:78 patterns/pricing-3-col.php:73
msgctxt "Name of membership package."
msgid "Single"
msgstr "Pojedyncze"

#: patterns/pricing-2-col.php:68 patterns/pricing-2-col.php:112
#: patterns/pricing-3-col.php:59 patterns/pricing-3-col.php:99
#: patterns/pricing-3-col.php:139
msgctxt "Button text, refers to joining a community. Verb."
msgid "Join"
msgstr "Dołącz"

#: patterns/pricing-2-col.php:60 patterns/pricing-2-col.php:104
#: patterns/services-subscriber-only-section.php:43
msgid "Join our forums."
msgstr "Dołącz do naszego forum."

#: patterns/pricing-2-col.php:56 patterns/pricing-2-col.php:100
#: patterns/services-subscriber-only-section.php:39
msgid "An elegant addition of home decor collection."
msgstr "Elegancki dodatek do domowej kolekcji dekoracji."

#: patterns/pricing-2-col.php:52 patterns/pricing-2-col.php:96
#: patterns/services-subscriber-only-section.php:35
msgid "Get a free tote bag."
msgstr "Zdobądź bezpłatną torbę."

#: patterns/pricing-2-col.php:48 patterns/pricing-2-col.php:92
#: patterns/services-subscriber-only-section.php:31
msgid "Join our IRL events."
msgstr "Dołącz do naszych spotkań stacjonarnych."

#: patterns/pricing-2-col.php:44 patterns/pricing-2-col.php:88
#: patterns/services-subscriber-only-section.php:27
msgid "Get access to our paid articles and weekly newsletter."
msgstr "Uzyskaj dostęp do naszych płatnych artykułów i cotygodniowego biuletynu."

#: patterns/pricing-2-col.php:38 patterns/pricing-3-col.php:49
msgid "0€"
msgstr "0 zł"

#: patterns/pricing-2-col.php:34 patterns/pricing-3-col.php:37
msgid "Free"
msgstr "Darmowe"

#: patterns/pricing-2-col.php:22
#: patterns/services-subscriber-only-section.php:61
msgid "Cancel or pause anytime."
msgstr "Anuluj lub wstrzymaj w dowolnym momencie."

#: patterns/pricing-2-col.php:18 patterns/pricing-3-col.php:23
msgid "Pricing"
msgstr "Cennik"

#: patterns/pricing-2-col.php
msgctxt "Pattern description"
msgid "Pricing section with two columns, pricing plan, description, and call-to-action buttons."
msgstr "Sekcja cenowa z dwiema kolumnami, planem cenowym, opisem i przyciskami wzywającymi do działania."

#: patterns/pricing-2-col.php
msgctxt "Pattern title"
msgid "Pricing, 2 columns"
msgstr "Cennik, 2 kolumny"

#: patterns/post-navigation.php:17 patterns/post-navigation.php:18
#: patterns/template-single-left-aligned-content.php:78
#: patterns/template-single-left-aligned-content.php:79
#: patterns/template-single-news-blog.php:95
#: patterns/template-single-news-blog.php:96
#: patterns/template-single-offset.php:61
#: patterns/template-single-offset.php:62
#: patterns/template-single-photo-blog.php:76
#: patterns/template-single-photo-blog.php:77
#: patterns/template-single-text-blog.php:36
#: patterns/template-single-text-blog.php:37
#: patterns/template-single-vertical-header-blog.php:82
#: patterns/template-single-vertical-header-blog.php:83
msgid "Post navigation"
msgstr "Nawigacja wpisu"

#: patterns/post-navigation.php
msgctxt "Pattern description"
msgid "Next and previous post links."
msgstr "Odnośniki do następnego i poprzedniego wpisu."

#: patterns/post-navigation.php
msgctxt "Pattern title"
msgid "Post navigation"
msgstr "Nawigacja wpisu"

#: patterns/page-shop-home.php
msgctxt "Pattern description"
msgid "A shop homepage pattern."
msgstr "Wzorzec strony głównej sklepu."

#: patterns/page-shop-home.php
msgctxt "Pattern title"
msgid "Shop homepage"
msgstr "Strona główna sklepu"

#: patterns/page-portfolio-home.php:229
msgctxt "Phone number."
msgid "****** 349 1806"
msgstr "+48 123 456 789"

#: patterns/page-portfolio-home.php:229
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: patterns/page-portfolio-home.php:27
msgid "My name is Anna Möller and these are some of my photo projects."
msgstr "Nazywam się Anna Möller i oto kilka moich projektów fotograficznych."

#: patterns/page-portfolio-home.php
msgctxt "Pattern description"
msgid "A portfolio homepage pattern."
msgstr "Wzorzec strony głównej portfolio."

#: patterns/page-portfolio-home.php
msgctxt "Pattern title"
msgid "Portfolio homepage"
msgstr "Strona główna portfolio"

#: patterns/page-link-in-bio-with-tight-margins.php:42
msgid "I’m Asahachi Kōno, a Japanese photographer, a member of Los Angeles’s Japanese Camera Pictorialists of California. Before returning to Japan, I worked as a photo retoucher."
msgstr "Jestem Asahachi Kōno, japońskim fotografem, członkiem grupy Japanese Camera Pictorialists of California w Los Angeles. Przed powrotem do Japonii pracowałem jako retuszer zdjęć."

#: patterns/page-link-in-bio-with-tight-margins.php:27
msgid "Black and white photo focusing on a woman and a child from afar."
msgstr "Czarno-białe zdjęcie na którym w oddali widać kobietę i dziecko."

#: patterns/page-link-in-bio-with-tight-margins.php
msgctxt "Pattern description"
msgid "A full-width, full-height link in bio section with an image, a paragraph and social links."
msgstr "Sekcja odnośników w biografii o pełnej szerokości i wysokości, z obrazkiem, akapitem i odnośnikami do mediów społecznościowych."

#: patterns/page-link-in-bio-with-tight-margins.php
msgctxt "Pattern title"
msgid "Link in bio with tight margins"
msgstr "Odnośniki w biografii  z wąskimi marginesami"

#: patterns/page-link-in-bio-wide-margins.php:38
msgctxt "Pattern placeholder text."
msgid "I’m Nora, a dedicated public interest attorney based in Denver. I’m a graduate of Stanford University."
msgstr "Nazywam się Nora, jestem prawnikiem działającym w interesie publicznym z siedzibą w Denver. Jestem absolwentką Uniwersytetu Stanforda."

#: patterns/page-link-in-bio-wide-margins.php:34
msgid "Nora Winslow Keene"
msgstr "Nora Winslow Keene"

#: patterns/page-link-in-bio-wide-margins.php
msgctxt "Pattern description"
msgid "A link in bio landing page with social links, a profile photo and a brief description."
msgstr "Strona docelowa odnośników w biografii z odnośnikami do mediów społecznościowych, zdjęciem profilowym i krótkim opisem."

#: patterns/page-link-in-bio-wide-margins.php
msgctxt "Pattern title"
msgid "Link in bio with profile, links and wide margins"
msgstr "Odnośniki w biografii z profilem, odnośnikami i szerokimi marginesami"

#: patterns/page-link-in-bio-heading-paragraph-links-image.php:57
msgid "Photo of a woman worker."
msgstr "Zdjęcie pracującej kobiety."

#: patterns/page-link-in-bio-heading-paragraph-links-image.php:30
msgid "Lewis W. Hine studied sociology before moving to New York in 1901 to work at the Ethical Culture School, where he took up photography to enhance his teaching practices"
msgstr "Lewis W. Hine studiował socjologię przed przeprowadzką do Nowego Jorku w 1901 roku, aby pracować w Ethical Culture School, gdzie zajął się fotografią, aby ulepszyć swoje nauczanie"

#: patterns/page-link-in-bio-heading-paragraph-links-image.php:26
msgid "Lewis Hine"
msgstr "Lewis Hine"

#: patterns/page-link-in-bio-heading-paragraph-links-image.php
msgctxt "Pattern description"
msgid "A link in bio landing page with a heading, paragraph, links and a full height image."
msgstr "Strona docelowa odnośników w biografii z nagłówkiem, akapitem, odnośnikami i obrazkiem na całą wysokość."

#: patterns/page-link-in-bio-heading-paragraph-links-image.php
msgctxt "Pattern title"
msgid "Link in bio heading, paragraph, links and full-height image"
msgstr "Odnośnik w biografii z nagłówkiem, akapitem, odnośnikami i obrazkiem na pełną wysokość"

#: patterns/page-landing-podcast.php
msgctxt "Pattern description"
msgid "A landing page for the podcast with a hero section, description, logos, grid with videos and newsletter signup."
msgstr "Strona docelowa dla podcastu z sekcją hero, opisem, logo, siatką z filmami oraz formularzem zapisu do newslettera."

#: patterns/page-landing-podcast.php
msgctxt "Pattern title"
msgid "Landing page for podcast"
msgstr "Strona docelowa dla podcastu"

#: patterns/page-landing-event.php
msgctxt "Pattern description"
msgid "A landing page for the event with a hero section, description, FAQs and call to action."
msgstr "Strona docelowa wydarzenia z sekcją bohatera, opisem, często zadawanymi pytaniami i wezwaniem do działania."

#: patterns/page-landing-event.php
msgctxt "Pattern title"
msgid "Landing page for event"
msgstr "Strona docelowa wydarzenia"

#: patterns/page-landing-book.php
msgctxt "Pattern description"
msgid "A landing page for the book with a hero section, pre-order links, locations, FAQs and newsletter signup."
msgstr "Strona docelowa książki z sekcją bohatera, linkami do przedsprzedaży, lokalizacjami, często zadawanymi pytaniami i zapisem do biuletynu."

#: patterns/page-landing-book.php
msgctxt "Pattern title"
msgid "Landing page for book"
msgstr "Strona docelowa dla książki"

#: patterns/page-cv-bio.php:47
msgctxt "Link to a page with information about what the person is working on right now."
msgid "Now"
msgstr "Obecnie"

#: patterns/page-cv-bio.php:47
msgid "LinkedIn"
msgstr "LinkedIn"

#: patterns/page-cv-bio.php:43 patterns/page-link-in-bio-wide-margins.php:24
#: patterns/services-team-photos.php:32
msgid "Woman on beach, splashing water."
msgstr "Kobieta na plaży chlapiąca wodą."

#: patterns/page-cv-bio.php:31
msgctxt "Pattern placeholder text."
msgid "My name is Nora Winslow Keene, and I’m a committed public interest attorney. Living in Denver, Colorado, I’ve spent years championing the rights of underrepresented workers. A graduate of Stanford University, I played a key role in securing critical protections for agricultural laborers, ensuring better wages and access to healthcare. My work has focused on advocating for environmental justice and improving the quality of life for rural communities. Every case I take on is driven by the belief that everyone deserves dignity and fair treatment in the workplace."
msgstr "Nazywam się Nora Winslow Keene i jestem zaangażowanym prawnikiem działającym w interesie publicznym. Mieszkając w Denver w stanie Kolorado, spędziłam lata broniąc praw niedostatecznie reprezentowanych pracowników. Jako absolwentka Uniwersytetu Stanforda odegrałam kluczową rolę w zapewnieniu krytycznej ochrony pracownikom rolnym, zapewniając im lepsze płace i dostęp do opieki zdrowotnej. Moja praca koncentrowała się na promowaniu sprawiedliwości środowiskowej i poprawie jakości życia społeczności wiejskich. W każdej sprawie, którą się zajmuję, kieruję się przekonaniem, że każdy zasługuje na godność i sprawiedliwe traktowanie w miejscu pracy."

#: patterns/page-cv-bio.php:28
msgctxt "Example heading in pattern."
msgid "Hey,"
msgstr "Cześć,"

#: patterns/page-cv-bio.php
msgctxt "Pattern description"
msgid "A pattern for a CV/Bio landing page."
msgstr "Wzorzec dla strony docelowej z CV/życiorysem."

#: patterns/page-cv-bio.php
msgctxt "Pattern title"
msgid "CV/bio"
msgstr "CV/życiorys"

#: patterns/page-coming-soon.php:33
msgid "Subscribe to get notified when our website is ready."
msgstr "Zapisz się, aby otrzymać powiadomienie, gdy nasza witryna internetowa będzie gotowa."

#: patterns/page-coming-soon.php:29
msgid "Something great is coming soon"
msgstr "Już wkrótce nadchodzi coś wspaniałego"

#: patterns/page-coming-soon.php:24
msgid "Event"
msgstr "Wydarzenie"

#: patterns/page-coming-soon.php
msgctxt "Pattern description"
msgid "A full-width cover banner that can be applied to a page or it can work as a single landing page."
msgstr "Pełnoekranowy baner okładkowy, który można zastosować na stronie lub który może pełnić funkcję pojedynczej strony docelowej."

#: patterns/page-coming-soon.php
msgctxt "Pattern title"
msgid "Coming soon"
msgstr "Wkrótce"

#: patterns/page-business-home.php
msgctxt "Pattern description"
msgid "A business homepage pattern."
msgstr "Wzorzec strony głównej biznesu."

#: patterns/page-business-home.php
msgctxt "Pattern title"
msgid "Business homepage"
msgstr "Strona główna firmy"

#: patterns/overlapped-images.php
msgctxt "Pattern description"
msgid "A section with overlapping images, and a description."
msgstr "Sekcja z nakładającymi się obrazkami i opisem."

#: patterns/overlapped-images.php
msgctxt "Pattern title"
msgid "Overlapping images and paragraph on right"
msgstr "Nakładające się obrazki i akapit po prawej stronie"

#: patterns/more-posts.php:18
msgid "More posts"
msgstr "Więcej wpisów"

#: patterns/more-posts.php
msgctxt "Pattern description"
msgid "Displays a list of posts with title and date."
msgstr "Wyświetla listę wpisów z tytułem i datą."

#: patterns/more-posts.php
msgctxt "Pattern title"
msgid "More posts"
msgstr "Więcej wpisów"

#: patterns/media-instagram-grid.php:56
msgid "Close up of two flowers on a dark background."
msgstr "Zbliżenie dwóch kwiatów na ciemnym tle."

#: patterns/media-instagram-grid.php:48
msgid "Portrait of an African Woman dressed in traditional costume, wearing decorative jewelry."
msgstr "Portret afrykańskiej kobiety ubranej w tradycyjny strój i noszącej ozdobną biżuterię."

#: patterns/media-instagram-grid.php:40
msgid "Profile portrait of a native person."
msgstr "Portret profilowy tubylca."

#: patterns/media-instagram-grid.php:28
msgctxt "Example username for social media account."
msgid "@example"
msgstr "@przykład"

#: patterns/media-instagram-grid.php
msgctxt "Pattern description"
msgid "A grid section with photos and a link to an Instagram profile."
msgstr "Sekcja siatki ze zdjęciami i odnośnikiem do profilu na Instagramie."

#: patterns/media-instagram-grid.php
msgctxt "Pattern title"
msgid "Instagram grid"
msgstr "Siatka Instagrama"

#: patterns/logos.php:17
msgid "The Stories Podcast is sponsored by"
msgstr "Podcast Opowieści jest sponsorowany przez"

#: patterns/logos.php
msgctxt "Pattern description"
msgid "Showcasing the podcast's clients with a heading and a series of client logos."
msgstr "Prezentacja klientów podcastu za pomocą nagłówka i serii logo klientów."

#: patterns/logos.php
msgctxt "Pattern title"
msgid "Logos"
msgstr "Logotypy"

#: patterns/hidden-written-by.php:20
msgid "in"
msgstr "w"

#: patterns/hidden-written-by.php:16
msgid "Written by "
msgstr "Autor: "

#: patterns/hidden-written-by.php
msgctxt "Pattern title"
msgid "Written by"
msgstr "Napisane przez"

#: patterns/hidden-sidebar.php:38 patterns/page-portfolio-home.php:65
#: patterns/page-portfolio-home.php:87 patterns/page-portfolio-home.php:121
#: patterns/page-portfolio-home.php:154 patterns/page-portfolio-home.php:176
#: patterns/page-portfolio-home.php:203 patterns/template-home-news-blog.php:40
#: patterns/template-home-posts-grid-news-blog.php:35
#: patterns/template-home-posts-grid-news-blog.php:60
#: patterns/template-home-posts-grid-news-blog.php:78
#: patterns/template-home-posts-grid-news-blog.php:103
#: patterns/template-home-with-sidebar-news-blog.php:62
#: patterns/template-home-with-sidebar-news-blog.php:119
#: patterns/template-query-loop-news-blog.php:55
#: patterns/template-query-loop-photo-blog.php:22
#: patterns/template-query-loop-text-blog.php:19
#: patterns/template-query-loop-vertical-header-blog.php:47
#: patterns/template-query-loop.php:31
msgctxt "Message explaining that there are no results returned from a search."
msgid "Sorry, but nothing was found. Please try a search with different keywords."
msgstr "Brak wyników. Prosimy spróbować wyszukiwania z innymi słowami kluczowymi."

#: patterns/hidden-sidebar.php:37
#: patterns/template-home-posts-grid-news-blog.php:34
#: patterns/template-home-with-sidebar-news-blog.php:61
msgid "Add text or blocks that will display when a query returns no results."
msgstr "Dodaj tekst lub bloki, które będą wyświetlane, gdy zapytanie nie zwróci żadnych wyników."

#: patterns/hidden-sidebar.php:14
msgid "Other Posts"
msgstr "Inne wpisy"

#: patterns/hidden-sidebar.php
msgctxt "Pattern title"
msgid "Sidebar"
msgstr "Panel boczny"

#: patterns/cta-heading-search.php:21 patterns/hidden-search.php:14
msgctxt "Button text. Verb."
msgid "Search"
msgstr "Szukaj"

#: patterns/hidden-search.php
msgctxt "Pattern title"
msgid "Search"
msgstr "Wyszukiwanie"

#: patterns/hidden-blog-heading.php
msgctxt "Pattern description"
msgid "Hidden heading for the home page and index template."
msgstr "Ukryty nagłówek dla strony głównej i szablonu indeksu."

#: patterns/hidden-blog-heading.php
msgctxt "Pattern title"
msgid "Hidden blog heading"
msgstr "Ukryty nagłówek blogu"

#: patterns/hidden-404.php:36
msgctxt "404 error message"
msgid "The page you are looking for doesn't exist, or it has been moved. Please try searching using the form below."
msgstr "Strona, której szukasz, nie istnieje lub została przeniesiona. Spróbuj wyszukać za pomocą poniższego formularza."

#: patterns/hidden-404.php:32
msgctxt "404 error message"
msgid "Page not found"
msgstr "Strona nie została znaleziona"

#: patterns/hidden-404.php:21
msgctxt "image description"
msgid "Small totara tree on ridge above Long Point"
msgstr "Małe drzewo totara na wzgórzu nad Long Point"

#: patterns/hidden-404.php
msgctxt "Pattern title"
msgid "404"
msgstr "404"

#: patterns/hero-podcast.php:65
msgctxt "Button text"
msgid "RSS"
msgstr "RSS"

#: patterns/hero-podcast.php:61
msgctxt "Button text"
msgid "Pocket Casts"
msgstr "Pocket Casts"

#: patterns/hero-podcast.php:57
msgctxt "Button text"
msgid "Spotify"
msgstr "Spotify"

#: patterns/hero-podcast.php:53
msgctxt "Button text"
msgid "Apple Podcasts"
msgstr "Podcasty Apple"

#: patterns/hero-podcast.php:49
msgctxt "Button text"
msgid "YouTube"
msgstr "YouTube"

#: patterns/hero-podcast.php:43
msgid "Subscribe on your favorite platform"
msgstr "Subskrybuj w swojej ulubionej platformie"

#: patterns/hero-podcast.php:36
msgctxt "Podcast description"
msgid "Storytelling, expert analysis, and vivid descriptions. The Stories Podcast brings history to life, making it accessible and engaging for a global audience."
msgstr "Opowiadanie historii, analiza ekspertów i żywe opisy. Podcast Stories ożywia historię, czyniąc ją dostępną i wciągającą dla każdego."

#: patterns/hero-podcast.php:32
msgid "The Stories Podcast"
msgstr "Podcast Opowieści"

#: patterns/hero-podcast.php:22
msgctxt "Alt text for hero image."
msgid "Picture of a person"
msgstr "Zdjęcie osoby"

#: patterns/hero-podcast.php
msgctxt "Pattern title"
msgid "Hero podcast"
msgstr "Sekcja hero dla podcastu"

#: patterns/hero-overlapped-book-cover-with-links.php:113
msgid "Book Image"
msgstr "Obrazek książki"

#: patterns/hero-overlapped-book-cover-with-links.php:34
msgctxt "Hero - Overlapped book cover pattern subline text"
msgid "A fine collection of moments in time featuring photographs from Louis Fleckenstein, Paul Strand and Asahachi Kōno."
msgstr "Wspaniała kolekcja uchwyconych w czasie klatek zawierająca fotografie Louisa Fleckensteina, Paula Stranda i Asahachi Kōno."

#: patterns/hero-overlapped-book-cover-with-links.php:28
msgctxt "Hero - Overlapped book cover pattern headline text"
msgid "The Stories Book"
msgstr "Książka Opowieści"

#: patterns/hero-overlapped-book-cover-with-links.php
msgctxt "Pattern description"
msgid "A hero with an overlapped book cover and links."
msgstr "Sekcja hero z nakładająca się okładką książki i odnośnikami."

#: patterns/hero-overlapped-book-cover-with-links.php
msgctxt "Pattern title"
msgid "Hero, overlapped book cover with links"
msgstr "Sekcja hero, nakładająca się okładka książki z odnośnikami"

#: patterns/hero-full-width-image.php:27
msgctxt "Sample hero paragraph"
msgid "Like flowers that bloom in unexpected places, every story unfolds with beauty and resilience, revealing hidden wonders."
msgstr "Podobnie jak kwiaty, które rozkwitają w nieoczekiwanych miejscach, każda historia rozwija się pięknie i dynamicznie, ujawniając ukryte cuda."

#: patterns/hero-full-width-image.php:23
msgctxt "Sample hero heading"
msgid "Tell your story"
msgstr "Podziel się swoją opowieścią"

#: patterns/hero-full-width-image.php:18
msgctxt "Alt text for cover image."
msgid "Picture of a flower"
msgstr "Zdjęcie kwiatu"

#: patterns/hero-full-width-image.php
msgctxt "Pattern description"
msgid "A hero with a full width image, heading, short paragraph and button."
msgstr "Sekcja hero z obrazkiem na pełną szerokość, nagłówkiem, krótkim akapitem i przyciskiem."

#: patterns/hero-full-width-image.php
msgctxt "Pattern title"
msgid "Hero, full width image"
msgstr "Sekcja hero, obrazek na pełną szerokość"

#: patterns/hero-book.php:46
msgctxt "CTA text of the hero section."
msgid "Available for pre-order now."
msgstr "Dostępne już teraz w przedsprzedaży."

#: patterns/hero-book.php:42
msgctxt "Content of the hero section."
msgid "A fine collection of moments in time featuring photographs from Louis Fleckenstein, Paul Strand and Asahachi Kōno."
msgstr "Wspaniała kolekcja uchwyconych w czasie klatek zawierająca fotografie Louisa Fleckensteina, Paula Stranda i Asahachi Kōno."

#: patterns/hero-book.php:38
msgctxt "Heading of the hero section."
msgid "The Stories Book"
msgstr "Książka opowieści"

#: patterns/hero-book.php:24
msgid "Image of the book"
msgstr "Obrazek książki"

#: patterns/hero-book.php
msgctxt "Pattern description"
msgid "A hero section for the book with a description and pre-order link."
msgstr "Sekcja hero dla książki zawierająca opis i odnośnik do przedsprzedaży."

#: patterns/hero-book.php
msgctxt "Pattern title"
msgid "Hero book"
msgstr "Sekcja hero dla książki"

#: patterns/heading-and-paragraph-with-image.php:36
msgctxt "Alt text for Overview picture."
msgid "Cliff Palace, Colorado"
msgstr "Cliff Palace, Kolorado"

#: patterns/heading-and-paragraph-with-image.php:27
msgctxt "Event Overview Text."
msgid "Held over a weekend, the event is structured around a series of exhibitions, workshops, and panel discussions. The exhibitions showcase a curated selection of photographs that tell compelling stories from various corners of the globe, each image accompanied by detailed narratives that provide context and deeper insight into the historical significance of the scenes depicted. These photographs are drawn from the archives of renowned photographers, as well as emerging talents, ensuring a blend of both classical and contemporary perspectives."
msgstr "Odbywające się w weekend wydarzenie składa się z serii wystaw, warsztatów i dyskusji panelowych. Wystawy prezentują wyselekcjonowany wybór fotografii, które opowiadają fascynujące historie z różnych zakątków świata, a każdemu obrazowi towarzyszą szczegółowe narracje, które zapewniają kontekst i głębszy wgląd w historyczne znaczenie przedstawionych scen. Fotografie te pochodzą z archiwów znanych fotografów, a także wschodzących talentów, zapewniając mieszankę zarówno klasycznych, jak i współczesnych perspektyw."

#: patterns/heading-and-paragraph-with-image.php:23
msgid "About the event"
msgstr "O wydarzeniu"

#: patterns/heading-and-paragraph-with-image.php
msgctxt "Pattern description"
msgid "A two-column section with a heading and paragraph on the left, and an image on the right."
msgstr "Sekcja dwukolumnowa z nagłówkiem i akapitem po lewej stronie oraz obrazkiem po prawej stronie."

#: patterns/heading-and-paragraph-with-image.php
msgctxt "Pattern title"
msgid "Heading and paragraph with image on the right"
msgstr "Nagłówek i akapit z obrazkiem po prawej stronie"

#: patterns/header.php
msgctxt "Pattern description"
msgid "Site header with site title and navigation."
msgstr "Nagłówek z tytułem witryny i nawigacją."

#: patterns/header.php
msgctxt "Pattern title"
msgid "Header"
msgstr "Nagłówek"

#: patterns/header-large-title.php
msgctxt "Pattern description"
msgid "Site header with large site title and right-aligned navigation."
msgstr "Nagłówek z dużym tytułem witryny i nawigacją wyrównaną do prawej."

#: patterns/header-large-title.php
msgctxt "Pattern title"
msgid "Header with large title"
msgstr "Nagłówek z dużym tytułem"

#: patterns/header-columns.php
msgctxt "Pattern description"
msgid "Site header with site title and navigation in columns."
msgstr "Nagłówek z tytułem witryny i nawigacją w kolumnach."

#: patterns/header-columns.php
msgctxt "Pattern title"
msgid "Header with columns"
msgstr "Nagłówek z kolumnami"

#: patterns/header-centered.php
msgctxt "Pattern description"
msgid "Site header with centered site title and navigation."
msgstr "Nagłówek z wyśrodkowanym tytułem witryny i nawigacją."

#: patterns/header-centered.php
msgctxt "Pattern title"
msgid "Centered site header"
msgstr "Wyśrodkowany nagłówek"

#: patterns/grid-with-categories.php:64
msgid "Sunflowers"
msgstr "Słoneczniki"

#: patterns/grid-with-categories.php:50
msgid "Cactus"
msgstr "Kaktus"

#: patterns/grid-with-categories.php:36
msgid "Anthuriums"
msgstr "Anturiumy"

#: patterns/grid-with-categories.php:29
msgid "Close up of a red anthurium."
msgstr "Zbliżenie czerwonego anturium."

#: patterns/grid-with-categories.php:22
msgid "Top Categories"
msgstr "Najpopularniejsze kategorie"

#: patterns/grid-with-categories.php
msgctxt "Pattern description"
msgid "A grid section with different categories."
msgstr "Sekcja siatki z różnymi kategoriami."

#: patterns/grid-with-categories.php
msgctxt "Pattern title"
msgid "Grid with categories"
msgstr "Siatka z kategoriami"

#: patterns/grid-videos.php:23
msgid "Podcast"
msgstr "Podcast"

#: patterns/grid-videos.php:19
msgid "Explore the episodes"
msgstr "Przeglądaj odcinki"

#: patterns/grid-videos.php
msgctxt "Pattern description"
msgid "A grid with videos."
msgstr "Siatka z filmami."

#: patterns/grid-videos.php
msgctxt "Pattern title"
msgid "Grid with videos"
msgstr "Siatka z filmami"

#: patterns/format-link.php:23
msgid "https://example.com"
msgstr "https://example.com"

#: patterns/format-link.php:17
msgid "The Stories Book, a fine collection of moments in time featuring photographs from Louis Fleckenstein, Paul Strand and Asahachi Kōno, is available for pre-order"
msgstr "The Stories Book, wspaniała kolekcja uchwyconych w czasie klatek zawierająca fotografie Louisa Fleckensteina, Paula Stranda i Asahachi Kōno, jest dostępna w przedsprzedaży"

#: patterns/format-link.php
msgctxt "Pattern description"
msgid "A link post format with a description and an emphasized link for key content."
msgstr "Format wpisu odnośnika z opisem oraz wyróżnionym odnośnikiem do kluczowej treści."

#: patterns/format-link.php
msgctxt "Pattern title"
msgid "Link format"
msgstr "Format odnośnika"

#: patterns/format-audio.php:30
msgid "Acoma Pueblo, in New Mexico, stands as a testament to the resilience and cultural heritage of the Acoma people"
msgstr "Acoma Pueblo w Nowym Meksyku jest świadectwem odporności i dziedzictwa kulturowego ludu Acoma."

#: patterns/format-audio.php:26
msgid "Episode 1: Acoma Pueblo with Prof. Fiona Presley"
msgstr "Odcinek 1: Pueblo Acoma z prof. Fioną Presley"

#: patterns/format-audio.php
msgctxt "Pattern description"
msgid "An audio post format with an image, title, audio player, and description."
msgstr "Dźwiękowy format wpisu z obrazkiem, tytułem, odtwarzaczem audio i opisem."

#: patterns/format-audio.php
msgctxt "Pattern title"
msgid "Audio format"
msgstr "Format dźwiękowy"

#: patterns/footer.php
msgctxt "Pattern description"
msgid "Footer columns with logo, title, tagline and links."
msgstr "Kolumny stopki z logo, tytułem, sloganem i odnośnikami."

#: patterns/footer.php
msgctxt "Pattern title"
msgid "Footer"
msgstr "Stopka"

#: patterns/footer-social.php
msgctxt "Pattern description"
msgid "Footer with centered site title and social links."
msgstr "Stopka z wyśrodkowanym tytułem witryny i odnośnikami do mediów społecznościowych."

#: patterns/footer-social.php
msgctxt "Pattern title"
msgid "Centered footer with social links"
msgstr "Wyśrodkowana stopka z odnośnikami do mediów społecznościowych"

#: patterns/footer-newsletter.php:24
msgid "Receive our articles in your inbox."
msgstr "Otrzymuj nasze artykuły w swojej skrzynce odbiorczej."

#: patterns/footer-newsletter.php
msgctxt "Pattern description"
msgid "Footer with large site title and newsletter signup."
msgstr "Stopka z dużym tytułem witryny i formularzem zapisu do newslettera."

#: patterns/footer-newsletter.php
msgctxt "Pattern title"
msgid "Footer with newsletter signup"
msgstr "Stopka z formularzem zapisu do newslettera"

#: patterns/footer-columns.php:52 patterns/footer.php:61
msgid "Themes"
msgstr "Motywy"

#: patterns/footer-columns.php:51 patterns/footer.php:59
msgid "Patterns"
msgstr "Wzorce"

#: patterns/footer-columns.php:50 patterns/footer.php:57
msgid "Shop"
msgstr "Sklep"

#: patterns/footer-columns.php:48
msgid "Featured"
msgstr "Wyróżnione"

#: patterns/footer-columns.php:39 patterns/footer.php:51
msgid "Authors"
msgstr "Autorzy"

#: patterns/footer-columns.php:38 patterns/footer.php:49
msgid "FAQs"
msgstr "Najczęściej zadawane pytania"

#: patterns/footer-columns.php:37 patterns/footer.php:47
msgid "About"
msgstr "O nas"

#: patterns/footer-columns.php:36 patterns/footer.php:45
#: patterns/hidden-blog-heading.php:15 patterns/template-home-text-blog.php:20
msgid "Blog"
msgstr "Blog"

#: patterns/footer-columns.php
msgctxt "Pattern description"
msgid "Footer columns with title, tagline and links."
msgstr "Kolumny stopki z tytułem, sloganem i odnośnikami."

#: patterns/footer-columns.php
msgctxt "Pattern title"
msgid "Footer with columns"
msgstr "Stopka z kolumnami"

#. translators: Designed with WordPress. %s: WordPress link.
#: patterns/footer-centered.php:33 patterns/footer-columns.php:73
#: patterns/footer-newsletter.php:49 patterns/footer-social.php:35
#: patterns/footer.php:82
msgid "Designed with %s"
msgstr "Stworzone z %s"

#: patterns/footer-centered.php
msgctxt "Pattern description"
msgid "Footer with centered site title and tagline."
msgstr "Stopka z wyśrodkowanym tytułem witryny i sloganem."

#: patterns/footer-centered.php
msgctxt "Pattern title"
msgid "Centered footer"
msgstr "Wyśrodkowana stopka"

#: patterns/event-schedule.php:174
msgid "An introduction to African dialects"
msgstr "Wprowadzenie do dialektów afrykańskich"

#: patterns/event-schedule.php:163
msgid "Black and white photo of an African woman."
msgstr "Czarno-białe zdjęcie afrykańskiej kobiety."

#: patterns/event-schedule.php:142
msgid "Ancient buildings and symbols"
msgstr "Starożytne budowle i symbole"

#: patterns/event-schedule.php:132 patterns/media-instagram-grid.php:52
msgid "The Acropolis of Athens."
msgstr "Akropol w Atenach."

#: patterns/event-schedule.php:89
msgid "Things you didn’t know about the deep ocean"
msgstr "Rzeczy, których nie wiesz o głębinach oceanu"

#: patterns/event-schedule.php:78 patterns/media-instagram-grid.php:44
msgid "View of the deep ocean."
msgstr "Widok na głęboki ocean."

#: patterns/event-schedule.php:65 patterns/event-schedule.php:97
#: patterns/event-schedule.php:150 patterns/event-schedule.php:182
msgctxt "Pattern placeholder text with link."
msgid "Lecture by <a href=\"#\">Prof. Fiona Presley</a>"
msgstr "Wykład prowadzony przez <a href=\"#\">prof. Fionę Presley</a>"

#: patterns/event-schedule.php:60 patterns/event-schedule.php:92
#: patterns/event-schedule.php:145 patterns/event-schedule.php:177
msgctxt "Example event time in pattern."
msgid "9 AM — 11 AM"
msgstr "9:00 — 11:00"

#: patterns/event-schedule.php:57
msgid "Fauna from North America and its characteristics"
msgstr "Fauna Ameryki Północnej i jej cechy charakterystyczne"

#: patterns/event-schedule.php:46 patterns/media-instagram-grid.php:60
msgid "Birds on a lake."
msgstr "Ptaki na jeziorze."

#: patterns/event-schedule.php:20
msgid "Agenda"
msgstr "Agenda"

#: patterns/event-schedule.php
msgctxt "Pattern description"
msgid "A section with specified dates and times for an event."
msgstr "Sekcja z określonymi datami i godzinami wydarzenia."

#: patterns/event-schedule.php
msgctxt "Pattern title"
msgid "Event schedule"
msgstr "Harmonogram wydarzenia"

#: patterns/event-rsvp.php:91
msgid "Close up photo of white flowers on a grey background"
msgstr "Zdjęcie zbliżenie białych kwiatów na szarym tle."

#: patterns/event-rsvp.php:81
msgctxt "Abbreviation for \"Please respond\"."
msgid "RSVP"
msgstr "RSVP"

#: patterns/event-rsvp.php:73
msgid "This immersive event celebrates the universal human experience through the lenses of history and ancestry, featuring a diverse array of photographers whose works capture the essence of different cultures and historical moments."
msgstr "To wciągające wydarzenie celebruje uniwersalne ludzkie doświadczenie przez pryzmat historii i pochodzenia, prezentując różnorodną gamę fotografów, których prace uchwyciły istotę różnych kultur i momentów historycznych."

#: patterns/event-rsvp.php:57
msgid "Free Workshop"
msgstr "Darmowe warsztaty"

#: patterns/event-rsvp.php
msgctxt "Pattern description"
msgid "RSVP for an upcoming event with a cover image and event details."
msgstr "Potwierdź swoje uczestnictwo w nadchodzącym wydarzeniu z obrazkiem w tle i szczegółami wydarzenia."

#: patterns/event-rsvp.php
msgctxt "Pattern title"
msgid "Event RSVP"
msgstr "Potwierdzenie obecności na wydarzeniu"

#: patterns/event-3-col.php:50 patterns/event-3-col.php:74
#: patterns/event-3-col.php:98
msgid "Event details"
msgstr "Szczegóły wydarzenia"

#: patterns/event-3-col.php:34 patterns/event-3-col.php:58
#: patterns/event-3-col.php:82 patterns/format-audio.php:20
msgid "Event image"
msgstr "Obraz wydarzenia"

#: patterns/event-3-col.php:24 patterns/event-schedule.php:23
msgid "These are some of the upcoming events."
msgstr "Oto niektóre z nadchodzących wydarzeń."

#: patterns/event-3-col.php:20 patterns/footer-columns.php:49
#: patterns/footer.php:55
msgid "Events"
msgstr "Wydarzenia"

#: patterns/event-3-col.php
msgctxt "Pattern description"
msgid "A header with title and text and three columns that show 3 events with their images and titles."
msgstr "Nagłówek z tytułem i tekstem oraz trzy kolumny, które pokazują 3 wydarzenia wraz z ich obrazkami i tytułami."

#: patterns/event-3-col.php
msgctxt "Pattern title"
msgid "Events, 3 columns with event images and titles"
msgstr "Wydarzenia, 3 kolumny z obrazkami i tytułami wydarzeń"

#: patterns/cta-newsletter.php:32 patterns/footer-newsletter.php:30
#: patterns/page-coming-soon.php:39
#: patterns/services-subscriber-only-section.php:51
msgid "Subscribe"
msgstr "Subskrybuj"

#: patterns/cta-newsletter.php:23
msgid "Get access to a curated collection of moments in time featuring photographs from historical relevance."
msgstr "Uzyskaj dostęp do starannie wyselekcjonowanej kolekcji wyjątkowych momentów, zawierającej fotografie o historycznym znaczeniu."

#: patterns/cta-newsletter.php:19
msgid "Sign up to get daily stories"
msgstr "Zapisz się, aby otrzymywać codzienne historie."

#: patterns/cta-newsletter.php
msgctxt "Pattern title"
msgid "Newsletter sign-up"
msgstr "Zapis do biuletynu"

#: patterns/cta-heading-search.php:21 patterns/hidden-search.php:14
msgctxt "Search input field placeholder text."
msgid "Type here..."
msgstr "Pisz tutaj…"

#: patterns/cta-heading-search.php:21 patterns/hidden-search.php:14
msgctxt "Search form label."
msgid "Search"
msgstr "Szukaj"

#: patterns/cta-heading-search.php:18
msgid "What are you looking for?"
msgstr "Czego szukasz?"

#: patterns/cta-heading-search.php
msgctxt "Pattern description"
msgid "Large heading with a search form for quick navigation."
msgstr "Duży nagłówek z formularzem wyszukiwania dla szybkiej nawigacji."

#: patterns/cta-heading-search.php
msgctxt "Pattern title"
msgid "Heading and search form"
msgstr "Nagłówek i formularz wyszukiwania"

#: patterns/cta-grid-products-link.php:134
msgid "Shop now"
msgstr "Kup teraz"

#: patterns/cta-grid-products-link.php:114
msgid "Botany flowers"
msgstr "Kwiaty botaniczne"

#: patterns/cta-grid-products-link.php:100
msgid "Cancel anytime"
msgstr "Anuluj w każdej chwili"

#: patterns/cta-grid-products-link.php:84
msgid "Free shipping"
msgstr "Darmowa wysyłka"

#: patterns/cta-grid-products-link.php:76
msgid "Tailored to your needs"
msgstr "Dopasowane do twoich potrzeb"

#: patterns/cta-grid-products-link.php:70
msgid "Flora of Akaka Falls State Park"
msgstr "Flora parku stanowego Akaka Falls"

#: patterns/cta-grid-products-link.php:59
msgid "30€"
msgstr "130 PLN"

#. translators: %s: Starting price, split into three rows using HTML <br> tags.
#. The price value has a font size set.
#: patterns/cta-grid-products-link.php:58
msgid "Starting at%s/month"
msgstr "Od %s/miesiąc"

#: patterns/cta-grid-products-link.php:38
msgid "Closeup of plantlife in the Malibu Canyon area"
msgstr "Zbliżenie na roślinność w rejonie kanionu Malibu."

#: patterns/cta-grid-products-link.php:32
msgid "Delivered every week"
msgstr "Dostarczane co tydzień"

#: patterns/cta-grid-products-link.php:26
#: patterns/cta-grid-products-link.php:126
msgid "Black and white flower"
msgstr "Czarno-biały kwiat"

#: patterns/cta-grid-products-link.php:20
msgid "Our online store."
msgstr "Nasz sklep internetowy."

#: patterns/cta-grid-products-link.php
msgctxt "Pattern description"
msgid "A call to action featuring product images."
msgstr "Wezwanie do działania z obrazkami produktów."

#: patterns/cta-grid-products-link.php
msgctxt "Pattern title"
msgid "Call to action with grid layout with products and link"
msgstr "Wezwanie do działania z układem siatki z produktami i odnośnikiem"

#: patterns/cta-events-list.php:106 patterns/cta-events-list.php:144
msgid "Thornville, OH, USA"
msgstr "Kraków, Polska"

#: patterns/cta-events-list.php:75
msgid "Mexico City, Mexico"
msgstr "Poznań, Polska"

#: patterns/cta-events-list.php:51 patterns/cta-events-list.php:89
#: patterns/cta-events-list.php:120 patterns/cta-events-list.php:158
msgid "Buy Tickets"
msgstr "Kup bilety"

#: patterns/cta-events-list.php:45 patterns/cta-events-list.php:83
#: patterns/cta-events-list.php:114 patterns/cta-events-list.php:152
#: patterns/event-3-col.php:44 patterns/event-3-col.php:68
#: patterns/event-3-col.php:92 patterns/event-rsvp.php:37
#: patterns/event-schedule.php:35 patterns/event-schedule.php:121
msgctxt "Example event date in pattern."
msgid "Mon, Jan 1"
msgstr "Poniedziałek, 1 stycznia"

#: patterns/cta-events-list.php:37
msgid "Atlanta, GA, USA"
msgstr "Wrocław, Polska"

#: patterns/cta-events-list.php:23
msgid "These are some of the upcoming events"
msgstr "Oto niektóre z nadchodzących wydarzeń"

#: patterns/cta-events-list.php:19
msgid "Upcoming events"
msgstr "Nadchodzące wydarzenia"

#: patterns/cta-events-list.php
msgctxt "Pattern description"
msgid "A list of events with call to action."
msgstr "Lista wydarzeń z wezwaniem do działania."

#: patterns/cta-events-list.php
msgctxt "Pattern title"
msgid "Events list"
msgstr "Lista wydarzeń"

#: patterns/banner-intro-image.php:42 patterns/cta-centered-heading.php:28
#: patterns/hero-full-width-image.php:33
msgid "Learn more"
msgstr "Dowiedz się więcej"

#: patterns/cta-centered-heading.php:22
msgid "Like flowers that bloom in unexpected places, every story unfolds with beauty and resilience, revealing hidden wonders."
msgstr "Podobnie jak kwiaty, które rozkwitają w nieoczekiwanych miejscach, każda historia rozwija się pięknie i dynamicznie, ujawniając ukryte cuda."

#: patterns/cta-centered-heading.php:19 patterns/cta-events-list.php:33
#: patterns/cta-events-list.php:102 patterns/event-3-col.php:40
#: patterns/event-3-col.php:64 patterns/event-3-col.php:88
#: patterns/template-home-photo-blog.php:27
msgid "Tell your story"
msgstr "Podziel się swoją historią"

#: patterns/cta-centered-heading.php
msgctxt "Pattern description"
msgid "A hero with a centered heading, paragraph and button."
msgstr "Sekcja hero z wyśrodkowanym nagłówkiem, akapitem i przyciskiem."

#: patterns/cta-centered-heading.php
msgctxt "Pattern title"
msgid "Centered heading"
msgstr "Wyśrodkowany nagłówek"

#: patterns/cta-book-locations.php:131
msgid "United Kingdom"
msgstr "Wielka Brytania"

#: patterns/cta-book-locations.php:119
msgid "United States"
msgstr "Stany Zjednoczone"

#: patterns/cta-book-locations.php:107
msgid "Switzerland"
msgstr "Szwajcaria"

#: patterns/cta-book-locations.php:95
msgid "New Zealand"
msgstr "Nowa Zelandia"

#: patterns/cta-book-locations.php:79
msgid "Japan"
msgstr "Japonia"

#: patterns/cta-book-locations.php:67
msgid "Canada"
msgstr "Kanada"

#: patterns/cta-book-locations.php:55
msgid "Brazil"
msgstr "Brazylia"

#: patterns/cta-book-locations.php:47 patterns/cta-book-locations.php:59
#: patterns/cta-book-locations.php:71 patterns/cta-book-locations.php:83
#: patterns/cta-book-locations.php:99 patterns/cta-book-locations.php:111
#: patterns/cta-book-locations.php:123 patterns/cta-book-locations.php:135
msgid "Book Store"
msgstr "Księgarnia"

#: patterns/cta-book-locations.php:43
msgid "Australia"
msgstr "Australia"

#: patterns/cta-book-locations.php:27
msgid "The Stories Book will be available from these international retailers."
msgstr "Książka Opowieści będzie dostępna w tych międzynarodowych sklepach."

#: patterns/cta-book-locations.php:23
msgid "International editions"
msgstr "Międzynarodowe wydania"

#: patterns/cta-book-locations.php
msgctxt "Pattern description"
msgid "A call to action section with links to get the book in the most popular locations."
msgstr "Sekcja wezwania do działania z odnośnikami do zakupu książki w najpopularniejszych miejscach."

#: patterns/cta-book-locations.php
msgctxt "Pattern title"
msgid "Call to action with locations"
msgstr "Wezwanie do działania z lokalizacjami"

#: patterns/cta-book-links.php:57
#: patterns/hero-overlapped-book-cover-with-links.php:100
msgctxt "Pattern placeholder text with link."
msgid "Outside Europe? View <a href=\"#\" rel=\"nofollow\">international editions</a>."
msgstr "Poza Europą? Zobacz <a href=\"#\" rel=\"nofollow\">wydania międzynarodowe</a>."

#: patterns/cta-book-links.php:51
msgctxt "Example brand name."
msgid "Simon &amp; Schuster"
msgstr "Simon i Schuster"

#: patterns/cta-book-links.php:47
msgctxt "Example brand name."
msgid "BAM!"
msgstr "BAM!"

#: patterns/cta-book-links.php:43
msgctxt "Example brand name."
msgid "Spotify"
msgstr "Spotify"

#: patterns/cta-book-links.php:39
msgctxt "Example brand name."
msgid "Bookshop.org"
msgstr "Bookshop.org"

#: patterns/cta-book-links.php:35
#: patterns/hero-overlapped-book-cover-with-links.php:62
msgctxt "Example brand name."
msgid "Apple Books"
msgstr "Apple Books"

#: patterns/cta-book-links.php:31
#: patterns/hero-overlapped-book-cover-with-links.php:84
msgctxt "Example brand name."
msgid "Barnes &amp; Noble"
msgstr "Barnes i Noble"

#: patterns/cta-book-links.php:27
#: patterns/hero-overlapped-book-cover-with-links.php:77
msgctxt "Example brand name."
msgid "Audible"
msgstr "Audible"

#: patterns/cta-book-links.php:23
#: patterns/hero-overlapped-book-cover-with-links.php:55
msgctxt "Example brand name."
msgid "Amazon"
msgstr "Amazon"

#: patterns/cta-book-links.php:17
msgid "Buy your copy of The Stories Book"
msgstr "Kup swój egzemplarz Książki Opowieści"

#: patterns/cta-book-links.php
msgctxt "Pattern description"
msgid "A call to action section with links to get the book in different websites."
msgstr "Sekcja wezwania do działania z odnośnikami do zakupu książki na różnych witrynach internetowych."

#: patterns/cta-book-links.php
msgctxt "Pattern title"
msgid "Call to action with book links"
msgstr "Wezwanie do działania z odnośnikami do książek"

#: patterns/contact-location-and-link.php:36
msgid "The business location"
msgstr "Lokalizacja biznesu"

#: patterns/contact-location-and-link.php:26
msgid "Get directions"
msgstr "Zobacz wskazówki dojazdu"

#: patterns/contact-location-and-link.php:22
msgid "Visit us at 123 Example St. Manhattan, NY 10300, United States"
msgstr "Odwiedź nas na Ulicy Przykładowej 123, 61-000 Poznań, Polska"

#: patterns/contact-location-and-link.php
msgctxt "Pattern description"
msgid "Contact section with a location address, a directions link, and an image of the location."
msgstr "Sekcja kontaktowa z adresem lokalizacji, odnośnikiem do wskazówek dojazdu oraz obrazkiem lokalizacji."

#: patterns/contact-location-and-link.php
msgctxt "Pattern title"
msgid "Contact location and link"
msgstr "Lokalizacja do odwiedzin i odnośnik"

#: patterns/contact-info-locations.php:86
msgid "Portland"
msgstr "Portland"

#: patterns/contact-info-locations.php:74
msgid "Salt Lake City"
msgstr "Gdańsk"

#: patterns/contact-info-locations.php:62
msgid "San Diego"
msgstr "Kraków"

#: patterns/contact-info-locations.php:54
#: patterns/contact-info-locations.php:66
#: patterns/contact-info-locations.php:78
#: patterns/contact-info-locations.php:90
msgid "123 Example St. Manhattan, NY 10300 United States"
msgstr "Ulica Przykładowa 123, 61-000 Poznań, Polska"

#: patterns/contact-info-locations.php:51
msgid "New York"
msgstr "Nowy Jork"

#: patterns/contact-info-locations.php:41
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: patterns/contact-info-locations.php:38
msgid "Email"
msgstr "Adres e-mail"

#: patterns/contact-info-locations.php:35
#: patterns/page-link-in-bio-heading-paragraph-links-image.php:44
#: patterns/page-link-in-bio-with-tight-margins.php:56
msgid "TikTok"
msgstr "TikTok"

#: patterns/contact-info-locations.php:34 patterns/footer-social.php:21
msgid "Facebook"
msgstr "Facebook"

#: patterns/contact-info-locations.php:33 patterns/footer-social.php:22
#: patterns/media-instagram-grid.php:24 patterns/page-cv-bio.php:47
#: patterns/page-link-in-bio-heading-paragraph-links-image.php:36
#: patterns/page-link-in-bio-with-tight-margins.php:48
msgid "Instagram"
msgstr "Instagram"

#: patterns/contact-info-locations.php:32 patterns/footer-social.php:23
#: patterns/page-link-in-bio-heading-paragraph-links-image.php:40
#: patterns/page-link-in-bio-with-tight-margins.php:52
msgctxt "Refers to the social media platform formerly known as Twitter."
msgid "X"
msgstr "X"

#: patterns/contact-info-locations.php:29
#: patterns/contact-info-locations.php:31 patterns/footer-social.php:20
msgid "Social media"
msgstr "Social media"

#: patterns/contact-info-locations.php:21
msgid "How to get in touch with us"
msgstr "Jak się z nami skontaktować"

#: patterns/contact-info-locations.php
msgctxt "Pattern description"
msgid "Contact section with social media links, email, and multiple location details."
msgstr "Sekcja kontaktowa z odnośnikami do mediów społecznościowych, adresem e-mail oraz szczegółami kilku lokalizacji."

#: patterns/contact-info-locations.php
msgctxt "Pattern title"
msgid "Contact, info and locations"
msgstr "Kontakt, informacje i lokalizacje"

#: patterns/contact-centered-social-link.php:21
msgctxt "Heading of the Contact social link pattern"
msgid "Got questions? <br><a href=\"#\" rel=\"nofollow\">Feel free to reach out.</a>"
msgstr "Masz pytania? <br><a href=\"#\" rel=\"nofollow\">Skontaktuj się z nami.</a>"

#: patterns/contact-centered-social-link.php
msgctxt "Pattern description"
msgid "Centered contact section with a prominent message and social media links."
msgstr "Wyśrodkowana sekcja kontaktowa z wyraźnym komunikatem i odnośnikami do mediów społecznościowych."

#: patterns/contact-centered-social-link.php
msgctxt "Pattern title"
msgid "Centered link and social links"
msgstr "Wyśrodkowany odnośnik oraz odnośniki do mediów społecznościowych"

#: patterns/comments.php:18
msgid "Comments"
msgstr "Komentarze"

#: patterns/comments.php
msgctxt "Pattern description"
msgid "Comments area with comments list, pagination, and comment form."
msgstr "Obszar komentarzy z listą komentarzy, paginacją i formularzem do wysłania komentarza."

#: patterns/comments.php
msgctxt "Pattern title"
msgid "Comments"
msgstr "Komentarze"

#: patterns/binding-format.php
msgctxt "Pattern description"
msgid "Prints the name of the post format with the help of the Block Bindings API."
msgstr "Wyświetla nazwę formatu wpisu za pomocą API powiązań bloków."

#: patterns/binding-format.php
msgctxt "Pattern title"
msgid "Post format name"
msgstr "Nazwa formatu wpisu"

#: patterns/banner-with-description-and-images-grid.php:48
#: patterns/overlapped-images.php:26
msgid "Black and white photography close up of a flower."
msgstr "Czarno-biała fotografia kwiatu z bliska."

#: patterns/banner-with-description-and-images-grid.php:42
#: patterns/overlapped-images.php:21
msgid "Photography close up of a red flower."
msgstr "Zdjęcie czerwonego kwiatu z bliska."

#. translators: %s is the brand name, e.g., 'Fleurs'.
#: patterns/banner-with-description-and-images-grid.php:31
#: patterns/overlapped-images.php:47
msgid "%s is a flower delivery and subscription business. Based in the EU, our mission is not only to deliver stunning flower arrangements across but also foster knowledge and enthusiasm on the beautiful gift of nature: flowers."
msgstr "%s to firma zajmująca się dostawą kwiatów oraz oferująca subskrypcję kwiatową. Z siedzibą w UE, naszą misją jest nie tylko dostarczanie zachwycających kompozycji kwiatowych, ale również wspieranie wiedzy i entuzjazmu na temat pięknego daru natury, jakim są kwiaty."

#: patterns/banner-with-description-and-images-grid.php:23
#: patterns/overlapped-images.php:37
msgid "About Us"
msgstr "O nas"

#: patterns/banner-with-description-and-images-grid.php
msgctxt "Pattern description"
msgid "A banner with a short paragraph, and two images displayed in a grid layout."
msgstr "Baner z krótkim akapitem oraz dwoma obrazkami wyświetlanymi w układzie siatki."

#: patterns/banner-with-description-and-images-grid.php
msgctxt "Pattern title"
msgid "Banner with description and images grid"
msgstr "Baner z opisem i siatką obrazków"

#: patterns/banner-poster.php:59
msgid "#stories"
msgstr "#historie"

#: patterns/banner-poster.php:51
msgid "Let’s hear them."
msgstr "Posłuchajmy ich."

#: patterns/banner-poster.php:39
msgid "Fuego Bar, Mexico City"
msgstr "Bar Fuego, Meksyk"

#: patterns/banner-poster.php:39
msgctxt "Example event date in pattern."
msgid "Aug 08—10 2025"
msgstr "08—10 sierpnia 2025"

#. translators: This string contains the word "Stories" in four different
#. languages with the first item in the locale's language.
#: patterns/banner-poster.php:28 patterns/cta-events-list.php:68
#: patterns/cta-events-list.php:137 patterns/event-rsvp.php:30
msgctxt "Placeholder heading in four languages."
msgid "“Stories, <span lang=\"es\">historias</span>, <span lang=\"uk\">iсторії</span>, <span lang=\"el\">iστορίες</span>”"
msgstr "„Historie, <span lang=\"es\">historias</span>, <span lang=\"uk\">iсторії</span>, <span lang=\"el\">iστορίες</span>”"

#: patterns/banner-poster.php:15
msgid "Picture of a historical building in ruins."
msgstr "Zdjęcie zabytkowego budynku w ruinie."

#: patterns/banner-poster.php
msgctxt "Pattern description"
msgid "A section that can be used as a banner or a landing page to announce an event."
msgstr "Sekcja, która może służyć jako baner lub strona docelowa w celu ogłoszenia wydarzenia."

#: patterns/banner-poster.php
msgctxt "Pattern title"
msgid "Poster-like section"
msgstr "Sekcja typu plakat"

#: patterns/banner-intro.php:22
#: patterns/banner-with-description-and-images-grid.php:32
#: patterns/footer-columns.php:46 patterns/overlapped-images.php:48
msgctxt "Example brand name."
msgid "Fleurs"
msgstr "Fleurs"

#. translators: %s is the brand name, e.g., 'Fleurs'.
#: patterns/banner-intro.php:21
msgctxt "Pattern placeholder text."
msgid "We're %s, our mission is to deliver exquisite flower arrangements that not only adorn living spaces but also inspire a deeper appreciation for natural beauty."
msgstr "Jesteśmy %s, naszą misją jest dostarczanie wykwintnych kompozycji kwiatowych, które nie tylko zdobią przestrzenie mieszkalne, ale także inspirują do głębszego doceniania piękna natury."

#: patterns/banner-intro.php
msgctxt "Pattern description"
msgid "A large left-aligned heading with a brand name emphasized in bold."
msgstr "Duży, wyrównany do lewej nagłówek z nazwą marki wyróżnioną pogrubioną czcionką."

#: patterns/banner-intro.php
msgctxt "Pattern title"
msgid "Intro with left-aligned description"
msgstr "Wprowadzenie z opisem wyrównanym do lewej"

#: patterns/banner-intro-image.php:35
msgctxt "Sample description for banner with flower."
msgid "Like flowers that bloom in unexpected places, every story unfolds with beauty and resilience, revealing hidden wonders."
msgstr "Podobnie jak kwiaty, które rozkwitają w nieoczekiwanych miejscach, każda historia rozwija się pięknie i dynamicznie, ujawniając ukryte cuda."

#: patterns/banner-intro-image.php:31
msgctxt "Heading for banner pattern."
msgid "New arrivals"
msgstr "Nowości"

#: patterns/banner-intro-image.php:22
msgctxt "Alt text for intro picture."
msgid "Picture of a flower"
msgstr "Zdjęcie kwiatu"

#: patterns/banner-intro-image.php
msgctxt "Pattern description"
msgid "A Intro pattern with Short heading, paragraph and image on the left."
msgstr "Wzorzec wprowadzenia z krótkim nagłówkiem, akapitem i obrazkiem po lewej stronie."

#: patterns/banner-intro-image.php
msgctxt "Pattern title"
msgid "Short heading and paragraph and image on the left"
msgstr "Krótki nagłówek i akapit oraz obrazek po lewej"

#: patterns/banner-cover-big-heading.php:27 patterns/footer-columns.php:33
#: patterns/footer-columns.php:35 patterns/footer-newsletter.php:20
#: patterns/template-home-photo-blog.php:22
msgid "Stories"
msgstr "Historie"

#: patterns/banner-cover-big-heading.php:20
#: patterns/media-instagram-grid.php:36 patterns/page-coming-soon.php:19
msgid "Photo of a field full of flowers, a blue sky and a tree."
msgstr "Zdjęcie przedstawiające pole pełne kwiatów, błękitne niebo i drzewo."

#: patterns/banner-cover-big-heading.php
msgctxt "Pattern description"
msgid "A full-width cover section with a large background image and an oversized heading."
msgstr "Pełnowymiarowa okładka z dużym obrazem w tle i powiększonym nagłówkiem."

#: patterns/banner-cover-big-heading.php
msgctxt "Pattern title"
msgid "Cover with big heading"
msgstr "Okładka z dużym nagłówkiem"

#: patterns/banner-about-book.php:34
msgid "Image of a book"
msgstr "Obrazek książki"

#: patterns/banner-about-book.php:26
msgctxt "Pattern placeholder text."
msgid "This exquisite compilation showcases a diverse array of photographs that capture the essence of different eras and cultures, reflecting the unique styles and perspectives of each artist. Fleckenstein’s evocative imagery, Strand’s groundbreaking modernist approach, and Kōno’s meticulous documentation of Japanese life come together in a harmonious blend that celebrates the art of photography. Each image in “The Stories Book” is accompanied by insightful commentary, providing historical context and revealing the stories behind the photographs. This collection is not only a visual feast but also a tribute to the power of photography to preserve and narrate the multifaceted experiences of humanity."
msgstr "Ta znakomita kompilacja prezentuje różnorodną gamę fotografii, które uchwyciły istotę różnych epok i kultur, odzwierciedlając unikalne style i perspektywy każdego artysty. Sugestywne obrazy Fleckensteina, przełomowe modernistyczne podejście Stranda i skrupulatna dokumentacja japońskiego życia Kōno łączą się w harmonijną mieszankę, która celebruje sztukę fotografii. Każdemu zdjęciu w „The Stories Book” towarzyszy wnikliwy komentarz, zapewniający kontekst historyczny i ujawniający historie kryjące się za fotografiami. Kolekcja jest nie tylko wizualną ucztą, ale także hołdem dla potęgi fotografii w zachowywaniu i opowiadaniu o różnorodnych doświadczeniach ludzkości."

#: patterns/banner-about-book.php:22
msgid "About the book"
msgstr "O książce"

#: patterns/banner-about-book.php
msgctxt "Pattern description"
msgid "Banner with book description and accompanying image for promotion."
msgstr "Baner z opisem książki i towarzyszącym obrazkiem do promocji."

#: patterns/banner-about-book.php
msgctxt "Pattern title"
msgid "Banner with book description"
msgstr "Baner z opisem książki"

#: functions.php:134
msgctxt "Label for the block binding placeholder in the editor"
msgid "Post format name"
msgstr "Nazwa formatu wpisu"

#: functions.php:114
msgid "A collection of post format patterns."
msgstr "Zbiór wzorców formatu wpisu."

#: functions.php:113
msgid "Post formats"
msgstr "Formaty wpisu"

#: functions.php:106
msgid "A collection of full page layouts."
msgstr "Kolekcja układów pełnych stron."

#: functions.php:105
msgid "Pages"
msgstr "Strony"

#: functions.php:76
msgid "Checkmark"
msgstr "Znacznik wyboru"

#. Author URI of the theme
#: style.css patterns/footer-centered.php:34 patterns/footer-columns.php:74
#: patterns/footer-newsletter.php:50 patterns/footer-social.php:36
#: patterns/footer.php:83
#, gp-priority: low
msgid "https://wordpress.org"
msgstr "https://pl.wordpress.org"

#. Author of the theme
#: style.css
#, gp-priority: low
msgid "the WordPress team"
msgstr "zespół WordPressa"

#. Theme URI of the theme
#: style.css
#, gp-priority: low
msgid "https://wordpress.org/themes/twentytwentyfive/"
msgstr "https://pl.wordpress.org/themes/twentytwentyfive/"
