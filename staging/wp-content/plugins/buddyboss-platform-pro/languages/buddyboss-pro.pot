# Copyright (C) 2025 BuddyBoss
# This file is distributed under the GPLv2 or later (license.txt).
msgid ""
msgstr ""
"Project-Id-Version: BuddyBoss Platform Pro 2.7.40\n"
"Report-Msgid-Bugs-To: https://www.buddyboss.com/contact/\n"
"POT-Creation-Date: 2025-06-12 18:53:41+00:00\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"PO-Revision-Date: 2025-MO-DA HO:MI+ZONE\n"
"Last-Translator: BuddyBoss <<EMAIL>>\n"
"Language-Team: BuddyBoss <<EMAIL>>\n"
"Language: en\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Poedit-Country: United States\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: "
"__;_e;_x:1,2c;_ex:1,2c;_n:1,2;_nx:1,2,4c;_n_noop:1,2;_nx_noop:1,2,3c;esc_"
"attr__;esc_html__;esc_attr_e;esc_html_e;esc_attr_x:1,2c;esc_html_x:1,2c;\n"
"X-Poedit-Basepath: ../\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-Bookmarks: \n"
"X-Textdomain-Support: yes\n"
"X-Generator: grunt-wp-i18n 1.0.3\n"

#. Plugin Name of the plugin/theme
msgid "BuddyBoss Platform Pro"
msgstr ""

#: buddyboss-platform-pro.php:38
msgid "requires the BuddyBoss Platform plugin to work. Please"
msgstr ""

#: buddyboss-platform-pro.php:39
msgid "install BuddyBoss Platform"
msgstr ""

#: buddyboss-platform-pro.php:40
msgid "first."
msgstr ""

#: buddyboss-platform-pro.php:53
msgid ""
"requires BuddyBoss Platform plugin version 1.3.5 or higher to work. Please "
"update BuddyBoss Platform."
msgstr ""

#: class-bb-platform-pro.php:73 class-bb-platform-pro.php:81
msgid "Cheatin&#8217; huh?"
msgstr ""

#: class-bb-platform-pro.php:325 includes/bb-pro-update-buddyboss.php:90
msgid "Release Notes"
msgstr ""

#: includes/access-control/bb-access-control-filters.php:483
msgid "You don't have access to send request membership."
msgstr ""

#: includes/access-control/bb-access-control-filters.php:707
msgid "You are restricted from sending new messages to this member."
msgstr ""

#: includes/access-control/bb-access-control-filters.php:709
#: includes/access-control/bb-access-control-filters.php:711
msgid "You are restricted from sending new messages to these members: "
msgstr ""

#: includes/access-control/bb-access-control-filters.php:711
#: includes/access-control/bb-access-control-rest-filters.php:479
#: includes/access-control/bb-access-control-rest-filters.php:555
msgid "..."
msgstr ""

#: includes/access-control/bb-access-control-functions.php:574
#: includes/integrations/meprlms/includes/admin/class-bb-meprlms-admin-integration-tab.php:312
#: includes/integrations/meprlms/includes/admin/class-bb-meprlms-admin-integration-tab.php:341
#: includes/integrations/meprlms/templates/admin/integration-tab-intro.php:90
#: includes/integrations/meprlms/templates/admin/integration-tab-intro.php:136
#: includes/integrations/onesignal/bb-onesignal-functions.php:56
#: includes/integrations/pusher/bb-pusher-admin-tab.php:238
#: includes/integrations/tutorlms/bb-tutorlms-admin-tab.php:337
#: includes/integrations/tutorlms/bb-tutorlms-admin-tab.php:364
#: includes/integrations/tutorlms/templates/admin/integration-tab-intro.php:90
#: includes/integrations/tutorlms/templates/admin/integration-tab-intro.php:132
#: includes/integrations/zoom/bp-zoom-functions.php:1308
#: includes/integrations/zoom/bp-zoom-functions.php:1335
#: includes/integrations/zoom/bp-zoom-functions.php:4237
#: includes/lib/buddyboss-updater/includes/views/package.php:20
#: includes/sso/admin/sso-fields.php:56 includes/sso/admin/sso-fields.php:116
#: includes/sso/admin/sso-fields.php:168 includes/sso/admin/sso-fields.php:241
#: includes/sso/admin/sso-fields.php:295 includes/sso/admin/sso-fields.php:353
#: includes/sso/admin/sso-fields.php:405 includes/sso/admin/sso-fields.php:469
msgid "View Tutorial"
msgstr ""

#: includes/access-control/bb-access-control-rest-filters.php:139
msgid "You don't have enough access to create an activity."
msgstr ""

#: includes/access-control/bb-access-control-rest-filters.php:142
msgid "You don't have enough access to create an activity comment."
msgstr ""

#: includes/access-control/bb-access-control-rest-filters.php:186
msgid "Sorry, You don't have enough access to update this activity."
msgstr ""

#: includes/access-control/bb-access-control-rest-filters.php:189
msgid "Sorry, you are not allowed to delete this activity."
msgstr ""

#: includes/access-control/bb-access-control-rest-filters.php:193
msgid "Sorry, You don't have enough access to create an activity comment."
msgstr ""

#: includes/access-control/bb-access-control-rest-filters.php:301
msgid "Sorry, You don't have enough membership to join the group."
msgstr ""

#: includes/access-control/bb-access-control-rest-filters.php:311
msgid "Sorry, You don't have enough membership to create a membership request."
msgstr ""

#: includes/access-control/bb-access-control-rest-filters.php:347
msgid ""
"Requested User don't have enough membership to approve membership requests "
"to this group."
msgstr ""

#: includes/access-control/bb-access-control-rest-filters.php:475
#: includes/access-control/bb-access-control-rest-filters.php:551
msgid ""
"You can no longer send replies to this thread as you are restricted from "
"sending messages to this member."
msgstr ""

#: includes/access-control/bb-access-control-rest-filters.php:477
#: includes/access-control/bb-access-control-rest-filters.php:479
#: includes/access-control/bb-access-control-rest-filters.php:553
#: includes/access-control/bb-access-control-rest-filters.php:555
msgid ""
"You can no longer send replies to this thread as you are restricted from "
"sending messages to these members: "
msgstr ""

#: includes/access-control/bb-access-control-rest-filters.php:643
msgid "You don't have enough access to send the friend request to this member."
msgstr ""

#: includes/access-control/includes/class-bb-access-control-access-control.php:65
msgid "LearnDash Group"
msgstr ""

#: includes/access-control/includes/class-bb-access-control-access-control.php:70
msgid "LifterLMS"
msgstr ""

#: includes/access-control/includes/class-bb-access-control-access-control.php:75
msgid "Memberium"
msgstr ""

#: includes/access-control/includes/class-bb-access-control-access-control.php:80
msgid "MemberPress"
msgstr ""

#: includes/access-control/includes/class-bb-access-control-access-control.php:85
msgid "Paid Memberships Pro"
msgstr ""

#: includes/access-control/includes/class-bb-access-control-access-control.php:90
msgid "Restrict Content Pro"
msgstr ""

#: includes/access-control/includes/class-bb-access-control-access-control.php:95
msgid "S2Member"
msgstr ""

#: includes/access-control/includes/class-bb-access-control-access-control.php:100
msgid "Wishlist Member"
msgstr ""

#: includes/access-control/includes/class-bb-access-control-access-control.php:105
msgid "WooCommerce Memberships"
msgstr ""

#: includes/access-control/includes/class-bb-access-control-gamipress.php:64
msgid "Achievement"
msgstr ""

#: includes/access-control/includes/class-bb-access-control-gamipress.php:69
msgid "Rank"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:107
msgid "Profile types"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:109
#: includes/access-control/includes/class-bb-access-control.php:317
msgid "Gender"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:115
#: includes/access-control/includes/class-bb-access-control.php:242
#. translators: Record type label. s
msgid "No %s found."
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:234
msgid "Memberships"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:236
msgid "Groups"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:297
msgid "WordPress Role"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:302
msgid "Profile Type"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:307
msgid "GamiPress"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:312
msgid "Membership"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:342
#: includes/access-control/includes/class-bb-access-control.php:351
msgid "Activity Access"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:343
#: includes/access-control/includes/class-bb-access-control.php:352
#: includes/access-control/includes/class-bb-access-control.php:453
#: includes/access-control/includes/class-bb-access-control.php:462
#: includes/access-control/includes/class-bb-access-control.php:570
#: includes/access-control/includes/class-bb-access-control.php:579
#: includes/access-control/includes/class-bb-access-control.php:763
#: includes/access-control/includes/class-bb-access-control.php:772
#: includes/access-control/includes/class-bb-access-control.php:971
#: includes/access-control/includes/class-bb-access-control.php:980
msgid " — requires license"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:363
msgid "Activity Posts"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:383
msgid "Note: These settings do not apply to administrators or group activity feeds."
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:399
#: includes/access-control/includes/class-bb-access-control.php:511
#: includes/access-control/includes/class-bb-access-control.php:626
#: includes/access-control/includes/class-bb-access-control.php:669
#: includes/access-control/includes/class-bb-access-control.php:702
#: includes/access-control/includes/class-bb-access-control.php:843
#: includes/access-control/includes/class-bb-access-control.php:1040
#: includes/integrations/meprlms/templates/admin/integration-tab-intro.php:38
#: includes/integrations/onesignal/templates/admin/integration-tab-intro.php:17
#: includes/integrations/pusher/templates/admin/integration-tab-intro.php:17
#: includes/integrations/tutorlms/templates/admin/integration-tab-intro.php:38
#: includes/integrations/zoom/templates/admin/integration-tab-intro.php:17
#. translators: %1$s - Platform Pro string, %2$s - License URL
#. translators: 1 - Platform Pro string, 2 - License URL
#. translators: %1$s - Platform Pro string, %2$s - License URL
#. translators: %1$s - Platform Pro string, %2$s - License URL
#. translators: %1$s - Platform Pro string, %2$s - License URL
#. translators: %1$s - Platform Pro string, %2$s - License URL
msgid "You need to activate a license key for %1$s to unlock this feature. %2$s"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:414
#: includes/access-control/includes/class-bb-access-control.php:526
#: includes/access-control/includes/class-bb-access-control.php:641
#: includes/access-control/includes/class-bb-access-control.php:684
#: includes/access-control/includes/class-bb-access-control.php:717
#: includes/access-control/includes/class-bb-access-control.php:858
#: includes/access-control/includes/class-bb-access-control.php:1055
#: includes/integrations/meprlms/templates/admin/integration-tab-intro.php:50
#: includes/integrations/onesignal/templates/admin/integration-tab-intro.php:32
#: includes/integrations/pusher/templates/admin/integration-tab-intro.php:32
#: includes/integrations/tutorlms/templates/admin/integration-tab-intro.php:53
#: includes/integrations/zoom/templates/admin/integration-tab-intro.php:32
msgid "Add License key"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:432
msgid "Select which members should have access to create activity posts, based on:"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:452
#: includes/access-control/includes/class-bb-access-control.php:461
msgid "Connection Access"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:474
msgid "Connection Request"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:494
#: includes/access-control/includes/class-bb-access-control.php:1022
msgid "Note: These settings do not apply to administrators."
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:569
#: includes/access-control/includes/class-bb-access-control.php:578
msgid "Messages Access"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:590
msgid "Send Messages"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:610
msgid "Note: These settings do not apply to administrators or group messages."
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:729
msgid ""
"Select which members should have access to send messages to other members, "
"based on:"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:730
msgid ""
"Members with the {{option_value}} {{select_value}} can send messages to "
"members with - Any Member / With Specific {{select_value}}(s)"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:741
msgid ""
"Select which members should have access to send connection requests to "
"other members, based on:"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:742
msgid ""
"Members with the {{option_value}} {{select_value}} can send connection "
"request to members with - Any Member / With Specific {{select_value}}(s)"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:762
#: includes/access-control/includes/class-bb-access-control.php:771
msgid "Media Access"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:785
msgid "Upload Photos"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:796
msgid "Upload Documents"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:807
msgid "Upload Videos"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:827
msgid "Note: These settings do not apply to administrators or group media."
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:876
msgid "Select which members should have access to upload photos, based on:"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:883
msgid ""
"Enable upload photos settings above in either profiles or messages or "
"forums, to control which members can upload photos in components above."
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:897
msgid "Select which members should have access to upload videos, based on:"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:904
msgid ""
"Enable upload videos settings above in either profiles or messages or "
"forums, to control which members can upload videos in components above."
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:918
msgid "Select which members should have access to upload documents, based on:"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:925
msgid ""
"Enable upload documents settings above in either profiles or messages or "
"forums, to control which members can upload documents in components above."
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:939
msgid "Select which members should have access to create groups, based on:"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:947
msgid ""
"Enable social group creation by all members above to control which members "
"can create groups."
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:970
#: includes/access-control/includes/class-bb-access-control.php:979
msgid "Group Access"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:992
msgid "Create Groups"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:1002
msgid "Join Groups"
msgstr ""

#: includes/access-control/includes/class-bb-access-control.php:1072
msgid ""
"Select which members should have access to join public groups or request "
"access to private groups, based on:"
msgstr ""

#: includes/access-control/templates/checkboxes-selected.php:114
#: includes/access-control/templates/multiple-options.php:53
msgid "Any"
msgstr ""

#: includes/access-control/templates/checkboxes-selected.php:116
#: includes/access-control/templates/multiple-options.php:60
msgid "Specific"
msgstr ""

#: includes/access-control/templates/core-options.php:61
msgid "- Select -"
msgstr ""

#: includes/access-control/templates/gamipress-options.php:52
#: includes/access-control/templates/gamipress-options.php:77
msgid "Select GamiPress Type"
msgstr ""

#: includes/access-control/templates/plugin-options.php:51
#: includes/access-control/templates/plugin-options.php:76
msgid "- Select Membership Type -"
msgstr ""

#: includes/bb-pro-update-buddyboss.php:84
msgid "Close pop-up"
msgstr ""

#: includes/bb-pro-update-buddyboss.php:85
#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:178
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:250
#: includes/integrations/zoom/templates/buddypress/zoom/meeting/recordings.php:279
#: includes/integrations/zoom/templates/buddypress/zoom/webinar/recordings.php:278
#: includes/reactions/includes/class-bb-reactions.php:409
#: includes/reactions/includes/class-bb-reactions.php:430
#: includes/reactions/templates/admin/emotion-picker/render-emotion-icons.php:21
msgid "Close"
msgstr ""

#: includes/bb-pro-update-buddyboss.php:91
msgid "BuddyBoss Platform Pro v"
msgstr ""

#: includes/bb-pro-update-buddyboss.php:95
msgid "Overview"
msgstr ""

#: includes/bb-pro-update-buddyboss.php:97
msgid "Changelog"
msgstr ""

#: includes/bb-pro-update-buddyboss.php:111
msgid "Welcome to BuddyBoss Theme 2.0 🥳"
msgstr ""

#: includes/bb-pro-update-buddyboss.php:112
msgid ""
"Check out the video below for a full walkthrough of all the new features "
"and updates available to you in this release."
msgstr ""

#: includes/bb-pro-update-buddyboss.php:117
#. translators: %1$s - Overview tab link for details.
msgid ""
"As this update contains a number of improvements to the theme’s colors, "
"layouts and styling, we recommend you reconfigure your Theme Options and "
"review any custom CSS you may have.  For more information on how to update, "
"%1$s."
msgstr ""

#: includes/bb-pro-update-buddyboss.php:122
#. translators: %1$s - Overview tab link for details. %2$s - tutorial text
msgid "check out this tutorial"
msgstr ""

#: includes/bb-pro-update-buddyboss.php:130
msgid "For more information, please watch the video below:"
msgstr ""

#: includes/bb-pro-update-buddyboss.php:145
msgid "Changes:"
msgstr ""

#: includes/integrations/meprlms/bb-meprlms-actions.php:62
#. translators: %1$s is the user link, %2$s is the course URL.
msgid "%1$s started on %2$s."
msgstr ""

#: includes/integrations/meprlms/bb-meprlms-actions.php:125
#. translators: %1$s is the user link, %2$s is the course URL.
msgid "%1$s completed %2$s."
msgstr ""

#: includes/integrations/meprlms/bb-meprlms-actions.php:197
#. translators: %1$s is the user link, %2$s is the lesson URL.
msgid "%1$s just completed %2$s"
msgstr ""

#: includes/integrations/meprlms/bb-meprlms-functions.php:532
msgid "No courses found!"
msgstr ""

#: includes/integrations/meprlms/bb-meprlms-functions.php:537
msgid "You have no access to any courses yet!"
msgstr ""

#: includes/integrations/meprlms/bb-meprlms-functions.php:537
msgid "This member has not access to any courses yet!"
msgstr ""

#: includes/integrations/meprlms/bb-meprlms-functions.php:542
#: includes/integrations/tutorlms/bb-tutorlms-functions.php:349
msgid "You have not created any courses yet!"
msgstr ""

#: includes/integrations/meprlms/bb-meprlms-functions.php:542
#: includes/integrations/tutorlms/bb-tutorlms-functions.php:349
msgid "This member has not created any courses yet!"
msgstr ""

#: includes/integrations/meprlms/bb-meprlms-functions.php:651
msgid "Search.."
msgstr ""

#: includes/integrations/meprlms/bb-meprlms-functions.php:652
msgid "Search For."
msgstr ""

#: includes/integrations/meprlms/bb-meprlms-functions.php:695
#: includes/integrations/tutorlms/bb-tutorlms-group-functions.php:69
msgid "Group member started a course"
msgstr ""

#: includes/integrations/meprlms/bb-meprlms-functions.php:696
#: includes/integrations/tutorlms/bb-tutorlms-group-functions.php:70
msgid "Group member completes a course"
msgstr ""

#: includes/integrations/meprlms/bb-meprlms-functions.php:697
msgid "Group member completes a lesson"
msgstr ""

#: includes/integrations/meprlms/bb-meprlms-functions.php:701
msgid "Group member completed an assignment"
msgstr ""

#: includes/integrations/meprlms/bb-meprlms-functions.php:705
msgid "Group member completed quiz"
msgstr ""

#: includes/integrations/meprlms/includes/admin/class-bb-meprlms-admin-integration-tab.php:158
#: includes/integrations/meprlms/includes/admin/class-bb-meprlms-admin-integration-tab.php:170
#. translators: 1. Text. 2. Text.
msgid "MemberPress Courses"
msgstr ""

#: includes/integrations/meprlms/includes/admin/class-bb-meprlms-admin-integration-tab.php:159
#: includes/integrations/meprlms/includes/admin/class-bb-meprlms-admin-integration-tab.php:532
#: includes/integrations/tutorlms/bb-tutorlms-admin-tab.php:157
#: includes/integrations/zoom/bp-zoom-admin-tab.php:334
#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:67
#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:68
msgid "Social Groups"
msgstr ""

#: includes/integrations/meprlms/includes/admin/class-bb-meprlms-admin-integration-tab.php:171
#: includes/integrations/tutorlms/bb-tutorlms-admin-tab.php:165
msgid "Posts in Activity Feed"
msgstr ""

#: includes/integrations/meprlms/includes/admin/class-bb-meprlms-admin-integration-tab.php:217
msgid "Enable Integration"
msgstr ""

#: includes/integrations/meprlms/includes/admin/class-bb-meprlms-admin-integration-tab.php:224
#: includes/integrations/tutorlms/bb-tutorlms-admin-tab.php:243
msgid "Course Visibility"
msgstr ""

#: includes/integrations/meprlms/includes/admin/class-bb-meprlms-admin-integration-tab.php:231
#: includes/integrations/tutorlms/bb-tutorlms-admin-tab.php:250
msgid "Display Course Activity"
msgstr ""

#: includes/integrations/meprlms/includes/admin/class-bb-meprlms-admin-integration-tab.php:242
#: includes/integrations/tutorlms/bb-tutorlms-admin-tab.php:261
msgid "Custom Posts"
msgstr ""

#: includes/integrations/meprlms/includes/admin/class-bb-meprlms-admin-integration-tab.php:356
msgid "Enable MemberPress Courses integration"
msgstr ""

#: includes/integrations/meprlms/includes/admin/class-bb-meprlms-admin-integration-tab.php:370
msgid ""
"Allow administrators to link their courses to groups during group creation "
"and group manage screens."
msgstr ""

#: includes/integrations/meprlms/includes/admin/class-bb-meprlms-admin-integration-tab.php:383
msgid ""
"Any option selected below will appear in group creation and management "
"screens, allowing only site admins to enable or disable course activity "
"posts for groups."
msgstr ""

#: includes/integrations/meprlms/includes/admin/class-bb-meprlms-admin-integration-tab.php:411
msgid ""
"Select which custom post types show in the activity feed when site owners "
"publish them, you can select whether or not to show comments in these "
"activity posts."
msgstr ""

#: includes/integrations/meprlms/includes/admin/class-bb-meprlms-admin-integration-tab.php:453
#: includes/integrations/tutorlms/bb-tutorlms-admin-tab.php:474
#. translators: %s: Post type name.
msgid "Comments are not supported for %s"
msgstr ""

#: includes/integrations/meprlms/includes/admin/class-bb-meprlms-admin-integration-tab.php:472
#: includes/integrations/tutorlms/bb-tutorlms-admin-tab.php:490
#. translators: %s: Post type name.
msgid "Enable %s comments in the activity feed."
msgstr ""

#: includes/integrations/meprlms/includes/admin/class-bb-meprlms-admin-integration-tab.php:531
msgid "Require"
msgstr ""

#: includes/integrations/meprlms/includes/admin/class-bb-meprlms-admin-integration-tab.php:533
msgid "component to be active"
msgstr ""

#: includes/integrations/meprlms/includes/class-bb-meprlms-group-settings.php:25
#: includes/integrations/meprlms/includes/class-bb-meprlms-profile.php:119
#: includes/integrations/tutorlms/includes/class-bb-tutorlms-group-settings.php:24
#: includes/integrations/tutorlms/includes/class-bb-tutorlms-profile.php:152
msgid "Courses"
msgstr ""

#: includes/integrations/meprlms/includes/class-bb-meprlms-group-settings.php:84
#: includes/integrations/meprlms/templates/admin/integration-tab-intro.php:28
#: includes/integrations/meprlms/templates/admin/integration-tab-intro.php:70
#: includes/integrations/meprlms/templates/admin/integration-tab-intro.php:116
#. translators: 1. Text. 2. Text.
msgid "Memberpress Courses"
msgstr ""

#: includes/integrations/meprlms/includes/class-bb-meprlms-integration.php:205
#: includes/integrations/meprlms/templates/buddypress/groups/single/admin/edit-courses.php:139
#: includes/integrations/tutorlms/bb-tutorlms-integration.php:182
#: includes/integrations/tutorlms/templates/buddypress/groups/single/admin/edit-courses.php:127
msgid "Start typing a course name to associate with this group."
msgstr ""

#: includes/integrations/meprlms/includes/class-bb-meprlms-profile.php:120
msgid "My Courses"
msgstr ""

#: includes/integrations/meprlms/includes/class-bb-meprlms-profile.php:121
#: includes/integrations/tutorlms/includes/class-bb-tutorlms-profile.php:153
msgid "My Created Courses"
msgstr ""

#: includes/integrations/meprlms/includes/class-bb-meprlms-profile.php:168
#: includes/integrations/tutorlms/includes/class-bb-tutorlms-profile.php:154
msgid "Created Courses"
msgstr ""

#: includes/integrations/meprlms/includes/class-bb-meprlms-profile.php:169
msgid "User Courses"
msgstr ""

#: includes/integrations/meprlms/templates/admin/integration-tab-intro.php:29
#: includes/integrations/tutorlms/templates/admin/integration-tab-intro.php:29
msgid "Requires license"
msgstr ""

#: includes/integrations/meprlms/templates/admin/integration-tab-intro.php:71
#: includes/integrations/meprlms/templates/admin/integration-tab-intro.php:117
#: includes/integrations/tutorlms/templates/admin/integration-tab-intro.php:75
#: includes/integrations/tutorlms/templates/admin/integration-tab-intro.php:117
msgid "Requires plugin to activate"
msgstr ""

#: includes/integrations/meprlms/templates/admin/integration-tab-intro.php:95
msgid ""
"BuddyBoss Platform Pro has integration settings for Memberpress courses. If "
"using Memberpress courses we add the ability to add courses to groups as an "
"instructor and utilize the BuddyBoss activity feeds for Course, Lessons & "
"Topics. We have also taken the time to style Memberpress courses to match "
"our theme for styling."
msgstr ""

#: includes/integrations/meprlms/templates/admin/integration-tab-intro.php:142
msgid ""
"BuddyBoss Platform Pro requires BuddyBoss Platform plugin version 2.7.40 or "
"higher to work. Please update BuddyBoss Platform."
msgstr ""

#: includes/integrations/meprlms/templates/buddypress/groups/single/admin/edit-courses.php:46
#: includes/integrations/tutorlms/templates/buddypress/groups/single/admin/edit-courses.php:46
msgid "Course Tab"
msgstr ""

#: includes/integrations/meprlms/templates/buddypress/groups/single/admin/edit-courses.php:51
msgid ""
"As an Instructor you can add a courses tab to your group and link "
"associated courses within the tab.<br />You can then select what activity "
"posts should automatically be added to the activity feed."
msgstr ""

#: includes/integrations/meprlms/templates/buddypress/groups/single/admin/edit-courses.php:64
msgid "Yes, I want to add a course tab"
msgstr ""

#: includes/integrations/meprlms/templates/buddypress/groups/single/admin/edit-courses.php:71
#: includes/integrations/tutorlms/templates/buddypress/groups/single/admin/edit-courses.php:61
msgid ""
"Add a course tab to your group and select which courses you would like to "
"show under the course tab."
msgstr ""

#: includes/integrations/meprlms/templates/buddypress/groups/single/admin/edit-courses.php:77
#: includes/integrations/tutorlms/templates/buddypress/groups/single/admin/edit-courses.php:67
msgid "Yes, I want to add courses to this group"
msgstr ""

#: includes/integrations/meprlms/templates/buddypress/groups/single/admin/edit-courses.php:93
#: includes/integrations/tutorlms/templates/buddypress/groups/single/admin/edit-courses.php:83
msgid "Select Course Activities"
msgstr ""

#: includes/integrations/meprlms/templates/buddypress/groups/single/admin/edit-courses.php:95
#: includes/integrations/tutorlms/templates/buddypress/groups/single/admin/edit-courses.php:85
msgid "Which activities should be displayed in this group?"
msgstr ""

#: includes/integrations/meprlms/templates/buddypress/groups/single/admin/edit-courses.php:119
#: includes/integrations/tutorlms/templates/buddypress/groups/single/admin/edit-courses.php:109
msgid "Select Courses"
msgstr ""

#: includes/integrations/meprlms/templates/buddypress/groups/single/admin/edit-courses.php:121
#: includes/integrations/tutorlms/templates/buddypress/groups/single/admin/edit-courses.php:111
msgid "Choose your courses you would like to associate with this group."
msgstr ""

#: includes/integrations/meprlms/templates/buddypress/groups/single/courses.php:71
#: includes/integrations/meprlms/templates/buddypress/members/single/courses.php:41
#: includes/integrations/tutorlms/templates/buddypress/members/single/tutor/courses.php:54
#. translators: %d is the courses count
msgid "<span class=\"bb-count\">%d</span> Course"
msgid_plural "<span class=\"bb-count\">%d</span> Courses"
msgstr[0] ""
msgstr[1] ""

#: includes/integrations/meprlms/templates/buddypress/groups/single/courses.php:148
#: includes/integrations/meprlms/templates/buddypress/members/single/courses.php:114
#: includes/integrations/meprlms/templates/memberpress/courses/archive-mpcs-courses.php:159
msgid " Complete"
msgstr ""

#: includes/integrations/meprlms/templates/buddypress/groups/single/courses.php:161
#: includes/integrations/meprlms/templates/buddypress/members/single/courses.php:125
#: includes/integrations/meprlms/templates/memberpress/courses/archive-mpcs-courses.php:170
msgid "Continue Course"
msgstr ""

#: includes/integrations/meprlms/templates/buddypress/groups/single/courses.php:202
#: includes/integrations/meprlms/templates/buddypress/members/single/courses.php:166
msgid "Page"
msgstr ""

#: includes/integrations/meprlms/templates/memberpress/assignments/section_assignment_row.php:53
msgid "Grade Pending"
msgstr ""

#: includes/integrations/meprlms/templates/memberpress/assignments/section_assignment_row.php:65
#: includes/integrations/meprlms/templates/memberpress/courses/course-resources.php:67
#: includes/integrations/meprlms/templates/memberpress/courses/courses_classroom_section_lessons.php:88
#: includes/integrations/meprlms/templates/memberpress/courses/courses_section_lesson_list.php:79
#: includes/integrations/meprlms/templates/memberpress/quizzes/section_quiz_row.php:50
msgid "View"
msgstr ""

#: includes/integrations/meprlms/templates/memberpress/assignments/section_assignment_row.php:71
#: includes/integrations/meprlms/templates/memberpress/courses/courses_classroom_section_lessons.php:94
#: includes/integrations/meprlms/templates/memberpress/courses/courses_section_lesson_list.php:83
#: includes/integrations/meprlms/templates/memberpress/quizzes/section_quiz_row.php:58
msgid "Start"
msgstr ""

#: includes/integrations/meprlms/templates/memberpress/assignments/section_assignment_row.php:82
#: includes/integrations/meprlms/templates/memberpress/courses/courses_classroom_section_lessons.php:105
#: includes/integrations/meprlms/templates/memberpress/courses/courses_section_lesson_list.php:92
#: includes/integrations/meprlms/templates/memberpress/quizzes/section_quiz_row.php:68
msgid ""
"Lesson unavailable. You must complete all previous lessons and quizzes "
"before you start this lesson."
msgstr ""

#: includes/integrations/meprlms/templates/memberpress/courses/archive-mpcs-courses.php:38
msgid "Category"
msgstr ""

#: includes/integrations/meprlms/templates/memberpress/courses/archive-mpcs-courses.php:44
#: includes/integrations/meprlms/templates/memberpress/courses/archive-mpcs-courses.php:63
msgid "Search"
msgstr ""

#: includes/integrations/meprlms/templates/memberpress/courses/archive-mpcs-courses.php:46
#: includes/integrations/meprlms/templates/memberpress/courses/archive-mpcs-courses.php:65
#: includes/topics/includes/class-bb-group-activity-topics-settings.php:376
msgid "All"
msgstr ""

#: includes/integrations/meprlms/templates/memberpress/courses/archive-mpcs-courses.php:56
msgid "Author"
msgstr ""

#: includes/integrations/meprlms/templates/memberpress/courses/archive-mpcs-courses.php:85
msgid "Find a course"
msgstr ""

#: includes/integrations/meprlms/templates/memberpress/courses/archive-mpcs-courses.php:201
msgid "No Course found"
msgstr ""

#: includes/integrations/meprlms/templates/memberpress/courses/course-resources.php:12
msgid "Resources"
msgstr ""

#: includes/integrations/meprlms/templates/memberpress/courses/course-resources.php:19
msgid "Downloads"
msgstr ""

#: includes/integrations/meprlms/templates/memberpress/courses/course-resources.php:83
msgid "Links"
msgstr ""

#: includes/integrations/meprlms/templates/memberpress/courses/course-resources.php:97
msgid "Visit"
msgstr ""

#: includes/integrations/meprlms/templates/memberpress/courses/courses_bookmark.php:14
#: includes/integrations/meprlms/templates/memberpress/courses/courses_classroom_bookmark.php:16
msgid "Course Curriculum"
msgstr ""

#: includes/integrations/meprlms/templates/memberpress/courses/courses_bookmark.php:19
#: includes/integrations/meprlms/templates/memberpress/courses/courses_classroom_bookmark.php:21
msgid "Start Next Lesson"
msgstr ""

#: includes/integrations/meprlms/templates/memberpress/courses/courses_instructor.php:28
msgid "Course Instructor"
msgstr ""

#: includes/integrations/meprlms/templates/memberpress/courses/courses_instructor.php:50
msgid "Email:"
msgstr ""

#: includes/integrations/meprlms/templates/memberpress/courses/courses_instructor.php:54
msgid "Website:"
msgstr ""

#: includes/integrations/meprlms/templates/memberpress/courses/courses_instructor.php:62
msgid "View profile"
msgstr ""

#: includes/integrations/meprlms/templates/memberpress/quizzes/section_quiz_row.php:56
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1084
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1224
#: includes/reactions/bb-reactions-actions.php:202
#: includes/reactions/bb-reactions-actions.php:469
msgid "Continue"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-actions.php:209
#: includes/integrations/onesignal/bb-onesignal-actions.php:223
#. translators: Image src.
msgid "Choose <img src=\"%s\"> in your browser's address bar"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-actions.php:214
#. translators: Image src.
msgid "Enable <img src=\"%s\"> Notifications"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-actions.php:217
#: includes/integrations/onesignal/bb-onesignal-actions.php:231
#: includes/integrations/onesignal/bb-onesignal-actions.php:245
#: includes/integrations/onesignal/bb-onesignal-actions.php:253
#: includes/integrations/onesignal/bb-onesignal-actions.php:262
#: includes/integrations/onesignal/bb-onesignal-actions.php:270
msgid "Refresh this page"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-actions.php:228
#. translators: Image src.
msgid "Disable <img src=\"%s\"> Notifications"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-actions.php:235
msgid "Enable Opera Notifications"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-actions.php:237
msgid "Enable Chrome Notifications"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-actions.php:239
msgid "Enable Safari Notifications"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-actions.php:241
#: includes/integrations/onesignal/bb-onesignal-actions.php:249
msgid "Open the Safari settings"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-actions.php:242
#: includes/integrations/onesignal/bb-onesignal-actions.php:250
msgid "In the Websites tab, open the Notifications section"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-actions.php:243
msgid "Change the permission for this site to Allow"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-actions.php:244
#: includes/integrations/onesignal/bb-onesignal-actions.php:252
msgid "Close the settings"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-actions.php:251
msgid "Change the permission for this site to Deny"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-actions.php:256
msgid "Enable Firefox Notifications"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-actions.php:258
#: includes/integrations/onesignal/bb-onesignal-actions.php:266
msgid "Open the Firefox settings"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-actions.php:259
#: includes/integrations/onesignal/bb-onesignal-actions.php:267
msgid ""
"In the Privacy & Security tab, open the Notifications settings (found under "
"Permissions)"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-actions.php:260
msgid "Change the Status for this site to Allow"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-actions.php:261
#: includes/integrations/onesignal/bb-onesignal-actions.php:269
msgid "Click Save Changes and close the settings"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-actions.php:268
msgid "Change the Status for this site to Block"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-actions.php:273
msgid "Enable Edge Notifications"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-actions.php:275
msgid "Enable IE Notifications"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-actions.php:277
msgid "Enable Browser Notifications"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-actions.php:291
msgid ""
"Receive web notifications through this browser, even when you're not on "
"this site."
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-actions.php:308
msgid "Enable notifications"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-actions.php:310
msgid ""
"To enable notifications, please allow notifications for this site in your "
"browser."
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-actions.php:332
msgid "Disable notifications"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-actions.php:334
msgid ""
"To disable notifications, please block notifications for this site in your "
"browser."
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-actions.php:549
#: includes/integrations/zoom/bp-zoom-actions.php:888
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:2075
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:2126
msgid "Sorry, something goes wrong please try again."
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-admin-tab.php:114
#: includes/polls/bb-polls-filters.php:71
msgid "Are you sure?"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-admin-tab.php:116
msgid "No file was uploaded."
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-admin-tab.php:117
msgid "There was a problem uploading the soft prompt photo."
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-admin-tab.php:119
msgid "soft prompt photo was uploaded successfully."
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-admin-tab.php:120
msgid "There was a problem deleting soft prompt photo. Please try again."
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-admin-tab.php:121
msgid "soft prompt photo was deleted successfully."
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-admin-tab.php:196
#: includes/integrations/onesignal/bb-onesignal-admin-tab.php:210
#: includes/integrations/pusher/bb-pusher-admin-tab.php:174
#: includes/integrations/pusher/bb-pusher-admin-tab.php:196
#: includes/integrations/zoom/bp-zoom-admin-tab.php:436
msgid "Not Connected"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-admin-tab.php:213
#: includes/integrations/pusher/bb-pusher-admin-tab.php:188
#: includes/integrations/pusher/bb-pusher-admin-tab.php:193
#: includes/integrations/zoom/bp-zoom-admin-tab.php:448
#: includes/integrations/zoom/bp-zoom-admin-tab.php:468
#: includes/sso/includes/class-bb-sso.php:1065
msgid "Connected"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-admin-tab.php:230
#: includes/integrations/onesignal/bb-onesignal-admin-tab.php:322
msgid "OneSignal"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-admin-tab.php:278
msgid "OneSignal App ID"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-admin-tab.php:284
#: includes/integrations/onesignal/bb-onesignal-admin-tab.php:363
msgid "Rest API Key"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-admin-tab.php:322
#. translators: %s is the BuddyBoss marketplace link.
msgid ""
"To use %1$s for web push notifications, create an app in your account and "
"enter the API credentials from the settings below."
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-admin-tab.php:347
msgid "OneSignal Auth Key"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-admin-tab.php:348
#: includes/integrations/zoom/bp-zoom-functions.php:4538
#: includes/integrations/zoom/bp-zoom-functions.php:4551
#: includes/integrations/zoom/bp-zoom-functions.php:4564
#: includes/integrations/zoom/bp-zoom-functions.php:4608
#: includes/integrations/zoom/bp-zoom-functions.php:4693
#: includes/integrations/zoom/bp-zoom-functions.php:4706
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:714
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:729
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:744
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:785
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:963
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1287
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1304
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1321
#: includes/lib/buddyboss-updater/includes/views/package.php:58
#: includes/lib/buddyboss-updater/includes/views/package.php:82
msgid "Toggle"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-admin-tab.php:388
#. translators: Onesignal setting page link
msgid ""
"Due to a change by OneSignal, you'll need to enter new %s in order to "
"resume sending web push notifications"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-admin-tab.php:392
msgid "API keys"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-filters.php:49
msgid "Enable Web Push Notifications"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-filters.php:56
msgid "Default Notification Icon"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-filters.php:62
msgid "Skip Active Members"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-filters.php:68
msgid "Automatically Request Permission"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-filters.php:74
msgid "Enable Soft Prompt"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-filters.php:132
msgid "There was a problem cropping custom profile avatar."
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-filters.php:133
msgid "The custom profile avatar was uploaded successfully."
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-filters.php:134
msgid "There was a problem deleting custom profile avatar. Please try again."
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-filters.php:135
msgid "The custom profile avatar was deleted successfully!"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:118
#. translators: 1. App name 2. App configuration URL
msgid ""
"The %1$s app is currently set to only support secure (HTTPS) sites. Please "
"update this app's %2$s in OneSignal or create a new app for this site."
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:120
#: includes/integrations/onesignal/bb-onesignal-functions.php:132
msgid "configuration"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:130
#. translators: 1. App name 2. App configuration URL
msgid ""
"The Site URL for the %1$s app does not match this site's url. Please update "
"this app's %2$s in OneSignal or create a new app for this site."
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:154
#. translators: Error from response.
msgid "There was a problem connecting to your OneSignal account %s"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:298
msgid "Allow"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:320
msgid "No Thanks"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:342
msgid ""
"Subscribe to push notifications to keep up to date with the latest activity "
"on this site."
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:391
msgid "Allow members to subscribe to notifications through their browser"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:392
msgid ""
"Once enabled, members will be able to opt-in to receive all BuddyBoss "
"Notifications as push notifications through their web browser."
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:405
#. translators: 1. OneSignal error type. 2. Admin integration url.
msgid "Please enter a valid %1$s in the %2$s settings."
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:407
msgid "OneSignal App ID and Rest API Key"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:420
msgid "Integration"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:452
#: includes/reactions/templates/admin/emotion-picker/bb-custom.php:46
msgid "Uploading..."
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:452
msgid "Upload"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:453
msgid "Removing..."
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:453
#: includes/sso/includes/class-bb-sso-provider-dummy.php:287
msgid "Remove"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:468
#. translators: 1: Notification icon width in pixels. 2: Notification icon
#. height in pixels
msgid ""
"Upload an image to be the default icon used for web push notifications. "
"Certain notification types may use different icons. The recommended size is "
"%1$spx by %2$spx."
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:489
msgid "visit"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:490
msgid "login"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:502
#. translators: Permission validate select box.
msgid ""
"Request permission to send notifications when members first %s through a "
"new browser"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:513
#. translators: Notification Preferences text.
msgid ""
"When enabled, a prompt will be presented by the browser, requesting "
"permission for the site to send web push notifications. The member must "
"click \"Allow\" to be subscribed. If disabled, members will need to allow "
"permission in the %s tab of their Account Settings."
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:515
msgid "Notification Preferences"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:533
msgid ""
"Show a \"soft prompt\" before triggering the browser's native permission "
"prompt"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:540
#. translators: Learn more link .
msgid ""
"A \"soft prompt\" is a customizable prompt that is show to the member "
"before the \"hard prompt\" of the native permission prompt triggered by the "
"browser. %s"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:542
#: includes/integrations/onesignal/bb-onesignal-functions.php:609
msgid "Learn More"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:549
msgid ""
"A \"soft prompt\" is a customizable prompt that is show to the member "
"before the \"hard prompt\" of the native permission prompt triggered by the "
"browser."
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:550
msgid ""
"If a member denies the native permission prompt, they will need to go "
"through a multi-step process to enable notification permissions in their "
"browser. As such, a \"soft prompt\" is highly beneficial, as it allows you "
"opportunity to persuade your members to subscribe to web push notifications "
"before the native permission prompt is triggered."
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:551
msgid ""
"However, the \"soft prompt\" does not replace the native permission prompt "
"and does not subscribe the member's browser to receive web push "
"notifications."
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:568
#: includes/integrations/onesignal/bb-onesignal-functions.php:569
msgid "Message"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:571
msgid "/90 characters"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:586
msgid "Image"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:593
#. translators: 1. Field name, 2. Learn more link .
msgid ""
"To change the image used in your prompt, enter the URL of the image into "
"the %1$s field in your OneSignal app’s settings. %2$s"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:595
msgid "Default Icon Url"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:632
#: includes/integrations/onesignal/bb-onesignal-functions.php:633
msgid "Allow Button"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:635
#: includes/integrations/onesignal/bb-onesignal-functions.php:642
msgid "/15 characters"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:639
#: includes/integrations/onesignal/bb-onesignal-functions.php:640
msgid "Cancel Button"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:682
#: includes/reactions/templates/admin/emotion-picker/bb-custom-settings.php:24
#: includes/reactions/templates/admin/emotion-picker/bb-emojis-settings.php:25
#: includes/reactions/templates/admin/emotion-picker/bb-icons-settings.php:26
msgid "Preview"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:884
msgid "Don't send push notifications when members are active on a device"
msgstr ""

#: includes/integrations/onesignal/bb-onesignal-functions.php:885
msgid ""
"When a member is actively using your site on any device, they won't receive "
"web push notifications. Once a member is inactive, they will begin "
"receiving push notifications after a short delay."
msgstr ""

#: includes/integrations/onesignal/templates/admin/integration-tab-intro.php:12
msgid "OneSignal <span>&mdash; requires license</span>"
msgstr ""

#: includes/integrations/pusher/bb-pusher-actions.php:216
msgid "Now"
msgstr ""

#: includes/integrations/pusher/bb-pusher-actions.php:223
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:193
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:520
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:516
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:68
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:68
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-form.php:123
#: includes/reactions/bb-reactions-actions.php:201
#: includes/reactions/bb-reactions-actions.php:217
#: includes/reactions/bb-reactions-actions.php:361
#: includes/reactions/bb-reactions-actions.php:468
#: includes/reactions/bb-reactions-actions.php:484
#: includes/sso/admin/templates/bb-sso-fields-html.php:88
#: includes/topics/templates/buddypress/groups/single/admin/activity-topics-delete-form.php:60
#: includes/topics/templates/buddypress/groups/single/admin/activity-topics-form.php:94
#: includes/topics/templates/buddypress/groups/single/admin/activity-topics.php:145
#: includes/topics/templates/buddypress/groups/single/admin/activity-topics.php:202
msgid "Cancel"
msgstr ""

#: includes/integrations/pusher/bb-pusher-actions.php:224
msgid "Not delivered"
msgstr ""

#: includes/integrations/pusher/bb-pusher-actions.php:225
msgid "Try again"
msgstr ""

#: includes/integrations/pusher/bb-pusher-actions.php:226
msgid "Sending..."
msgstr ""

#: includes/integrations/pusher/bb-pusher-actions.php:229
msgid "The conversation with %s has been deleted."
msgstr ""

#: includes/integrations/pusher/bb-pusher-actions.php:231
#: includes/integrations/pusher/bb-pusher-actions.php:241
msgid "You have left "
msgstr ""

#: includes/integrations/pusher/bb-pusher-actions.php:232
msgid "You are banned in "
msgstr ""

#: includes/integrations/pusher/bb-pusher-actions.php:233
msgid " are typing..."
msgstr ""

#: includes/integrations/pusher/bb-pusher-actions.php:234
msgid " is typing..."
msgstr ""

#: includes/integrations/pusher/bb-pusher-actions.php:235
msgid " members are typing..."
msgstr ""

#: includes/integrations/pusher/bb-pusher-actions.php:236
msgid " others are typing..."
msgstr ""

#: includes/integrations/pusher/bb-pusher-actions.php:237
msgid " and "
msgstr ""

#: includes/integrations/pusher/bb-pusher-actions.php:238
msgid "Group messages have been disabled by a site administrator."
msgstr ""

#: includes/integrations/pusher/bb-pusher-actions.php:239
msgid " was deleted."
msgstr ""

#: includes/integrations/pusher/bb-pusher-actions.php:240
msgid "Private messages have been disabled by a site administrator."
msgstr ""

#: includes/integrations/pusher/bb-pusher-actions.php:242
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:103
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:107
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:149
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:153
#: includes/schedule-posts/templates/buddypress/common/js-templates/activity/parts/bb-activity-schedule-post.php:75
msgid "AM"
msgstr ""

#: includes/integrations/pusher/bb-pusher-actions.php:243
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:107
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:111
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:153
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:157
#: includes/schedule-posts/templates/buddypress/common/js-templates/activity/parts/bb-activity-schedule-post.php:79
msgid "PM"
msgstr ""

#: includes/integrations/pusher/bb-pusher-actions.php:429
msgid "Typing..."
msgstr ""

#: includes/integrations/pusher/bb-pusher-actions.php:1786
msgid "It's not a post request."
msgstr ""

#: includes/integrations/pusher/bb-pusher-actions.php:1799
msgid "Not a valid thread access"
msgstr ""

#: includes/integrations/pusher/bb-pusher-actions.php:1810
msgid "Message marked as read."
msgstr ""

#: includes/integrations/pusher/bb-pusher-admin-tab.php:46
msgid "mt1 (N. Virginia)"
msgstr ""

#: includes/integrations/pusher/bb-pusher-admin-tab.php:47
msgid "us2 (Ohio)"
msgstr ""

#: includes/integrations/pusher/bb-pusher-admin-tab.php:48
msgid "us3 (Oregon)"
msgstr ""

#: includes/integrations/pusher/bb-pusher-admin-tab.php:49
msgid "eu (Ireland)"
msgstr ""

#: includes/integrations/pusher/bb-pusher-admin-tab.php:50
msgid "ap1 (Singapore)"
msgstr ""

#: includes/integrations/pusher/bb-pusher-admin-tab.php:51
msgid "ap2 (Mumbai)"
msgstr ""

#: includes/integrations/pusher/bb-pusher-admin-tab.php:52
msgid "ap3 (Tokyo)"
msgstr ""

#: includes/integrations/pusher/bb-pusher-admin-tab.php:53
msgid "ap4 (Sydney)"
msgstr ""

#: includes/integrations/pusher/bb-pusher-admin-tab.php:54
msgid "sa1 (São Paulo)"
msgstr ""

#: includes/integrations/pusher/bb-pusher-admin-tab.php:55
#: includes/reactions/bb-reactions-functions.php:246
msgid "Custom"
msgstr ""

#: includes/integrations/pusher/bb-pusher-admin-tab.php:207
msgid "Pusher"
msgstr ""

#: includes/integrations/pusher/bb-pusher-admin-tab.php:209
msgid ""
"In your app's settings, please enable \"Client events\" and \"Authorized "
"connections\" for this integration to work correctly."
msgstr ""

#: includes/integrations/pusher/bb-pusher-admin-tab.php:283
msgid "Information"
msgstr ""

#: includes/integrations/pusher/bb-pusher-admin-tab.php:289
msgid "Dashboard"
msgstr ""

#: includes/integrations/pusher/bb-pusher-admin-tab.php:295
#: includes/integrations/pusher/bb-pusher-admin-tab.php:400
msgid "Pusher App ID"
msgstr ""

#: includes/integrations/pusher/bb-pusher-admin-tab.php:301
#: includes/integrations/pusher/bb-pusher-admin-tab.php:412
msgid "Pusher App Key"
msgstr ""

#: includes/integrations/pusher/bb-pusher-admin-tab.php:307
msgid "Pusher Secret Key"
msgstr ""

#: includes/integrations/pusher/bb-pusher-admin-tab.php:313
msgid "Pusher Cluster"
msgstr ""

#: includes/integrations/pusher/bb-pusher-admin-tab.php:322
msgid "Enable Features"
msgstr ""

#: includes/integrations/pusher/bb-pusher-admin-tab.php:369
#. translators: pusher channels link
msgid ""
"The BuddyBoss Platform has an integration with %s, a WebSocket service "
"which can power realtime features on your BuddyBoss community such as live "
"messaging."
msgstr ""

#: includes/integrations/pusher/bb-pusher-admin-tab.php:370
msgid "Pusher Channels"
msgstr ""

#: includes/integrations/pusher/bb-pusher-admin-tab.php:386
#. translators: 1. pusher channels dashboard link 2. App Keys text
msgid ""
"After creating your app in your Pusher Channels %1$s, enter the %2$s below "
"to connect it to this site."
msgstr ""

#: includes/integrations/pusher/bb-pusher-admin-tab.php:387
msgid "account"
msgstr ""

#: includes/integrations/pusher/bb-pusher-admin-tab.php:388
msgid "App Keys"
msgstr ""

#: includes/integrations/pusher/bb-pusher-admin-tab.php:428
msgid "Pusher App Secret"
msgstr ""

#: includes/integrations/pusher/bb-pusher-admin-tab.php:457
#: includes/integrations/pusher/bb-pusher-admin-tab.php:458
msgid "Cluster Name"
msgstr ""

#: includes/integrations/pusher/bb-pusher-functions.php:137
msgid "Live Messaging"
msgstr ""

#: includes/integrations/pusher/bb-pusher-functions.php:138
msgid ""
"When enabled, members will send and receive private messages in realtime "
"across their devices."
msgstr ""

#: includes/integrations/pusher/bb-pusher-functions.php:146
#. translators: BuddyBoss components link
msgid "To use Live Messaging, please enable the %s component."
msgstr ""

#: includes/integrations/pusher/bb-pusher-functions.php:155
msgid "Private Messaging"
msgstr ""

#: includes/integrations/pusher/bb-pusher-functions.php:236
#. translators: Error Message.
msgid "There was a problem connecting to your Pusher Channels app: %s"
msgstr ""

#: includes/integrations/pusher/includes/class-bb-rest-pusher-endpoint.php:62
msgid "Channel Name"
msgstr ""

#: includes/integrations/pusher/includes/class-bb-rest-pusher-endpoint.php:67
#: includes/integrations/pusher/includes/class-bb-rest-pusher-endpoint.php:87
msgid "Socket ID"
msgstr ""

#: includes/integrations/pusher/includes/class-bb-rest-pusher-endpoint.php:235
msgid "This content has been hidden as you have blocked this member."
msgstr ""

#: includes/integrations/pusher/includes/class-bb-rest-pusher-endpoint.php:260
msgid "This content has been hidden from site admin."
msgstr ""

#: includes/integrations/pusher/includes/class-bb-rest-pusher-endpoint.php:314
#: includes/polls/includes/class-bb-rest-poll-endpoint.php:431
#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:179
#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:445
#: includes/polls/includes/class-bb-rest-poll-option-vote-endpoint.php:156
msgid "Sorry, Restrict access to only logged-in members."
msgstr ""

#: includes/integrations/pusher/includes/class-bb-rest-pusher-endpoint.php:326
msgid "Pusher does not setup properly."
msgstr ""

#: includes/integrations/pusher/includes/class-bb-rest-pusher-endpoint.php:360
msgid "a valid channel_name param is required."
msgstr ""

#: includes/integrations/pusher/includes/class-bb-rest-pusher-endpoint.php:363
msgid "a valid socket_id param is required."
msgstr ""

#: includes/integrations/pusher/includes/class-bb-rest-pusher-endpoint.php:402
#: includes/integrations/pusher/includes/class-bb-rest-pusher-endpoint.php:463
#: includes/integrations/pusher/includes/class-bb-rest-pusher-endpoint.php:468
msgid "You are not authorized to subscribe this channel."
msgstr ""

#: includes/integrations/pusher/includes/class-bb-rest-pusher-endpoint.php:410
#: includes/integrations/pusher/includes/class-bb-rest-pusher-endpoint.php:484
msgid "Sorry, you are not allowed to access this endpoint"
msgstr ""

#: includes/integrations/pusher/includes/class-bb-rest-pusher-endpoint.php:522
#: includes/integrations/pusher/includes/class-bb-rest-pusher-endpoint.php:539
msgid "There is an error while authorizing the user."
msgstr ""

#: includes/integrations/pusher/templates/admin/integration-tab-intro.php:12
msgid "Pusher <span>&mdash; requires license</span>"
msgstr ""

#: includes/integrations/tutorlms/bb-tutorlms-actions.php:89
msgid "%s just enrolled in %s."
msgstr ""

#: includes/integrations/tutorlms/bb-tutorlms-actions.php:147
msgid "%s started on %s."
msgstr ""

#: includes/integrations/tutorlms/bb-tutorlms-actions.php:205
msgid "%s successfully completed %s."
msgstr ""

#: includes/integrations/tutorlms/bb-tutorlms-actions.php:275
msgid "A new lesson has been published for %s. Go check it out %s"
msgstr ""

#: includes/integrations/tutorlms/bb-tutorlms-actions.php:343
msgid "I updated %s. See what's new!"
msgstr ""

#: includes/integrations/tutorlms/bb-tutorlms-actions.php:406
msgid "%s just started %s"
msgstr ""

#: includes/integrations/tutorlms/bb-tutorlms-actions.php:469
msgid "%s just completed %s"
msgstr ""

#: includes/integrations/tutorlms/bb-tutorlms-admin-tab.php:156
#: includes/integrations/tutorlms/includes/class-bb-tutorlms-group-settings.php:83
#: includes/integrations/tutorlms/templates/admin/integration-tab-intro.php:28
#: includes/integrations/tutorlms/templates/admin/integration-tab-intro.php:74
#: includes/integrations/tutorlms/templates/admin/integration-tab-intro.php:116
#. translators: 1. Text. 2. Text.
msgid "TutorLMS"
msgstr ""

#: includes/integrations/tutorlms/bb-tutorlms-admin-tab.php:236
msgid "TutorLMS Group Sync"
msgstr ""

#: includes/integrations/tutorlms/bb-tutorlms-admin-tab.php:379
msgid "Enable TutorLMS integration settings"
msgstr ""

#: includes/integrations/tutorlms/bb-tutorlms-admin-tab.php:393
msgid ""
"Allow course instructors to link their courses to groups during group "
"creation and group manage screens."
msgstr ""

#: includes/integrations/tutorlms/bb-tutorlms-admin-tab.php:406
msgid ""
"Any option selected below will show in group creation and group manage "
"screens to allow group organizer to enable or disable course activity posts "
"for their own group."
msgstr ""

#: includes/integrations/tutorlms/bb-tutorlms-admin-tab.php:434
msgid ""
"Select which custom post types show in the activity feed when members "
"instructors and site owners publish them, you can select whether or not to "
"show comments in these activity posts."
msgstr ""

#: includes/integrations/tutorlms/bb-tutorlms-admin-tab.php:547
msgid ""
"To migrate BuddyPress group courses to BuddyBoss group courses. <a "
"href=\"%s\">Click Here</a>."
msgstr ""

#: includes/integrations/tutorlms/bb-tutorlms-admin-tab.php:577
msgid ""
"You need to activate the <a href=\"%s\">Social Groups Component</a> in "
"order to sync TutorLMS with Social Groups."
msgstr ""

#: includes/integrations/tutorlms/bb-tutorlms-filters.php:135
msgid "Migrate BuddyPress group courses to BuddyBoss group courses for TutorLMS"
msgstr ""

#: includes/integrations/tutorlms/bb-tutorlms-filters.php:235
#. translators: %s: number of groups
msgid "%s groups updated successfully."
msgstr ""

#: includes/integrations/tutorlms/bb-tutorlms-filters.php:241
msgid ""
"TutorLMS courses successfully updated from BuddyPress to BuddyBoss for each "
"group &hellip; %s"
msgstr ""

#: includes/integrations/tutorlms/bb-tutorlms-filters.php:247
msgid "Complete!"
msgstr ""

#: includes/integrations/tutorlms/bb-tutorlms-functions.php:344
msgid "You have not enrolled in any courses yet!"
msgstr ""

#: includes/integrations/tutorlms/bb-tutorlms-functions.php:344
msgid "This member has not enrolled in any courses yet!"
msgstr ""

#: includes/integrations/tutorlms/bb-tutorlms-group-functions.php:68
msgid "Group member enrolled in a course"
msgstr ""

#: includes/integrations/tutorlms/bb-tutorlms-group-functions.php:71
msgid "Group member creates a lesson"
msgstr ""

#: includes/integrations/tutorlms/bb-tutorlms-group-functions.php:72
msgid "Group member updates a lesson"
msgstr ""

#: includes/integrations/tutorlms/bb-tutorlms-group-functions.php:73
msgid "Group member started a quiz"
msgstr ""

#: includes/integrations/tutorlms/bb-tutorlms-group-functions.php:74
msgid "Group member finished a quiz"
msgstr ""

#: includes/integrations/tutorlms/includes/class-bb-tutorlms-profile.php:155
msgid "My Enrolled Courses"
msgstr ""

#: includes/integrations/tutorlms/includes/class-bb-tutorlms-profile.php:156
msgid "Enrolled Courses"
msgstr ""

#: includes/integrations/tutorlms/templates/admin/integration-tab-intro.php:95
msgid ""
"BuddyBoss Platform Pro has integration settings for TutorLMS. If using "
"TutorLMS we add the ability to add courses to groups as an instructor and "
"utilize the BuddyBoss activity feeds for Course, Lessons & Topics. We have "
"also taken the time to style TutorLMS to match our theme for styling."
msgstr ""

#: includes/integrations/tutorlms/templates/admin/integration-tab-intro.php:137
msgid ""
"BuddyBoss Platform Pro requires BuddyBoss Platform plugin version 2.5.00 or "
"higher to work. Please update BuddyBoss Platform."
msgstr ""

#: includes/integrations/tutorlms/templates/buddypress/groups/single/admin/edit-courses.php:49
msgid ""
"As an Instructor you can add a courses tab to your group and link "
"associated courses within the tab. <br> You can then select what activity "
"posts should automatically be added to the activity feed."
msgstr ""

#: includes/integrations/tutorlms/templates/buddypress/groups/single/admin/edit-courses.php:54
msgid "Yes, I want to add a group tab"
msgstr ""

#: includes/integrations/zoom/bp-zoom-actions.php:216
#: includes/integrations/zoom/bp-zoom-filters.php:202
#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:98
msgid "A Zoom meeting is scheduled in a group"
msgstr ""

#: includes/integrations/zoom/bp-zoom-actions.php:220
#: includes/integrations/zoom/bp-zoom-actions.php:236
msgid "Yes, send email"
msgstr ""

#: includes/integrations/zoom/bp-zoom-actions.php:226
#: includes/integrations/zoom/bp-zoom-actions.php:242
msgid "No, do not send email"
msgstr ""

#: includes/integrations/zoom/bp-zoom-actions.php:232
#: includes/integrations/zoom/bp-zoom-filters.php:214
#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:113
msgid "A Zoom webinar is scheduled in a group"
msgstr ""

#: includes/integrations/zoom/bp-zoom-admin-tab.php:140
#: includes/integrations/zoom/bp-zoom-group-functions.php:854
msgid "Email not found in Zoom account."
msgstr ""

#: includes/integrations/zoom/bp-zoom-admin-tab.php:182
#: includes/integrations/zoom/bp-zoom-functions.php:4351
#: includes/integrations/zoom/bp-zoom-group-functions.php:899
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:2115
msgid "The Account ID is required."
msgstr ""

#: includes/integrations/zoom/bp-zoom-admin-tab.php:184
#: includes/integrations/zoom/bp-zoom-functions.php:4360
#: includes/integrations/zoom/bp-zoom-group-functions.php:901
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:2117
msgid "The Client ID is required."
msgstr ""

#: includes/integrations/zoom/bp-zoom-admin-tab.php:186
#: includes/integrations/zoom/bp-zoom-functions.php:4369
#: includes/integrations/zoom/bp-zoom-group-functions.php:903
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:2119
msgid "The Client Secret is required."
msgstr ""

#: includes/integrations/zoom/bp-zoom-admin-tab.php:278
msgid "Zoom Gutenberg Blocks"
msgstr ""

#: includes/integrations/zoom/bp-zoom-admin-tab.php:283
msgid "Zoom In-Browser Meetings"
msgstr ""

#: includes/integrations/zoom/bp-zoom-admin-tab.php:287
msgid ""
"For webinars, hosts will be required to join the webinar using the Zoom app "
"and only authenticated users will be able to join."
msgstr ""

#: includes/integrations/zoom/bp-zoom-admin-tab.php:288
msgid ""
"Members will not be able to register for webinars on your site or "
"participate in polls while accessing webinars through their browser."
msgstr ""

#: includes/integrations/zoom/bp-zoom-admin-tab.php:293
msgid "Zoom Settings"
msgstr ""

#: includes/integrations/zoom/bp-zoom-admin-tab.php:341
msgid "Recordings"
msgstr ""

#: includes/integrations/zoom/bp-zoom-admin-tab.php:348
msgid "Recording Links"
msgstr ""

#: includes/integrations/zoom/bp-zoom-admin-tab.php:358
msgid "Zoom Gutenberg Settings"
msgstr ""

#: includes/integrations/zoom/bp-zoom-admin-tab.php:366
msgid "Zoom Browser Settings"
msgstr ""

#: includes/integrations/zoom/bp-zoom-filters.php:206
#: includes/integrations/zoom/bp-zoom-filters.php:218
msgid "Yes"
msgstr ""

#: includes/integrations/zoom/bp-zoom-filters.php:207
#: includes/integrations/zoom/bp-zoom-filters.php:219
msgid "No"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:54
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:218
msgid "Days"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:55
msgid "Hours"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:56
msgid "Minutes"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:57
msgid "Seconds"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:102
msgid "Are you sure you want to delete this meeting?"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:104
msgid "Are you sure you want to delete this webinar?"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:106
msgid "Guest"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:115
#: includes/integrations/zoom/bp-zoom-functions.php:2576
#: includes/integrations/zoom/bp-zoom-functions.php:2790
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:240
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:244
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:34
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:43
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:34
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:43
msgid "day"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:116
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:40
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:40
msgid "month"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:117
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:37
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:37
msgid "week"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:1120
msgid "Allow Zoom meetings in social groups"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:1123
msgid ""
"Allow group organizers to connect their Zoom account to their groups, in "
"order to create and synchronize meetings and webinars with the groups."
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:1184
msgid "Display Zoom recordings for past meetings"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:1192
msgid "Display buttons to 'Download' recording, and to 'Copy Link' to the recording"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:1349
msgid "Allow Zoom Webinars in the blocks"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:1717
msgid "Sorry, no meetings were found."
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:1722
msgid "Sorry, no webinars were found."
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:2570
#: includes/integrations/zoom/bp-zoom-functions.php:2587
#: includes/integrations/zoom/bp-zoom-functions.php:2638
#: includes/integrations/zoom/bp-zoom-functions.php:2784
#: includes/integrations/zoom/bp-zoom-functions.php:2801
#: includes/integrations/zoom/bp-zoom-functions.php:2852
msgid "Every"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:2574
#: includes/integrations/zoom/bp-zoom-functions.php:2788
msgid "days"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:2580
#: includes/integrations/zoom/bp-zoom-functions.php:2631
#: includes/integrations/zoom/bp-zoom-functions.php:2692
#: includes/integrations/zoom/bp-zoom-functions.php:2794
#: includes/integrations/zoom/bp-zoom-functions.php:2845
#: includes/integrations/zoom/bp-zoom-functions.php:2906
msgid "until"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:2584
#: includes/integrations/zoom/bp-zoom-functions.php:2635
#: includes/integrations/zoom/bp-zoom-functions.php:2696
#: includes/integrations/zoom/bp-zoom-functions.php:2798
#: includes/integrations/zoom/bp-zoom-functions.php:2849
#: includes/integrations/zoom/bp-zoom-functions.php:2910
msgid "occurrence"
msgid_plural "occurrences"
msgstr[0] ""
msgstr[1] ""

#: includes/integrations/zoom/bp-zoom-functions.php:2591
#: includes/integrations/zoom/bp-zoom-functions.php:2805
msgid "weeks on"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:2593
#: includes/integrations/zoom/bp-zoom-functions.php:2807
msgid "week on"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:2608
#: includes/integrations/zoom/bp-zoom-functions.php:2822
msgid " Sun"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:2611
#: includes/integrations/zoom/bp-zoom-functions.php:2825
msgid " Mon"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:2614
#: includes/integrations/zoom/bp-zoom-functions.php:2828
msgid " Tue"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:2617
#: includes/integrations/zoom/bp-zoom-functions.php:2831
msgid " Wed"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:2620
#: includes/integrations/zoom/bp-zoom-functions.php:2834
msgid " Thu"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:2623
#: includes/integrations/zoom/bp-zoom-functions.php:2837
msgid " Fri"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:2626
#: includes/integrations/zoom/bp-zoom-functions.php:2840
msgid " Sat"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:2642
#: includes/integrations/zoom/bp-zoom-functions.php:2856
msgid "months on the"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:2644
#: includes/integrations/zoom/bp-zoom-functions.php:2858
msgid "month on the"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:2648
#: includes/integrations/zoom/bp-zoom-functions.php:2862
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:225
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:272
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:294
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:276
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:298
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:306
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:326
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:310
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:330
msgid "of the month"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:2654
#: includes/integrations/zoom/bp-zoom-functions.php:2868
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:226
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:279
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:283
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:311
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:315
msgid "First"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:2656
#: includes/integrations/zoom/bp-zoom-functions.php:2870
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:227
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:280
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:284
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:312
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:316
msgid "Second"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:2658
#: includes/integrations/zoom/bp-zoom-functions.php:2872
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:228
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:281
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:285
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:313
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:317
msgid "Third"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:2660
#: includes/integrations/zoom/bp-zoom-functions.php:2874
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:229
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:282
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:286
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:314
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:318
msgid "Fourth"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:2662
#: includes/integrations/zoom/bp-zoom-functions.php:2876
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:230
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:283
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:287
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:315
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:319
msgid "Last"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:2669
#: includes/integrations/zoom/bp-zoom-functions.php:2883
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:248
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:286
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:252
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:290
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:283
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:318
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:287
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:322
msgid "Sun"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:2672
#: includes/integrations/zoom/bp-zoom-functions.php:2886
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:250
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:287
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:254
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:291
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:285
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:319
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:289
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:323
msgid "Mon"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:2675
#: includes/integrations/zoom/bp-zoom-functions.php:2889
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:252
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:288
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:256
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:292
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:287
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:320
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:291
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:324
msgid "Tue"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:2678
#: includes/integrations/zoom/bp-zoom-functions.php:2892
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:254
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:289
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:258
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:293
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:289
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:321
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:293
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:325
msgid "Wed"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:2681
#: includes/integrations/zoom/bp-zoom-functions.php:2895
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:256
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:290
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:260
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:294
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:291
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:322
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:295
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:326
msgid "Thu"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:2684
#: includes/integrations/zoom/bp-zoom-functions.php:2898
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:258
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:291
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:262
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:295
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:293
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:323
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:297
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:327
msgid "Fri"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:2687
#: includes/integrations/zoom/bp-zoom-functions.php:2901
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:260
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:292
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:264
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:296
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:295
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:324
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:299
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:328
msgid "Sat"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:3069
msgid "Once in 5 minutes"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:3577
#: includes/integrations/zoom/bp-zoom-functions.php:3723
#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:192
#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:27
#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:27
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:120
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:119
#: includes/integrations/zoom/templates/buddypress/zoom/loop-meeting.php:37
#: includes/integrations/zoom/templates/buddypress/zoom/loop-webinar.php:34
#: includes/integrations/zoom/templates/buddypress/zoom/meeting/recordings.php:176
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:24
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:24
#: includes/integrations/zoom/templates/buddypress/zoom/webinar/recordings.php:175
msgid " at "
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:3584
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:194
#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:61
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:163
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:111
msgid "Meeting ID"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:3598
#: includes/integrations/zoom/bp-zoom-functions.php:3744
#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:55
#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:53
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:149
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:148
msgid "Date and Time"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:3606
#: includes/integrations/zoom/bp-zoom-functions.php:3752
#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:78
#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:76
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:179
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:178
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:114
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:118
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:165
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:169
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:134
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:134
msgid "Duration"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:3616
#: includes/integrations/zoom/bp-zoom-functions.php:3762
#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:83
#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:81
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:184
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:183
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:139
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:139
#. translators: %d number of hours
#. translators: %d is number of hours
#. translators: %d is number of hours.
#. translators: %d number of hours
#. translators: %d number of hours
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] ""
msgstr[1] ""

#: includes/integrations/zoom/bp-zoom-functions.php:3620
#: includes/integrations/zoom/bp-zoom-functions.php:3766
#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:87
#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:85
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:188
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:187
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:143
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:143
#. translators: %d number of minutes
#. translators: %d is number of hours
#. translators: %d is number of minutes.
#. translators: %d number of minutes
#. translators: %d number of minutes
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] ""
msgstr[1] ""

#: includes/integrations/zoom/bp-zoom-functions.php:3633
#: includes/integrations/zoom/bp-zoom-functions.php:3779
#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:156
#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:152
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:223
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:222
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:207
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:207
msgid "Registration Link"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:3649
#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:169
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:235
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:221
msgid "Meeting Link"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:3661
#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:44
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:140
msgid "Meeting Details"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:3730
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:245
#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:59
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:162
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:111
msgid "Webinar ID"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:3795
#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:165
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:234
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:221
msgid "Webinar Link"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:3807
#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:43
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:139
msgid "Webinar Details"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:4339
#: includes/integrations/zoom/bp-zoom-functions.php:4436
#: includes/integrations/zoom/bp-zoom-functions.php:4444
#: includes/integrations/zoom/bp-zoom-functions.php:4454
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1999
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:2054
msgid "No Zoom account found"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:4406
msgid "Default"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:4528
msgid ""
"To create Zoom meetings and webinars using Gutenberg blocks, create a "
"<strong>Server-to-Server OAuth</strong> app in your Zoom account and "
"connect it below."
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:4534
#: includes/integrations/zoom/bp-zoom-functions.php:4537
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:710
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1269
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1282
msgid "Account ID"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:4542
msgid ""
"Enter the <strong>Account ID</strong> from the <strong>App "
"Credentials</strong> section in your Zoom app’s settings."
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:4547
#: includes/integrations/zoom/bp-zoom-functions.php:4550
#: includes/integrations/zoom/bp-zoom-functions.php:4689
#: includes/integrations/zoom/bp-zoom-functions.php:4692
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:725
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1270
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1299
#: includes/sso/admin/sso-fields.php:20 includes/sso/admin/sso-fields.php:76
#: includes/sso/admin/sso-fields.php:128 includes/sso/admin/sso-fields.php:368
msgid "Client ID"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:4555
#: includes/integrations/zoom/bp-zoom-functions.php:4697
msgid ""
"Enter the <strong>Client ID</strong> from the <strong>App "
"Credentials</strong> section in your Zoom app’s settings."
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:4560
#: includes/integrations/zoom/bp-zoom-functions.php:4563
#: includes/integrations/zoom/bp-zoom-functions.php:4702
#: includes/integrations/zoom/bp-zoom-functions.php:4705
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:740
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1271
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1316
#: includes/sso/admin/sso-fields.php:26 includes/sso/admin/sso-fields.php:188
#: includes/sso/admin/sso-fields.php:374
msgid "Client Secret"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:4569
#: includes/integrations/zoom/bp-zoom-functions.php:4710
#. translators: %s is the buddyboss marketplace link.
msgid ""
"Enter the <strong>Client Secret</strong> from the <strong>App "
"Credentials</strong> section in your Zoom app’s settings."
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:4574
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:755
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1333
msgid "Account Email"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:4593
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:770
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1350
msgid "Select a Zoom account"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:4599
msgid ""
"After entering the details above, select the <strong>Zoom account</strong> "
"to sync Zoom meetings and webinars from."
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:4604
#: includes/integrations/zoom/bp-zoom-functions.php:4607
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:781
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:952
msgid "Secret Token"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:4612
msgid ""
"Enter the <strong>Secret Tokens</strong> from the <strong>Features</strong> "
"section in your Zoom app’s settings."
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:4617
#: includes/integrations/zoom/bp-zoom-functions.php:4620
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:796
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:998
msgid "Notification URL"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:4621
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:800
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1002
msgid "Copied"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:4622
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:800
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1002
msgid "Copy"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:4625
msgid ""
"Enter as the <strong>Event notification endpoint URL</strong> when "
"configuring Event Subscriptions in your Zoom app’s settings."
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:4682
msgid ""
"To require members attend your Zoom meetings and webinars directly on your "
"site, create a <strong>Meeting SDK</strong> app in your Zoom account and "
"connect it below. When enabled, members will not be able to attend using "
"the Zoom app."
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:4715
msgid "Enabled For"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:4719
msgid "Meetings and webinars"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:4721
#: includes/integrations/zoom/templates/buddypress/groups/single/parts/zoom-subnav.php:42
#: includes/integrations/zoom/templates/buddypress/zoom/meetings.php:27
msgid "Meetings"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:4723
#: includes/integrations/zoom/templates/buddypress/groups/single/parts/zoom-subnav.php:43
#: includes/integrations/zoom/templates/buddypress/zoom/webinars.php:28
msgid "Webinars"
msgstr ""

#: includes/integrations/zoom/bp-zoom-functions.php:4725
msgid "None"
msgstr ""

#: includes/integrations/zoom/bp-zoom-group-functions.php:858
#. translators: %s: Account Email.
msgid "Connected to Zoom %s"
msgstr ""

#: includes/integrations/zoom/bp-zoom-template.php:246
#: includes/integrations/zoom/bp-zoom-template.php:1863
#. translators: %1$s - from page number, %2$s - to page number, %3$s - total
#. pages.
msgid "Viewing 1 item"
msgid_plural "Viewing %1$s - %2$s of %3$s items"
msgstr[0] ""
msgstr[1] ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:107
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:114
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1690
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1697
msgid "There was a problem when updating. Please try again."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:118
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:306
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1086
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1701
msgid "Groups is not active."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:124
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:312
msgid "You do not have permission to create meeting in this group."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:128
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:319
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1099
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1711
msgid "This group does not have Zoom enabled. Please check the settings."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:139
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:331
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1116
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1722
msgid "Please choose API Host in the settings and try again."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:147
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:345
msgid "Please select the meeting date."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:151
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:349
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1134
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1734
msgid "Please enter valid date as format <strong>yyyy-mm-dd</strong>."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:155
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:386
msgid "Please change the meeting date to a future date."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:175
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:369
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:550
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:640
msgid "Please select the meeting duration to a minimum of 15 minutes."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:255
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1829
msgid "There was an error saving the occurrence."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:283
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1852
msgid "There was a problem when updating an occurrence. Please try again."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:292
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:533
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1073
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1311
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:404
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:409
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:457
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:462
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:517
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:522
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:588
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:597
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:779
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:801
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:808
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:885
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:962
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:967
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:1014
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:1019
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:1073
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:1078
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:1141
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:1150
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:1323
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:1345
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:1352
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:1428
msgid ""
"Something went wrong. If passcode is entered then please make sure it "
"matches Zoom Passcode requirements and try again."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:337
msgid "Please enter the meeting title."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:395
msgid "Please select the meeting week day."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:581
msgid "There was an error saving the meeting."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:673
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:677
msgid "You do not have permission to delete a meeting in this group."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:747
msgid "Please provide ID of the meeting."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:769
msgid "There was a issue in fetching invitation."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:810
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:855
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1425
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1671
#: includes/integrations/zoom/templates/buddypress/zoom/meetings.php:107
#: includes/integrations/zoom/templates/buddypress/zoom/webinars.php:98
#: includes/schedule-posts/templates/buddypress/activity-schedule/activity-schedule-loop.php:50
msgid "Load More"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:876
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:916
msgid "Please provide meeting ID."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1000
#. translators: %s total meetings
msgid "%s meetings updated successfully."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1016
msgid "Meetings update complete!"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1092
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1707
msgid "You do not have permission to create webinar in this group."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1107
msgid "Webinars not allowed in this group."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1122
msgid "Please enter the webinar title."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1130
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1730
msgid "Please select the webinar date."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1154
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1758
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:1105
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:1192
msgid "Please select the webinar duration to a minimum of 15 minutes."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1171
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1738
msgid "Please change the webinar date to a future date."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1180
msgid "Please select the webinar week day."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1358
msgid "There was an error saving the webinar."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1444
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1484
msgid "Please provide webinar ID."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1576
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1580
msgid "You do not have permission to delete a webinar in this group."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1947
#. translators: %s total meetings
msgid "%s webinars updated successfully."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:1963
msgid "Webinars update complete!"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:2084
#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:2121
msgid "The group ID is required."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-ajax.php:2091
msgid "The group is not connected with group zoom."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:183
msgid "Are you sure you want to delete this occurrence?"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:186
msgid "Zoom Meeting"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:187
msgid "Create meeting or add existing meeting."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:188
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:178
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2681
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2732
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:11
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:457
msgid "Create Meeting"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:189
msgid "Create meeting in Zoom"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:190
msgid "Add Existing Meeting"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:191
msgid "Existing Meeting"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:192
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1083
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1367
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1380
msgid "Save"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:195
msgid "Enter meeting ID without spaces…"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:196
msgid "Meeting Synced."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:197
msgid "Meeting Updated."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:198
#: includes/integrations/zoom/templates/buddypress/groups/single/parts/zoom-subnav.php:51
msgid "Sync"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:199
msgid "Title"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:200
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:88
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:92
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:99
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:103
msgid "When"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:201
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:143
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:147
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:193
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:197
msgid "Timezone"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:202
msgid "Search timezone"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:203
#: includes/reactions/templates/admin/emotion-picker/render-emotion-icons.php:166
msgid "No results found"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:204
msgid "Auto Recording"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:205
msgid "No Recordings"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:206
msgid "Local"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:207
msgid "Cloud"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:208
msgid "Save Meeting"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:209
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-view.php:46
#: includes/topics/templates/buddypress/groups/single/admin/activity-topics-list.php:75
#: includes/topics/templates/buddypress/groups/single/admin/activity-topics-list.php:77
#: includes/topics/templates/buddypress/groups/single/admin/activity-topics-list.php:78
msgid "Delete"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:210
msgid "Sunday"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:211
msgid "Monday"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:212
msgid "Tuesday"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:213
msgid "Wednesday"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:214
msgid "Thursday"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:215
msgid "Friday"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:216
msgid "Saturday"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:217
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:266
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:270
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:300
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:304
msgid "Day"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:219
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:231
msgid "Occures on"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:220
msgid "Day of the month"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:221
msgid "Week of the month"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:222
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:224
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:228
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:251
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:255
msgid "Daily"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:223
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:225
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:229
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:252
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:256
msgid "Weekly"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:224
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:226
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:230
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:253
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:257
msgid "Monthly"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:232
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:156
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:155
msgid "Occurrences"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:233
msgid "occurences"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:234
msgid "Occurrence Deleted."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:235
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-view.php:41
#: includes/topics/templates/buddypress/groups/single/admin/activity-topics-list.php:70
#: includes/topics/templates/buddypress/groups/single/admin/activity-topics-list.php:71
#: includes/topics/templates/buddypress/groups/single/admin/activity-topics-list.php:72
msgid "Edit"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:236
#: includes/schedule-posts/templates/buddypress/common/js-templates/activity/parts/bb-activity-schedule-post.php:60
msgid "Date"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:237
msgid "End by"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:238
msgid "End After"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:239
msgid "Zoom Webinar"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:240
msgid "Create webinar in Zoom"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:241
msgid "Create webinar or add existing webinar."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:242
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2693
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2744
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:11
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:453
msgid "Create Webinar"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:243
msgid "Existing Webinar"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:244
msgid "Add Existing Webinar"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:246
msgid "Enter webinar ID without spaces…"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:247
msgid "Webinar Synced."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:248
msgid "Webinar Deleted."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:249
msgid "Webinar Updated."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:250
msgid "Save Webinar"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:251
msgid "Duration (minutes)"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:252
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-form.php:78
#: includes/reactions/templates/admin/emotion-picker/bb-custom-settings.php:48
#: includes/reactions/templates/admin/emotion-picker/bb-emojis-settings.php:56
#: includes/reactions/templates/admin/emotion-picker/bb-icons-settings.php:44
msgid "Settings"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:253
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:69
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:73
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:79
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:83
msgid "Description (optional)"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:254
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:76
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:80
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:86
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:90
msgid "Passcode (optional)"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:255
msgid "Default Host"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:256
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:437
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:433
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:494
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:490
msgid "Alternative Hosts"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:257
msgid "Example: <EMAIL>"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:258
msgid ""
"Entered by email, comma separated. Each email added needs to match with a "
"user in your Zoom account."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:259
msgid "Start video when host joins"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:260
msgid "Start video when participants join"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:261
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:370
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:374
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:400
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:404
msgid "Require Registration"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:262
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:265
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:389
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:420
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:252
msgid "Enable practice session"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:263
#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:230
#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:200
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:304
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:269
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:400
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:431
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:426
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:279
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:256
msgid "Only authenticated users can join"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:264
#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:226
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:300
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:395
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:425
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:275
msgid "Enable waiting room"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:265
#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:222
#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:196
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:296
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:390
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:420
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:271
msgid "Mute participants upon entry"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:266
#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:218
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:292
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:385
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:415
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:267
msgid "Enable join before host"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:267
msgid "Recurring Options"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:268
msgid "Recurring Webinar"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:269
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:221
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:225
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:249
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:253
msgid "Recurrence"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:270
msgid "Recurring Meeting"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:271
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:233
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:237
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:259
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:263
msgid "Repeat every"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:272
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:374
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:378
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:404
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:408
msgid "Attendees register once and can attend any of the occurrences"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:273
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:376
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:380
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:406
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:410
msgid "Attendees need to register for each occurrence to attend"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:274
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:378
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:382
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:408
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:412
msgid "Attendees register once and can choose one or more occurrences to attend"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:275
msgid "Meeting Deleted."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:416
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:469
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:529
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:612
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:974
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:1026
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:1085
#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:1165
msgid "Please choose API Host Email in the settings and try again."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:814
msgid "Please provide Meeting ID."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-blocks.php:1358
msgid "Please provide Webinar ID."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-conference-api.php:1050
msgid "Invalid credentials."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:116
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:203
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:529
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:617
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:627
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2600
msgid "Zoom"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:137
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2677
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2728
msgid "Upcoming Meetings"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:146
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2673
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2724
#: includes/integrations/zoom/templates/buddypress/zoom/meetings.php:25
msgid "Past Meetings"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:158
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2689
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2740
msgid "Upcoming Webinars"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:167
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2685
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2736
#: includes/integrations/zoom/templates/buddypress/zoom/webinars.php:26
msgid "Past Webinars"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:188
msgid "Create Webinars"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:237
msgid "Group meetings were successfully synced with Zoom."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:244
msgid "Group webinars were successfully synced with Zoom."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:621
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:846
msgid "Setup Wizard"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:628
msgid ""
"Create and sync Zoom meetings and webinars directly within this group by "
"connecting your Zoom account."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:633
msgid "Yes, I want to connect this group to Zoom."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:643
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:654
msgid "Authentication"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:646
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:815
msgid "Group Permissions"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:659
msgid ""
"This group has been connected to the site's Zoom account by a site "
"administrator."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:668
#. translators: Added bold HTML tag.
msgid ""
"To connect your Zoom account to this group, create a %s app in your Zoom "
"account and enter the information in the fields below."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:672
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:909
#. translators: OAuth app name.
msgid "Server-to-Server OAuth"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:714
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:729
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:744
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:785
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:963
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1287
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1304
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1321
msgid "Show key"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:714
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:729
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:744
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:785
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:963
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1287
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1304
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1321
msgid "Hide key"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:718
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1291
msgid "The Account ID from the App Credentials section in your Zoom app's settings."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:733
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1308
msgid "The Client ID from the App Credentials section in your Zoom app's settings."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:748
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1325
msgid ""
"The Client Secret from the App Credentials section in your Zoom app's "
"settings."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:774
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1354
msgid "Select the Zoom account to sync Zoom meetings and webinars from."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:789
msgid ""
"Enter the Secret Token from the Features section in your Zoom app's "
"settings."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:804
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1006
msgid ""
"Use as the Event notification endpoint URL when configuring Event "
"Subscriptions in your Zoom app's settings."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:816
msgid ""
"Which members of this group are allowed to create, edit and delete Zoom "
"meetings?"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:820
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2891
msgid "Organizers only"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:825
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2896
msgid "Organizers and Moderators only"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:830
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2901
#: includes/topics/bb-topics-functions.php:158
msgid "All group members"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:833
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2887
msgid ""
"The Zoom account connected to this group will be assigned as the default "
"host for every meeting and webinar, regardless of which member they are "
"created by."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:843
msgid "Save Settings"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:852
msgid "Zoom Login"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:853
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:922
msgid "Create App"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:854
msgid "App Information"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:855
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:959
msgid "Security Token"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:856
msgid "Permissions"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:857
msgid "Activation"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:858
msgid "Credentials"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:868
msgid ""
"To use Zoom, we will need you to create an \"app\" in your Zoom account and "
"connect it to this group so we can sync meeting data with Zoom. This should "
"only take a few minutes if you already have a Zoom account. Note that cloud "
"recordings and alternate hosts will only work if you have a \"Pro\" or "
"\"Business\" Zoom account."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:878
#. translators: 1: marketplace link, 2: Sign In, 3: Sign Up.
msgid ""
"Start by going to the %1$s and clicking the %2$s link in the titlebar. You "
"can sign in using your existing Zoom credentials. If you do not yet have a "
"Zoom account, just click the %3$s link in the titlebar. Once you have "
"successfully signed into Zoom App Marketplace you can move to the next step."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:879
msgid "Zoom App Marketplace"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:880
msgid "Sign In"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:881
msgid "Sign Up"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:895
#. translators: 1: Build app link in zoom, 2: Titles.
msgid ""
"Once you are signed into Zoom App Marketplace, you need to %1$s. You can "
"always find the Build App link by going to %2$s from the titlebar."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:896
msgid "build an app"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:897
msgid "Develop"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:897
msgid "Build App"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:908
#. translators: 1: App Type, 2: Action name.
msgid ""
"On the next page, select the %1$s option as the app type and click the %2$s "
"button."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:910
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:923
msgid "Create"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:921
#. translators: 1: Create App, 2: Action name.
msgid ""
"After clicking %1$s you will get a popup asking you to enter an App Name. "
"Enter any name that will remind you the app is being used for this website. "
"Then click the %2$s button."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:935
msgid ""
"With the app created, the first step is to fill in your Basic and Developer "
"Contact Information. This information is mandatory before you can activate "
"your app."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:944
msgid ""
"We now need to configure the event notifications by Zoom on the Feature "
"tab. This step is necessary to allow meeting updates from Zoom to "
"automatically sync back into your group."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:945
msgid ""
"Note that within the group on this site, you can also click the \"Sync\" "
"button at any time to force a manual sync."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:950
#. translators: 1: copy, 2: Secret Token.
msgid "Firstly you need to %1$s your %2$s and insert it below"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:951
msgid "copy"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:978
#. translators: Add Event Subscription.
msgid "Next we need to enable Event Subscriptions and select %s"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:979
msgid "Add Event Subscription"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:991
#. translators: Event notification endpoint URL.
msgid ""
"For the Subscription name, you can add any name. You should then use the "
"Notification URL below and copy it into the %s"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:992
msgid "Event notification endpoint URL"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1016
#. translators: Validate.
msgid "Click %s."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1017
msgid "Validate"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1029
#. translators: Add Event Subscription.
msgid ""
"After that, you need to add Events for the app to subscribe to. Click %s "
"and now add the follower permissions under each section"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1030
msgid "Add Events"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1038
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1101
msgid "Meeting"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1040
msgid "Start Meeting"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1041
msgid "End Meeting"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1042
msgid "Meeting has been updated"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1043
msgid "Meeting has been deleted"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1049
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1128
msgid "Webinar"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1051
msgid "Start Webinar"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1052
msgid "End Webinar"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1053
msgid "Webinar has been updated"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1054
msgid "Webinar has been deleted"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1060
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1154
msgid "Recording"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1062
msgid "All Recordings have completed"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1068
#. translators: 1: 9 scopes added, 2: Done.
msgid ""
"At this point, you should see that you have %1$s.Once all these have been "
"enabled, click %2$s."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1069
msgid "9 scopes added"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1070
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1223
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-form.php:124
#: includes/reactions/templates/admin/emotion-picker/bb-custom.php:47
msgid "Done"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1082
#. translators: 1: Save, 2: Continue.
msgid "Click %1$s and then %2$s to the next step."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1096
msgid ""
"Now we add the appropriate account permissions from the Scopes tab. Click "
"+Add Scopes and add the following permissions under each scope type"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1104
msgid "View all user meetings"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1107
msgid "View a meeting - meeting:read:meeting:admin"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1108
msgid "View a past meeting's instances - meeting:read:list_past_instances:admin"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1109
msgid "View a meeting's invitation - meeting:read:invitation:admin"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1112
msgid "View and manage all user meetings"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1114
msgid "Delete a meeting - meeting:delete:meeting:admin"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1115
msgid "Update a meeting - meeting:update:meeting:admin"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1116
msgid "Create a meeting for a user - meeting:write:meeting:admin"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1131
msgid "View all user Webinars"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1133
msgid "View a past webinar's instances - webinar:read:list_past_instances:admin"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1134
msgid "View a webinar - webinar:read:webinar:admin"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1138
msgid "View and manage all user Webinars"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1140
msgid "Delete a webinar - webinar:delete:webinar:admin"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1141
msgid "Update a webinar - webinar:update:webinar:admin"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1142
msgid "Create a webinar for a user - webinar:write:webinar:admin"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1157
msgid "View all user recordings"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1159
msgid "list account recording - cloud_recording:read:list_account_recordings:admin"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1160
msgid ""
"Returns all of a meeting's recordings - "
"cloud_recording:read:list_recording_files:admin"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1161
msgid ""
"Lists all cloud recordings for a user - "
"cloud_recording:read:list_user_recordings:admin"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1169
#: includes/sso/lib/class-bb-sso-gdpr.php:143
msgid "User"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1172
msgid "View all user information"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1174
msgid "View users - user:read:list_users:admin"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1175
msgid "View a user - user:read:user:admin"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1176
msgid "View a user's settings - user:read:settings:admin"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1180
msgid "View and manage sub account's user information"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1182
msgid "View a user's settings - user:read:settings:master"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1186
msgid "View users information and manage users"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1188
msgid "Create a user - user:write:user:admin"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1189
msgid "Delete a user - user:delete:user:admin"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1190
msgid "Update a user - user:update:user:admin"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1202
msgid "Report"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1205
msgid "View report data"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1207
msgid "View meeting detail reports - report:read:meeting:admin"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1208
msgid "View webinar detail reports - report:read:webinar:admin"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1221
#. translators: 1: 8 scopes added, 2: Done, 3: Continue.
msgid ""
"At this point, you should see that you have %1$s. Once all these have been "
"enabled, click %2$s and then %3$s to the last step."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1222
msgid "23 scopes added"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1237
#. translators: Activate your app.
msgid ""
"With all the previous steps completed, your app should now be ready for "
"activation. Click %s. we can now activate your app."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1238
msgid "Activate your app"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1250
#. translators: Your app is activated on the account.
msgid ""
"You should see a message that says %s. At this point we are now ready to "
"head to the final task of the setup."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1251
msgid "Your app is activated on the account"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1267
#. translators: 1 - App Credentials, 2 - Account ID, 3 - Client ID, 4 - Client
#. Secret.
msgid ""
"Once you get to the %1$s page, copy the %2$s, %3$s and %4$s and paste them "
"into the fields in the form below."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1268
msgid "App Credentials"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1275
msgid ""
"If multiple zoom users are available, you will then need to select the "
"email address of the associated account for this group."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1366
#. translators: Save.
msgid ""
"Make sure to click the %s button on this tab to save the data you entered. "
"You have now successfully connected Zoom to your group."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1377
msgid "Previous"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1378
#: includes/schedule-posts/templates/buddypress/common/js-templates/activity/parts/bb-activity-schedule-post.php:99
msgid "Next"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1426
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:3007
msgid "Group Zoom settings were successfully updated."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1543
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1554
msgid "New Zoom meeting"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1565
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1576
msgid "New Zoom webinar"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1621
#. translators: %1$s - user link, %2$s - meeting link., %3$s - group link.
msgid "%1$s scheduled a Zoom meeting %2$s in the group %3$s"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1630
#. translators: %1$s - user link, %2$s - meeting link., %3$s - group link.
msgid "%1$s scheduled Zoom meeting %2$s starting soon in the group %3$s"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1677
#. translators: %1$s - user link, %2$s - group link.
msgid "%1$s scheduled a Zoom webinar %2$s in the group %3$s"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1686
#. translators: %1$s - user link, %2$s - webinar link., %3$s - group link.
msgid "%1$s - Zoom webinar %2$s is starting soon in the group %3$s"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1871
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2034
#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:272
#. translators: total number of groups.
msgid "You have %1$d new Zoom meetings in groups"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1917
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2080
#. translators: 1 Meeting title. 2 Group Title.
msgid "You have a meeting \"%1$s\" scheduled in the group \"%2$s\""
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:1924
#. translators: 1 Meeting title. 2 Group Title.
msgid "Zoom meeting \"%1$s\" created in the group \"%2$s\""
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2242
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2378
#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:278
#. translators: total number of groups.
msgid "You have %1$d new Zoom webinars in groups"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2288
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2424
#. translators: 1 Webinar title. 2 Group Title.
msgid "You have a webinar \"%1$s\" scheduled in the group \"%2$s\""
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2295
#. translators: 1 Webinar title. 2 Group Title.
msgid "Zoom webinar \"%1$s\" created in the group \"%2$s\""
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2808
msgid "not connected"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2830
msgid "Create and sync Zoom meetings and webinars directly within this group."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2834
msgid "Yes, I want to connect this group to Zoom"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2842
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2845
msgid "How should this group be connected to Zoom?"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2850
#. translators: %s: Zoom integration tab.
msgid ""
"You can let the group organizers create and connect their own Zoom app to "
"this group, or connect using the app defined in your site’s %s."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2855
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:32
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:32
#. translators: 1: Zoom setting url, 2: Zoom setting title
msgid "Zoom settings"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2863
msgid "Let the group organizer(s) connect their own Zoom app"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2872
#. translators: %s: Account Email.
msgid "Use this site’s Zoom app (%s)"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2883
#: includes/integrations/zoom/includes/class-bp-zoom-group.php:2886
msgid "Which group members can create, edit and delete Zoom meetings?"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:84
msgid "New meeting or webinar is scheduled in one of your groups"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:85
msgid "A Zoom meeting or webinar is scheduled in a group"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:93
#. translators: do not remove {} brackets or translate its contents.
msgid ""
"[{{{site.name}}}] {{poster.name}} scheduled a Zoom Meeting in the group: "
"\"{{group.name}}\""
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:95
#. translators: do not remove {} brackets or translate its contents.
msgid ""
"<a href=\"{{{poster.url}}}\">{{poster.name}}</a> scheduled a Zoom Meeting "
"in the group \"<a href=\"{{{group.url}}}\">{{group.name}}</a>\":\n"
"\n"
"{{{zoom_meeting}}}"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:97
#. translators: do not remove {} brackets or translate its contents.
msgid ""
"{{poster.name}} scheduled a Zoom Meeting in the group \"{{group.name}}\":\n"
"\n"
"{{{zoom_meeting}}}"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:99
msgid ""
"You will no longer receive emails when someone schedules a meeting in a "
"group."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:108
#. translators: do not remove {} brackets or translate its contents.
msgid ""
"[{{{site.name}}}] {{poster.name}} scheduled a Zoom Webinar in the group: "
"\"{{group.name}}\""
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:110
#. translators: do not remove {} brackets or translate its contents.
msgid ""
"<a href=\"{{{poster.url}}}\">{{poster.name}}</a> scheduled a Zoom Webinar "
"in the group \"<a href=\"{{{group.url}}}\">{{group.name}}</a>\":\n"
"\n"
"{{{zoom_webinar}}}"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:112
#. translators: do not remove {} brackets or translate its contents.
msgid ""
"{{poster.name}} scheduled a Zoom Webinar in the group \"{{group.name}}\":\n"
"\n"
"{{{zoom_webinar}}}"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:114
msgid ""
"You will no longer receive emails when someone schedules a webinar in a "
"group."
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:127
msgid "Group meetings and webinars"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:202
#. translators: %s: The meeting start date.
msgid "New meeting scheduled for %s"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:208
#. translators: %s: The meeting start date.
msgid "Update meeting scheduled for %s"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:214
#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:328
msgid "New meeting scheduled"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:216
#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:338
msgid "Update meeting scheduled"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:236
#. translators: %s: The meeting start date
msgid "New webinar scheduled for %s"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:242
#. translators: %s: The meeting start date.
msgid "Update webinar scheduled for %s"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:248
#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:396
msgid "New webinar scheduled"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:250
#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:406
msgid "Update webinar scheduled"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:292
#. translators: 1. Group name. 2. The meeting start date.
msgid "%1$s: New meeting scheduled for %2$s"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:299
#. translators: %s: The meeting start date
msgid "New meeting scheduled for %1$s"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:307
#. translators: 1. Group name. 2. The meeting start date.
msgid "%1$s: Update meeting scheduled for %2$s"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:314
#. translators: %s: The meeting start date
msgid "Update meeting scheduled for %1$s"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:324
#. translators: %s: Group name
msgid "%1$s: New meeting scheduled"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:334
#. translators: %s: Group name
msgid "%1$s: Update meeting scheduled"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:360
#. translators: 1. Group name. 2. The meeting start date.
msgid "%1$s: New webinar scheduled for %2$s"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:367
#. translators: %s: The meeting start date
msgid "New webinar scheduled for %1$s"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:375
#. translators: 1. Group name. 2. The meeting start date.
msgid "%1$s: Update webinar scheduled for %2$s"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:382
#. translators: %s: The meeting start date
msgid "Update webinar scheduled for %1$s"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:392
#. translators: %s: Group name
msgid "%1$s: New webinar scheduled"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-groups-notification.php:402
#. translators: %s: Group name
msgid "%1$s: Update webinar scheduled"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-meeting-template.php:255
#: includes/integrations/zoom/includes/class-bp-zoom-webinar-template.php:254
msgid "&larr;"
msgstr ""

#: includes/integrations/zoom/includes/class-bp-zoom-meeting-template.php:256
#: includes/integrations/zoom/includes/class-bp-zoom-webinar-template.php:255
msgid "&rarr;"
msgstr ""

#: includes/integrations/zoom/templates/admin/integration-tab-intro.php:12
msgid "Zoom <span>&mdash; requires license</span>"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/groups/single/parts/zoom-subnav.php:16
msgid "Group zoom navigation menu"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/groups/single/parts/zoom-subnav.php:49
msgid "Sync group webinars with Zoom"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/groups/single/parts/zoom-subnav.php:49
msgid "Sync group meetings with Zoom"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:68
#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:66
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:170
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:169
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:122
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:122
msgid "Description"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:106
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:182
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:207
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:163
msgid "Meeting Notifications"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:111
#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:108
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:168
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:168
#. translators: %d number of hours
msgid "%d hour before"
msgid_plural "%d hours before"
msgstr[0] ""
msgstr[1] ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:114
#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:111
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:171
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:171
#. translators: %d number of minutes
msgid "%d minute before"
msgid_plural "%d minutes before"
msgstr[0] ""
msgstr[1] ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:116
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:173
msgid "Immediately before the meeting"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:124
msgid "Meeting Password"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:135
#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:131
#: includes/integrations/zoom/templates/buddypress/zoom/meeting/recordings.php:255
#: includes/integrations/zoom/templates/buddypress/zoom/webinar/recordings.php:254
msgid "Show password"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:139
#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:135
msgid "Hide password"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:146
#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:142
msgid "No password required"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:173
#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:177
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:242
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:247
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:225
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:228
msgid "View Invitation"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:186
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:260
#: includes/integrations/zoom/templates/buddypress/zoom/meeting/recordings.php:264
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:235
#: includes/integrations/zoom/templates/buddypress/zoom/webinar/recordings.php:263
msgid "Copied to clipboard"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:186
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:260
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:235
msgid "Copy Meeting Invitation"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:194
#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:174
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:268
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:243
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:335
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:339
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:365
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:369
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:243
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:230
msgid "Video"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:197
#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:177
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:271
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:246
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:339
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:429
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:343
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:425
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:369
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:485
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:373
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:481
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:246
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:233
msgid "Host"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:198
#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:178
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:272
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:247
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:247
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:234
msgid " On"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:198
#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:202
#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:178
#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:182
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:272
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:276
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:247
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:251
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:247
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:251
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:234
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:238
msgid "Off"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:201
#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:181
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:275
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:250
msgid "Participant"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:202
#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:182
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:276
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:251
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:251
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:238
msgid "On"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:207
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:281
#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:365
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:395
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:256
msgid "Meeting Options"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:237
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:311
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:286
msgid "Record the meeting automatically in the cloud"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:239
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:313
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:288
msgid "Record the meeting automatically in the local computer"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:241
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:315
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:290
msgid "Do not record the meeting."
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:289
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:345
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:341
msgid "Host Meeting in Browser"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:291
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:347
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:343
msgid "Join Meeting in Browser"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:299
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:355
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:353
msgid "Host Meeting in Zoom"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-meeting-entry.php:301
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:357
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:355
msgid "Join Meeting in Zoom"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:103
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:186
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:211
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:163
msgid "Webinar Notifications"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:113
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:173
msgid "Immediately before the webinar"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:120
msgid "Webinar Password"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:187
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:256
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:369
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:399
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:243
msgid "Webinar Options"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:207
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:276
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:263
msgid "Record the webinar automatically in the cloud"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:209
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:278
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:265
msgid "Record the webinar automatically in the local computer"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:211
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:280
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:267
msgid "Do not record the webinar."
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:264
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:309
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:338
msgid "Join Webinar in Browser"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:279
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:315
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:360
msgid "Host Webinar in Zoom"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/activity-webinar-entry.php:281
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:317
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:362
msgid "Join Webinar in Zoom"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:128
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:127
#: includes/integrations/zoom/templates/buddypress/zoom/loop-meeting.php:34
#: includes/integrations/zoom/templates/buddypress/zoom/loop-webinar.php:31
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:17
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:17
msgid "Recurring"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:131
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:130
#: includes/integrations/zoom/templates/buddypress/zoom/loop-meeting.php:17
#: includes/integrations/zoom/templates/buddypress/zoom/loop-webinar.php:16
msgid "Live"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:194
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:181
msgid "Meeting Passcode"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:204
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:203
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:190
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:190
msgid "Show passcode"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:207
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:206
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:191
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:191
msgid "Hide passcode"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/blocks/meeting-block.php:214
#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:213
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:197
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:197
msgid "No passcode required"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:193
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:181
msgid "Webinar Passcode"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/blocks/webinar-block.php:250
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:353
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:383
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:237
msgid "Panelists"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:31
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:31
#. translators: %s is settings link
msgid "This group does not have Zoom properly configured. Please update the %s."
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:62
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:72
msgid "Meeting Title"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:124
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:128
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:175
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:179
msgid "hr"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:137
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:141
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:187
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:191
msgid "min"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:188
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:192
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:214
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:218
msgid "Send"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:190
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:194
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:216
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:220
msgid "immediately"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:191
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:195
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:217
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:221
msgid "15 minutes"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:192
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:196
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:218
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:222
msgid "30 minutes"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:193
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:197
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:219
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:223
msgid "1 hour"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:194
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:198
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:220
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:224
msgid "2 hours"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:195
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:199
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:221
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:225
msgid "3 hours"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:196
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:200
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:222
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:226
msgid "4 hours"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:197
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:201
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:223
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:227
msgid "5 hours"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:199
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:225
msgid "before meeting"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:202
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:206
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:228
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:232
msgid "Enabling this option will create the following: "
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:204
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:208
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:230
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:234
msgid "Site notification for group members."
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:205
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:209
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:231
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:235
msgid "Email notification to group members."
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:206
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:210
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:232
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:236
msgid "Activity notification in group news feed."
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:215
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:243
msgid "Recurring meeting"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:244
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:248
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:279
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:283
msgid "Occurs on"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:301
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:305
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:332
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:336
msgid "End date"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:306
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:310
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:337
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:341
msgid "By"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:315
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:319
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:346
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:350
msgid "After"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:321
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:325
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:352
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:356
msgid "occurrences"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:349
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:379
msgid "Participants"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:356
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:386
msgid "Start video when host and participants join the meeting."
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:406
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:449
msgid "Record the meeting automatically"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:410
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:406
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:464
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:460
msgid "On the local computer"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:412
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:408
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:466
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:462
msgid "In the cloud"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:417
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:413
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:471
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:467
msgid "Record automatically onto local computer"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:432
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:488
msgid "Default host for all meetings in this group."
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:439
#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:435
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:496
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:492
msgid "Example: <EMAIL>, <EMAIL>"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-meeting.php:440
#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:497
msgid ""
"Additional hosts for this meeting, entered by email, comma separated. Each "
"email added needs to match with a user in the default host's Zoom account."
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:66
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:76
msgid "Webinar Title"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:203
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:229
msgid "before webinar"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:219
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:247
msgid "Recurring webinar"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:360
msgid "Start video when host and panelists join the webinar."
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:395
msgid "Require authentication to join"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:402
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:445
msgid "Record the webinar automatically"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:428
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:484
msgid "Default host for all webinars in this group."
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/create-webinar.php:436
#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:493
msgid ""
"Additional hosts for this webinar, entered by email, comma separated. Each "
"email added needs to match with a user in the default host's Zoom account."
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:64
msgid "Edit Meeting"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/edit-meeting.php:521
msgid "Update Meeting"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:68
msgid "Edit Webinar"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:390
msgid "Start video when host and participants join the webinar."
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/edit-webinar.php:517
msgid "Update Webinar"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/loop-meeting.php:32
#: includes/integrations/zoom/templates/buddypress/zoom/loop-webinar.php:29
#. translators: %d is meeting ID from zoom.
#. translators: %d is webinar ID from zoom.
msgid "ID: %d"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/meeting/recordings.php:150
#: includes/integrations/zoom/templates/buddypress/zoom/webinar/recordings.php:149
msgid "Show Recordings"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/meeting/recordings.php:154
#: includes/integrations/zoom/templates/buddypress/zoom/webinar/recordings.php:153
msgid " (Recordings)"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/meeting/recordings.php:225
#: includes/integrations/zoom/templates/buddypress/zoom/webinar/recordings.php:224
msgid "Play"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/meeting/recordings.php:235
#: includes/integrations/zoom/templates/buddypress/zoom/webinar/recordings.php:234
msgid "Video Recording"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/meeting/recordings.php:237
#: includes/integrations/zoom/templates/buddypress/zoom/webinar/recordings.php:236
msgid "Audio Recording"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/meeting/recordings.php:239
#: includes/integrations/zoom/templates/buddypress/zoom/webinar/recordings.php:238
msgid "Chat File"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/meeting/recordings.php:241
#: includes/integrations/zoom/templates/buddypress/zoom/webinar/recordings.php:240
msgid "Audio Transcript"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/meeting/recordings.php:243
#: includes/integrations/zoom/templates/buddypress/zoom/webinar/recordings.php:242
msgid "Timeline"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/meeting/recordings.php:264
#: includes/integrations/zoom/templates/buddypress/zoom/webinar/recordings.php:263
msgid "Copy Link"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/meeting/recordings.php:267
#: includes/integrations/zoom/templates/buddypress/zoom/webinar/recordings.php:266
msgid "Download"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/meeting/recordings.php:292
#: includes/integrations/zoom/templates/buddypress/zoom/webinar/recordings.php:291
msgid "Your browser does not support HTML5 video."
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/meeting/recordings.php:299
#: includes/integrations/zoom/templates/buddypress/zoom/webinar/recordings.php:298
msgid "Your browser does not support HTML5 audio."
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/meetings.php:37
#: includes/integrations/zoom/templates/buddypress/zoom/webinars.php:38
msgid "Recorded"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/meetings.php:42
#: includes/integrations/zoom/templates/buddypress/zoom/webinars.php:43
msgid "Create New"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/meetings.php:52
#: includes/integrations/zoom/templates/buddypress/zoom/meetings.php:53
#: includes/integrations/zoom/templates/buddypress/zoom/meetings.php:56
msgid "Search Meetings"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/meetings.php:116
#: includes/integrations/zoom/templates/buddypress/zoom/webinars.php:106
msgid "Timezone:"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:43
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:43
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-form.php:45
msgid "Options"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:54
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:73
msgid "Edit this Meeting"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:57
msgid "You're changing a recurring meeting."
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:61
msgid ""
"Do you want to edit all occurrences of this meeting, or only the selected "
"occurrence?"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:66
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:66
msgid "All occurrences"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:67
msgid "Only this meeting"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:81
#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:99
msgid "Delete this Meeting"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:84
msgid "Delete Meeting"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:88
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:88
msgid "Topic: "
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:89
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:89
msgid "Time: "
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:94
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:94
msgid "Delete This Occurrence"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/single-meeting-item.php:95
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:95
msgid "Delete All Occurrences"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:54
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:73
msgid "Edit this Webinar"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:57
msgid "You're changing a recurring webinar."
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:61
msgid ""
"Do you want to edit all occurrences of this webinar, or only the selected "
"occurrence?"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:67
msgid "Only this webinar"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:81
#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:99
msgid "Delete this Webinar"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/single-webinar-item.php:84
msgid "Delete Webinar"
msgstr ""

#: includes/integrations/zoom/templates/buddypress/zoom/webinars.php:53
#: includes/integrations/zoom/templates/buddypress/zoom/webinars.php:54
#: includes/integrations/zoom/templates/buddypress/zoom/webinars.php:57
msgid "Search Webinars"
msgstr ""

#: includes/lib/buddyboss-updater/buddyboss-updater.php:67
msgid "Once every 4 hours"
msgstr ""

#: includes/lib/buddyboss-updater/includes/admin.php:256
msgid "BuddyBoss License Keys"
msgstr ""

#: includes/lib/buddyboss-updater/includes/admin.php:257
msgid "License Keys"
msgstr ""

#: includes/lib/buddyboss-updater/includes/admin.php:647
msgid "Dismiss this notice."
msgstr ""

#: includes/lib/buddyboss-updater/includes/admin.php:679
msgid "activate your product licenses"
msgstr ""

#: includes/lib/buddyboss-updater/includes/admin.php:680
msgid ""
"<strong>Your BuddyBoss products are almost ready.</strong> To get started, "
"please %s."
msgstr ""

#: includes/lib/buddyboss-updater/includes/admin.php:709
msgid "here"
msgstr ""

#: includes/lib/buddyboss-updater/includes/admin.php:710
msgid "This license for <strong>%1$s</strong> is invalid or incomplete. %2$s"
msgstr ""

#: includes/lib/buddyboss-updater/includes/admin.php:868
#: includes/lib/buddyboss-updater/includes/admin.php:899
#: includes/lib/buddyboss-updater/includes/admin.php:988
#: includes/lib/buddyboss-updater/includes/admin.php:994
msgid ""
"Your account was successfully connected. No new activations have been made. "
"If you are attempting to activate a new license key, please make sure that "
"the product is installed on your site, and that an active license key is "
"available in your BuddyBoss.com account."
msgstr ""

#: includes/lib/buddyboss-updater/includes/admin.php:959
msgid ""
"Congratulations! License keys for the following product(s) have been "
"activated: %s"
msgstr ""

#: includes/lib/buddyboss-updater/includes/admin.php:983
#: includes/lib/buddyboss-updater/includes/admin.php:996
msgid ""
"The connection was successful, however, no license key was activated. We "
"could not find any active license in your account, for any of the installed "
"BuddyBoss products."
msgstr ""

#: includes/lib/buddyboss-updater/includes/classes/license.php:126
#: includes/lib/buddyboss-updater/includes/classes/license.php:161
msgid ""
"We've checked the license key, but it <strong>doesn't appear to be a valid "
"BuddyBoss license.</strong> Please double check the license key and try "
"again."
msgstr ""

#: includes/lib/buddyboss-updater/includes/classes/license.php:142
msgid "We are unable to validate the license key. Service unavailable."
msgstr ""

#: includes/lib/buddyboss-updater/includes/classes/updater.php:408
msgid ""
"An Unexpected HTTP Error occurred during the API request.</p> <p><a "
"href=\"?\" onclick=\"document.location.reload(); return false;\">Try "
"again</a>"
msgstr ""

#: includes/lib/buddyboss-updater/includes/classes/updater.php:412
#: includes/lib/buddyboss-updater/includes/classes/updater.php:413
msgid "An unknown error occurred"
msgstr ""

#: includes/lib/buddyboss-updater/includes/functions.php:84
#: includes/lib/buddyboss-updater/includes/functions.php:88
msgid "Boss Theme"
msgstr ""

#: includes/lib/buddyboss-updater/includes/functions.php:101
#: includes/lib/buddyboss-updater/includes/functions.php:105
#: includes/lib/buddyboss-updater/includes/functions.php:264
#: includes/lib/buddyboss-updater/includes/functions.php:350
msgid "OneSocial Theme"
msgstr ""

#: includes/lib/buddyboss-updater/includes/functions.php:120
msgid "BB Theme & Platform Pro"
msgstr ""

#: includes/lib/buddyboss-updater/includes/functions.php:126
msgid "BuddyBoss Theme"
msgstr ""

#: includes/lib/buddyboss-updater/includes/functions.php:164
msgid "BB Platform Pro"
msgstr ""

#: includes/lib/buddyboss-updater/includes/functions.php:181
#: includes/lib/buddyboss-updater/includes/functions.php:185
msgid "BuddyBoss Media"
msgstr ""

#: includes/lib/buddyboss-updater/includes/functions.php:198
#: includes/lib/buddyboss-updater/includes/functions.php:202
msgid "BuddyBoss Wall"
msgstr ""

#: includes/lib/buddyboss-updater/includes/functions.php:213
#: includes/lib/buddyboss-updater/includes/functions.php:217
msgid "Location Autocomplete"
msgstr ""

#: includes/lib/buddyboss-updater/includes/functions.php:230
msgid "Social Learner - Learndash"
msgstr ""

#: includes/lib/buddyboss-updater/includes/functions.php:239
msgid "Boss for Learndash"
msgstr ""

#: includes/lib/buddyboss-updater/includes/functions.php:243
msgid "Boss"
msgstr ""

#: includes/lib/buddyboss-updater/includes/functions.php:256
#: includes/lib/buddyboss-updater/includes/functions.php:260
msgid "Social MarketPlace"
msgstr ""

#: includes/lib/buddyboss-updater/includes/functions.php:277
#: includes/lib/buddyboss-updater/includes/functions.php:281
msgid "BuddyPress Member Types"
msgstr ""

#: includes/lib/buddyboss-updater/includes/functions.php:325
#: includes/lib/buddyboss-updater/includes/functions.php:329
#: includes/lib/buddyboss-updater/includes/functions.php:346
msgid "BuddyPress User Blog"
msgstr ""

#: includes/lib/buddyboss-updater/includes/functions.php:342
msgid "Social Blogger"
msgstr ""

#: includes/lib/buddyboss-updater/includes/views/admin.php:8
msgid "Auto Connect (Recommended)"
msgstr ""

#: includes/lib/buddyboss-updater/includes/views/admin.php:10
msgid ""
"Click the \"Connect to BuddyBoss\" button to log into your BuddyBoss "
"account. Then click \"Allow\" to have your license automatically filled in "
"to activate your products."
msgstr ""

#: includes/lib/buddyboss-updater/includes/views/admin.php:14
msgid "Connect to BuddyBoss"
msgstr ""

#: includes/lib/buddyboss-updater/includes/views/admin.php:16
msgid "Connecting"
msgstr ""

#: includes/lib/buddyboss-updater/includes/views/admin.php:22
msgid "Manual Connect"
msgstr ""

#: includes/lib/buddyboss-updater/includes/views/admin.php:25
msgid "Log into %s"
msgstr ""

#: includes/lib/buddyboss-updater/includes/views/admin.php:28
msgid "Go to your %s"
msgstr ""

#: includes/lib/buddyboss-updater/includes/views/admin.php:31
msgid "Go to the \"Subscriptions\" tab"
msgstr ""

#: includes/lib/buddyboss-updater/includes/views/admin.php:34
msgid "Find your product's license key"
msgstr ""

#: includes/lib/buddyboss-updater/includes/views/admin.php:37
msgid "Enter your license key below"
msgstr ""

#: includes/lib/buddyboss-updater/includes/views/admin.php:40
msgid "Enter your BuddyBoss account email"
msgstr ""

#: includes/lib/buddyboss-updater/includes/views/admin.php:43
msgid "Click \"Update License\""
msgstr ""

#: includes/lib/buddyboss-updater/includes/views/admin.php:51
msgid "Benefits of a License"
msgstr ""

#: includes/lib/buddyboss-updater/includes/views/admin.php:54
msgid "Stay Up to Date"
msgstr ""

#: includes/lib/buddyboss-updater/includes/views/admin.php:55
msgid "Get the latest features right away"
msgstr ""

#: includes/lib/buddyboss-updater/includes/views/admin.php:58
msgid "Admin Notifications"
msgstr ""

#: includes/lib/buddyboss-updater/includes/views/admin.php:59
msgid "Get updates in WordPress"
msgstr ""

#: includes/lib/buddyboss-updater/includes/views/admin.php:62
msgid "Professional Support"
msgstr ""

#: includes/lib/buddyboss-updater/includes/views/admin.php:63
msgid "Get help with any questions"
msgstr ""

#: includes/lib/buddyboss-updater/includes/views/intro.php:2
msgid "How does BuddyBoss subscriptions work?"
msgstr ""

#: includes/lib/buddyboss-updater/includes/views/intro.php:3
#: includes/lib/buddyboss-updater/includes/views/intro.php:7
msgid ""
"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cras porttitor "
"placerat ipsum. Maecenas venenatis euismod urna pretium faucibus. Fusce at "
"interdum neque, vitae cursus augue. In ac dignissim mi. Quisque et nulla "
"commodo, elementum dui at, tempus magna. Nam fringilla ac ipsum non tempor."
msgstr ""

#: includes/lib/buddyboss-updater/includes/views/package.php:8
msgid "Update License"
msgstr ""

#: includes/lib/buddyboss-updater/includes/views/package.php:28
msgid "Product(s)"
msgstr ""

#: includes/lib/buddyboss-updater/includes/views/package.php:36
msgid "%s"
msgstr ""

#: includes/lib/buddyboss-updater/includes/views/package.php:45
msgid "License Key"
msgstr ""

#: includes/lib/buddyboss-updater/includes/views/package.php:49
msgid ""
"You can find the license key for your product by going to the <a "
"href=\"https://www.buddyboss.com/my-account/?tab=mysubscriptions\" "
"target=\"_blank\" rel=\"noopener\" >My Subscriptions</a> page in your "
"account area."
msgstr ""

#: includes/lib/buddyboss-updater/includes/views/package.php:68
msgid "Email"
msgstr ""

#: includes/lib/buddyboss-updater/includes/views/package.php:72
msgid "This is your account email you use to log into your BuddyBoss.com account."
msgstr ""

#: includes/polls/bb-polls-actions.php:32
msgid "Add Poll"
msgstr ""

#: includes/polls/bb-polls-actions.php:78
msgid "You must be logged in to create a poll."
msgstr ""

#: includes/polls/bb-polls-actions.php:94
msgid "You do not have permission to create polls."
msgstr ""

#: includes/polls/bb-polls-actions.php:102
msgid "Poll question is required."
msgstr ""

#: includes/polls/bb-polls-actions.php:124
#: includes/polls/bb-polls-actions.php:676
#: includes/polls/includes/class-bb-rest-poll-endpoint.php:181
#: includes/polls/includes/class-bb-rest-poll-endpoint.php:542
#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:352
#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:607
msgid "Poll option must be between 1 and 50 characters long."
msgstr ""

#: includes/polls/bb-polls-actions.php:131
#: includes/polls/includes/class-bb-rest-poll-endpoint.php:193
#: includes/polls/includes/class-bb-rest-poll-endpoint.php:554
msgid "Poll options are required."
msgstr ""

#: includes/polls/bb-polls-actions.php:133
#: includes/polls/includes/class-bb-rest-poll-endpoint.php:201
#: includes/polls/includes/class-bb-rest-poll-endpoint.php:562
msgid "Poll options must be an array."
msgstr ""

#: includes/polls/bb-polls-actions.php:135
#: includes/polls/includes/class-bb-rest-poll-endpoint.php:209
#: includes/polls/includes/class-bb-rest-poll-endpoint.php:570
msgid "Poll options must be at least 2."
msgstr ""

#: includes/polls/bb-polls-actions.php:137
#: includes/polls/includes/class-bb-rest-poll-endpoint.php:217
#: includes/polls/includes/class-bb-rest-poll-endpoint.php:578
msgid "Poll options must be at most 10."
msgstr ""

#: includes/polls/bb-polls-actions.php:139
#: includes/polls/includes/class-bb-rest-poll-endpoint.php:225
#: includes/polls/includes/class-bb-rest-poll-endpoint.php:586
msgid "Poll options must be unique."
msgstr ""

#: includes/polls/bb-polls-actions.php:146
msgid "Invalid poll duration."
msgstr ""

#: includes/polls/bb-polls-actions.php:243
msgid "Error creating poll."
msgstr ""

#: includes/polls/bb-polls-actions.php:463
#: includes/polls/bb-polls-filters.php:70
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-entry.php:65
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-view.php:74
msgid "Added by you"
msgstr ""

#: includes/polls/bb-polls-actions.php:472
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-entry.php:71
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-view.php:82
#. translators: %s: User link
msgid "Added by %s"
msgstr ""

#: includes/polls/bb-polls-actions.php:512
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-entry.php:109
msgid "Remove Option"
msgstr ""

#: includes/polls/bb-polls-actions.php:534
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-entry.php:130
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-form.php:75
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-view.php:104
msgid "Add Option"
msgstr ""

#: includes/polls/bb-polls-actions.php:537
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-entry.php:133
msgid "Submit option"
msgstr ""

#: includes/polls/bb-polls-actions.php:540
#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:367
#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:635
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-entry.php:136
msgid "This is already an option"
msgstr ""

#: includes/polls/bb-polls-actions.php:548
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-entry.php:141
msgid "See All"
msgstr ""

#: includes/polls/bb-polls-actions.php:549
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-entry.php:142
msgid "See Less"
msgstr ""

#: includes/polls/bb-polls-actions.php:559
#: includes/polls/bb-polls-actions.php:984
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-entry.php:151
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-view.php:114
#. translators: %s: Total votes
#. translators: %d: Total votes
msgid "%s vote"
msgstr ""

#: includes/polls/bb-polls-actions.php:559
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-entry.php:156
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-view.php:119
#. translators: %d: Total votes
msgid "%s votes"
msgstr ""

#: includes/polls/bb-polls-actions.php:565
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-entry.php:162
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-view.php:125
msgid "Poll Closed"
msgstr ""

#: includes/polls/bb-polls-actions.php:579
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-entry.php:173
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-view.php:136
#. translators: Days left
msgid "%sd left"
msgstr ""

#: includes/polls/bb-polls-actions.php:582
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-entry.php:178
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-view.php:141
#. translators: Hours, minutes left
#. translators: Days left
msgid "%sd %sh %sm left"
msgstr ""

#: includes/polls/bb-polls-actions.php:586
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-entry.php:183
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-view.php:146
#. translators: Hours, minutes left
msgid "%sh %sm left"
msgstr ""

#: includes/polls/bb-polls-actions.php:589
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-entry.php:188
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-view.php:151
#. translators: Minutes, seconds left
msgid "%sm left"
msgstr ""

#: includes/polls/bb-polls-actions.php:592
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-entry.php:193
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-view.php:156
#. translators: Seconds left
msgid "%ss left"
msgstr ""

#: includes/polls/bb-polls-actions.php:647
msgid "You must be logged in to create a option."
msgstr ""

#: includes/polls/bb-polls-actions.php:652
msgid "You can not add new option to this activity."
msgstr ""

#: includes/polls/bb-polls-actions.php:662
msgid "You do not have permission to create option."
msgstr ""

#: includes/polls/bb-polls-actions.php:667
msgid "You can not add new option to this poll."
msgstr ""

#: includes/polls/bb-polls-actions.php:672
msgid "Poll option is required."
msgstr ""

#: includes/polls/bb-polls-actions.php:686
msgid "The new option exceeds the allowed limit of 10 options."
msgstr ""

#: includes/polls/bb-polls-actions.php:702
#: includes/polls/bb-polls-actions.php:891
msgid "Error while saving option data."
msgstr ""

#: includes/polls/bb-polls-actions.php:750
msgid "You must be logged in to delete a option."
msgstr ""

#: includes/polls/bb-polls-actions.php:755
msgid "Activity ID is required to remove this poll option."
msgstr ""

#: includes/polls/bb-polls-actions.php:760
msgid "Poll ID is required to remove this poll option."
msgstr ""

#: includes/polls/bb-polls-actions.php:765
#: includes/polls/bb-polls-actions.php:869
#: includes/polls/bb-polls-actions.php:1073
msgid "Poll not found."
msgstr ""

#: includes/polls/bb-polls-actions.php:771
msgid "You do not have permission to remove option."
msgstr ""

#: includes/polls/bb-polls-actions.php:776
msgid "Option ID is required to remove this poll option."
msgstr ""

#: includes/polls/bb-polls-actions.php:785
msgid "Poll options empty."
msgstr ""

#: includes/polls/bb-polls-actions.php:831
msgid "Option not exists in the poll"
msgstr ""

#: includes/polls/bb-polls-actions.php:854
msgid "You must be logged in to vote."
msgstr ""

#: includes/polls/bb-polls-actions.php:859
msgid "Activity ID is required to vote this poll."
msgstr ""

#: includes/polls/bb-polls-actions.php:864
msgid "Poll ID is required to vote this poll."
msgstr ""

#: includes/polls/bb-polls-actions.php:875
msgid "You do not have permission to add vote."
msgstr ""

#: includes/polls/bb-polls-actions.php:880
msgid "Option ID is required to vote this poll."
msgstr ""

#: includes/polls/bb-polls-actions.php:937
msgid "You must be logged in to see vote state."
msgstr ""

#: includes/polls/bb-polls-actions.php:942
msgid "Activity ID is required to see vote state."
msgstr ""

#: includes/polls/bb-polls-actions.php:947
msgid "Poll ID is required to see vote state."
msgstr ""

#: includes/polls/bb-polls-actions.php:952
msgid "Option ID is required to see vote state."
msgstr ""

#: includes/polls/bb-polls-actions.php:982
#. translators: %1$s: Total votes, %2$s: Percentage
msgid "%1$s (%2$s%%)"
msgstr ""

#: includes/polls/bb-polls-actions.php:1063
msgid "You must be logged in to delete a poll."
msgstr ""

#: includes/polls/bb-polls-actions.php:1068
msgid "Poll ID is required to remove this poll."
msgstr ""

#: includes/polls/bb-polls-actions.php:1078
msgid "You do not have permission to remove vote."
msgstr ""

#: includes/polls/bb-polls-actions.php:1086
msgid "Incorrect Poll ID."
msgstr ""

#: includes/polls/bb-polls-actions.php:1097
msgid "Error while deleting poll."
msgstr ""

#: includes/polls/bb-polls-filters.php:69
msgid "Are you sure you would like to delete this poll?"
msgstr ""

#: includes/polls/bb-polls-filters.php:72
msgid "Any options you have chosen will be removed"
msgstr ""

#: includes/polls/includes/class-bb-polls.php:668
msgid "The question is required to add poll."
msgstr ""

#: includes/polls/includes/class-bb-polls.php:690
msgid "The item type is invalid."
msgstr ""

#: includes/polls/includes/class-bb-polls.php:771
msgid "There is an error while adding the poll."
msgstr ""

#: includes/polls/includes/class-bb-polls.php:892
msgid "The Poll ID is required to get poll option."
msgstr ""

#: includes/polls/includes/class-bb-polls.php:1102
msgid "The Poll id is required to update poll option."
msgstr ""

#: includes/polls/includes/class-bb-polls.php:1113
msgid "The Option title is required to update poll option."
msgstr ""

#: includes/polls/includes/class-bb-polls.php:1124
#: includes/polls/includes/class-bb-polls.php:1654
msgid "Invalid User ID."
msgstr ""

#: includes/polls/includes/class-bb-polls.php:1225
msgid "There is an error while adding the poll options."
msgstr ""

#: includes/polls/includes/class-bb-polls.php:1318
#: includes/polls/includes/class-bb-polls.php:1823
msgid "Invalid request."
msgstr ""

#: includes/polls/includes/class-bb-polls.php:1343
msgid "Unable to removing the poll options."
msgstr ""

#: includes/polls/includes/class-bb-polls.php:1425
msgid "The Poll ID is required to get poll vote."
msgstr ""

#: includes/polls/includes/class-bb-polls.php:1439
msgid "The Option ID is required to get poll vote."
msgstr ""

#: includes/polls/includes/class-bb-polls.php:1632
msgid "The Poll ID is required to update poll vote."
msgstr ""

#: includes/polls/includes/class-bb-polls.php:1643
msgid "The Option ID is required to update poll vote."
msgstr ""

#: includes/polls/includes/class-bb-polls.php:1723
msgid "There is an error while adding the poll votes."
msgstr ""

#: includes/polls/includes/class-bb-polls.php:1849
msgid "Unable to removing the poll votes."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-endpoint.php:76
#: includes/polls/includes/class-bb-rest-poll-endpoint.php:1142
#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:70
#: includes/polls/includes/class-bb-rest-poll-option-vote-endpoint.php:70
msgid "A unique numeric ID for the Poll."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-endpoint.php:140
msgid "Please do not leave the question blank."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-endpoint.php:247
#: includes/polls/includes/class-bb-rest-poll-endpoint.php:598
msgid "Could not update the poll."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-endpoint.php:320
#: includes/polls/includes/class-bb-rest-poll-endpoint.php:341
#: includes/polls/includes/class-bb-rest-poll-endpoint.php:358
msgid "Sorry, you are not allowed to create a poll."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-endpoint.php:443
#: includes/polls/includes/class-bb-rest-poll-endpoint.php:482
#: includes/polls/includes/class-bb-rest-poll-endpoint.php:705
#: includes/polls/includes/class-bb-rest-poll-endpoint.php:837
#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:191
#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:457
#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:754
#: includes/polls/includes/class-bb-rest-poll-option-vote-endpoint.php:168
#: includes/polls/includes/class-bb-rest-poll-option-vote-endpoint.php:299
#: includes/polls/includes/class-bb-rest-poll-option-vote-endpoint.php:450
msgid "Invalid poll ID."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-endpoint.php:692
#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:564
msgid "Sorry, you need to be logged in to update this poll."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-endpoint.php:713
#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:599
msgid ""
"You are not authorized to update this poll option. Only the poll creator "
"can update it."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-endpoint.php:722
#: includes/polls/includes/class-bb-rest-poll-endpoint.php:854
msgid "Activity post polls are not enabled. Please enable them to update this poll."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-endpoint.php:735
#: includes/polls/includes/class-bb-rest-poll-endpoint.php:867
msgid "You must be an admin or a moderator of the group to update this poll."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-endpoint.php:782
msgid "Could not delete the poll."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-endpoint.php:824
msgid "Sorry, you need to be logged in to delete this poll."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-endpoint.php:845
msgid ""
"You are not authorized to delete this poll. Only the poll creator can "
"delete it."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-endpoint.php:1066
msgid "Question."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-endpoint.php:1073
#: includes/polls/includes/class-bb-rest-poll-endpoint.php:1185
msgid "Allow multiple selections."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-endpoint.php:1080
#: includes/polls/includes/class-bb-rest-poll-endpoint.php:1190
msgid "Allow users to add options."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-endpoint.php:1087
#: includes/polls/includes/class-bb-rest-poll-endpoint.php:1195
msgid "Duration of the poll."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-endpoint.php:1096
msgid "Poll options."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-endpoint.php:1108
#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:998
#: includes/polls/includes/class-bb-rest-poll-option-vote-endpoint.php:679
#: includes/polls/includes/class-bb-rest-poll-option-vote-endpoint.php:719
msgid "A unique numeric ID for the poll."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-endpoint.php:1147
msgid "Item ID."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-endpoint.php:1153
msgid "Item Type."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-endpoint.php:1158
msgid "Secondary Item ID."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-endpoint.php:1163
msgid "User ID who created poll."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-endpoint.php:1168
msgid "The question for the poll."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-endpoint.php:1174
msgid "Settings for the poll."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-endpoint.php:1202
msgid "The options for the poll."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-endpoint.php:1211
#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:1070
#: includes/polls/includes/class-bb-rest-poll-option-vote-endpoint.php:786
msgid "The date the option was recorded."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-endpoint.php:1218
msgid "The date the option was updated."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-endpoint.php:1225
msgid "Vote disabled date for poll."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-endpoint.php:1232
msgid "The status of the poll."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-endpoint.php:1239
msgid "Total votes for poll."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-endpoint.php:1246
msgid "Current user's permission with the media."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-endpoint.php:1309
msgid "Activity poll id."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:74
#: includes/polls/includes/class-bb-rest-poll-option-vote-endpoint.php:75
msgid "A unique numeric ID for the Poll option."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:131
#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:304
#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:583
msgid "Invalid poll."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:235
msgid "Invalid poll option title."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:290
msgid "Sorry, you are not allowed to create an option."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:314
msgid "Adding new options is not allowed for this poll."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:323
#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:591
#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:774
#: includes/polls/includes/class-bb-rest-poll-option-vote-endpoint.php:319
#: includes/polls/includes/class-bb-rest-poll-option-vote-endpoint.php:469
msgid "Activity post polls are not enabled."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:341
msgid "You must be an admin, moderator, or member of the group to add new options."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:467
#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:619
#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:766
#: includes/polls/includes/class-bb-rest-poll-option-vote-endpoint.php:183
#: includes/polls/includes/class-bb-rest-poll-option-vote-endpoint.php:484
msgid "Invalid poll option ID."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:524
msgid "Could not update the poll option."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:627
msgid "You can not update this poll option."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:643
msgid "You cannot update this poll option because it has votes."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:699
msgid "Could not delete the poll option."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:741
msgid "Sorry, you need to be logged in to delete this poll option."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:785
msgid ""
"You are not authorized to delete this poll option. Only the option creator "
"can delete it."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:914
#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:925
msgid "Poll ID."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:932
msgid "Option ID."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:1004
#: includes/polls/includes/class-bb-rest-poll-option-vote-endpoint.php:725
msgid "Order sort attribute ascending or descending."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:1013
#: includes/polls/includes/class-bb-rest-poll-option-vote-endpoint.php:734
msgid "Order by a specific parameter."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:1043
msgid "Unique identifier for the option."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:1049
msgid "ID of the poll this option belongs to."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:1055
#: includes/polls/includes/class-bb-rest-poll-option-vote-endpoint.php:781
msgid "ID of the user who created the option."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:1060
msgid "The title of the option."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:1065
msgid "Order of the option."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:1077
msgid "The date the option was last updated."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:1084
msgid "The total votes for poll option."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:1091
msgid "Option user data."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:1102
msgid "Poll option username."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:1107
msgid "Poll option user link."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:1114
msgid "Whether to check user voted the option or not."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-endpoint.php:1120
msgid "User's vote id."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-vote-endpoint.php:80
msgid "A unique numeric ID for the vote."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-vote-endpoint.php:236
msgid "Could not add the vote."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-vote-endpoint.php:281
msgid "Sorry, you are not allowed to add a poll vote."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-vote-endpoint.php:310
msgid "Sorry, Poll has been closed. you are not allowed to add vote."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-vote-endpoint.php:337
msgid "Sorry, you are not allowed to add vote."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-vote-endpoint.php:395
msgid "Could not delete the poll option vote."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-vote-endpoint.php:437
msgid "Sorry, you need to be logged in to delete this vote."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-vote-endpoint.php:461
msgid "Sorry, Poll has been closed. you are not allowed to delete this vote."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-vote-endpoint.php:495
msgid "Invalid poll option vote ID."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-vote-endpoint.php:503
msgid "Sorry, you are not allowed to delete this vote."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-vote-endpoint.php:686
msgid "ID of the user who created the vote."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-vote-endpoint.php:764
msgid "Unique identifier for the option vote."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-vote-endpoint.php:770
msgid "ID of the poll."
msgstr ""

#: includes/polls/includes/class-bb-rest-poll-option-vote-endpoint.php:776
msgid "ID of the poll option."
msgstr ""

#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-form.php:26
msgid "Edit poll"
msgstr ""

#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-form.php:28
msgid "Add poll"
msgstr ""

#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-form.php:41
msgid "Ask a Question"
msgstr ""

#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-form.php:43
msgid "Enter your question..."
msgstr ""

#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-form.php:60
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-form.php:66
#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-form.php:69
msgid "Option"
msgstr ""

#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-form.php:85
msgid "Allow user to choose multiple answers"
msgstr ""

#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-form.php:96
msgid "Allow user to add new options"
msgstr ""

#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-form.php:101
msgid "Poll Duration"
msgstr ""

#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-form.php:104
msgid "1 Day"
msgstr ""

#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-form.php:116
msgid "3 Days"
msgstr ""

#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-form.php:117
msgid "1 Week"
msgstr ""

#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-form.php:118
msgid "2 Weeks"
msgstr ""

#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-view.php:21
msgid "More Options"
msgstr ""

#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-view.php:40
msgid "Edit Poll"
msgstr ""

#: includes/polls/templates/buddypress/common/js-templates/activity/parts/bb-activity-poll-view.php:45
msgid "Delete Poll"
msgstr ""

#: includes/reactions/bb-reactions-actions.php:172
#: includes/reactions/bb-reactions-actions.php:383
msgid "Unable to submit this form, please refresh and try again."
msgstr ""

#: includes/reactions/bb-reactions-actions.php:181
#: includes/reactions/bb-reactions-actions.php:252
#: includes/reactions/bb-reactions-actions.php:392
msgid "This action can not be proceed while the conversion is in progress."
msgstr ""

#: includes/reactions/bb-reactions-actions.php:218
#: includes/reactions/bb-reactions-actions.php:485
#: includes/reactions/bb-reactions-functions.php:206
msgid "Start conversion"
msgstr ""

#: includes/reactions/bb-reactions-actions.php:243
#: includes/reactions/bb-reactions-actions.php:512
msgid "Unable to processed this request, please refresh and try again."
msgstr ""

#: includes/reactions/bb-reactions-actions.php:263
msgid "Emotion ID is required."
msgstr ""

#: includes/reactions/bb-reactions-actions.php:284
msgid "Provided emotion ID is not correct. please refresh and try again."
msgstr ""

#: includes/reactions/bb-reactions-actions.php:300
#. translators: 1: Emotion name, 2: Emotion count.
msgid ""
"You are about to delete the %1$s Emotion, including all %2$s instances of "
"members using this emotion as a reaction."
msgstr ""

#: includes/reactions/bb-reactions-actions.php:308
msgid "If you want to retain this data, you can:"
msgstr ""

#: includes/reactions/bb-reactions-actions.php:313
msgid "Edit or deactivate this Emotion instead of deleting it"
msgstr ""

#: includes/reactions/bb-reactions-actions.php:314
msgid ""
"Use the migration wizard to convert this Emotion’s data to a different "
"Emotion before deleting"
msgstr ""

#: includes/reactions/bb-reactions-actions.php:319
msgid "Otherwise, click the button below to proceed with deleting."
msgstr ""

#: includes/reactions/bb-reactions-actions.php:325
#: includes/reactions/bb-reactions-actions.php:345
msgid "This action %s be undone."
msgstr ""

#: includes/reactions/bb-reactions-actions.php:328
#: includes/reactions/bb-reactions-actions.php:348
msgid "cannot"
msgstr ""

#: includes/reactions/bb-reactions-actions.php:337
#. translators: 1: Emotion name.
msgid "Are you sure you want to delete the %s Emotion?"
msgstr ""

#: includes/reactions/bb-reactions-actions.php:362
#: includes/topics/templates/buddypress/groups/single/admin/activity-topics-delete-form.php:62
#: includes/topics/templates/buddypress/groups/single/admin/activity-topics-form.php:96
#: includes/topics/templates/buddypress/groups/single/admin/activity-topics.php:154
#: includes/topics/templates/buddypress/groups/single/admin/activity-topics.php:209
msgid "Confirm"
msgstr ""

#: includes/reactions/bb-reactions-actions.php:402
msgid "Unable to find any existing data for migration."
msgstr ""

#: includes/reactions/bb-reactions-actions.php:409
msgid "Convert Likes"
msgstr ""

#: includes/reactions/bb-reactions-actions.php:415
msgid "Convert Reactions"
msgstr ""

#: includes/reactions/bb-reactions-actions.php:540
msgid "Unable dismiss notice, please refresh and try again."
msgstr ""

#: includes/reactions/bb-reactions-actions.php:563
msgid "Unable to stop migration, please refresh and try again."
msgstr ""

#: includes/reactions/bb-reactions-actions.php:616
msgid ""
"Reactions are currently being migrated. Once complete, the new reactions "
"will be visible on your site."
msgstr ""

#: includes/reactions/bb-reactions-filters.php:42
msgid "Notice"
msgstr ""

#: includes/reactions/bb-reactions-filters.php:58
#: includes/reactions/bb-reactions-functions.php:471
msgid "Emotions"
msgstr ""

#: includes/reactions/bb-reactions-filters.php:83
msgid ""
"When switching reactions mode, use our %s to map existing reactions to the "
"new options."
msgstr ""

#: includes/reactions/bb-reactions-filters.php:85
msgid "migration wizard"
msgstr ""

#: includes/reactions/bb-reactions-functions.php:103
msgid "%1$s were successfully converted to the %2$s emotion."
msgstr ""

#: includes/reactions/bb-reactions-functions.php:115
#: includes/reactions/bb-reactions-functions.php:124
msgid "%1$s <strong>reactions</strong> were successfully converted %2$s."
msgstr ""

#: includes/reactions/bb-reactions-functions.php:159
msgid "%1$s out of %2$s %3$s reactions have been converted"
msgstr ""

#: includes/reactions/bb-reactions-functions.php:164
msgid ""
"This action is being performed in the background, but may take some time "
"based on the amount of data."
msgstr ""

#: includes/reactions/bb-reactions-functions.php:165
msgid "Recheck status"
msgstr ""

#: includes/reactions/bb-reactions-functions.php:166
msgid "Stop"
msgstr ""

#: includes/reactions/bb-reactions-functions.php:179
msgid "Do you want to convert your existing Likes to an Emotion?"
msgstr ""

#: includes/reactions/bb-reactions-functions.php:181
msgid ""
"You have %s Likes previously submitted on your site which can be converted "
"to an Emotion."
msgstr ""

#: includes/reactions/bb-reactions-functions.php:188
msgid "Do you want to convert your existing reactions to Likes?"
msgstr ""

#: includes/reactions/bb-reactions-functions.php:190
msgid ""
"You have %s reactions previously submitted on your site which can be "
"converted to Likes."
msgstr ""

#: includes/reactions/bb-reactions-functions.php:207
msgid "Do later"
msgstr ""

#: includes/reactions/bb-reactions-functions.php:238
msgid "Emojis"
msgstr ""

#: includes/reactions/bb-reactions-functions.php:242
#: includes/reactions/templates/admin/emotion-picker/render-emotion-icons.php:26
msgid "Icons"
msgstr ""

#: includes/reactions/bb-reactions-functions.php:391
msgid "You have no reactions to convert"
msgstr ""

#: includes/reactions/bb-reactions-functions.php:397
msgid "Unable to find any existing reactions to convert to an Emotion."
msgstr ""

#: includes/reactions/bb-reactions-functions.php:402
msgid "Unable to find any existing reactions to convert to Likes."
msgstr ""

#: includes/reactions/bb-reactions-functions.php:453
msgid ""
"This action will convert reactions previously submitted by members on your "
"site to an Emotion of your choice. Reactions not selected can be converted "
"at any point in the future using this migration wizard."
msgstr ""

#: includes/reactions/bb-reactions-functions.php:454
msgid "Which reactions do you want to convert?"
msgstr ""

#: includes/reactions/bb-reactions-functions.php:462
#: includes/reactions/bb-reactions-functions.php:639
#: includes/reactions/bb-reactions-functions.php:665
#: includes/reactions/bb-reactions-functions.php:699
#: includes/reactions/includes/class-bb-reactions.php:353
#: includes/reactions/includes/class-bb-reactions.php:355
msgid "Likes"
msgstr ""

#: includes/reactions/bb-reactions-functions.php:491
msgid "Which Emotion would you like to convert your reactions to?"
msgstr ""

#: includes/reactions/bb-reactions-functions.php:513
#: includes/reactions/bb-reactions-functions.php:695
#. translators: 1: Emotion name with count, 2: Convert to emotion name.
msgid "You are about to convert %1$s to the %2$s Emotion"
msgstr ""

#: includes/reactions/bb-reactions-functions.php:516
#: includes/reactions/bb-reactions-functions.php:572
#: includes/reactions/bb-reactions-functions.php:635
#: includes/reactions/includes/class-bb-reactions.php:352
msgid "reactions"
msgstr ""

#: includes/reactions/bb-reactions-functions.php:523
#: includes/reactions/bb-reactions-functions.php:706
msgid ""
"The new reactions will be immediately visible on your site after being "
"converted"
msgstr ""

#: includes/reactions/bb-reactions-functions.php:524
#: includes/reactions/bb-reactions-functions.php:580
#: includes/reactions/bb-reactions-functions.php:646
#: includes/reactions/bb-reactions-functions.php:707
msgid "Depending on the amount of data to convert, the migration may take a while"
msgstr ""

#: includes/reactions/bb-reactions-functions.php:525
#: includes/reactions/bb-reactions-functions.php:581
#: includes/reactions/bb-reactions-functions.php:647
#: includes/reactions/bb-reactions-functions.php:708
msgid "You will be unable to edit reactions while the conversion is in progress"
msgstr ""

#: includes/reactions/bb-reactions-functions.php:526
msgid ""
"This action cannot be undone, but you can convert reactions to another "
"reaction in the future"
msgstr ""

#: includes/reactions/bb-reactions-functions.php:527
#: includes/reactions/bb-reactions-functions.php:583
#: includes/reactions/bb-reactions-functions.php:649
#: includes/reactions/bb-reactions-functions.php:710
msgid ""
"We recommend backing up your site before migrating and performing this "
"action during an off-peak period"
msgstr ""

#: includes/reactions/bb-reactions-functions.php:529
#: includes/reactions/bb-reactions-functions.php:585
#: includes/reactions/bb-reactions-functions.php:651
#: includes/reactions/bb-reactions-functions.php:712
msgid "Do you want to start the conversion now?"
msgstr ""

#: includes/reactions/bb-reactions-functions.php:536
msgid ""
"This action will convert reactions previously submitted by members on your "
"site to Likes. Reactions not selected can be converted at any point in the "
"future using this migration wizard."
msgstr ""

#: includes/reactions/bb-reactions-functions.php:537
#: includes/reactions/bb-reactions-functions.php:600
msgid "Which reactions do you want to convert to Likes?"
msgstr ""

#: includes/reactions/bb-reactions-functions.php:542
#: includes/reactions/bb-reactions-functions.php:605
msgid "All emotions"
msgstr ""

#: includes/reactions/bb-reactions-functions.php:569
msgid "You are about to convert %1$s to the %2$s Likes"
msgstr ""

#: includes/reactions/bb-reactions-functions.php:579
#: includes/reactions/bb-reactions-functions.php:645
msgid "The new Likes will be immediately visible on your site after being converted"
msgstr ""

#: includes/reactions/bb-reactions-functions.php:582
#: includes/reactions/bb-reactions-functions.php:648
msgid ""
"This action cannot be undone, but you can convert Likes back to other "
"reactions in the future"
msgstr ""

#: includes/reactions/bb-reactions-functions.php:599
msgid ""
"This action will convert reactions previously submitted by members on your "
"site to Likes. Reactions not selected can be converted at any point in the "
"future using the migration wizard."
msgstr ""

#: includes/reactions/bb-reactions-functions.php:632
msgid "You are about to convert %1$s to %2$s"
msgstr ""

#: includes/reactions/bb-reactions-functions.php:661
#. translators: Emotion name with count.
msgid ""
"This action will convert the %s previously submitted by members on your "
"site to an Emotion of your choice. You can preform this action at any point "
"in the future using this migration wizard."
msgstr ""

#: includes/reactions/bb-reactions-functions.php:671
msgid "Which Emotion would you like to convert your Likes to?"
msgstr ""

#: includes/reactions/bb-reactions-functions.php:673
msgid "Select an Emotion"
msgstr ""

#: includes/reactions/bb-reactions-functions.php:709
msgid ""
"This action cannot be undone, but you can convert reactions back to to "
"Likes in the future"
msgstr ""

#: includes/reactions/includes/class-bb-reactions-picker.php:91
msgid ""
"Unsupported image format. You must provide an image in an accepted format "
"(PNG)."
msgstr ""

#: includes/reactions/includes/class-bb-reactions-picker.php:92
msgid "Sorry - you're not allowed to upload icon bigger than 400KB."
msgstr ""

#: includes/reactions/includes/class-bb-reactions-picker.php:93
msgid "No data found."
msgstr ""

#: includes/reactions/includes/class-bb-reactions-picker.php:94
msgid "Emotion label is required."
msgstr ""

#: includes/reactions/includes/class-bb-reactions-picker.php:95
msgid "Emotion label characters should be within limit."
msgstr ""

#: includes/reactions/includes/class-bb-reactions-picker.php:96
msgid "Error while uploading, please try again."
msgstr ""

#: includes/reactions/includes/class-bb-reactions-picker.php:123
msgid "You don't have permission to upload a custom icon"
msgstr ""

#: includes/reactions/includes/class-bb-reactions-picker.php:131
msgid "Icon not found on file object."
msgstr ""

#: includes/reactions/includes/class-bb-reactions-picker.php:138
#: includes/reactions/includes/class-bb-reactions-picker.php:145
msgid "Icon you provided is invalid."
msgstr ""

#: includes/reactions/includes/class-bb-reactions-picker.php:152
msgid "Please provide an icon with correct size."
msgstr ""

#: includes/reactions/includes/class-bb-reactions-picker.php:188
msgid "You don't have permission to delete a custom icon"
msgstr ""

#: includes/reactions/includes/class-bb-reactions-picker.php:196
#: includes/reactions/includes/class-bb-reactions-picker.php:203
msgid "Please choose custom icon to delete."
msgstr ""

#: includes/reactions/includes/class-bb-reactions-picker.php:227
msgid ""
"The icon is already assigned to another emotion. Please update first and "
"then delete."
msgstr ""

#: includes/reactions/includes/class-bb-reactions-picker.php:232
msgid "Icon deleted."
msgstr ""

#: includes/reactions/includes/class-bb-reactions-picker.php:234
msgid "There is no icon for deleting."
msgstr ""

#: includes/reactions/includes/class-bb-reactions-picker.php:236
msgid "There was a problem deleting the icon."
msgstr ""

#: includes/reactions/includes/class-bb-reactions-picker.php:239
msgid "Security check failed."
msgstr ""

#: includes/reactions/includes/class-bb-reactions-picker.php:362
#: includes/reactions/includes/class-bb-reactions-picker.php:454
msgid "All Categories"
msgstr ""

#: includes/reactions/includes/class-bb-reactions.php:98
#: includes/reactions/includes/class-bb-reactions.php:406
msgid "Migration wizard"
msgstr ""

#: includes/reactions/includes/class-bb-reactions.php:280
#. translators: Emotion names with comma separator.
msgid "The %s Emotion was successfully deleted."
msgstr ""

#: includes/reactions/includes/class-bb-reactions.php:427
msgid "Delete Emotion"
msgstr ""

#: includes/reactions/templates/admin/emotion-options.php:33
#: includes/reactions/templates/admin/emotion-picker/render-emotion-icons.php:101
msgid "Remove Emotion"
msgstr ""

#: includes/reactions/templates/admin/emotion-options.php:72
#: includes/reactions/templates/admin/emotion-picker/render-emotion-icons.php:141
msgid "Edit Emotion"
msgstr ""

#: includes/reactions/templates/admin/emotion-options.php:87
#: includes/reactions/templates/admin/emotion-picker/render-emotion-icons.php:89
msgid "Add New Emotion"
msgstr ""

#: includes/reactions/templates/admin/emotion-options.php:87
#: includes/reactions/templates/admin/emotion-picker/bb-custom.php:18
#: includes/reactions/templates/admin/emotion-picker/render-emotion-icons.php:89
msgid "Add new"
msgstr ""

#: includes/reactions/templates/admin/emotion-options.php:87
#: includes/reactions/templates/admin/emotion-picker/render-emotion-icons.php:89
msgid "up"
msgstr ""

#: includes/reactions/templates/admin/emotion-picker/bb-custom-settings.php:17
msgid "Select a custom icon to configure its appearance."
msgstr ""

#: includes/reactions/templates/admin/emotion-picker/bb-custom-settings.php:41
msgid "Delete Permanently"
msgstr ""

#: includes/reactions/templates/admin/emotion-picker/bb-custom-settings.php:56
#: includes/reactions/templates/admin/emotion-picker/bb-emojis-settings.php:64
#: includes/reactions/templates/admin/emotion-picker/bb-icons-settings.php:52
msgid "Label"
msgstr ""

#: includes/reactions/templates/admin/emotion-picker/bb-custom-settings.php:63
#: includes/reactions/templates/admin/emotion-picker/bb-emojis-settings.php:71
#: includes/reactions/templates/admin/emotion-picker/bb-icons-settings.php:59
msgid "Notification Text"
msgstr ""

#: includes/reactions/templates/admin/emotion-picker/bb-custom-settings.php:65
#: includes/reactions/templates/admin/emotion-picker/bb-emojis-settings.php:73
msgid "reacted to"
msgstr ""

#: includes/reactions/templates/admin/emotion-picker/bb-custom-settings.php:69
#: includes/reactions/templates/admin/emotion-picker/bb-emojis-settings.php:77
#: includes/reactions/templates/admin/emotion-picker/bb-icons-settings.php:65
msgid "Text Color"
msgstr ""

#: includes/reactions/templates/admin/emotion-picker/bb-custom.php:17
msgid "Uploaded"
msgstr ""

#: includes/reactions/templates/admin/emotion-picker/bb-custom.php:38
msgid "Add file to upload"
msgstr ""

#: includes/reactions/templates/admin/emotion-picker/bb-custom.php:43
msgid "Crop & Adjust"
msgstr ""

#: includes/reactions/templates/admin/emotion-picker/bb-custom.php:52
#: includes/reactions/templates/admin/emotion-picker/bb-custom.php:54
msgid "Upload Icon"
msgstr ""

#: includes/reactions/templates/admin/emotion-picker/bb-custom.php:57
msgid "For best results upload an image greater than 200px in size."
msgstr ""

#: includes/reactions/templates/admin/emotion-picker/bb-emojis-settings.php:17
msgid "Select an emotion to configure its appearance."
msgstr ""

#: includes/reactions/templates/admin/emotion-picker/bb-icons-settings.php:18
msgid "Select an icon to configure its appearance."
msgstr ""

#: includes/reactions/templates/admin/emotion-picker/bb-icons-settings.php:75
msgid "Icon Color"
msgstr ""

#: includes/reactions/templates/admin/emotion-picker/render-emotion-icons.php:27
msgid "Emotion Editor"
msgstr ""

#: includes/reactions/templates/admin/emotion-picker/render-emotion-icons.php:50
msgid "Search Icons"
msgstr ""

#: includes/reactions/templates/admin/emotion-picker/render-emotion-icons.php:77
msgid "Save Emotion"
msgstr ""

#: includes/reactions/templates/admin/emotion-picker/render-emotion-icons.php:78
msgid "Select Icon"
msgstr ""

#: includes/reactions/templates/admin/emotion-picker/render-emotion-icons.php:167
msgid "No, matching results found, Try again"
msgstr ""

#: includes/schedule-posts/bb-schedule-posts-filters.php:85
msgid "Schedule"
msgstr ""

#: includes/schedule-posts/bb-schedule-posts-filters.php:86
msgid "Are you sure you want to delete that permanently?"
msgstr ""

#: includes/schedule-posts/bb-schedule-posts-filters.php:87
msgid "Schedule Outdated"
msgstr ""

#: includes/schedule-posts/bb-schedule-posts-filters.php:88
msgid "Scheduled Post Deleted"
msgstr ""

#: includes/schedule-posts/bb-schedule-posts-filters.php:89
msgid "Your scheduled post has been deleted."
msgstr ""

#: includes/schedule-posts/bb-schedule-posts-filters.php:90
msgid "Successfully Scheduled Post"
msgstr ""

#: includes/schedule-posts/bb-schedule-posts-filters.php:91
msgid "Your post has been scheduled."
msgstr ""

#: includes/schedule-posts/bb-schedule-posts-filters.php:92
msgid "Successfully Updated Post"
msgstr ""

#: includes/schedule-posts/bb-schedule-posts-filters.php:93
msgid "Your post schedule has been updated."
msgstr ""

#: includes/schedule-posts/bb-schedule-posts-filters.php:94
msgid "View now"
msgstr ""

#: includes/schedule-posts/bb-schedule-posts-filters.php:95
msgid "View all posts"
msgstr ""

#: includes/schedule-posts/bb-schedule-posts-filters.php:97
msgid "Unable to schedule post as you are not the owner or moderator of this group"
msgstr ""

#: includes/schedule-posts/templates/buddypress/activity-schedule/entry.php:83
msgid "Schedule for:"
msgstr ""

#: includes/schedule-posts/templates/buddypress/common/js-templates/activity/parts/bb-activity-schedule-details.php:19
msgid "Posting:"
msgstr ""

#: includes/schedule-posts/templates/buddypress/common/js-templates/activity/parts/bb-activity-schedule-details.php:19
#: includes/schedule-posts/templates/buddypress/common/js-templates/activity/parts/bb-activity-schedule-post.php:56
msgid "at"
msgstr ""

#: includes/schedule-posts/templates/buddypress/common/js-templates/activity/parts/bb-activity-schedule-post.php:27
msgid "Schedule Post"
msgstr ""

#: includes/schedule-posts/templates/buddypress/common/js-templates/activity/parts/bb-activity-schedule-post.php:31
msgid "View Scheduled Posts"
msgstr ""

#: includes/schedule-posts/templates/buddypress/common/js-templates/activity/parts/bb-activity-schedule-post.php:45
msgid "Schedule post"
msgstr ""

#: includes/schedule-posts/templates/buddypress/common/js-templates/activity/parts/bb-activity-schedule-post.php:66
msgid "Time"
msgstr ""

#: includes/schedule-posts/templates/buddypress/common/js-templates/activity/parts/bb-activity-schedule-post.php:85
msgid "View all scheduled posts"
msgstr ""

#: includes/schedule-posts/templates/buddypress/common/js-templates/activity/parts/bb-activity-schedule-post.php:94
msgid "Clear Schedule"
msgstr ""

#: includes/schedule-posts/templates/buddypress/common/js-templates/activity/parts/bb-activity-schedule-post.php:98
msgid "Back"
msgstr ""

#: includes/schedule-posts/templates/buddypress/common/js-templates/activity/parts/bb-activity-schedule-post.php:117
msgid "Scheduled posts"
msgstr ""

#: includes/schedule-posts/templates/buddypress/common/js-templates/activity/parts/bb-activity-schedule-post.php:129
msgid "No Scheduled Posts Found"
msgstr ""

#: includes/schedule-posts/templates/buddypress/common/js-templates/activity/parts/bb-activity-schedule-post.php:130
msgid "You do not have any posts scheduled at the moment."
msgstr ""

#: includes/sso/admin/class-bb-social-login-admin.php:57
#: includes/sso/admin/class-bb-social-login-admin.php:94
msgid "Invalid provider."
msgstr ""

#: includes/sso/admin/class-bb-social-login-admin.php:86
msgid "Settings saved "
msgstr ""

#: includes/sso/admin/class-bb-social-login-admin.php:91
msgid "Error saving provider settings."
msgstr ""

#: includes/sso/admin/class-bb-social-login-admin.php:140
msgid "View is empty."
msgstr ""

#: includes/sso/admin/class-bb-social-login-admin.php:148
msgid "Providers are not valid."
msgstr ""

#: includes/sso/admin/class-bb-social-login-admin.php:163
msgid "Saved Successfully."
msgstr ""

#: includes/sso/admin/class-bb-social-login-admin.php:169
msgid "Error saving providers order."
msgstr ""

#: includes/sso/admin/class-bb-social-login-admin.php:176
msgid "Error saving order."
msgstr ""

#: includes/sso/admin/sso-fields.php:13
msgid "Google"
msgstr ""

#: includes/sso/admin/sso-fields.php:16
msgid "Web"
msgstr ""

#: includes/sso/admin/sso-fields.php:32 includes/sso/admin/sso-fields.php:217
#: includes/sso/admin/sso-fields.php:270 includes/sso/admin/sso-fields.php:328
#: includes/sso/admin/sso-fields.php:380 includes/sso/admin/sso-fields.php:444
msgid "Callback URI / Redirect URL"
msgstr ""

#: includes/sso/admin/sso-fields.php:42 includes/sso/admin/sso-fields.php:102
#: includes/sso/admin/sso-fields.php:154 includes/sso/admin/sso-fields.php:227
#: includes/sso/admin/sso-fields.php:281 includes/sso/admin/sso-fields.php:339
#: includes/sso/admin/sso-fields.php:391 includes/sso/admin/sso-fields.php:455
#. translators: %1$s: URL, %2$s: Click here, %3$s: URL, %4$s: View Tutorial
#. translators: %1$s: URL, %2$s: Click here, %3$s: URL, %4$s: View Tutorial.
#. translators: %1$s: URL, %2$s: Click here, %3$s: URL, %4$s: View Tutorial.
msgid ""
"To create an app, <a href=\"%1$s\" target=\"_blank\">%2$s</a>.<br />To "
"learn how to create an app, <a href=\"%3$s\" target=\"_blank\">%4$s</a>"
msgstr ""

#: includes/sso/admin/sso-fields.php:44 includes/sso/admin/sso-fields.php:104
#: includes/sso/admin/sso-fields.php:156 includes/sso/admin/sso-fields.php:229
#: includes/sso/admin/sso-fields.php:283 includes/sso/admin/sso-fields.php:341
#: includes/sso/admin/sso-fields.php:393 includes/sso/admin/sso-fields.php:457
msgid "Click here"
msgstr ""

#: includes/sso/admin/sso-fields.php:71
msgid "Android App"
msgstr ""

#: includes/sso/admin/sso-fields.php:79 includes/sso/admin/sso-fields.php:131
msgid "For Release App"
msgstr ""

#: includes/sso/admin/sso-fields.php:88 includes/sso/admin/sso-fields.php:140
msgid "Test Build Client ID"
msgstr ""

#: includes/sso/admin/sso-fields.php:91 includes/sso/admin/sso-fields.php:143
msgid "For Test App"
msgstr ""

#: includes/sso/admin/sso-fields.php:123
msgid "iOS App"
msgstr ""

#: includes/sso/admin/sso-fields.php:176
msgid "Microsoft"
msgstr ""

#: includes/sso/admin/sso-fields.php:182
msgid "Application (client) ID"
msgstr ""

#: includes/sso/admin/sso-fields.php:194
msgid "Audience"
msgstr ""

#: includes/sso/admin/sso-fields.php:198
msgid ""
"Accounts in any organizational directory (Any Azure AD directory - "
"Multitenant)"
msgstr ""

#: includes/sso/admin/sso-fields.php:199
msgid ""
"Accounts in any organizational directory (Any Azure AD directory - "
"Multitenant) and personal Microsoft accounts (e.g. Skype, Xbox)"
msgstr ""

#: includes/sso/admin/sso-fields.php:200
msgid "Personal Microsoft accounts only"
msgstr ""

#: includes/sso/admin/sso-fields.php:201
msgid "Only users in an organizational directory from a particular Azure AD tenant:"
msgstr ""

#: includes/sso/admin/sso-fields.php:206
msgid "Authorization Prompt"
msgstr ""

#: includes/sso/admin/sso-fields.php:210
msgid "Display account select modal"
msgstr ""

#: includes/sso/admin/sso-fields.php:211
msgid "Force user to enter login credentials on each login"
msgstr ""

#: includes/sso/admin/sso-fields.php:212
msgid "Display authorization and authentication dialog only when necessary"
msgstr ""

#: includes/sso/admin/sso-fields.php:251
msgid "Facebook"
msgstr ""

#: includes/sso/admin/sso-fields.php:258
msgid "App ID"
msgstr ""

#: includes/sso/admin/sso-fields.php:264
msgid "App Secret"
msgstr ""

#: includes/sso/admin/sso-fields.php:305
msgid "X"
msgstr ""

#: includes/sso/admin/sso-fields.php:312
msgid "Client ID (V2)"
msgstr ""

#: includes/sso/admin/sso-fields.php:320
msgid "Client Secret (V2)"
msgstr ""

#: includes/sso/admin/sso-fields.php:361
msgid "LinkedIn"
msgstr ""

#: includes/sso/admin/sso-fields.php:413
msgid "Apple"
msgstr ""

#: includes/sso/admin/sso-fields.php:420
msgid "Private Key ID"
msgstr ""

#: includes/sso/admin/sso-fields.php:426
msgid "Private Key"
msgstr ""

#: includes/sso/admin/sso-fields.php:432
msgid "Team Identifier"
msgstr ""

#: includes/sso/admin/sso-fields.php:438
msgid "Service Identifier"
msgstr ""

#: includes/sso/admin/templates/bb-sso-fields-html.php:99
msgid "Verify Settings"
msgstr ""

#: includes/sso/admin/templates/bb-sso-fields-html.php:103
msgid "Verify Settings Again"
msgstr ""

#: includes/sso/admin/templates/bb-sso-fields-html.php:120
msgid "Save Changes"
msgstr ""

#: includes/sso/bb-sso-actions.php:49
msgid "Pull Additional Data from Social Account"
msgstr ""

#: includes/sso/bb-sso-actions.php:80
msgid "Registration Option"
msgstr ""

#: includes/sso/bb-sso-actions.php:122
msgid "Update the keys"
msgstr ""

#: includes/sso/bb-sso-actions.php:153
msgid "Edit Social Icon"
msgstr ""

#: includes/sso/bb-sso-actions.php:173
msgid "Any change will require new iOS and Android app builds."
msgstr ""

#: includes/sso/bb-sso-actions.php:192
msgid "Name"
msgstr ""

#: includes/sso/bb-sso-actions.php:210
#: includes/sso/includes/class-bb-sso-provider.php:1620
msgid "Profile Picture"
msgstr ""

#: includes/sso/bb-sso-actions.php:224
msgid ""
"Deselect the data options you do not want your users to sync when "
"registering using a social login option. These data options can not be "
"disabled for 'X'."
msgstr ""

#: includes/sso/bb-sso-actions.php:239
msgid "Enable"
msgstr ""

#: includes/sso/bb-sso-actions.php:242
msgid "Use both Wordpress and social login."
msgstr ""

#: includes/sso/bb-sso-actions.php:249
msgid "Disable"
msgstr ""

#: includes/sso/bb-sso-actions.php:252
msgid ""
"Does not allow registration but allows sign in from those who already have "
"an account."
msgstr ""

#: includes/sso/includes/class-bb-sso-oauth2.php:116
#: includes/sso/includes/class-bb-sso-oauth2.php:404
#: includes/sso/includes/class-bb-sso-oauth2.php:500
#: includes/sso/providers/apple/class-bb-social-provider-apple-client.php:195
#: includes/sso/providers/facebook/class-bb-social-provider-facebook-client.php:149
#: includes/sso/providers/twitter/class-bb-social-provider-twitter-client.php:194
#. translators: %s: The response body.
#. translators: %s: response body
#. translators: %s: The response body.
#. translators: %s: The response body.
msgid "Unexpected response: %s"
msgstr ""

#: includes/sso/includes/class-bb-sso-oauth2.php:170
msgid "No additional details available"
msgstr ""

#: includes/sso/includes/class-bb-sso-provider-dummy.php:276
msgid "Connect"
msgstr ""

#: includes/sso/includes/class-bb-sso-provider-oauth.php:384
msgid "The code and state parameters are empty!"
msgstr ""

#: includes/sso/includes/class-bb-sso-provider-oauth.php:386
msgid "The state parameter is empty!"
msgstr ""

#: includes/sso/includes/class-bb-sso-provider-oauth.php:388
msgid "The code parameter is empty!"
msgstr ""

#: includes/sso/includes/class-bb-sso-provider-oauth.php:473
msgid "Access token"
msgstr ""

#: includes/sso/includes/class-bb-sso-provider.php:417
msgid "Error Please check and try again"
msgstr ""

#: includes/sso/includes/class-bb-sso-provider.php:436
msgid "Authentication failed"
msgstr ""

#: includes/sso/includes/class-bb-sso-provider.php:665
#: includes/sso/includes/class-bb-sso-provider.php:1751
msgid "Authentication successful"
msgstr ""

#: includes/sso/includes/class-bb-sso-provider.php:746
msgid "Provider identifier is empty"
msgstr ""

#: includes/sso/includes/class-bb-sso-provider.php:1234
msgid "Authentication error"
msgstr ""

#: includes/sso/includes/class-bb-sso-provider.php:1252
msgid "Unlink successful."
msgstr ""

#: includes/sso/includes/class-bb-sso-provider.php:1257
msgid "Unlink is not allowed!"
msgstr ""

#: includes/sso/includes/class-bb-sso-provider.php:1452
#: includes/sso/includes/class-bb-sso-provider.php:1458
msgid "Saved Successfully"
msgstr ""

#: includes/sso/includes/class-bb-sso-provider.php:1466
msgid "Settings have been verified. Please wait while we redirect you."
msgstr ""

#: includes/sso/includes/class-bb-sso-provider.php:1612
msgid "Identifier"
msgstr ""

#: includes/sso/includes/class-bb-sso-provider.php:1804
msgid "Continue..."
msgstr ""

#: includes/sso/includes/class-bb-sso-user.php:134
#. translators: 1: provider name, 2: provider name
msgid ""
"Your %1$s account is successfully linked with your account. Now you can "
"sign in with %2$s easily."
msgstr ""

#: includes/sso/includes/class-bb-sso-user.php:149
#. translators: 1: provider name, 2: provider name
msgid ""
"You have already linked a(n) %1$s account. Please unlink the current and "
"then you can link another %2$s account."
msgstr ""

#: includes/sso/includes/class-bb-sso-user.php:161
#. translators: %s: provider name
msgid "This %s account is already linked to another user."
msgstr ""

#: includes/sso/includes/class-bb-sso-user.php:282
#: includes/sso/lib/class-bb-sso-rest.php:266
msgid ""
"Registration to this site has been disabled, please contact site owners for "
"further assistance."
msgstr ""

#: includes/sso/includes/class-bb-sso-user.php:356
#. translators: %s: provider name
msgid ""
"<p>Email address could not be identified. Please share the email used for "
"your %s account to register.</p>"
msgstr ""

#: includes/sso/includes/class-bb-sso-user.php:662
#: includes/sso/lib/class-bb-sso-rest.php:313
#. translators: %1$s: required fields list, %2$s: required fields list
msgid "Please fill in the required fields to complete your registration:"
msgstr ""

#: includes/sso/includes/class-bb-sso-user.php:883
#. translators: %1$s: provider name, %2$s: provider name
msgid ""
"We found a user with your %1$s email address. Unfortunately, it belongs to "
"another %2$s account, so we are unable to log you in. Please use the linked "
"%1$s account or log in with your password!"
msgstr ""

#: includes/sso/includes/class-bb-sso-user.php:998
msgid "User login is currently not allowed."
msgstr ""

#: includes/sso/includes/class-bb-sso.php:579
msgid "Hold On"
msgstr ""

#: includes/sso/includes/class-bb-sso.php:580
msgid "You are being redirected to another page,<br>it may take a few seconds."
msgstr ""

#: includes/sso/includes/class-bb-sso.php:581
msgid "The selected provider doesn't support embedded browsers!"
msgstr ""

#: includes/sso/includes/class-bb-sso.php:1327
#: includes/sso/includes/class-bb-sso.php:1360
#: includes/sso/includes/class-bb-sso.php:1683
msgid "Social Accounts"
msgstr ""

#: includes/sso/lib/PKCE/class-bb-sso-pkce.php:68
msgid ""
"Code verifier must be created with a minimum length of 43 characters and a "
"maximum length of 128 characters!"
msgstr ""

#: includes/sso/lib/class-bb-sso-gdpr.php:60
msgid ""
"We collect Telemetry data to better understand which features and "
"configurations \n"
"\t\t\tare most popular, allowing us to create and refine tools that meet "
"your needs. \n"
"\t\t\tThis data empowers us to continuously enhance the platform in ways "
"that benefit all users."
msgstr ""

#: includes/sso/lib/class-bb-sso-gdpr.php:69
#. translators: %s: URL to BuddyBoss Telemetry page
msgid ""
"BuddyBoss does not interact, track or share any Personally Identifiable "
"Data \n"
"\t\t\t\tor User Generated Content from the users on your website. \n"
"\t\t\t\tYou can find additional information about the statistics we gather "
"and their purpose <a href=\"%1$s\" target=\"_blank\" "
"rel=\"noopener\">here</a>"
msgstr ""

#: includes/sso/lib/class-bb-sso-gdpr.php:78
msgid "Suggested text:"
msgstr ""

#: includes/sso/lib/class-bb-sso-gdpr.php:82
msgid ""
"This website collects telemetry data to track feature and configuration "
"usage. Collected data is processed in such a way that visitors cannot be "
"identified and no Personally Identifiable Data or User Generated Content is "
"shared."
msgstr ""

#: includes/sso/lib/class-bb-sso-rest.php:129
msgid "This provider doesn't support REST API calls!"
msgstr ""

#: includes/sso/lib/class-bb-sso-rest.php:175
msgid "Invalid Facebook token"
msgstr ""

#: includes/sso/lib/class-bb-sso-rest.php:221
msgid "Email is required."
msgstr ""

#: includes/sso/lib/class-bb-sso-rest.php:257
msgid ""
"User already have another social account from this provider linked to the "
"WordPress account that has the email match. They should use that account."
msgstr ""

#: includes/sso/lib/class-bb-sso-rest.php:376
#. translators: 1. Term agreement page. 2. Privacy page.
msgid "I agree to the %1$s and %2$s."
msgstr ""

#: includes/sso/lib/class-bb-sso-rest.php:383
#: includes/sso/lib/class-bb-sso-rest.php:389
#. translators: Term agreement page.
#. translators: Privacy page.
msgid "I agree to the %s."
msgstr ""

#: includes/sso/lib/class-bb-sso-rest.php:570
msgid "There was an error with the registration."
msgstr ""

#: includes/sso/providers/apple/class-bb-social-provider-apple-client.php:161
msgid "Unable to validate CSRF state"
msgstr ""

#: includes/sso/providers/apple/class-bb-social-provider-apple.php:153
#: includes/sso/providers/apple/class-bb-social-provider-apple.php:165
#: includes/sso/providers/facebook/class-bb-social-provider-facebook.php:197
#: includes/sso/providers/google/class-bb-social-provider-google.php:159
#: includes/sso/providers/linkedin/class-bb-social-provider-linkedin.php:111
#: includes/sso/providers/microsoft/class-bb-social-provider-microsoft.php:121
#: includes/sso/providers/twitter/class-bb-social-provider-twitter.php:143
#. translators: %1$s is the field name, %2$s is the field name.
#. translators: %s: Field name.
#. translators: %s is the required field name.
#. translators: %s is the name of the required field.
#. translators: %1$s: The required field name, %2$s: The required field name.
#. translators: %1$s is the required field name.
msgid "The %1$s entered did not appear to be a valid. Please enter a valid %2$s."
msgstr ""

#: includes/sso/providers/apple/class-bb-social-provider-apple.php:208
#. translators: %1$s is the error message.
msgid "An error occurred when storing of the expiration timestamp : %1$s"
msgstr ""

#: includes/sso/providers/apple/class-bb-social-provider-apple.php:216
#: includes/sso/providers/apple/class-bb-social-provider-apple.php:249
#: includes/sso/providers/apple/class-bb-social-provider-apple.php:270
#: includes/sso/providers/apple/class-bb-social-provider-apple.php:389
#. translators: %1$s is the error message.
msgid "Token generation failed: %1$s"
msgstr ""

#: includes/sso/providers/apple/class-bb-social-provider-apple.php:217
msgid "Please check your credentials!"
msgstr ""

#: includes/sso/providers/apple/class-bb-social-provider-apple.php:250
msgid "Private key format is not valid!"
msgstr ""

#: includes/sso/providers/apple/class-bb-social-provider-apple.php:348
#. translators: %s is the plugin name.
msgid ""
"%s detected that your Apple credentials have expired. Please delete the "
"current credentials and generate new one!"
msgstr ""

#: includes/sso/providers/apple/class-bb-social-provider-apple.php:351
msgid "Fix Error"
msgstr ""

#: includes/sso/providers/apple/class-bb-social-provider-apple.php:351
msgid "Apple Credentials"
msgstr ""

#: includes/sso/providers/apple/class-bb-social-provider-apple.php:405
#: includes/sso/providers/apple/class-bb-social-provider-apple.php:416
msgid "Continue with Apple"
msgstr ""

#: includes/sso/providers/facebook/class-bb-social-provider-facebook.php:306
#: includes/sso/providers/facebook/class-bb-social-provider-facebook.php:317
msgid "Continue with Facebook"
msgstr ""

#: includes/sso/providers/google/class-bb-social-provider-google.php:340
#: includes/sso/providers/google/class-bb-social-provider-google.php:351
msgid "Continue with Google"
msgstr ""

#: includes/sso/providers/linkedin/class-bb-social-provider-linkedin.php:243
#: includes/sso/providers/linkedin/class-bb-social-provider-linkedin.php:254
msgid "Continue with LinkedIn"
msgstr ""

#: includes/sso/providers/microsoft/class-bb-social-provider-microsoft.php:84
#: includes/sso/providers/microsoft/class-bb-social-provider-microsoft.php:240
msgid "Continue with Microsoft"
msgstr ""

#: includes/sso/providers/twitter/class-bb-social-provider-twitter-client.php:201
msgid "Rate limit exceeded. Please try again in a few minutes."
msgstr ""

#: includes/sso/providers/twitter/class-bb-social-provider-twitter.php:294
#: includes/sso/providers/twitter/class-bb-social-provider-twitter.php:305
msgid "Continue with X"
msgstr ""

#: includes/sso/templates/buddypress/login/below-separator.php:15
#: includes/sso/templates/buddypress/register/above-separator.php:15
msgid "OR"
msgstr ""

#: includes/topics/bb-topics-functions.php:159
msgid "Organizers and Moderators Only"
msgstr ""

#: includes/topics/bb-topics-functions.php:160
msgid "Organizers Only"
msgstr ""

#: includes/topics/includes/class-bb-group-activity-topics-settings.php:26
#: includes/topics/includes/class-bb-group-activity-topics-settings.php:145
#: includes/topics/templates/buddypress/groups/single/admin/activity-topics.php:55
msgid "Topics"
msgstr ""

#: includes/topics/includes/class-bb-group-activity-topics-settings.php:93
msgid "Group Topic Options"
msgstr ""

#: includes/topics/includes/class-bb-group-activity-topics-settings.php:115
msgid "Allow group organizers to use topics from only activity topics."
msgstr ""

#: includes/topics/includes/class-bb-group-activity-topics-settings.php:122
msgid "Allow group organizers to create own topics"
msgstr ""

#: includes/topics/includes/class-bb-group-activity-topics-settings.php:129
msgid "Allow both"
msgstr ""

#: includes/topics/includes/class-bb-group-activity-topics-settings.php:314
msgid "New Topic"
msgstr ""

#: includes/topics/includes/class-bb-group-activity-topics-settings.php:315
msgid "Please enter a valid topic name."
msgstr ""

#: includes/topics/includes/class-bb-group-activity-topics-settings.php:330
msgid "Group not found."
msgstr ""

#: includes/topics/includes/class-bb-group-activity-topics-settings.php:342
msgid "You are not allowed to add a topic."
msgstr ""

#: includes/topics/templates/buddypress/groups/single/admin/activity-topics-delete-form.php:25
#: includes/topics/templates/buddypress/groups/single/admin/activity-topics.php:166
msgid "Deleting"
msgstr ""

#: includes/topics/templates/buddypress/groups/single/admin/activity-topics-delete-form.php:34
#: includes/topics/templates/buddypress/groups/single/admin/activity-topics.php:177
msgid "Would you like to move all previously tagged posts into another topic?"
msgstr ""

#: includes/topics/templates/buddypress/groups/single/admin/activity-topics-delete-form.php:40
#: includes/topics/templates/buddypress/groups/single/admin/activity-topics.php:184
msgid "Yes, move posts to another topic"
msgstr ""

#: includes/topics/templates/buddypress/groups/single/admin/activity-topics-delete-form.php:43
#: includes/topics/templates/buddypress/groups/single/admin/activity-topics.php:188
msgid "Select topic"
msgstr ""

#: includes/topics/templates/buddypress/groups/single/admin/activity-topics-delete-form.php:49
#: includes/topics/templates/buddypress/groups/single/admin/activity-topics.php:195
msgid "No, delete the topic"
msgstr ""

#: includes/topics/templates/buddypress/groups/single/admin/activity-topics-form.php:30
msgid "Create Topic"
msgstr ""

#: includes/topics/templates/buddypress/groups/single/admin/activity-topics-form.php:38
msgid "Topic Name"
msgstr ""

#: includes/topics/templates/buddypress/groups/single/admin/activity-topics-form.php:45
#: includes/topics/templates/buddypress/groups/single/admin/activity-topics-form.php:46
#: includes/topics/templates/buddypress/groups/single/admin/activity-topics.php:100
#: includes/topics/templates/buddypress/groups/single/admin/activity-topics.php:101
msgid "Select a topic"
msgstr ""

#: includes/topics/templates/buddypress/groups/single/admin/activity-topics-form.php:67
msgid "Who can post in this topic?"
msgstr ""

#: includes/topics/templates/buddypress/groups/single/admin/activity-topics-list.php:50
msgid "Global"
msgstr ""

#: includes/topics/templates/buddypress/groups/single/admin/activity-topics-list.php:75
msgid "Remove from group"
msgstr ""

#: includes/topics/templates/buddypress/groups/single/admin/activity-topics-list.php:96
msgid "Add New Topic"
msgstr ""

#: includes/topics/templates/buddypress/groups/single/admin/activity-topics.php:61
msgid "Organize your group posts with topics and make them easier to find."
msgstr ""

#: includes/topics/templates/buddypress/groups/single/admin/activity-topics.php:64
msgid ""
"You can create up to 20 custom topics for each group, which members can use "
"to filter posts using the topic navigation bar. If you use a global topic, "
"your post will also appear in the main activity feed under that "
"topic—giving it even more visibility."
msgstr ""

#: includes/topics/templates/buddypress/groups/single/admin/activity-topics.php:79
msgid "Create topic"
msgstr ""

#: includes/topics/templates/buddypress/groups/single/admin/activity-topics.php:92
msgid "Topic name"
msgstr ""

#: includes/topics/templates/buddypress/groups/single/admin/activity-topics.php:124
msgid "Who can post?"
msgstr ""

#. Author URI of the plugin/theme
msgid "https://buddyboss.com/"
msgstr ""

#. Description of the plugin/theme
msgid "Adds premium features to BuddyBoss Platform."
msgstr ""

#. Author of the plugin/theme
msgid "BuddyBoss"
msgstr ""

#: includes/lib/buddyboss-updater/includes/views/package.php:35
msgctxt "Conjuction joining different product names"
msgid " and "
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Accessibility"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Alerts"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Arrows"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Brands"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Interfaces"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Media & Files"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Objects"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Shopping & Money"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Technology"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Text & Formatting"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Users & People"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Activity"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Activity Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Address Book"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Airplay"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Alarm"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Album"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Align Center"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Align Justify"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Align Left"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Align Right"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Anchor"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Angle Double Down"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Angle Double Left"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Angle Double Right"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Angle Double Up"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Angle Right"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Angle Down"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Angle Left"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Angle Up"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Arrow Down"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Arrow Left"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Arrow Right"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Arrows Horizontal"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Arrows Vertical"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Arrow Up"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Article"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Article Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Asterisk"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "At"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Audio Description"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Award"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Award Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Backward"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Ball Soccer"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Bars"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Bars 2"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Battery"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Battery Empty"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Battery Full"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Battery Quarter"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Battery Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Battery Three Quarters"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Bell"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Bell Plus"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Bell Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Bluetooth"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Bold"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Bolt"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Book"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Bookmark"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Book Open"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Books"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Box"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Android"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Aperture"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Apple"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "BuddyBoss"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "BuddyBoss App"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "BuddyBoss Bolt"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "BuddyPress"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Bunny Net"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Chrome"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Clubhouse"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "CodePen"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Dribbble"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Microsoft Edge"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Elementor"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Facebook"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Facebook F"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Firefox"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Flickr"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "GamiPress"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "GitHub"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Google"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "GitLab"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Google AdMob"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Google Firebase"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Google Fonts"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Instagram"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Intercom"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "LearnDash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "LifterLMS"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "LinkedIn"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Medium"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Meetup"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "MemberPress"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "OneSignal"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Paid Memberships Pro"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Pinterest"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Pusher"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Quora"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "React"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Reddit"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Restrict Content Pro"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Safari"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Skype"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Snapchat"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Slack"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Spotify"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Telegram"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "TikTok"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Tumblr"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Tutor LMS"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Twitch"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Twitter"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Vimeo"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "VK"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "WhatsApp"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "WishList Member"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "WooCommerce"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "WordPress"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "XING"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "X"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "YouTube"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Zoom"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Briefcase"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Browser"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Browser Code"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Browser Terminal"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Bug"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Bullhorn"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Bullseye"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Calendar"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Camera"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Camera Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Cancel"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Caret Down"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Caret Left"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Caret Right"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Caret Up"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Car Small"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Cart Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Cast"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Certificate"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Chart Area"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Chart Bar Horizontal"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Chart Bar Trending"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Chart Bar Vertical"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Chart Line"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Chart Pie"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Check"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Checkbox"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Circle"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Clipboard"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Clock"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Closed Captioning"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Cloud"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Cloud Check"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Cloud Download"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Cloud Exclamation"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Cloud Drizzle"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Cloud Lightning"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Cloud Rain"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Cloud Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Cloud Snow"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Cloud Sync"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Cloud Times"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Cloud Upload"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Code"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Code Branch"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Cog"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Cogs"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Coin"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Coins"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Columns"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Command"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Comment"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Comment Activity"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Comment Dots"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Comment Notification"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Comments"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Comment Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Comment Square"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Comment Square Dots"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Comment Square Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Comments Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Comments Square"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Comments Square Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Compass"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Compress"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Copy"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Copyright"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Corner Left"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Corner Right"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Course"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Course Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "CPU"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Credit Card"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Crop"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Crosshairs"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Crown"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Cube"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Currency Bitcoin"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Currency Crypto"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Currency Dollar"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Currency Euro"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Currency Lira"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Currency Peso"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Currency Pound"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Currency Ruble"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Currency Rupee"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Currency Won"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Currency Yen"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Cut"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Database"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Delete Tag"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Desktop"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Divide"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Dot Circle"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Download"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Droplet"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Duplicate"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Edit"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Eject"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Ellipsis Horizontal"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Emoticon Confused"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Emoticon Frown"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Emoticon Smile"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Emoticon Wink"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Emotion Laugh"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Envelope"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Envelope Open"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Exchange"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Exclamation"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Exclamation Triangle"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Expand"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "External Link"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Eye"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Eye Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Fast Backward"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Fast Forward"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Feather"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Album"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Archive"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Article"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Attach"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Audio"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Bookmark"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Checklist"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Cloud"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Code"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Contact"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File CSS"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Design"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Doc"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Dollar"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Download"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Excel"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Exclamation"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Export"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Font"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File GIF"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Globe"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Image"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Import"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Info"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File List"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File List Numeric"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Minus"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Mobile"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File PDF"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Plus"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Powerpoint"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Presentation"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Question"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File RSS"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Share"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Signature"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Spreadsheet"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Text"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Times"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Upload"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Vector"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Video"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "File Word"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Film"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Filter"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Filter Alt"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Flag"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Flask"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Folder"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Folder Alt"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Folder Alt Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Folder Download"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Folder Move"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Folder Open"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Folder Plus"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Folder Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Folder Upload"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Font"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Forest"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Forward"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Gem"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "GIF"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Gift"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Globe"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Globe Alt"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Globe Layers"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Graduation Cap"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Grid Large"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Grid Small"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Grid H"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Grid V"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Hashtag"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Hand Pointer"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Heading"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Headphones"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Heart"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Home"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Image"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Images"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Image Move"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Image Plus"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Image Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Images Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Image Video"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Image Video Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Inbox"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Indent"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Info"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Info Triangle"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Italic"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Key"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Keyboard"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Key Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Laptop"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Layers"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Less Than"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Less Than Equal"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Life Ring"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Lightbulb"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Link"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "List"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "List Number"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Location Arrow"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Lock Alt"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Lock Alt Open"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Lock Open"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Map"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Map Marker"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Marketplace"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Maximize"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Membership Card"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Merge"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Microphone"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Microphone Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Minimize"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Minus"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Mobile"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Mobile Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Money"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Moon"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "More Than"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "More Than Equal"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Mouse Pointer"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Music"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Music Note"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Music Note Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Music Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Newspaper"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Outdent"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Oxtagon"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Package"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Paperclip"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Paper Plane"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Paragraph"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Paste"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Pause"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Pen"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Pencil"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Percentage"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Phone"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Phone Call"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Phone Forwarded"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Phone Incoming"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Phone Missed"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Phone Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Picture In Picture"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Pin"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Pin Star"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Pizza Slice"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Play"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Plug"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Plus"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Pocket"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Poll"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Poll Horizontal"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Power On"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Printer"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Question"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Question Triangle"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Quiz"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Quote Left"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Quote Right"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Radio"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Random"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Receipt"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Redo"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Registered"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Repeat"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Reply"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Reply All"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Rocket"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Rss"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Save"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Scissors"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Search"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Search Minus"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Search Plus"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Search Times"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Server"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Share"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Share Dots"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Share Square"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Shield"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Shield Half"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Shopping Bag"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Shopping Basket"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Shopping Cart"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Sidebar"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Signal 1"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Signal 2"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Signal 3"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Signal Full"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Signal Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Sign In"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Sign Out"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Sliders Horizontal"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Sliders Vertical"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Sort"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Sort Alpha Down"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Sort Alpha Up"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Sort Amount Down"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Sort Amount Up"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Sort Down"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Sort Numeric Down"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Sort Numeric Up"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Sort Up"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Speaker"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Spinner"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Spinner Alt"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Spinner Third"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Split"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Star"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Star Half"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Star Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Step Backward"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Step Forward"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Stop"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Stopwatch"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Strikethrough"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Sun"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Sunrise"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Sync"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Tablet"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Tag"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Tags"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Terminal"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Text"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Thermometer"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Thumbs Down"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Thumbs Up"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Times"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Times Triangle"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Toggle Off"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Toggle On"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Tools"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Trademark"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Trash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Trash Restore"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Trending Down"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Trending Up"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Trophy"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Trophy Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "TV"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Umbrella"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Underline"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Undo"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Unlink"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "User"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "User Arrow Down"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "User Arrow Up"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "User Avatar"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "User Badge"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "User Cancel"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "User Card"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "User Check"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "User Clock"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "User Cog"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "User Crown"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "User Edit"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "User Friends"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "User Friends Alt"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "User Friends Alt Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "User Friends Plus"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "User Friends Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "User Info"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "User Lock"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "User Minus"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "User Plus"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Users"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "User Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Users Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "User Times"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "U Turn Down"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "U Turn Left"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "U Turn Right"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "U Turn Up"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Video"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Video Plus"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Video Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Voicemail"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Volume Down"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Volume Mute"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Volume Off"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Volume Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Volume Up"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Wallet"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Watch"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Wheelchair"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Wi-Fi"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Wi-Fi Slash"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Wind"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Wrench"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Battery Half"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Upload"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Ellipsis Vertical"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Lock"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Attach"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Thumbtack"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Thumbtack Star"
msgstr ""

#: includes/reactions/assets/icons/bb-icons/font-map.php:3
msgctxt "BuddyBoss Pro"
msgid "Thumbtack Times"
msgstr ""