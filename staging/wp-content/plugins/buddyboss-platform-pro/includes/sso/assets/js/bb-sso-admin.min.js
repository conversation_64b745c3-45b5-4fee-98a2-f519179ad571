window.bb=window.bb||{},function(b){bb.SSO_Admin={start:function(){this.setupGlobals(),this.ssoSortable(),this.addListeners()},setupGlobals:function(){},addListeners:function(){b(document).on("click",".bb-box-item-edit--sso",this.bbSSOConnectionStep),b(document).on("click",".bb-hello-sso .close-modal, .bb-hello-sso #sso_cancel",this.bbSSOConnectionStepClose),b(document).on("keyup",'.bb-hello-sso input[type="text"], .bb-hello-sso input[type="radio"]',this.enableSaveChangesButton),b(document).on("click","#sso_submit",this.bbSSOSubmit),b(document).on("change",".sso-enable, .sso-disable",this.bbSSOEnable),b(document).on("change","input#bb_enable_sso",this.ssoBlockState),b(document).on("change","input#bp-enable-site-registration",this.ssoRegistrationOptions)},ssoSortable:function(){b(window).on("load",function(){var e=b(".bb-box-panel--sortable");0<e.length&&b(e).sortable({cursor:"move",items:"> div.bb-box-item",placeholder:"bb-box-item bb-sortable-placeholder",stop:function(e,t){for(var o=b(".bb-sso-list > .bb-sso-item"),s=[],n=0;n<o.length;n++)s.push(o.eq(n).data("provider"));t.item.closest(".bb-box-panel").next(".bb-sso-provider-notice").remove();var i=b('<div class="bb-sso-provider-notice"></div>');t.item.closest(".bb-box-panel").after(i),b.ajax({type:"post",dataType:"json",url:bbSSOAdminVars.ajax_url,data:{nonce:bbSSOAdminVars.nonce,action:"bb-social-login",view:"orderProviders",ordering:s},success:function(e){i.html('<div class="notice-container notice-container--success">'+e.data.message+"</div>"),setTimeout(function(){i.fadeOut(300,function(){i.remove()})},2e3)},error:function(e){i.html('<div class="notice-container notice-container--error">'+e.data.message+"</div>"),setTimeout(function(){i.fadeOut(300,function(){i.remove()})},3e3)}})}})})},bbSSOConnectionStep:function(e){e.preventDefault(),b(document).find("#bb-hello-backdrop").length||(e=b(document).find(".bb-hello-sso"),b('<div id="bb-hello-backdrop" style="display: none;"></div>').insertBefore(e));var e=document.getElementById("bb-hello-backdrop"),t=document.getElementById("bb-hello-container");if(null===e)return!1;document.body.classList.add("bp-disable-scroll"),e.style.display="",t.style.display="";var e=b(this),t=e.closest(".bb-sso-item").data("provider"),e=e.closest(".bb-sso-list"),o=bbSSOAdminVars.sso_fields[t],s=void 0===(s=e.find("#sso_validate_popup_"+t+"_data").attr("data-hidden-attr"))?{}:JSON.parse(s),o=_.template(b("#sso-fields-template").html())({provider:o,providerId:t,hiddenAttr:s});b(e).find(".bb-hello-sso").html(o),"not-configured"!==s.state&&(t=bb.SSO_Admin.verifySettingButton(s),(0<b(e).find(".bb-hello-sso #sso_submit").length?b(e).find(".bb-hello-sso #sso_submit"):b(e).find(".bb-hello-sso #sso_cancel")).before(t)),"not-configured"===s.state&&(o=bb.SSO_Admin.submitButtonRender(s),b(e).find(".bb-hello-sso #sso_cancel").before(o))},bbSSOConnectionStepClose:function(e){e.preventDefault(),window.location.reload()},enableSaveChangesButton:function(e){e.preventDefault();var t,o,s=[],e=b('.bb-hello-sso input[type="text"]:visible'),n=b('.bb-hello-sso input[type="radio"]:visible'),i=b(".bb-hello-sso textarea:visible"),e=(0<e.length&&e.each(function(){var e=b(this).val().trim(),t=b(this).attr("data-old-value").trim();""===e&&s.push(!1),""!==e&&e===t&&s.push(!0),""!==e&&e!==t&&s.push(!1)}),0<n.length&&(t={},n.each(function(){var e=b(this).attr("name");t[e]||(t[e]=!0,0===b('input[name="'+e+'"]:checked').length&&s.push(!1))})),0<i.length&&i.each(function(){var e=b(this).val().trim(),t=b(this).attr("data-old-value").trim();""===e&&s.push(!1),""!==e&&e===t&&s.push(!0),""!==e&&e!==t&&s.push(!1)}),b(".bb-hello-sso #sso_submit")),n=b(".bb-hello-sso #bb-sso-test-button"),i=b("#sso_validate_popup_hidden_data"),a=b(".bb-hello-sso #sso_cancel");(-1!==jQuery.inArray(!1,s)?(e.length||(o=i.data("hidden-attr"),o=bb.SSO_Admin.submitButtonRender(o),a.before(o)),b(e).is(":hidden")&&(e.show(),e.removeAttr("disabled")),n):(n.length||(o=i.data("hidden-attr"),n=bb.SSO_Admin.verifySettingButton(o),(0<e.length?e:a).before(n)),e)).remove()},bbSSOSubmit:function(e){e.preventDefault();var o=b(this),t=b("#bb-sso-test-button"),e=(o.attr("disabled","disabled"),b(".bb-hello-content .bb-hello-error")),e=(0<e.length&&e.remove(),b(".bb-hello-content").find(":input").serializeArray()),s={action:"bb_sso_save_settings",nonce:bbSSOAdminVars.nonce};b.each(e,function(e,t){s[t.name]=t.value}),bb.SSO_Admin.formDataArray=s,b.ajax({url:bbSSOAdminVars.ajax_url,type:"POST",data:s,success:function(e){var t;e.success?e.data.redirect?(bb.SSO_Admin.updateFormData(bb.SSO_Admin.formDataArray),t=bb.SSO_Admin.verifySettingButton(b("#sso_validate_popup_hidden_data").data("hidden-attr")),b("#bb-sso-test-button").length||o.before(t),t=b("#bb-sso-test-button"),o.hide(),o.attr("disabled","disabled"),t.trigger("click")):window.location.reload():(e.data&&(t=e.data,b.isArray(t)&&1<t.length?(t=t.reverse(),b.each(t,function(e,t){b(".bb-hello-content").prepend('<div class="bb-hello-error"><i class="bb-icon-rf bb-icon-exclamation"></i>'+t+"</div>")})):b(".bb-hello-content").prepend('<div class="bb-hello-error"><i class="bb-icon-rf bb-icon-exclamation"></i>'+e.data+"</div>")),o.removeAttr("disabled"))},error:function(){t.hide(),o.show(),o.removeAttr("disabled")}})},verifySettingButton:function(e){return _.template(b("#sso-verify-settings-template").html())({hiddenAttr:e})},submitButtonRender:function(e){return _.template(b("#sso-submit-template").html())({hiddenAttr:e})},bbSSOEnable:function(e){e.preventDefault();var t,o,s=b(this);s.attr("disabled","disabled"),"not-configured"!==s.data("state")&&(e=s.data("provider"),t=s.data("state"),o=s.is(":checked"),b.ajax({url:bbSSOAdminVars.ajax_url,type:"POST",data:{action:"bb_sso_enable_provider",nonce:bbSSOAdminVars.nonce,provider:e,state:t},success:function(e){e.success&&(o?(s.prop("checked","checked"),s.closest(".bb-box-item").attr("data-state","enabled")):(s.removeAttr("checked"),s.closest(".bb-box-item").attr("data-state","disabled")),bb.SSO_Admin.listOfEnableProvider(),s.removeAttr("disabled"))},error:function(){}}))},ssoBlockRestrict:function(){b('.bb-box-item-actions-disable input[type="checkbox"]').prop("disabled",!0),b("#bb_enable_sso").is(":checked")?(b('.bb-box-item-actions-label:not(.bb-box-item-actions-disable) input[type="checkbox"]').prop("disabled",!1),b('.sso-additional-fields input[type="checkbox"]').prop("disabled",!1)):(b('.bb-box-item-actions-label input[type="checkbox"]').prop("disabled",!0),b('.sso-additional-fields input[type="checkbox"]').prop("disabled",!0)),bb.SSO_Admin.listOfEnableProvider()},ssoBlockState:function(e){e.preventDefault(),bb.SSO_Admin.ssoBlockRestrict()},updateFormData:function(e){b.each(e,function(e,t){e=b('.bb-hello-content [name="'+e+'"]');e.length&&(e.attr("value",t),e.attr("data-old-value",t))})},listOfEnableProvider:function(){var e=[];b('.bb-sso-list .bb-box-item[data-state="enabled"]').each(function(){e.push(b(this).data("provider"))}),1===e.length&&"twitter"===e[0]?b(".sso-additional-fields input").each(function(){b(this).attr("disabled","disabled")}):b(".sso-additional-fields input").each(function(){b(this).removeAttr("disabled")})},ssoRegistrationOptions:function(e){e.preventDefault();var t=b(this).is(":checked"),e=b('input[name="bb-sso-reg-options"]');e.prop("checked",!1),e.each(function(){var e=b(this);e.val()===(t?"1":"0")&&e.prop("checked",!0)}),t?b("#bb-sso-registration-enable").removeAttr("disabled"):b("#bb-sso-registration-enable").attr("disabled","disabled")}},b(document).on("ready",function(){bb.SSO_Admin.start()})}((bb,jQuery));