.activity-update-form .activity-form.focus-in .bb-poll-form .bb-model-header {
	background-color: transparent;
	margin: -20px -30px 0;
}

.bb-poll-form .bb-action-popup-content > label {
	font-weight: 500;
}

.bb-poll-form #message-feedabck {
	position: static;
	margin: -10px -10px 10px -10px;
	width: auto;
	display: none;
}

.bb-poll-form #message-feedabck.active {
	display: flex;
}

.bb-poll-form .input-field + label {
	margin-top: 15px;
}

.bb-poll-form .bb-poll-question_options > input {
	margin-bottom: 8px;
}

#activity-form-submit-wrapper .bb-poll-form .bb-poll_duration {
	height: 41px;
	width: 100%;
}

.bb-poll-form #bb-activity-poll-form_modal.edit-activity-poll .bb-poll_duration {
	opacity: 0.75;
	pointer-events: none;
}

.bb-poll-form .bp-checkbox-wrap {
	position: relative;
}

.bb-poll-form .bb-poll-question_options {
	width: calc(100% + 20px);
	margin-right: -20px;
}

.bb-poll-form .sortable-placeholder,
.bb-poll-form .input-field input[type="text"] {
	min-height: 40px;
	padding: 14px 12px;
	border-radius: 6px;
	border: 1px dashed #eee;
}

.bb-poll-form .sortable-placeholder {
	margin: 0 20px 10px 40px;
}

.bb-poll-form .input-field input[type="text"] {
	border-style: solid;
}

.bb-poll-form .bb-poll_option_draggable {
	position: relative;
	padding: 0 20px 0 0;
	margin-bottom: 8px;
}

.bb-poll-form .bb-poll_option_draggable input {
	width: calc(100% - 20px);
}

.bb-poll-form .bb-poll_option_draggable input[disabled] {
	pointer-events: none;
}

.bb-poll-form .bb-poll_option_draggable .bb-poll-edit-option_remove {
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
	left: 0;
	color: #333;
	opacity: 0;
	visibility: hidden;
}

.bb-poll-form .bb-poll_option_draggable .bb-poll-edit-option_remove span {
	font-size: 20px;
}

.bb-poll-form .bb-poll_option_draggable:hover:before,
.bb-poll-form .bb-poll_option_draggable:hover .bb-poll-edit-option_remove {
	opacity: 0.4;
	visibility: visible;
}

.bb-poll-form .bb-poll_option_draggable:before {
	cursor: move;
	content: "\e9fd";
	font-family: bb-icons;
	font-weight: 400;
	font-size: 16px;
	color: #333;
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
	right: 0;
	opacity: 0;
	visibility: hidden;
}

.bb-poll-form .bb-activity-poll-submit {
	min-width: 65px;
	margin-right: 10px;
}

.bb-poll-form .bb-activity-poll-submit[disabled] {
	pointer-events: none;
	opacity: 0.5;
}

.bb-poll-form .bb-activity-poll-submit.loading:after {
	content: "\ef30";
	font-family: bb-icons;
	font-size: 16px;
	font-weight: 300;
	animation: spin 2s infinite linear;
	display: inline-block;
	margin-right: 5px;
}

.bb-poll-form .bb-poll-option_add {
	padding: 10px;
}

.bb-poll-form .bb-poll-option_add[disabled] {
	pointer-events: none;
	opacity: 0.5;
}

.bb-poll-form .bb-model-footer {
	background-color: transparent;
}

.bb-poll-form .bb-activity-poll-cancel {
	min-width: 75px;
}

.bb-activity-poll_block {
	padding: 16px 24px;
	border: 1px solid #eee;
	border-radius: 6px;
}

.activity-inner .bb-activity-poll_block {
	padding: 0;
	border: 0;
}

.activity-inner .bb-activity-poll_block .bb-activity-poll-option:hover {
	border-color: #ccc;
}

.bb-activity-poll_block .bb-activity-poll_header {
	position: relative;
	margin-bottom: 15px;
}

.bb-activity-poll_block .bb-activity-poll-options-wrap {
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	z-index: 1;
}

.bb-activity-poll_block .bb-activity-poll-options-wrap .bb-activity-poll-options-action {
	cursor: pointer;
}

.bb-activity-poll_block .bb-activity-poll-options-wrap .bb-activity-poll-options-action i {
	font-size: 16px;
	color: #333;
	opacity: 0.4;
}

.bb-activity-poll_block .bb-activity-poll-options-wrap.active .bb-activity-poll-action-options {
	visibility: visible;
	opacity: 1;
}

.bb-activity-poll_block h3 {
	font-size: 16px;
	margin-bottom: 0 !important;
}

.bb-activity-poll_block .bb-activity-poll-action-options {
	position: absolute;
	left: 0;
	background-color: #fff;
	padding: 8px;
	border-radius: 6px;
	min-width: 200px;
	box-shadow: 0 6px 24px 0 rgba(0, 0, 0, 0.12);
	visibility: hidden;
	opacity: 0;
}

.bb-activity-poll_block .bb-activity-poll-action-options .bb-activity-poll-action-option {
	display: block;
	padding: 8px 15px;
	border-radius: 6px;
}

.bb-activity-poll_block .bb-activity-poll-action-options .bb-activity-poll-action-option span {
	color: #555;
	font-size: 14px;
	display: flex;
	align-items: center;
}

.bb-activity-poll_block .bb-activity-poll-action-options .bb-activity-poll-action-option span i {
	color: #767676;
	font-size: 20px;
	margin-left: 7px;
}

.bb-activity-poll_block .bb-activity-poll-action-options .bb-activity-poll-action-option:hover {
	background-color: #eaeaea;
}

.bb-activity-poll_block .bb-activity-poll-option {
	display: flex;
	justify-content: space-between;
	align-items: center;
	position: relative;
	padding: 8px 12px;
	border: 1px solid #eee;
	border-radius: 6px;
	margin-bottom: 8px;
}

.activity-form .bb-activity-poll_block .bb-activity-poll-option {
	pointer-events: none;
}

.bb-activity-poll_block .bb-activity-poll-option.bb-activity-poll-option-hide {
	display: none;
}

.bb-activity-poll_block .bb-activity-poll-option.is-visible {
	display: flex;
}

.bb-activity-poll_block .bb-activity-poll-option .bb-option-field-wrap {
	display: flex;
	flex: 1 0 auto;
	max-width: calc(100% - 85px);
}

.bb-activity-poll_block .bb-activity-poll-option .bb-poll-option-fill {
	position: absolute;
	inset: 0;
	left: inherit;
	background-color: rgba(80, 135, 229, 0.1);
}

.bb-activity-poll_block .bb-activity-poll-option label {
	display: flex;
	align-items: center;
	flex: 1 0 auto;
	font-size: 14px;
	font-weight: 500;
	margin-bottom: 0;
	max-width: 100%;
}

.bb-activity-poll_block .bb-activity-poll-option label > span {
	max-width: calc(100% - 25px);
	line-height: 1.3;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
}

.bb-activity-poll_block .bb-activity-poll-option .bb-activity-poll-option-note {
	font-size: 11px;
}

.bb-activity-poll_block .bb-activity-poll-option .bs-styled-radio + label:before,
.bb-activity-poll_block .bb-activity-poll-option .bs-styled-radio + .bb-radio-label:before {
	vertical-align: middle;
}

.bb-activity-poll_block .bb-activity-poll-option .bs-styled-radio + label:before,
.bb-activity-poll_block .bb-activity-poll-option .bs-styled-radio + .bb-radio-label:before {
	width: 18px;
	height: 18px;
	margin-left: 6px;
}

.bb-template-v2 .bb-activity-poll_block .bb-activity-poll-option .bs-styled-radio.is-checked + label:before, .bb-template-v2
.bb-activity-poll_block .bb-activity-poll-option .bs-styled-radio[checked="checked"] + label:before {
	border-width: 6px;
	border-color: var(--bb-primary-color);
}

.bb-template-v2 .bb-activity-poll_block .bb-activity-poll-option .bs-styled-radio:checked + label:after {
	display: none;
}

.bb-template-v1 .bb-activity-poll_block .bb-activity-poll-option .bs-styled-radio:checked + label:after {
	width: 10px;
	height: 10px;
	right: 4px;
	top: 4px;
}

.bb-activity-poll_block .bb-activity-poll-option .bb-poll-option-state {
	font-size: 16px;
	font-weight: 500;
	color: #333;
}

.bb-activity-poll_block .bb-activity-poll-option .bb-poll-no-vote {
	visibility: hidden;
	padding: 2px;
	font-size: 16px;
}

.bb-activity-poll_block .bb-activity-poll-option .bb-poll-option-view-state {
	font-size: 16px;
	color: #333;
	opacity: 0.4;
	padding: 2px;
}

.bb-activity-poll_block .bb-activity-poll-option .bb-poll-option-view-state:focus, .bb-activity-poll_block .bb-activity-poll-option .bb-poll-option-view-state:hover {
	opacity: 1;
	background-color: #eaeaea;
	border-radius: 6px;
}

.bb-activity-poll_block .bb-activity-poll-option .bb-poll-right {
	position: relative;
}

.bb-activity-poll_block .bb-activity-poll-option:has(.bb-activity-poll-option-note) label {
	transform: translateY(6px);
}

.bb-activity-poll_block .bb-activity-poll-option:has(.bb-activity-poll-option-note) .bs-styled-checkbox + label:before {
	transform: translateY(-5px);
}

.bb-activity-poll_block .bb-activity-poll-option:has(.bb-activity-poll-option-note) .bs-styled-checkbox:checked + label:after {
	top: -2px;
}

.bb-template-v1 .bb-activity-poll_block .bb-activity-poll-option:has(.bb-activity-poll-option-note) .bs-styled-radio:checked + label:after {
	transform: translateY(-5px);
}

.bb-activity-poll_block .bb-activity-poll-option:has(.bb-activity-poll-option-note) .bb-activity-poll-option-note {
	position: absolute;
	top: 5px;
	right: 44px;
}

.activity-form .bb-activity-poll_block .bb-activity-poll-option:has(.bb-activity-poll-option-note) .bb-activity-poll-option-note {
	top: -3px;
}

.bb-activity-poll_block .bb-activity-poll-option:has(.bb-activity-poll-option-note) .bp-radio-wrap .bb-activity-poll-option-note {
	right: 37px;
}

.bb-activity-poll_block .bb-activity-poll-option:has(.bb-activity-poll-option-note) .bs-styled-radio + label:before {
	transform: translateY(-5px);
}

.bb-activity-poll_block .bb-activity-poll-option:has(.bb-poll-option_remove) {
	margin-left: 22px;
}

.bb-activity-poll_block .bb-activity-poll-option .bb-poll-option_remove {
	font-size: 20px;
	color: #333;
	position: absolute;
	left: -22px;
	top: 50%;
	transform: translateY(-50%);
	opacity: 0.4;
	cursor: pointer;
}

.bb-activity-poll_block .bb-activity-poll-option .bb-poll-option_remove:hover {
	opacity: 1;
}

.bb-activity-poll_block .bb-poll-error {
	display: none;
	font-size: 13px;
	font-weight: 500;
	margin-top: -5px;
	margin-bottom: 5px;
}

.bb-activity-poll_block .bb-poll-error.is-visible {
	display: block;
}

.bb-activity-poll_block .bb-activity-poll-footer {
	font-size: 14px;
	display: flex;
}

.bb-activity-poll_block .bb-activity-poll-footer .bb-activity-poll_duration {
	display: flex;
	align-items: center;
}

.bb-activity-poll_block .bb-activity-poll-footer .bb-activity-poll_duration:before {
	content: "";
	height: 4px;
	width: 4px;
	background-color: #767676;
	display: inline-block;
	vertical-align: middle;
	margin: 0 8px;
	border-radius: 50%;
}

.bb-activity-poll_block .bb-activity-poll-new-option {
	display: flex;
	align-items: center;
	padding: 3px 12px;
}

.bb-activity-poll_block .bb-activity-poll-new-option.bb-activity-poll-option-hide {
	display: none;
}

.bb-activity-poll_block .bb-activity-poll-new-option.is-visible {
	display: flex;
}

.bb-activity-poll_block .bb-activity-poll-new-option > span {
	font-size: 20px;
	color: #333;
	opacity: 0.3;
	margin-left: 4px;
}

.bb-activity-poll_block .bb-activity-poll-new-option .bb-activity-poll-new-option-input {
	flex: 1;
	border: 0 !important;
	outline: none !important;
	padding: 0;
	box-shadow: none !important;
}

.bb-activity-poll_block .bb-activity-poll-new-option .bb-activity-option-submit {
	display: none;
	padding: 7px;
	background-color: #5087e5;
	border-radius: 6px;
	opacity: 0.5;
	pointer-events: none;
}

.bb-activity-poll_block .bb-activity-poll-new-option .bb-activity-option-submit > span {
	display: block;
	color: #fff;
	font-size: 16px;
	line-height: 1;
}

.bb-activity-poll_block .bb-activity-poll-new-option .bb-activity-option-submit.active {
	opacity: 1;
}

.bb-activity-poll_block .bb-activity-poll-new-option.is-valid .bb-activity-option-submit {
	opacity: 1;
	pointer-events: all;
}

.bb-activity-poll_block .bb-activity-poll-new-option:focus-within > span, .bb-activity-poll_block .bb-activity-poll-new-option.is-valid > span {
	display: none;
}

.bb-activity-poll_block .bb-activity-poll-new-option:focus-within .bb-activity-option-submit, .bb-activity-poll_block .bb-activity-poll-new-option.is-valid .bb-activity-option-submit {
	display: block;
}

.bb-activity-poll_block .bb-activity-poll-new-option.is-invalid {
	border-color: #b71717;
	box-shadow: 0 2px rgba(183, 23, 23, 0.2), 0 -2px rgba(183, 23, 23, 0.2);
}

.bb-activity-poll_block .bb-activity-poll-see-more-link {
	display: block;
	font-size: 16px;
	font-weight: 500;
	color: #333;
	background-color: #eaeaea;
	text-align: center;
	border-radius: 6px;
	padding: 10px;
	margin-bottom: 8px;
}

.bb-activity-poll_block .bb-activity-poll-see-more-link .bb-poll-see-less-text {
	display: none;
}

.bb-activity-poll_block .bb-activity-poll-see-more-link.see-less .bb-poll-see-more-text {
	display: none;
}

.bb-activity-poll_block .bb-activity-poll-see-more-link.see-less .bb-poll-see-less-text {
	display: block;
}

#bb-activity-poll-state_modal .bb-activity-poll-loader {
	display: flex;
	align-items: center;
	justify-content: center;
	min-height: 150px;
}

#bb-activity-poll-state_modal .bb-activity-poll-loader > i {
	font-size: 18px;
	line-height: 1;
}

#bb-activity-poll-state_modal .bb-option-state {
	display: block;
	font-size: 16px;
	font-weight: 500;
	margin-bottom: 10px;
}

#bb-activity-poll-state_modal .activity-state_users {
	margin: 0;
}

#bb-activity-poll-state_modal .bb-poll-state-loader {
	display: flex;
	justify-content: center;
	padding: 10px 0;
	margin: 0;
}

#bb-activity-poll-state_modal .bb-poll-state-loader .bb-icon-spinner {
	font-size: 18px;
	line-height: 1;
}

#bb-activity-poll-state_modal .bb-action-popup-content-dynamic:empty {
	display: flex;
	align-items: center;
	justify-content: center;
	min-height: 150px;
}

#bb-activity-poll-state_modal .bb-action-popup-content-dynamic:empty:after {
	content: "\ef30";
	font-family: bb-icons;
	font-size: 16px;
	font-weight: 300;
	animation: spin 2s infinite linear;
	display: inline-block;
	margin-right: 5px;
}

#bb-activity-poll-state_modal .activity-state_user {
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	padding: 10px 0;
	margin: 0;
}

#bb-activity-poll-state_modal .activity-state_user .activity-state_user__avatar {
	width: 38px;
	height: 38px;
	object-fit: cover;
	position: relative;
	margin-left: 13px;
}

#bb-activity-poll-state_modal .activity-state_user .activity-state_user__avatar img.avatar {
	width: 100%;
	max-width: 100%;
}

#bb-activity-poll-state_modal .activity-state_user .activity-state_user__name {
	color: #222;
	font-size: 18px;
	font-weight: 600;
	line-height: 1.33;
	text-transform: capitalize;
	overflow: hidden;
	text-overflow: ellipsis;
	max-width: calc(100% - 155px);
}

#bb-activity-poll-state_modal .activity-state_user .activity-state_user__name a {
	color: #222;
	font-size: 16px;
	font-weight: 600;
}

#bb-activity-poll-state_modal .activity-state_user .activity-state_user__role {
	color: #fff;
	font-size: 11px;
	font-weight: 600;
	background-color: #595895;
	border-radius: 4px;
	padding: 4px 8px;
	margin-right: auto;
	text-transform: capitalize;
	max-width: 100px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

#bb-activity-poll-state_modal .modal-mask {
	background-color: transparent;
}

#bb-activity-poll-state_modal .bb-activity-poll-state_overlay {
	background-color: rgba(250, 251, 253, 0.9);
	position: fixed;
	inset: 0;
}
