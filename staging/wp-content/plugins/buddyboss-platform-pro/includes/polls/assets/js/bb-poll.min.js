window.wp=window.wp||{},window.bp=window.bp||{},function(b){"undefined"!=typeof BP_Nouveau&&(_.extend(bp,_.pick(wp,"Backbone","ajax","template")),bp.Models=bp.Models||{},bp.Collections=bp.Collections||{},bp.Views=bp.Views||{},bp.Nouveau=bp.Nouveau||{},bp.Nouveau.Poll={start:function(){this.setupGlobals(),this.addListeners()},setupGlobals:function(){this.pollFormInitialized=!1},addListeners:function(){b("#bp-nouveau-activity-form").on("click",".post-elements-buttons-item.post-poll",this.showPollForm.bind(this)),b("#bp-nouveau-activity-form").on("click",".bb-activity-poll-cancel, .bb-activity-poll_modal .bb-model-close-button",this.hidePollForm.bind(this)),b(document).on("keyup",".bb-activity-poll-new-option-input",this.validateNewOption.bind(this)),b(document).on("click",".bb-activity-option-submit",this.submitNewOption.bind(this)),b(document).on("click",".bb-activity-poll-see-more-link",this.seeMoreOptions.bind(this)),b(document).on("click",".bb-poll-option-view-state",this.showPollState.bind(this)),b(document).on("click",".bb-poll-option_remove",this.removePollOption.bind(this)),b(document).on("click",'.bb-activity-poll-option input[type="radio"]',this.addPollVote.bind(this)),b(document).on("change",'.bb-activity-poll-option input[type="checkbox"]',this.addPollVote.bind(this)),b(document).on("click",".bb-close-action-popup, .bb-activity-poll-state_overlay",this.closeVoteState.bind(this))},showPollForm:function(){function o(){b("#bb-activity-poll-form_modal .bb-action-popup-content").find(".bb-poll_option_draggable:not(.custom)").each(function(t){b(this).find(".bb-poll-question_option").attr("name","bb-poll-question-option["+t+"]")}),b("#bb-activity-poll-form_modal .bb-activity-poll-submit").removeAttr("disabled")}function a(){var t=!0,i=b("#bb-activity-poll-form_modal");i.find(".bb-poll-question_option").each(function(){""!==b(this).val()||b(this).parent().hasClass("custom")||(t=!1)}),(t=""===i.find(".bb-poll-question-field").val()?!1:t)?i.find(".bb-activity-poll-submit").removeAttr("disabled"):i.find(".bb-activity-poll-submit").attr("disabled",!0)}b.fn.sortable&&!b(".bb-poll-question_options").hasClass("ui-sortable")&&b(".bb-poll-question_options").sortable({placeholder:"sortable-placeholder",update:function(){o()},cancel:"input, .bb-poll-edit-option_remove"}),this.pollFormInitialized||(b(document).on("click",".bb-poll-option_add",function(t){t.preventDefault();var i,t=b(this),o=(t.attr("disabled","disabled"),t.closest(".bb-action-popup-content")),e=o.find(".bb-poll_option_draggable:not(.custom)").length,l=e;10<=e||((i=o.find(".bb-poll_option_draggable:first").clone()).find(".bb-poll-question_option").val("").removeAttr("disabled"),i.find(".bb-poll-question_option").attr("name","bb-poll-question-option["+e+"]"),i.find(".bb-poll-question_option").attr("data-opt_id",++l),2<=e&&i.append('<a href="#" class="bb-poll-edit-option_remove"><span class="bb-icon-l bb-icon-times"></span></a>'),i.insertAfter(o.find(".bb-poll-question_options .bb-poll_option_draggable").last()),t.removeAttr("disabled"),9===e&&o.find(".bb-poll-option_add").attr("disabled","disabled").parent().addClass("bp-hide"),a())}),b(document).on("click",".bb-poll-edit-option_remove",function(t){t.preventDefault();var t=b(this),i=t.closest(".bb-action-popup-content");i.find(".bb-poll_option_draggable:not(.custom)").length<=2||(t.closest(".bb-poll_option_draggable").remove(),i.find(".bb-poll-option_add").removeAttr("disabled").parent().removeClass("bp-hide"),o(),a())}),b(document).on("keyup",".bb-poll-question-field, .bb-poll-question_option",function(){a()}),b(document).on("change",".bb-poll_duration, #bb-poll-allow-multiple-answer, #bb-poll-allow-new-option",function(){a()}),this.pollFormInitialized=!0),b(".whats-new-form-footer").find(".bb-poll-form .bb-action-popup").show()},hidePollForm:function(t){t.preventDefault();var i=b(t.currentTarget).closest(".bb-action-popup");i.data("poll_id")||(0<i.find('input[type="text"]').filter(function(){return""!==b(this).val()}).length||1!==i.find("select").prop("selectedIndex")||i.find('input[type="checkbox"]').is(":checked"))&&confirm(BP_Nouveau.activity_polls.strings.closePopupConfirm)&&(i.find('input[type="text"]').val(""),i.find("select").prop("selectedIndex",1),i.find('input[type="checkbox"]').prop("checked",!1),i.find(".bp-messages.bp-feedback").hide(),i.find(".bb-poll_option_draggable").not(":lt(2)").remove(),i.find(".bb-activity-poll-submit").attr("disabled",!0)),b(t.currentTarget).closest(".bb-action-popup").hide(),b(t.currentTarget).closest(".whats-new-form-footer").find(".post-poll.active").removeClass("active"),window.activityMediaAction=null},validateNewOption:function(t){var i=b(t.currentTarget),o=i.closest(".bb-activity-poll-new-option"),i=i.val().trim();""!==i&&i.length<=50?o.addClass("is-valid"):o.removeClass("is-valid"),"Enter"!==t.key&&13!==t.keyCode||o.hasClass("is-valid")&&this.submitNewOption(t)},submitNewOption:function(t){t.preventDefault();var i,o,e,l,a=b(t.currentTarget);a.hasClass("adding")||(a.addClass("adding"),i=a.parents(".activity-item").data("bp-activity"),_.isUndefined(i))||_.isUndefined(i.poll)||_.isUndefined(i.poll.id)||_.isUndefined(i.poll.allow_new_option)||(t=i.poll.id,e=i.poll.options?Object.values(i.poll.options):[],o=a.parent().find(".bb-activity-poll-new-option-input").val(),e=e.some(function(t){return jQuery.trim(t.option_title)===jQuery.trim(o)}),l=i.id,e?(a.parent().addClass("is-invalid").removeClass("is-valid"),a.closest(".bb-activity-poll-option").siblings(".bb-poll-error.duplicate-error").addClass("is-visible"),a.removeClass("adding")):b.ajax({url:bbPollsVars.ajax_url,data:{action:"bb_pro_add_poll_option",nonce:bbPollsVars.nonce.add_poll_option_nonce,poll_id:t,activity_id:l,poll_option:o},method:"POST"}).done(function(t){!t.success||_.isUndefined(t.data)||_.isUndefined(t.data.option_data)?(a.parent().addClass("is-invalid").removeClass("is-valid"),a.closest(".bb-activity-poll-option").siblings(".bb-poll-error.limit-error").addClass("is-visible").text(t.data)):(i.poll.options=t.data.all_options,t=new bp.Views.ActivityPollEntry(t.data),b("#activity-"+l+" .bb-activity-poll_block").html(t.render().el),a.parents(".activity-item").attr("data-bp-activity",JSON.stringify(i)),a.parent().removeClass("is-invalid"),a.parent().siblings(".bb-poll-error").removeClass("is-visible")),a.removeClass("adding")}))},seeMoreOptions:function(t){t.preventDefault();t=b(t.currentTarget);t.hasClass("see-less")?(t.removeClass("see-less"),t.parents(".bb-activity-poll-options").find(".bb-activity-poll-option").not(":has(.bb-activity-poll-new-option-input)").slice(5,10).addClass("bb-activity-poll-option-hide").removeClass("is-visible")):(t.parents(".bb-activity-poll-options").find(".bb-activity-poll-option").not(":has(.bb-activity-poll-new-option-input)").slice(5,10).addClass("is-visible"),t.addClass("see-less"))},showPollState:function(t){t.preventDefault();var t=b(t.currentTarget),i=t.closest("#bb-poll-view").find("#bb-activity-poll-state_modal"),o=t.closest(".bb-activity-poll-option").find("label").text(),e=t.closest("#bb-poll-view").find(".bb-action-popup-content"),o=(i.find(".bb-model-header h4").text(o),i.show(),t.parents(".activity-item").data("bp-activity"));_.isUndefined(o)||_.isUndefined(o.poll)||_.isUndefined(o.poll.id)||_.isUndefined(o.poll.options)||(i=t.data("opt_id"))&&(t={element:e,poll_id:o.poll.id,activity_id:o.id,option_id:i,paged:1},this.loadPollState(t))},loadPollState:function(t){var e=this,l=t.element,a=t.poll_id,n=t.activity_id,s=t.option_id,d=t.paged;b.ajax({url:bbPollsVars.ajax_url,data:{action:"bb_pro_poll_vote_state",nonce:bbPollsVars.nonce.poll_vote_state_nonce,poll_id:a,activity_id:n,option_id:s,paged:d},method:"POST"}).done(function(t){var i,o;!t.success||_.isUndefined(t.data)||_.isUndefined(t.data.members)||(i={vote_state:t.success?t.data:{}},i=new bp.Views.ActivityPollState(i),1===d?l.html(i.render().el):l.find(".has-more-vote-state li:last").after(i.render().el),l.find(".bb-poll-state-loader").remove(),(o=l.find(".has-more-vote-state")).attr("data-paged",t.data.others.paged).attr("data-total_pages",t.data.others.total_pages),t.data.others.paged>=t.data.others.total_pages?o.removeClass("has-more-vote-state"):l.off("scroll").on("scroll",function(){l.scrollTop()+l.innerHeight()>=l[0].scrollHeight&&o.hasClass("has-more-vote-state")&&0===l.find(".bb-poll-state-loader").length&&(l.find("ul.activity-state_users").append('<li class="bb-poll-state-loader"><span class="bb-icon-spinner animate-spin"></span></li>'),e.loadPollState({element:l,poll_id:a,activity_id:n,option_id:s,paged:d+1}))}))})},closeVoteState:function(t){t=b(t.currentTarget).closest("#bb-poll-view").find("#bb-activity-poll-state_modal");t.find(".bb-action-popup-content .bb-action-popup-content-dynamic").html(""),t.find(".bb-activity-poll-loader").show(),t.hide()},removePollOption:function(t){t.preventDefault();var i,o,e=b(t.currentTarget),l=e.parents(".activity-item").data("bp-activity");_.isUndefined(l)||_.isUndefined(l.poll)||_.isUndefined(l.poll.id)||(t=l.poll.id,i=l.id,o=0,(o=(e.parent(".bb-activity-poll-option").find(".bp-radio-wrap").length?e.parent(".bb-activity-poll-option").find(".bs-styled-radio"):e.parent(".bb-activity-poll-option").find(".bs-styled-checkbox")).data("opt_id"))&&confirm(BP_Nouveau.activity_polls.strings.areYouSure)&&b.ajax({url:bbPollsVars.ajax_url,data:{action:"bb_pro_remove_poll_option",nonce:bbPollsVars.nonce.remove_poll_option_nonce,poll_id:t,activity_id:i,option_id:o},method:"POST"}).done(function(t){!t.success||_.isUndefined(t.data)||_.isUndefined(t.data.option_data)||(l.poll.options=t.data.all_options,t=new bp.Views.ActivityPollEntry(t.data),b("#activity-"+i+" .bb-activity-poll_block").html(t.render().el),e.parents(".activity-item").attr("data-bp-activity",JSON.stringify(l)),e.parent(".bb-activity-poll-option").remove())}))},addPollVote:function(t){t.preventDefault();var i,o,e=b(t.currentTarget),l=e.parents(".activity-item").data("bp-activity");_.isUndefined(l)||_.isUndefined(l.poll)||_.isUndefined(l.poll.id)||_.isUndefined(l.poll.options)||(t=l.poll.id,i=l.id,o=e.data("opt_id"),e.addClass("is-checked"),o&&b.ajax({url:bbPollsVars.ajax_url,data:{action:"bb_pro_add_poll_vote",nonce:bbPollsVars.nonce.add_poll_vote_nonce,poll_id:t,activity_id:i,option_id:o},method:"POST"}).done(function(t){!t.success||_.isUndefined(t.data)||_.isUndefined(t.data.vote_data)||(l.poll.total_votes=t.data.total_votes,l.poll.options=t.data.all_options,t=new bp.Views.ActivityPollEntry(t.data),b("#activity-"+i+" .bb-activity-poll_block").html(t.render().el),e.parents(".activity-item").attr("data-bp-activity",JSON.stringify(l)))}))}},bp.Views.activityPollForm=Backbone.View.extend({tagName:"div",id:"bb-poll-form",className:"bb-poll-form",template:bp.template("bb-activity-poll-form"),events:{"click .bb-activity-poll-submit":"submitPoll"},initialize:function(){this.model.on("change:poll change:poll_id",this.render,this)},render:function(){return this.$el.html(this.template(this.model.attributes)),this},submitPoll:function(o){o.preventDefault();var e=b(o.target).closest("#bb-activity-poll-form_modal"),t=e.data("poll_id"),i=e.find(".bb-poll-question-field").val(),l=e.find(".bb-poll-question_option").map(function(){var t;if(!b(this).parent().hasClass("custom"))return(t={})[b(this).attr("data-opt_id")]=b(this).val(),t}).get(),a=e.find("#bb-poll-allow-multiple-answer").is(":checked"),n=e.find("#bb-poll-allow-new-option").is(":checked"),s=e.find(".bb-poll_duration").val(),d=e.data("activity_id"),p=this.model,c=0;b("#item-header").length&&"groups"===b("#item-header").data("bp-item-component")?c=b("#item-header").data("bp-item-id"):_.isUndefined(p.get("object"))||"group"!==p.get("object")||_.isUndefined(p.get("item_id"))||(c=p.get("item_id")),_.isUndefined(p.get("edit_activity"))||!0!==p.get("edit_activity")||_.isUndefined(p.get("poll"))||_.isUndefined(p.get("poll").duration)||p.get("poll").duration===s||(s=p.get("poll").duration),b(o.target).addClass("loading"),b.ajax({url:bbPollsVars.ajax_url,data:{action:"bb_pro_add_poll",nonce:bbPollsVars.nonce.add_poll_nonce,poll_id:t,activity_id:d,group_id:c,questions:i,options:l,allow_multiple_answer:a,allow_new_option:n,duration:s},method:"POST"}).done(function(t){var i;t.success&&void 0!==t.data&&(i={id:t.data.id,item_id:t.data.item_id,user_id:parseInt(t.data.user_id),vote_disabled_date:t.data.vote_disabled_date,question:t.data.question,options:t.data.options,total_votes:t.data.total_votes},t.data.settings&&(i.allow_multiple_options=t.data.settings.allow_multiple_options||!1,i.allow_new_option=t.data.settings.allow_new_option||!1,i.duration=t.data.settings.duration||3),p.set("poll",i),p.set("poll_id",t.data.id),_.isUndefined(BP_Nouveau.activity.params.topics)||_.isUndefined(BP_Nouveau.activity.params.topics.bb_is_enabled_activity_topics)||!BP_Nouveau.activity.params.topics.bb_is_enabled_activity_topics||_.isUndefined(BP_Nouveau.activity.params.topics.bb_is_activity_topic_required)||!BP_Nouveau.activity.params.topics.bb_is_activity_topic_required||"undefined"==typeof BBTopicsManager||void 0===BBTopicsManager.bbTopicValidateContent?b("#whats-new-form").removeClass("focus-in--empty"):BBTopicsManager.bbTopicValidateContent({self:this,selector:b("#whats-new-form"),validContent:!0,class:"focus-in--empty",data:p.attributes,action:"poll_form_submitted"}),b(o.target).closest("#bb-activity-poll-form_modal").hide(),window.activityMediaAction=null,e.find("#bb-poll-allow-multiple-answer .bb-action-popup-content > .bp-feedback").removeClass("active")),!1===t.success&&e.find(".bb-action-popup-content > .bp-feedback").addClass("active").find("p").text(t.data),b(o.target).removeClass("loading")})}}),bp.Views.activityPollView=Backbone.View.extend({tagName:"div",id:"bb-poll-view",className:"bb-poll-view",template:bp.template("bb-activity-poll-view"),events:{"click .bb-activity-poll-options-action":"showOptions","click .bb-activity-poll-action-edit":"editPoll","click .bb-activity-poll-action-delete":"deletePoll"},initialize:function(){this.model.on("change:activity_poll_title change:activity_poll_options change:activity_poll_allow_multiple_answer change:activity_poll_allow_new_option change:activity_poll_duration",this.render,this),this.model.on("change",this.render,this),b(document).on("click",function(t){b(t.target).closest(".bb-activity-poll-options-wrap").length||b(".bb-activity-poll-options-wrap").removeClass("active")})},render:function(){return this.$el.html(this.template(this.model.attributes)),this},showOptions:function(t){t.preventDefault(),b(t.target).closest(".bb-activity-poll-options-wrap").toggleClass("active")},editPoll:function(t){t.preventDefault(),bp.Nouveau.Poll.showPollForm()},deletePoll:function(t){t.preventDefault();var i,t=b(t.currentTarget),o=this;confirm(BP_Nouveau.activity_polls.strings.DeletePollConfirm)?(i=t.parents(".poll_view").data("poll_id"),t=t.parents(".poll_view").data("activity_id"),b.ajax({url:bbPollsVars.ajax_url,data:{action:"bb_pro_remove_poll",nonce:bbPollsVars.nonce.remove_poll_nonce,poll_id:i,activity_id:t},method:"POST"}).done(function(t){t.success&&(o.resetPollData(),b(".post-poll.active").removeClass("active"),bp.draft_activity.data.poll?""===bp.draft_activity.data.content?b("#discard-draft-activity").trigger("click"):bp.Nouveau.Activity.postForm.collectDraftActivity():bp.Nouveau.refreshActivities())})):b(".bb-activity-poll-options-wrap.active").removeClass("active")},resetPollData:function(){this.model.set("poll",{}),this.model.set("poll_id","")}}),bp.Views.ActivityPollState=Backbone.View.extend({tagName:"div",className:"bb-action-popup-content-dynamic",template:bp.template("bb-activity-poll-state"),initialize:function(t){this.data=t},render:function(){return this.$el.html(this.template(this.data)),this}}),bp.Views.ActivityPollEntry=Backbone.View.extend({tagName:"div",className:"bb-activity-poll_block",template:bp.template("bb-activity-poll-entry"),initialize:function(t){this.data=t},render:function(){return this.$el.html(this.template(this.data)),this}}),bp.Nouveau.Poll.start())}((bp,jQuery));