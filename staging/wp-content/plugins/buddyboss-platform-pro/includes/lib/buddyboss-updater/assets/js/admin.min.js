jQuery(document).ready(function(n){0<n(".tooltip-persistent-container").length&&(n(".tooltip-persistent-container").mouseenter(function(e){n(this).addClass("hover")}),n(".tooltip-persistent-container").mouseleave(function(e){n(this).removeClass("hover")})),jQuery(".license-key-input").on("input",function(){jQuery('input[name="license_key"]').val(jQuery(this).val())}),jQuery("#show-license").on("click",function(e){e.preventDefault(),jQuery(".license-key-input").val(jQuery('input[name="license_key"]').val()),jQuery("#hide-license").show(),jQuery(this).hide()}),jQuery("#hide-license").on("click",function(e){e.preventDefault(),jQuery(".license-key-input").val(jQuery(".license-key-input").data("value")),jQuery('input[name="license_key"]').val(jQuery('input[name="license_key"]').data("value")),jQuery("#show-license").show(),jQuery(this).hide()}),BBOSS_UPDATER_ADMIN.bb_connect.init()}),BBOSS_UPDATER_ADMIN.bb_connect={},function(e,o,r){var c={};e.init=function(){e.getElements()&&c.$connector_button.click(function(){c.$overlay_outer.show(),r("body").addClass("bb_connect_overlay"),r(".bb_connect .connecting").show();var e=Number(screen.width/2-195),n=Number(screen.height/2-277.5);c.win=o.open(BBOSS_UPDATER_ADMIN.connector_url,"Connect to BuddyBoss.com","width=390,height=555,top="+n+",left="+e);var t=setInterval(function(){c.win.closed&&(clearInterval(t),r("body").removeClass("bb_connect_overlay"),c.$overlay_outer.hide())},500)})},e.getElements=function(){return c.$overlay_outer=r("#bb_connector_overlay_wrapper"),0!=c.$overlay_outer.length&&(c.$connector_button=r("#btn_bb_connect"),!0)},e.receive_message=function(e){var n=e.data;if(e.origin!=BBOSS_UPDATER_ADMIN.connector_host&&"updater_bb_connect"!=n.message_type)return!1;n.action="updater_bb_connect_received_message",n.nonce=BBOSS_UPDATER_ADMIN.nonce_received_message,r.ajax({method:"POST",url:ajaxurl,data:n,success:function(e){r("body").removeClass("bb_connect_overlay"),c.$overlay_outer.hide(),(e=r.parseJSON(e)).status&&(e.message&&alert(e.message),e.redirect_to&&(o.location.href=e.redirect_to))},error:function(){r("body").removeClass("bb_connect_overlay"),c.$overlay_outer.hide(),alert("Error - Operation Failed.")}})}}(BBOSS_UPDATER_ADMIN.bb_connect,window,window.jQuery),window.addEventListener("message",BBOSS_UPDATER_ADMIN.bb_connect.receive_message,!1);