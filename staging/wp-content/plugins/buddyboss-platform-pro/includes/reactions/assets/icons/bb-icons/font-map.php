<?php

$bbpro_icons = array("name" => "bb-icons", "version" => "1.0.8", "copyright" => "BuddyBoss", "groups" => array(array("id" => "accessibility", "label" => _x( "Accessibility", "BuddyBoss Pro", "buddyboss-pro" )), array("id" => "alerts", "label" => _x( "<PERSON>erts", "BuddyBoss Pro", "buddyboss-pro" )), array("id" => "arrows", "label" => _x( "Arrows", "BuddyBoss Pro", "buddyboss-pro" )), array("id" => "brands", "label" => _x( "Brands", "BuddyBoss Pro", "buddyboss-pro" )), array("id" => "interfaces", "label" => _x( "Interfaces", "BuddyBoss Pro", "buddyboss-pro" )), array("id" => "media-files", "label" => _x( "Media & Files", "BuddyBoss Pro", "buddyboss-pro" )), array("id" => "objects", "label" => _x( "Objects", "BuddyBoss Pro", "buddyboss-pro" )), array("id" => "shopping-money", "label" => _x( "Shopping & Money", "BuddyBoss Pro", "buddyboss-pro" )), array("id" => "technology", "label" => _x( "Technology", "BuddyBoss Pro", "buddyboss-pro" )), array("id" => "text-formatting", "label" => _x( "Text & Formatting", "BuddyBoss Pro", "buddyboss-pro" )), array("id" => "users-people", "label" => _x( "Users & People", "BuddyBoss Pro", "buddyboss-pro" ))), "glyphs" => array(array("css" => "activity", "code" => 59476, "label" => _x( "Activity", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "activity-slash", "code" => 60816, "label" => _x( "Activity Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "address-book", "code" => 60817, "label" => _x( "Address Book", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "users-people"), array("css" => "airplay", "code" => 59479, "label" => _x( "Airplay", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "alarm", "code" => 60819, "label" => _x( "Alarm", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "album", "code" => 60820, "label" => _x( "Album", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "align-center", "code" => 59478, "label" => _x( "Align Center", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "text-formatting"), array("css" => "align-justify", "code" => 59480, "label" => _x( "Align Justify", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "text-formatting"), array("css" => "align-left", "code" => 59481, "label" => _x( "Align Left", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "text-formatting"), array("css" => "align-right", "code" => 59482, "label" => _x( "Align Right", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "text-formatting"), array("css" => "anchor", "code" => 59485, "label" => _x( "Anchor", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "angle-double-down", "code" => 59517, "label" => _x( "Angle Double Down", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "arrows"), array("css" => "angle-double-left", "code" => 59520, "label" => _x( "Angle Double Left", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "arrows"), array("css" => "angle-double-right", "code" => 59518, "label" => _x( "Angle Double Right", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "arrows"), array("css" => "angle-double-up", "code" => 59519, "label" => _x( "Angle Double Up", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "arrows"), array("css" => "angle-right", "code" => 59431, "label" => _x( "Angle Right", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "arrows"), array("css" => "angle-down", "code" => 59430, "label" => _x( "Angle Down", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "arrows"), array("css" => "angle-left", "code" => 59563, "label" => _x( "Angle Left", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "arrows"), array("css" => "angle-up", "code" => 59516, "label" => _x( "Angle Up", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "arrows"), array("css" => "arrow-down", "code" => 59489, "label" => _x( "Arrow Down", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "arrows"), array("css" => "arrow-left", "code" => 59487, "label" => _x( "Arrow Left", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "arrows"), array("css" => "arrow-right", "code" => 59488, "label" => _x( "Arrow Right", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "arrows"), array("css" => "arrows", "code" => 60837, "label" => _x( "Arrows", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "arrows"), array("css" => "arrows-h", "code" => 60838, "label" => _x( "Arrows Horizontal", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "arrows"), array("css" => "arrows-v", "code" => 60839, "label" => _x( "Arrows Vertical", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "arrows"), array("css" => "arrow-up", "code" => 59492, "label" => _x( "Arrow Up", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "arrows"), array("css" => "article", "code" => 60841, "label" => _x( "Article", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "article-slash", "code" => 60842, "label" => _x( "Article Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "asterisk", "code" => 60843, "label" => _x( "Asterisk", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "text-formatting"), array("css" => "at", "code" => 59495, "label" => _x( "At", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "text-formatting"), array("css" => "audio-description", "code" => 60845, "label" => _x( "Audio Description", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "accessibility"), array("css" => "award", "code" => 59493, "label" => _x( "Award", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "award-slash", "code" => 59397, "label" => _x( "Award Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "backward", "code" => 60847, "label" => _x( "Backward", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "ball-soccer", "code" => 59393, "label" => _x( "Ball Soccer", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "bars", "code" => 60849, "label" => _x( "Bars", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "bars-2", "code" => 60850, "label" => _x( "Bars 2", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "battery", "code" => 59497, "label" => _x( "Battery", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "battery-empty", "code" => 59499, "label" => _x( "Battery Empty", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "battery-full", "code" => 60853, "label" => _x( "Battery Full", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "battery-quarter", "code" => 60855, "label" => _x( "Battery Quarter", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "battery-slash", "code" => 60856, "label" => _x( "Battery Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "battery-three-quarters", "code" => 60857, "label" => _x( "Battery Three Quarters", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "bell", "code" => 59501, "label" => _x( "Bell", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "bell-plus", "code" => 59557, "label" => _x( "Bell Plus", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "bell-slash", "code" => 59498, "label" => _x( "Bell Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "bluetooth", "code" => 59500, "label" => _x( "Bluetooth", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "technology"), array("css" => "bold", "code" => 59722, "label" => _x( "Bold", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "text-formatting"), array("css" => "bolt", "code" => 60863, "label" => _x( "Bolt", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "book", "code" => 59502, "label" => _x( "Book", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "bookmark", "code" => 59506, "label" => _x( "Bookmark", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "book-open", "code" => 59759, "label" => _x( "Book Open", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "books", "code" => 60867, "label" => _x( "Books", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "box", "code" => 59766, "label" => _x( "Box", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "brand-android", "code" => 60869, "label" => _x( "Android", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-aperture", "code" => 59486, "label" => _x( "Aperture", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-apple", "code" => 60871, "label" => _x( "Apple", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-buddyboss", "code" => 60872, "label" => _x( "BuddyBoss", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-buddyboss-app", "code" => 60873, "label" => _x( "BuddyBoss App", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-buddyboss-bolt", "code" => 60874, "label" => _x( "BuddyBoss Bolt", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-buddypress", "code" => 60875, "label" => _x( "BuddyPress", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-bunnynet", "code" => 59882, "label" => _x( "Bunny Net", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-chrome", "code" => 59523, "label" => _x( "Chrome", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-clubhouse", "code" => 60877, "label" => _x( "Clubhouse", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-codepen", "code" => 60878, "label" => _x( "CodePen", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-dribbble", "code" => 60879, "label" => _x( "Dribbble", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-edge", "code" => 60880, "label" => _x( "Microsoft Edge", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-elementor", "code" => 60881, "label" => _x( "Elementor", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-facebook", "code" => 60882, "label" => _x( "Facebook", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-facebook-f", "code" => 59399, "label" => _x( "Facebook F", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-firefox", "code" => 60883, "label" => _x( "Firefox", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-flickr", "code" => 60884, "label" => _x( "Flickr", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-gamipress", "code" => 60885, "label" => _x( "GamiPress", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-github", "code" => 60886, "label" => _x( "GitHub", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-google", "code" => 60887, "label" => _x( "Google", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-gitlab", "code" => 60888, "label" => _x( "GitLab", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-google-admob", "code" => 60889, "label" => _x( "Google AdMob", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-google-firebase", "code" => 60890, "label" => _x( "Google Firebase", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-google-fonts", "code" => 60891, "label" => _x( "Google Fonts", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-instagram", "code" => 60892, "label" => _x( "Instagram", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-intercom", "code" => 59891, "label" => _x( "Intercom", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-learndash", "code" => 60893, "label" => _x( "LearnDash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-lifterlms", "code" => 60894, "label" => _x( "LifterLMS", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-linkedin", "code" => 60895, "label" => _x( "LinkedIn", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-medium", "code" => 60896, "label" => _x( "Medium", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-meetup", "code" => 59392, "label" => _x( "Meetup", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-memberpress", "code" => 60848, "label" => _x( "MemberPress", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-onesignal", "code" => 59394, "label" => _x( "OneSignal", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-paidmembershipspro", "code" => 60900, "label" => _x( "Paid Memberships Pro", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-pinterest", "code" => 60901, "label" => _x( "Pinterest", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-pusher", "code" => 60902, "label" => _x( "Pusher", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-quora", "code" => 60903, "label" => _x( "Quora", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-react", "code" => 59924, "label" => _x( "React", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-reddit", "code" => 60904, "label" => _x( "Reddit", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-restrictcontentpro", "code" => 60905, "label" => _x( "Restrict Content Pro", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-safari", "code" => 60906, "label" => _x( "Safari", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-skype", "code" => 60907, "label" => _x( "Skype", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-snapchat", "code" => 60908, "label" => _x( "Snapchat", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-slack", "code" => 60909, "label" => _x( "Slack", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-spotify", "code" => 60910, "label" => _x( "Spotify", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-telegram", "code" => 60911, "label" => _x( "Telegram", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-tiktok", "code" => 60912, "label" => _x( "TikTok", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-tumblr", "code" => 60913, "label" => _x( "Tumblr", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-tutorlms", "code" => 60914, "label" => _x( "Tutor LMS", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-twitch", "code" => 60915, "label" => _x( "Twitch", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-twitter", "code" => 60916, "label" => _x( "Twitter", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-vimeo", "code" => 60917, "label" => _x( "Vimeo", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-vk", "code" => 60918, "label" => _x( "VK", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-whatsapp", "code" => 60919, "label" => _x( "WhatsApp", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-wishlistmember", "code" => 60920, "label" => _x( "WishList Member", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-woocommerce", "code" => 60921, "label" => _x( "WooCommerce", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-wordpress", "code" => 60922, "label" => _x( "WordPress", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-xing", "code" => 60923, "label" => _x( "XING", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-x", "code" => 59922, "label" => _x( "X", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-youtube", "code" => 60924, "label" => _x( "YouTube", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "brand-zoom", "code" => 60925, "label" => _x( "Zoom", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "brands"), array("css" => "briefcase", "code" => 59503, "label" => _x( "Briefcase", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "browser", "code" => 60927, "label" => _x( "Browser", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "technology"), array("css" => "browser-code", "code" => 59767, "label" => _x( "Browser Code", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "technology"), array("css" => "browser-terminal", "code" => 60929, "label" => _x( "Browser Terminal", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "technology"), array("css" => "bug", "code" => 60930, "label" => _x( "Bug", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "technology"), array("css" => "bullhorn", "code" => 60931, "label" => _x( "Bullhorn", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "bullseye", "code" => 60932, "label" => _x( "Bullseye", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "calendar", "code" => 59395, "label" => _x( "Calendar", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "camera", "code" => 60934, "label" => _x( "Camera", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "camera-slash", "code" => 59504, "label" => _x( "Camera Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "cancel", "code" => 60936, "label" => _x( "Cancel", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "caret-down", "code" => 60937, "label" => _x( "Caret Down", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "arrows"), array("css" => "caret-left", "code" => 60938, "label" => _x( "Caret Left", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "arrows"), array("css" => "caret-right", "code" => 60939, "label" => _x( "Caret Right", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "arrows"), array("css" => "caret-up", "code" => 60940, "label" => _x( "Caret Up", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "arrows"), array("css" => "car-small", "code" => 59398, "label" => _x( "Car Small", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "cart-slash", "code" => 59890, "label" => _x( "Cart Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "shopping-money"), array("css" => "cast", "code" => 59512, "label" => _x( "Cast", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "certificate", "code" => 59765, "label" => _x( "Certificate", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "chart-area", "code" => 60944, "label" => _x( "Chart Area", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "chart-bar-h", "code" => 60945, "label" => _x( "Chart Bar Horizontal", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "chart-bar-trending", "code" => 60946, "label" => _x( "Chart Bar Trending", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "chart-bar-v", "code" => 59496, "label" => _x( "Chart Bar Vertical", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "chart-line", "code" => 60948, "label" => _x( "Chart Line", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "chart-pie", "code" => 60949, "label" => _x( "Chart Pie", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "check", "code" => 59510, "label" => _x( "Check", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "checkbox", "code" => 59509, "label" => _x( "Checkbox", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "circle", "code" => 59521, "label" => _x( "Circle", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "clipboard", "code" => 59522, "label" => _x( "Clipboard", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "clock", "code" => 59524, "label" => _x( "Clock", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "closed-captioning", "code" => 60955, "label" => _x( "Closed Captioning", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "accessibility"), array("css" => "cloud", "code" => 59717, "label" => _x( "Cloud", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "technology"), array("css" => "cloud-check", "code" => 59892, "label" => _x( "Cloud Check", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "technology"), array("css" => "cloud-download", "code" => 59529, "label" => _x( "Cloud Download", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "technology"), array("css" => "cloud-exclamation", "code" => 59893, "label" => _x( "Cloud Exclamation", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "technology"), array("css" => "cloud-drizzle", "code" => 59526, "label" => _x( "Cloud Drizzle", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "cloud-lightning", "code" => 59525, "label" => _x( "Cloud Lightning", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "cloud-rain", "code" => 60960, "label" => _x( "Cloud Rain", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "cloud-slash", "code" => 59528, "label" => _x( "Cloud Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "technology"), array("css" => "cloud-snow", "code" => 60962, "label" => _x( "Cloud Snow", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "cloud-sync", "code" => 60963, "label" => _x( "Cloud Sync", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "technology"), array("css" => "cloud-times", "code" => 59894, "label" => _x( "Cloud Times", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "technology"), array("css" => "cloud-upload", "code" => 60964, "label" => _x( "Cloud Upload", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "technology"), array("css" => "code", "code" => 60965, "label" => _x( "Code", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "technology"), array("css" => "code-branch", "code" => 60966, "label" => _x( "Code Branch", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "technology"), array("css" => "cog", "code" => 60967, "label" => _x( "Cog", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "cogs", "code" => 60968, "label" => _x( "Cogs", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "coin", "code" => 60969, "label" => _x( "Coin", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "shopping-money"), array("css" => "coins", "code" => 60970, "label" => _x( "Coins", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "shopping-money"), array("css" => "columns", "code" => 60971, "label" => _x( "Columns", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "text-formatting"), array("css" => "command", "code" => 60972, "label" => _x( "Command", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "comment", "code" => 59769, "label" => _x( "Comment", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "comment-activity", "code" => 59751, "label" => _x( "Comment Activity", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "comment-dots", "code" => 60975, "label" => _x( "Comment Dots", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "comment-notification", "code" => 60976, "label" => _x( "Comment Notification", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "comments", "code" => 60977, "label" => _x( "Comments", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "comment-slash", "code" => 60978, "label" => _x( "Comment Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "comment-square", "code" => 60979, "label" => _x( "Comment Square", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "comment-square-dots", "code" => 60980, "label" => _x( "Comment Square Dots", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "comment-square-slash", "code" => 60981, "label" => _x( "Comment Square Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "comments-slash", "code" => 60982, "label" => _x( "Comments Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "comments-square", "code" => 60983, "label" => _x( "Comments Square", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "comments-square-slash", "code" => 60984, "label" => _x( "Comments Square Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "compass", "code" => 60985, "label" => _x( "Compass", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "compress", "code" => 60986, "label" => _x( "Compress", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "copy", "code" => 60987, "label" => _x( "Copy", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "copyright", "code" => 60988, "label" => _x( "Copyright", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "text-formatting"), array("css" => "corner-left", "code" => 60989, "label" => _x( "Corner Left", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "arrows"), array("css" => "corner-right", "code" => 60990, "label" => _x( "Corner Right", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "arrows"), array("css" => "course", "code" => 60991, "label" => _x( "Course", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "course-slash", "code" => 60992, "label" => _x( "Course Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "cpu", "code" => 60993, "label" => _x( "CPU", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "technology"), array("css" => "credit-card", "code" => 60994, "label" => _x( "Credit Card", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "shopping-money"), array("css" => "crop", "code" => 60995, "label" => _x( "Crop", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "text-formatting"), array("css" => "crosshairs", "code" => 60996, "label" => _x( "Crosshairs", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "crown", "code" => 59895, "label" => _x( "Crown", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "cube", "code" => 59507, "label" => _x( "Cube", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "currency-bitcoin", "code" => 60998, "label" => _x( "Currency Bitcoin", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "shopping-money"), array("css" => "currency-crypto", "code" => 60999, "label" => _x( "Currency Crypto", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "shopping-money"), array("css" => "currency-dollar", "code" => 61000, "label" => _x( "Currency Dollar", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "shopping-money"), array("css" => "currency-euro", "code" => 61001, "label" => _x( "Currency Euro", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "shopping-money"), array("css" => "currency-lira", "code" => 61002, "label" => _x( "Currency Lira", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "shopping-money"), array("css" => "currency-peso", "code" => 61003, "label" => _x( "Currency Peso", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "shopping-money"), array("css" => "currency-pound", "code" => 61004, "label" => _x( "Currency Pound", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "shopping-money"), array("css" => "currency-ruble", "code" => 61005, "label" => _x( "Currency Ruble", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "shopping-money"), array("css" => "currency-rupee", "code" => 61006, "label" => _x( "Currency Rupee", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "shopping-money"), array("css" => "currency-won", "code" => 61007, "label" => _x( "Currency Won", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "shopping-money"), array("css" => "currency-yen", "code" => 61008, "label" => _x( "Currency Yen", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "shopping-money"), array("css" => "cut", "code" => 61009, "label" => _x( "Cut", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "text-formatting"), array("css" => "database", "code" => 61010, "label" => _x( "Database", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "technology"), array("css" => "delete-tag", "code" => 61011, "label" => _x( "Delete Tag", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "desktop", "code" => 61012, "label" => _x( "Desktop", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "technology"), array("css" => "divide", "code" => 61013, "label" => _x( "Divide", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "text-formatting"), array("css" => "dot-circle", "code" => 61014, "label" => _x( "Dot Circle", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "download", "code" => 61015, "label" => _x( "Download", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "droplet", "code" => 61016, "label" => _x( "Droplet", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "duplicate", "code" => 61017, "label" => _x( "Duplicate", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "edit", "code" => 61018, "label" => _x( "Edit", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "eject", "code" => 61019, "label" => _x( "Eject", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "ellipsis-h", "code" => 61020, "label" => _x( "Ellipsis Horizontal", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "emoticon-confused", "code" => 59896, "label" => _x( "Emoticon Confused", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "emoticon-frown", "code" => 61022, "label" => _x( "Emoticon Frown", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "emoticon-smile", "code" => 61023, "label" => _x( "Emoticon Smile", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "emoticon-wink", "code" => 61024, "label" => _x( "Emoticon Wink", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "emotion-laugh", "code" => 61025, "label" => _x( "Emotion Laugh", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "envelope", "code" => 61026, "label" => _x( "Envelope", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "envelope-open", "code" => 61027, "label" => _x( "Envelope Open", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "exchange", "code" => 61028, "label" => _x( "Exchange", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "exclamation", "code" => 61029, "label" => _x( "Exclamation", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "alerts"), array("css" => "exclamation-triangle", "code" => 61030, "label" => _x( "Exclamation Triangle", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "alerts"), array("css" => "expand", "code" => 61031, "label" => _x( "Expand", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "external-link", "code" => 61032, "label" => _x( "External Link", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "eye", "code" => 61033, "label" => _x( "Eye", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "eye-slash", "code" => 61034, "label" => _x( "Eye Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "fast-backward", "code" => 61035, "label" => _x( "Fast Backward", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "fast-forward", "code" => 61036, "label" => _x( "Fast Forward", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "feather", "code" => 61037, "label" => _x( "Feather", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "file", "code" => 61038, "label" => _x( "File", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-album", "code" => 61039, "label" => _x( "File Album", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-archive", "code" => 61040, "label" => _x( "File Archive", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-article", "code" => 59917, "label" => _x( "File Article", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-attach", "code" => 59776, "label" => _x( "File Attach", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-audio", "code" => 61042, "label" => _x( "File Audio", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-bookmark", "code" => 61043, "label" => _x( "File Bookmark", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-checklist", "code" => 61044, "label" => _x( "File Checklist", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-cloud", "code" => 61045, "label" => _x( "File Cloud", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-code", "code" => 61046, "label" => _x( "File Code", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-contact", "code" => 61047, "label" => _x( "File Contact", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-css", "code" => 61048, "label" => _x( "File CSS", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-design", "code" => 61049, "label" => _x( "File Design", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-doc", "code" => 61050, "label" => _x( "File Doc", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-dollar", "code" => 61051, "label" => _x( "File Dollar", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-download", "code" => 61052, "label" => _x( "File Download", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-excel", "code" => 61053, "label" => _x( "File Excel", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-exclamation", "code" => 61054, "label" => _x( "File Exclamation", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-export", "code" => 61055, "label" => _x( "File Export", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-font", "code" => 61056, "label" => _x( "File Font", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-gif", "code" => 61057, "label" => _x( "File GIF", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-globe", "code" => 61058, "label" => _x( "File Globe", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-image", "code" => 61059, "label" => _x( "File Image", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-import", "code" => 61060, "label" => _x( "File Import", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-info", "code" => 61061, "label" => _x( "File Info", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-list", "code" => 61062, "label" => _x( "File List", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-list-numeric", "code" => 61063, "label" => _x( "File List Numeric", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-minus", "code" => 61064, "label" => _x( "File Minus", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-mobile", "code" => 61065, "label" => _x( "File Mobile", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-pdf", "code" => 61066, "label" => _x( "File PDF", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-plus", "code" => 61067, "label" => _x( "File Plus", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-powerpoint", "code" => 61068, "label" => _x( "File Powerpoint", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-presentation", "code" => 61069, "label" => _x( "File Presentation", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-question", "code" => 61070, "label" => _x( "File Question", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-rss", "code" => 61071, "label" => _x( "File RSS", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-share", "code" => 61072, "label" => _x( "File Share", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-signature", "code" => 61073, "label" => _x( "File Signature", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-spreadsheet", "code" => 61074, "label" => _x( "File Spreadsheet", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-text", "code" => 61075, "label" => _x( "File Text", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-times", "code" => 61076, "label" => _x( "File Times", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-upload", "code" => 61077, "label" => _x( "File Upload", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-vector", "code" => 61078, "label" => _x( "File Vector", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-video", "code" => 61079, "label" => _x( "File Video", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "file-word", "code" => 61080, "label" => _x( "File Word", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "film", "code" => 61081, "label" => _x( "Film", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "filter", "code" => 61082, "label" => _x( "Filter", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "filter-alt", "code" => 61083, "label" => _x( "Filter Alt", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "flag", "code" => 61084, "label" => _x( "Flag", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "flask", "code" => 61085, "label" => _x( "Flask", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "folder", "code" => 61086, "label" => _x( "Folder", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "folder-alt", "code" => 61087, "label" => _x( "Folder Alt", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "folder-alt-slash", "code" => 61088, "label" => _x( "Folder Alt Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "folder-download", "code" => 59897, "label" => _x( "Folder Download", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "folder-move", "code" => 59898, "label" => _x( "Folder Move", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "folder-open", "code" => 61089, "label" => _x( "Folder Open", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "folder-plus", "code" => 61090, "label" => _x( "Folder Plus", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "folder-slash", "code" => 61091, "label" => _x( "Folder Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "folder-upload", "code" => 59899, "label" => _x( "Folder Upload", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "font", "code" => 61092, "label" => _x( "Font", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "text-formatting"), array("css" => "forest", "code" => 61093, "label" => _x( "Forest", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "forward", "code" => 61094, "label" => _x( "Forward", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "gem", "code" => 61095, "label" => _x( "Gem", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "gif", "code" => 61096, "label" => _x( "GIF", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "gift", "code" => 61097, "label" => _x( "Gift", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "globe", "code" => 61098, "label" => _x( "Globe", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "globe-alt", "code" => 59918, "label" => _x( "Globe Alt", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "globe-layers", "code" => 61099, "label" => _x( "Globe Layers", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "graduation-cap", "code" => 61100, "label" => _x( "Graduation Cap", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "grid-large", "code" => 61101, "label" => _x( "Grid Large", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "grid-small", "code" => 61102, "label" => _x( "Grid Small", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "grip-h", "code" => 59900, "label" => _x( "Grid H", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "grip-v", "code" => 59901, "label" => _x( "Grid V", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "hashtag", "code" => 61103, "label" => _x( "Hashtag", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "hand-pointer", "code" => 59884, "label" => _x( "Hand Pointer", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "heading", "code" => 61104, "label" => _x( "Heading", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "text-formatting"), array("css" => "headphones", "code" => 61105, "label" => _x( "Headphones", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "heart", "code" => 61106, "label" => _x( "Heart", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "home", "code" => 61107, "label" => _x( "Home", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "image", "code" => 61108, "label" => _x( "Image", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "images", "code" => 61109, "label" => _x( "Images", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "image-move", "code" => 59902, "label" => _x( "Image Move", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "image-plus", "code" => 59885, "label" => _x( "Image Plus", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "image-slash", "code" => 61110, "label" => _x( "Image Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "images-slash", "code" => 61111, "label" => _x( "Images Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "image-video", "code" => 61112, "label" => _x( "Image Video", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "image-video-slash", "code" => 61113, "label" => _x( "Image Video Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "inbox", "code" => 61114, "label" => _x( "Inbox", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "indent", "code" => 61115, "label" => _x( "Indent", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "text-formatting"), array("css" => "info", "code" => 61116, "label" => _x( "Info", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "alerts"), array("css" => "info-triangle", "code" => 61117, "label" => _x( "Info Triangle", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "alerts"), array("css" => "italic", "code" => 61118, "label" => _x( "Italic", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "text-formatting"), array("css" => "key", "code" => 61119, "label" => _x( "Key", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "keyboard", "code" => 61120, "label" => _x( "Keyboard", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "key-slash", "code" => 61121, "label" => _x( "Key Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "laptop", "code" => 61122, "label" => _x( "Laptop", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "technology"), array("css" => "layers", "code" => 61123, "label" => _x( "Layers", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "technology"), array("css" => "less-than", "code" => 61124, "label" => _x( "Less Than", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "less-than-equal", "code" => 61125, "label" => _x( "Less Than Equal", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "life-ring", "code" => 61126, "label" => _x( "Life Ring", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "lightbulb", "code" => 60933, "label" => _x( "Lightbulb", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "link", "code" => 61128, "label" => _x( "Link", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "list", "code" => 61129, "label" => _x( "List", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "text-formatting"), array("css" => "list-number", "code" => 61130, "label" => _x( "List Number", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "text-formatting"), array("css" => "location-arrow", "code" => 61131, "label" => _x( "Location Arrow", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "lock-alt", "code" => 61133, "label" => _x( "Lock Alt", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "lock-alt-open", "code" => 61134, "label" => _x( "Lock Alt Open", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "lock-open", "code" => 61135, "label" => _x( "Lock Open", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "map", "code" => 61136, "label" => _x( "Map", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "map-marker", "code" => 61137, "label" => _x( "Map Marker", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "marketplace", "code" => 61138, "label" => _x( "Marketplace", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "maximize", "code" => 61139, "label" => _x( "Maximize", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "membership-card", "code" => 61140, "label" => _x( "Membership Card", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "shopping-money"), array("css" => "merge", "code" => 61141, "label" => _x( "Merge", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "microphone", "code" => 61142, "label" => _x( "Microphone", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "microphone-slash", "code" => 61143, "label" => _x( "Microphone Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "minimize", "code" => 59886, "label" => _x( "Minimize", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "minus", "code" => 61144, "label" => _x( "Minus", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "mobile", "code" => 61145, "label" => _x( "Mobile", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "technology"), array("css" => "mobile-slash", "code" => 59903, "label" => _x( "Mobile Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "technology"), array("css" => "money", "code" => 61146, "label" => _x( "Money", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "shopping-money"), array("css" => "moon", "code" => 61147, "label" => _x( "Moon", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "more-than", "code" => 61148, "label" => _x( "More Than", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "more-than-equal", "code" => 61149, "label" => _x( "More Than Equal", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "mouse-pointer", "code" => 61150, "label" => _x( "Mouse Pointer", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "music", "code" => 61151, "label" => _x( "Music", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "music-note", "code" => 61152, "label" => _x( "Music Note", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "music-note-slash", "code" => 61153, "label" => _x( "Music Note Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "music-slash", "code" => 61154, "label" => _x( "Music Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "newspaper", "code" => 59919, "label" => _x( "Newspaper", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "outdent", "code" => 61155, "label" => _x( "Outdent", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "text-formatting"), array("css" => "oxtagon", "code" => 61156, "label" => _x( "Oxtagon", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "package", "code" => 61157, "label" => _x( "Package", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "paperclip", "code" => 61158, "label" => _x( "Paperclip", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "paper-plane", "code" => 61159, "label" => _x( "Paper Plane", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "paragraph", "code" => 61160, "label" => _x( "Paragraph", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "text-formatting"), array("css" => "paste", "code" => 61161, "label" => _x( "Paste", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "pause", "code" => 61162, "label" => _x( "Pause", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "pen", "code" => 61163, "label" => _x( "Pen", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "pencil", "code" => 61164, "label" => _x( "Pencil", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "percentage", "code" => 61165, "label" => _x( "Percentage", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "phone", "code" => 61166, "label" => _x( "Phone", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "phone-call", "code" => 61167, "label" => _x( "Phone Call", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "phone-forwarded", "code" => 61168, "label" => _x( "Phone Forwarded", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "phone-incoming", "code" => 61169, "label" => _x( "Phone Incoming", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "phone-missed", "code" => 61170, "label" => _x( "Phone Missed", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "phone-slash", "code" => 61171, "label" => _x( "Phone Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "picture-in-picture", "code" => 59888, "label" => _x( "Picture In Picture", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "pin", "code" => 61172, "label" => _x( "Pin", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "pin-star", "code" => 61173, "label" => _x( "Pin Star", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "pizza-slice", "code" => 61174, "label" => _x( "Pizza Slice", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "play", "code" => 61175, "label" => _x( "Play", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "plug", "code" => 61176, "label" => _x( "Plug", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "plus", "code" => 61177, "label" => _x( "Plus", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "pocket", "code" => 61178, "label" => _x( "Pocket", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "poll", "code" => 61179, "label" => _x( "Poll", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "poll-h", "code" => 61180, "label" => _x( "Poll Horizontal", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "power-on", "code" => 61181, "label" => _x( "Power On", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "printer", "code" => 61182, "label" => _x( "Printer", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "question", "code" => 61183, "label" => _x( "Question", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "alerts"), array("css" => "question-triangle", "code" => 59477, "label" => _x( "Question Triangle", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "alerts"), array("css" => "quiz", "code" => 61185, "label" => _x( "Quiz", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "quote-left", "code" => 61186, "label" => _x( "Quote Left", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "text-formatting"), array("css" => "quote-right", "code" => 61187, "label" => _x( "Quote Right", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "text-formatting"), array("css" => "radio", "code" => 61188, "label" => _x( "Radio", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "random", "code" => 61189, "label" => _x( "Random", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "receipt", "code" => 61190, "label" => _x( "Receipt", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "shopping-money"), array("css" => "redo", "code" => 61191, "label" => _x( "Redo", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "registered", "code" => 61192, "label" => _x( "Registered", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "repeat", "code" => 61193, "label" => _x( "Repeat", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "reply", "code" => 61194, "label" => _x( "Reply", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "reply-all", "code" => 61195, "label" => _x( "Reply All", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "rocket", "code" => 61196, "label" => _x( "Rocket", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "rss", "code" => 61197, "label" => _x( "Rss", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "save", "code" => 61198, "label" => _x( "Save", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "scissors", "code" => 61199, "label" => _x( "Scissors", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "search", "code" => 61200, "label" => _x( "Search", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "search-minus", "code" => 61201, "label" => _x( "Search Minus", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "search-plus", "code" => 61202, "label" => _x( "Search Plus", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "search-times", "code" => 59925, "label" => _x( "Search Times", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "server", "code" => 61203, "label" => _x( "Server", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "technology"), array("css" => "share", "code" => 61204, "label" => _x( "Share", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "share-dots", "code" => 61205, "label" => _x( "Share Dots", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "share-square", "code" => 61206, "label" => _x( "Share Square", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "shield", "code" => 61207, "label" => _x( "Shield", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "shield-half", "code" => 61208, "label" => _x( "Shield Half", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "shopping-bag", "code" => 61209, "label" => _x( "Shopping Bag", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "shopping-money"), array("css" => "shopping-basket", "code" => 61210, "label" => _x( "Shopping Basket", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "shopping-money"), array("css" => "shopping-cart", "code" => 61211, "label" => _x( "Shopping Cart", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "shopping-money"), array("css" => "sidebar", "code" => 59552, "label" => _x( "Sidebar", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "signal-1", "code" => 61213, "label" => _x( "Signal 1", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "signal-2", "code" => 61214, "label" => _x( "Signal 2", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "signal-3", "code" => 61215, "label" => _x( "Signal 3", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "signal-full", "code" => 61216, "label" => _x( "Signal Full", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "signal-slash", "code" => 61217, "label" => _x( "Signal Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "sign-in", "code" => 61218, "label" => _x( "Sign In", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "sign-out", "code" => 61219, "label" => _x( "Sign Out", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "sliders-h", "code" => 61220, "label" => _x( "Sliders Horizontal", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "sliders-v", "code" => 61221, "label" => _x( "Sliders Vertical", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "sort", "code" => 61222, "label" => _x( "Sort", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "sort-alpha-down", "code" => 61223, "label" => _x( "Sort Alpha Down", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "sort-alpha-up", "code" => 61224, "label" => _x( "Sort Alpha Up", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "sort-amount-down", "code" => 61225, "label" => _x( "Sort Amount Down", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "sort-amount-up", "code" => 61226, "label" => _x( "Sort Amount Up", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "sort-down", "code" => 61227, "label" => _x( "Sort Down", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "sort-numeric-down", "code" => 61228, "label" => _x( "Sort Numeric Down", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "sort-numeric-up", "code" => 61229, "label" => _x( "Sort Numeric Up", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "sort-up", "code" => 61230, "label" => _x( "Sort Up", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "speaker", "code" => 61231, "label" => _x( "Speaker", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "spinner", "code" => 61232, "label" => _x( "Spinner", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "spinner-alt", "code" => 59887, "label" => _x( "Spinner Alt", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "spinner-third", "code" => 61233, "label" => _x( "Spinner Third", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "split", "code" => 59904, "label" => _x( "Split", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "star", "code" => 61234, "label" => _x( "Star", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "star-half", "code" => 61235, "label" => _x( "Star Half", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "star-slash", "code" => 59905, "label" => _x( "Star Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "step-backward", "code" => 61236, "label" => _x( "Step Backward", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "step-forward", "code" => 61237, "label" => _x( "Step Forward", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "stop", "code" => 61238, "label" => _x( "Stop", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "stopwatch", "code" => 59906, "label" => _x( "Stopwatch", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "strikethrough", "code" => 59907, "label" => _x( "Strikethrough", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "text-formatting"), array("css" => "sun", "code" => 61239, "label" => _x( "Sun", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "sunrise", "code" => 61240, "label" => _x( "Sunrise", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "sync", "code" => 61241, "label" => _x( "Sync", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "tablet", "code" => 61242, "label" => _x( "Tablet", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "technology"), array("css" => "tag", "code" => 61243, "label" => _x( "Tag", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "tags", "code" => 61244, "label" => _x( "Tags", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "terminal", "code" => 61245, "label" => _x( "Terminal", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "technology"), array("css" => "text", "code" => 61246, "label" => _x( "Text", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "text-formatting"), array("css" => "thermometer", "code" => 61247, "label" => _x( "Thermometer", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "thumbs-down", "code" => 61248, "label" => _x( "Thumbs Down", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "thumbs-up", "code" => 61249, "label" => _x( "Thumbs Up", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "times", "code" => 59432, "label" => _x( "Times", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "alerts"), array("css" => "times-triangle", "code" => 61251, "label" => _x( "Times Triangle", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "alerts"), array("css" => "toggle-off", "code" => 61252, "label" => _x( "Toggle Off", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "toggle-on", "code" => 61253, "label" => _x( "Toggle On", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "tools", "code" => 61254, "label" => _x( "Tools", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "trademark", "code" => 61255, "label" => _x( "Trademark", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "text-formatting"), array("css" => "trash", "code" => 61256, "label" => _x( "Trash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "trash-restore", "code" => 61257, "label" => _x( "Trash Restore", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "trending-down", "code" => 61258, "label" => _x( "Trending Down", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "trending-up", "code" => 61259, "label" => _x( "Trending Up", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "trophy", "code" => 61260, "label" => _x( "Trophy", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "trophy-slash", "code" => 59908, "label" => _x( "Trophy Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "tv", "code" => 61261, "label" => _x( "TV", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "technology"), array("css" => "umbrella", "code" => 61262, "label" => _x( "Umbrella", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "underline", "code" => 61263, "label" => _x( "Underline", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "text-formatting"), array("css" => "undo", "code" => 61264, "label" => _x( "Undo", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "unlink", "code" => 61265, "label" => _x( "Unlink", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "user", "code" => 61266, "label" => _x( "User", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "users-people"), array("css" => "user-arrow-down", "code" => 59913, "label" => _x( "User Arrow Down", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "users-people"), array("css" => "user-arrow-up", "code" => 59914, "label" => _x( "User Arrow Up", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "users-people"), array("css" => "user-avatar", "code" => 61267, "label" => _x( "User Avatar", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "users-people"), array("css" => "user-badge", "code" => 61268, "label" => _x( "User Badge", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "users-people"), array("css" => "user-cancel", "code" => 59920, "label" => _x( "User Cancel", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "users-people"), array("css" => "user-card", "code" => 61269, "label" => _x( "User Card", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "users-people"), array("css" => "user-check", "code" => 61270, "label" => _x( "User Check", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "users-people"), array("css" => "user-clock", "code" => 61271, "label" => _x( "User Clock", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "users-people"), array("css" => "user-cog", "code" => 61272, "label" => _x( "User Cog", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "users-people"), array("css" => "user-crown", "code" => 59915, "label" => _x( "User Crown", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "users-people"), array("css" => "user-edit", "code" => 61273, "label" => _x( "User Edit", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "users-people"), array("css" => "user-friends", "code" => 61274, "label" => _x( "User Friends", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "users-people"), array("css" => "user-friends-alt", "code" => 61275, "label" => _x( "User Friends Alt", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "users-people"), array("css" => "user-friends-alt-slash", "code" => 61276, "label" => _x( "User Friends Alt Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "users-people"), array("css" => "user-friends-plus", "code" => 59916, "label" => _x( "User Friends Plus", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "users-people"), array("css" => "user-friends-slash", "code" => 61277, "label" => _x( "User Friends Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "users-people"), array("css" => "user-info", "code" => 59921, "label" => _x( "User Info", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "users-people"), array("css" => "user-lock", "code" => 61278, "label" => _x( "User Lock", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "users-people"), array("css" => "user-minus", "code" => 61279, "label" => _x( "User Minus", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "users-people"), array("css" => "user-plus", "code" => 61280, "label" => _x( "User Plus", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "users-people"), array("css" => "users", "code" => 59741, "label" => _x( "Users", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "users-people"), array("css" => "user-slash", "code" => 61282, "label" => _x( "User Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "users-people"), array("css" => "users-slash", "code" => 61283, "label" => _x( "Users Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "users-people"), array("css" => "user-times", "code" => 61284, "label" => _x( "User Times", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "users-people"), array("css" => "u-turn-down", "code" => 59909, "label" => _x( "U Turn Down", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "arrows"), array("css" => "u-turn-left", "code" => 59910, "label" => _x( "U Turn Left", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "arrows"), array("css" => "u-turn-right", "code" => 59911, "label" => _x( "U Turn Right", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "arrows"), array("css" => "u-turn-up", "code" => 59912, "label" => _x( "U Turn Up", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "arrows"), array("css" => "video", "code" => 61285, "label" => _x( "Video", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "video-plus", "code" => 59889, "label" => _x( "Video Plus", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "video-slash", "code" => 61286, "label" => _x( "Video Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "media-files"), array("css" => "voicemail", "code" => 61287, "label" => _x( "Voicemail", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "volume-down", "code" => 61288, "label" => _x( "Volume Down", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "volume-mute", "code" => 61289, "label" => _x( "Volume Mute", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "volume-off", "code" => 61290, "label" => _x( "Volume Off", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "volume-slash", "code" => 61291, "label" => _x( "Volume Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "volume-up", "code" => 61292, "label" => _x( "Volume Up", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "wallet", "code" => 61293, "label" => _x( "Wallet", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "shopping-money"), array("css" => "watch", "code" => 61294, "label" => _x( "Watch", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "wheelchair", "code" => 61295, "label" => _x( "Wheelchair", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "accessibility"), array("css" => "wifi", "code" => 61296, "label" => _x( "Wi-Fi", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "wifi-slash", "code" => 61297, "label" => _x( "Wi-Fi Slash", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "wind", "code" => 61298, "label" => _x( "Wind", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "objects"), array("css" => "wrench", "code" => 61299, "label" => _x( "Wrench", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "battery-half", "code" => 60854, "label" => _x( "Battery Half", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "upload", "code" => 59880, "label" => _x( "Upload", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "ellipsis-v", "code" => 61021, "label" => _x( "Ellipsis Vertical", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "lock", "code" => 61132, "label" => _x( "Lock", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "attach", "code" => 59878, "label" => _x( "Attach", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "thumbtack", "code" => 59396, "label" => _x( "Thumbtack", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "thumbtack-star", "code" => 59881, "label" => _x( "Thumbtack Star", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces"), array("css" => "thumbtack-times", "code" => 59923, "label" => _x( "Thumbtack Times", "BuddyBoss Pro", "buddyboss-pro" ), "group" => "interfaces")));

?>