window.bp=window.bp||{},function(e){bp.Reaction_Admin={start:function(){this.setupGlobals(),this.addListeners()},setupGlobals:function(){e(window).on("load",function(){e(".bb_emotions_list").sortable({cursor:"move",items:"> div:not(.bb_emotions_item_action)",update:function(){e(".bb_emotions_list input:checkbox:first").prop("checked",!0)}})}),bp.Reaction_Admin.delete_emotion="",bp.Reaction_Admin.remove_emotion_ajax_request=null,bp.Reaction_Admin.footer_migration_ajax_request=null,bp.Reaction_Admin.switch_migration_ajax_request=null,bp.Reaction_Admin.modal_loader='<div class="bbpro-modal-box_loader"><span class="bb-icons bb-icon-spinner animate-spin"></span></div>',bp.Reaction_Admin.auto_refresh_interval=null,"undefined"==typeof bbProAddNewEmotionPlaceholder&&"undefined"!=typeof wp&&(window.bbProAddNewEmotionPlaceholder=wp.template("bb-pro-add-new-emotion-placeholder"))},addListeners:function(){e(document).on("click",".bb_emotions_item .bb_emotions_actions_enable input",this.disableEmotion),e(document).on("click",".bbpro-modal-box #bbpro_icon_modal_close",this.closeMigrationModal),e(document).on("click","#bbpro_migration_wizard .next_migration_wizard",this.migrationWizardNext),e(document).on("click","#bbpro_migration_wizard .migration_wizard_prev",this.migrationWizardPrevious),e(document).on("change",'#bp_reaction_settings_section input[name="bb_reaction_mode"]',this.onChangeReactionMode),e(document).on("click",".bb_emotions_item .bb_emotions_actions_remove",this.onRemoveEmotion),e(document).on("click","#bbpro_reaction_delete_confirmation .bb-pro-reaction-delete-emotion",this.onDeleteEmotion),e(document).on("click","#bbpro_reaction_delete_confirmation .bb-pro-reaction-cancel-delete-emotion",this.onCancelDeleteEmotion),e(document).on("change","#bbpro_migration_wizard .migrate_single_emotion_input",this.onChangeWizardEmotion),e(document).on("click","#bbpro_migration_wizard .cancel_migration_wizard",this.onCancelWizardEmotion),e(document).on("change","#bbpro_migration_wizard #migrate_all_emotions",this.migrationEmotionSelectAll),e(document).on("change","#bbpro_migration_wizard #migration_emotion_select",this.migrationEmotionSelect),e(document).on("click","#bp_reaction_settings_section .footer-reaction-migration-wizard",this.openFooterMigrationWizard),e(document).on("click","#bbpro_migration_wizard .footer_next_wizard_screen",this.openFooterNextMigrationWizard),e(document).on("click","#bbpro_migration_wizard .start_migration_wizard",this.submitFooterNextMigrationWizard),e(document).on("click","#bb-pro-reaction-migration-exists-notice .reaction-start-conversion",this.migrationStartConversion),e(document).on("click","#bb-pro-reaction-migration-exists-notice .reaction-do-later",this.migrationDoLater),e(document).on("input","#bb-reaction-button-text",function(t){t.stopImmediatePropagation();t=e(this).val().substring(0,12);e(this).val(t),e(this).siblings(".bb-reaction-button-text-limit").children("span").text(t.length)}),e(document).on("focus","#bb-reaction-button-text",function(){e(this).parent(".bb-reaction-button-label").find(".bb-reaction-button-text-limit").addClass("active")}),e(document).on("blur","#bb-reaction-button-text",function(){e(this).parent(".bb-reaction-button-label").find(".bb-reaction-button-text-limit").removeClass("active")}),e(document).on("click",".bb-pro-reaction-notice.loading .recheck-status",this.recheckStatus),e(document).on("click",".bb-pro-reaction-notice.loading .stop-migration",this.stopMigration),e(document).on("click",".bb-pro-reaction-notice.success .close-reaction-notice",this.hideSuccessNotice),"inprogress"===bbReactionAdminVars.migration_status?this.auto_refresh_interval=setInterval(function(){location.reload()},3e5):clearInterval(this.auto_refresh_interval)},submitSettingForm:function(){e('.buddyboss_page_bp-settings .wrap form .submit input[type="submit"]').trigger("click")},getErrorNotice:function(t){return'<div class="bb-pro-reaction-notice error"><p>'+t+"</p></div>"},disableEmotion:function(){e(this).closest(".bb_emotions_item").toggleClass("is-disabled")},hideNoticeElement:function(){var t=e(".bb-pro-reaction-notices");""===e.trim(t.find("td").html())&&t.addClass("bp-hide")},closeMigrationModal:function(){e(".bbpro-modal-box").hide(),e("#migration_action").val("no"),e(".bb-reaction-delete-modal__content").html(bp.Reaction_Admin.modal_loader),bp.Reaction_Admin.remove_emotion_ajax_request&&bp.Reaction_Admin.remove_emotion_ajax_request.abort(),e("#bbpro_migration_wizard .wizard-label").html(bbReactionAdminVars.wizard_label),e("#bbpro_migration_wizard .modal-content").html(bp.Reaction_Admin.modal_loader),bp.Reaction_Admin.footer_migration_ajax_request&&bp.Reaction_Admin.footer_migration_ajax_request.abort(),bp.Reaction_Admin.switch_migration_ajax_request&&bp.Reaction_Admin.switch_migration_ajax_request.abort()},migrationWizardNext:function(t){t.preventDefault(),e(this).hasClass("disabled")||e("#bbpro_migration_wizard").find(".bbpro_migration_wizard_screens.active").removeClass("active").next(".bbpro_migration_wizard_screens").addClass("active")},migrationWizardPrevious:function(t){t.preventDefault(),e(this).hasClass("disabled")||e("#bbpro_migration_wizard").find(".bbpro_migration_wizard_screens.active").removeClass("active").prev(".bbpro_migration_wizard_screens").addClass("active")},onChangeReactionMode:function(){var t="",o="";e(this).is(":checked")&&(t=e(this).val(),o=e(this).data("notice")),"likes"===t?e(".bb_emotions_list_row, .bb_reaction_button_row").addClass("bp-hide"):"emotions"===t&&e(".bb_emotions_list_row, .bb_reaction_button_row").removeClass("bp-hide"),""!==o&&e(".bb-reaction-mode-description").html(o)},onRemoveEmotion:function(t){t.preventDefault();var t=e(this).closest(".bb_emotions_item"),o=t.data("reaction-id");bp.Reaction_Admin.delete_emotion=t,0<jQuery.trim(o).length?(e("#bbpro_reaction_delete_confirmation").css("display","block"),bp.Reaction_Admin.remove_emotion_ajax_request=e.ajax({url:bbReactionAdminVars.ajax_url,data:{action:"bb_pro_reaction_check_delete_emotion",emotion_id:o,nonce:bbReactionAdminVars.nonce.check_delete_emotion},method:"POST"}).done(function(t){var o=t.data;!0===t.success&&void 0!==o.content?e(".bb-reaction-delete-modal__content").html(o.content):void 0!==o.message&&0<o.message.length&&e(".bb-reaction-delete-modal__content").html(bp.Reaction_Admin.getErrorNotice(o.message))})):(bp.Reaction_Admin.delete_emotion.remove(),bp.Reaction_Admin.delete_emotion="",e(".bb_emotions_list").append(window.bbProAddNewEmotionPlaceholder()))},onDeleteEmotion:function(t){t.preventDefault(),bp.Reaction_Admin.delete_emotion.remove(),bp.Reaction_Admin.delete_emotion="",bp.Reaction_Admin.submitSettingForm()},onCancelDeleteEmotion:function(t){t.preventDefault(),bp.Reaction_Admin.closeMigrationModal()},onChangeWizardEmotion:function(){0<e(".migrate_single_emotion_input:checked").map(function(){return e(this).val()}).get().length?e(".footer_next_wizard_screen").removeClass("disabled"):e(".footer_next_wizard_screen").addClass("disabled")},onCancelWizardEmotion:function(t){t.preventDefault(),bp.Reaction_Admin.closeMigrationModal()},migrationEmotionSelectAll:function(){!0===e(this).prop("checked")?e(this).closest(".bbpro_migration_wizard_screens").find(".migrate_emotion_input").prop("checked",!0).not("#migrate_all_emotions").prop("disabled",!0):e(this).closest(".bbpro_migration_wizard_screens").find(".migrate_emotion_input").prop("checked",!1).prop("disabled",!1),bp.Reaction_Admin.onChangeWizardEmotion()},migrationEmotionSelect:function(){void 0!==e(this).val().length&&0<e(this).val().length?e(".footer_next_wizard_screen").removeClass("disabled"):e(".footer_next_wizard_screen").addClass("disabled")},openFooterMigrationWizard:function(t){t.preventDefault(),e("#migration_action").val("footer"),e("#bbpro_migration_wizard").css("display","block"),bp.Reaction_Admin.footer_migration_ajax_request=e.ajax({url:bbReactionAdminVars.ajax_url,data:{action:"bb_pro_reaction_footer_migration",nonce:bbReactionAdminVars.nonce.footer_migration},method:"POST"}).done(function(t){var o=t.data;!0===t.success&&void 0!==o.content?e("#bbpro_migration_wizard .modal-content").html(o.content):void 0!==o.message&&0<o.message.length&&e("#bbpro_migration_wizard .modal-content").html(bp.Reaction_Admin.getErrorNotice(o.message))})},openFooterNextMigrationWizard:function(t){t.preventDefault();var o=e(".migrate_single_emotion_input:checked").map(function(){return e(this).data("count")}).get().reduce(function(t,o){return t+o},0),i=e("#migration_emotion_select option:selected").text();e(this).parents(".modal-content").find(".from-reactions-count").html(bp.BB_Pro_Admin.getNumberFormat(o)),e(this).parents(".modal-content").find(".to-reactions-label").html(i),bp.Reaction_Admin.migrationWizardNext(t)},submitFooterNextMigrationWizard:function(t){t.preventDefault(),e(this).css("pointer-events","none"),e(".cancel_migration_wizard").prop("disabled",!0),bp.Reaction_Admin.submitSettingForm()},migrationStartConversion:function(t){t.preventDefault();var i=e(t.currentTarget);e("#migration_action").val("switch"),e("#bbpro_migration_wizard").css("display","block"),bp.Reaction_Admin.footer_migration_ajax_request=e.ajax({url:bbReactionAdminVars.ajax_url,data:{action:"bb_pro_reaction_migration_start_conversion",nonce:bbReactionAdminVars.nonce.migration_start_conversion},method:"POST"}).done(function(t){var o=t.data;void 0!==o.label&&e("#bbpro_migration_wizard .wizard-label").html(o.label),!0===t.success&&void 0!==o.content?e("#bbpro_migration_wizard .modal-content").html(o.content):void 0!==o.message&&0<o.message.length&&e("#bbpro_migration_wizard .modal-content").html(bp.Reaction_Admin.getErrorNotice(o.message)),void 0!==o.total_reactions&&(i.parents("#bb-pro-reaction-migration-exists-notice").find(".reaction-notice-count").html(bp.BB_Pro_Admin.getNumberFormat(o.total_reactions)),bp.Reaction_Admin.hideNoticeElement()),void 0!==o.is_notice_dismissed&&!0===o.is_notice_dismissed&&(i.parents("#bb-pro-reaction-migration-exists-notice").remove(),bp.Reaction_Admin.hideNoticeElement())})},migrationDoLater:function(t){t.preventDefault();var o=e("#bb-pro-reaction-migration-exists-notice");e(t.currentTarget).addClass("loading"),bp.Reaction_Admin.remove_emotion_ajax_request=e.ajax({url:bbReactionAdminVars.ajax_url,method:"POST",data:{action:"bb_pro_reaction_migration_do_later",nonce:bbReactionAdminVars.nonce.migration_do_later},success:function(t){!0===t.success?(o.remove(),bp.Reaction_Admin.hideNoticeElement()):alert(t.data.message)}})},recheckStatus:function(t){t.preventDefault(),e(t.currentTarget).addClass("loading"),location.reload()},stopMigration:function(t){t.preventDefault(),e(t.currentTarget).addClass("loading"),e.ajax({url:bbReactionAdminVars.ajax_url,data:{action:"bb_pro_reaction_migration_stop_conversion",nonce:bbReactionAdminVars.nonce.migration_stop_conversion},method:"POST"}).done(function(){location.reload()})},hideSuccessNotice:function(){e(this).parents(".bb-pro-reaction-notice.success").remove(),bp.Reaction_Admin.hideNoticeElement()}},bp.Reaction_Admin.start()}((bp,jQuery));