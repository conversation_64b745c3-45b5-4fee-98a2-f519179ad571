window.bp=window.bp||{},function(n){bp.Reaction={start:function(){this.setupGlobals(),this.addListeners()},setupGlobals:function(){},addListeners:function(){var t=this;n(window).ready(function(){n("body").hasClass("bb-is-mobile")&&n("body").hasClass("bb-reactions-mode")?(n(document).on("contextmenu",".button.bp-like-button",function(t){return t.preventDefault(),!1}),n(document).on("touchstart","a.button.fav, .button.reaction, a.button.has-like, a.button.has-emotion, .button.bp-like-button",t.showReactionsOnTouch.bind(t)).on("touchend","a.button.fav, .button.reaction, a.button.has-like, a.button.has-emotion, .button.bp-like-button",function(){clearTimeout(window.reactionTouchTimeout)}),n(document).on("touchend",".activity-item .ac-emotions_list, .button.fav, .button.unfav, .button.has-like, .button.has-emotion, .button.bp-like-button",t.ReactionsOnTouchEnd.bind(t)),n(document).on("click",function(t){var o=n(".button.fav, .button.unfav, .button.has-like, .button.has-emotion, .button.bp-like-button");o.is(t.target)||o.has(t.target).length||n(".activity-item .bp-generic-meta").find(".ac-emotions_list.active").removeClass("active")})):(n(document).on("mouseover","a.button.fav, .button.reaction, a.button.has-like, a.button.has-emotion",t.showReactions.bind(t)).on("mouseout","a.button.fav, .button.reaction, a.button.has-like, a.button.has-emotion",function(){clearTimeout(window.reactionHoverTimeout)}),n(document).on("mouseleave",".activity-item .ac-emotions_list, .button.fav, .button.unfav, .button.has-like, .button.has-emotion",t.hideReactions.bind(t)))})},showReactionsOnTouch:function(t){var o=n(t.currentTarget);o.hasClass("bb-reaction-migration-inprogress")||(window.reactionTouchTimeout=setTimeout(function(){o.closest(".bp-generic-meta").find(".ac-emotions_list").addClass("active")},500))},ReactionsOnTouchEnd:function(t){t=n(t.currentTarget);t.closest(".bp-generic-meta").find(".ac-emotions_list").hasClass("active")||(t.trigger("click"),t.trigger(n.Event("click",{customTriggered:!0})))},showReactions:function(t){var o=n(t.currentTarget);o.hasClass("bb-reaction-migration-inprogress")||(window.reactionHoverTimeout=setTimeout(function(){o.closest(".bp-generic-meta").find(".ac-emotions_list").addClass("active")},500))},hideReactions:function(t){n(t.currentTarget).closest(".bp-generic-meta").find(".ac-emotions_list").removeClass("active")}},bp.Reaction.start()}((bp,jQuery));