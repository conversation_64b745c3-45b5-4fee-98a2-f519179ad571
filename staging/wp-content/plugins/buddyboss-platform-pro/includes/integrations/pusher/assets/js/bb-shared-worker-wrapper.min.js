window.bp=window.bp||{},window.Backbone=window.Backbone||[],bp,j<PERSON><PERSON>y,bp.bb_pusher_shared={constructor:function(e,s){return this.app_key=e,this.options=s,this.worker=!1,this.bind_callbacks=[],this.channels="",bp.bb_pusher_shared.startWorker(),bp.bb_pusher_shared},startWorker:function(){var s;this.worker||(this.worker=new SharedWorker(bb_pusher_vars.bb_pro_pusher_shared_worker_url+"?v="+bb_pusher_vars.bb_pro_version,"Shared Worker"),this.worker.port.start(),(s=this).worker.port.onmessage=function(e){s.onMessage(e)},bp.bb_pusher_shared.sendMessage("pusher_init",{app_key:this.app_key,options:this.options}))},sendMessage:function(e,s){this.worker.port.postMessage({type:e,data:s,current_user_id:bb_pusher_vars.loggedin_user_id})},onMessage:function(e){var s=e.data;switch(s.type){case"pusher_init":break;case"pusher_event":bp.bb_pusher_shared.triggerBinds(s);break;case"pusher_channels":bp.bb_pusher_shared.updateChannels(s);break;case"before-message-ajax-send":bp.bb_pusher_shared.onSendMessage(s);break;case"resend-message-ajax-send":bp.bb_pusher_shared.onResendMessage(s);break;case"renderAjaxUpdateFailedMessage":bp.bb_pusher_shared.renderAjaxUpdateFailedMessage(s);break;case"onCancelRemoveMessage":bp.bb_pusher_shared.onCancelRemoveMessage(s);break;case"updateReconnect":bp.bb_pusher_shared.updateReconnect(s.data);break;case"pusher_disconnected":bp.bb_pusher_shared.onDisconnected()}},updateChannels:function(e){this.channels=e.data.channels},updateReconnect:function(e){bp.bb_pusher_shared.sendMessage("pusher_reconnect",{try_reconnect:e.data.try_reconnect})},onDisconnected:function(){bp.Pusher_FrontCommon.removed_channels()},onSendMessage:function(e){bp.Pusher_FrontCommon.renderMessage(e.data.data)},onResendMessage:function(e){bp.Pusher_FrontCommon.renderUpdateSendingMessage(e.data.data)},renderAjaxUpdateFailedMessage:function(e){bp.Pusher_FrontCommon.renderAjaxUpdateFailedMessage(e.data.data)},onCancelRemoveMessage:function(e){window.Backbone.trigger("onCancelRemoveMessage",e.data.data),window.Backbone.trigger("relistelements")},triggerBinds:function(s){void 0!==this.bind_callbacks[s.data.channel_name]&&void 0!==this.bind_callbacks[s.data.channel_name][s.data.event]&&s.data.user_id===bb_pusher_vars.loggedin_user_id&&this.bind_callbacks[s.data.channel_name][s.data.event].forEach(function(e){e(s.data.data)})},subscribe:function(n){bp.bb_pusher_shared.sendMessage("pusher_subscribe",{channel_name:n});var a=this;return{bind:function(e,s){void 0===a.bind_callbacks[n]&&(a.bind_callbacks[n]=[]),void 0===a.bind_callbacks[n][e]&&(a.bind_callbacks[n][e]=[]),a.bind_callbacks[n][e].push(s)},trigger:function(e,s){a.sendMessage("pusher_send_event",{channel_name:n,event:e,data:s,loggedin_user_id:bb_pusher_vars.loggedin_user_id})}}},unsubscribe:function(e){bp.bb_pusher_shared.sendMessage("pusher_unsubscribe",{channel_name:e,loggedin_user_id:bb_pusher_vars.loggedin_user_id})},signin:function(){bp.bb_pusher_shared.sendMessage("pusher_signin",{})},connect:function(){bp.bb_pusher_shared.sendMessage("pusher_connect",{})}};