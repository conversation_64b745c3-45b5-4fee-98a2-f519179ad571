#bb_pusher_settings_section .bb-pusher-status {
	margin-left: 15px;
}

#bb_pusher_settings_section .notes-hidden-header > th {
	display: none;
}

#bb_pusher_settings_section .notes-hidden-header > th + td {
	padding-left: 0;
	padding-right: 0;
}

#bb_pusher_settings_section .hidden-header > th {
	opacity: 0;
	visibility: hidden;
	padding: 0;
	margin: 0;
	font-size: 0;
	line-height: 0;
}

#bb_pusher_settings_section .hidden-header > td {
	padding-left: 0;
	padding-right: 0;
	margin: 0;
}

#bb_pusher_settings_section .hidden-header > td:empty {
	padding: 0;
}

#bb_pusher_settings_section .hidden-header .show-full-width {
	margin-left: -210px;
	word-break: break-word;
}

@media (max-width: 782px) {
	#bb_pusher_settings_section .hidden-header .show-full-width {
		margin-left: 0;
	}
}

#bb_pusher_settings_section input[type="text"],
#bb_pusher_settings_section input[type="email"],
#bb_pusher_settings_section select,
#bb_pusher_settings_section input[type="password"] {
	min-width: 50%;
}

#bb_pusher_settings_section select {
	max-width: none;
}

#bb_pusher_settings_section .custom-cluster {
	margin-top: 15px;
}

#bb_pusher_settings_section .custom-cluster label {
	display: block;
	margin-bottom: 5px;
}

#bb_pusher_settings_section .password-toggle {
	position: relative;
	display: inline-block;
	width: 50%;
}

@media (max-width: 782px) {
	#bb_pusher_settings_section .password-toggle {
		width: 100%;
	}
}

#bb_pusher_settings_section .password-toggle .bb-hide-pw {
	position: absolute;
	right: 0;
	top: 0;
	height: 100%;
	width: 30px;
	border: 0;
	padding: 0;
	background: none;
	box-shadow: none;
}

#bb_pusher_settings_section .password-toggle .bb-hide-pw .bb-icon {
	font-size: 16px;
	line-height: 1;
}

#bb_pusher_settings_section .password-toggle input[type="text"],
#bb_pusher_settings_section .password-toggle input[type="password"] {
	padding-right: 30px;
	width: 100%;
}

#bb_pusher_settings_section .password-toggle input[type="text"] + .bb-hide-pw .bb-icon::before {
	content: "\ee6a";
}

#bb_pusher_settings_section .checkbox-wrap input[type="checkbox"] {
	margin-top: 0;
}

#bb_pusher_settings_section .checkbox-wrap + .checkbox-wrap {
	margin-top: 15px;
}

#bb_pusher_settings_section .bb-bottom-notice {
	text-align: center;
}
