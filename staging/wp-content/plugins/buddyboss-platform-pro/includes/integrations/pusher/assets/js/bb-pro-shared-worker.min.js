var clients=[],pusher=!1,channels=[],current_user_id=0,try_reconnect=!1;function onMessage(e){switch(e.data.type){case"pusher_init":pusherInit(e.data);break;case"pusher_subscribe":pusherSubscribe(e.data);break;case"pusher_unsubscribe":pusherUnsubscribe(e.data);break;case"pusher_send_event":pusherChannelTrigger(e.data);break;case"pusher_signin":pusherSignIn();break;case"pusher_connect":pusherConnect();break;case"pusher_reconnect":pusherReconnect(e.data);break;default:sendMessages(e.data.type,e.data)}}function pusherInit(e){var n=e.data.options;return pusher&&current_user_id===e.current_user_id||(current_user_id=e.current_user_id,importScripts("https://js.pusher.com/8.0.2/pusher.worker.min.js"),importScripts(n.authorizer),n.authorizer=PusherBatchAuthorizer,pusher=new Pusher(e.data.app_key,n),sendMessages("pusher_init_first",{success:!0})),sendMessages("pusher_init",{success:!0}),pusherSignIn(),send_channels(),bind_connections(),pusher}function pusherSignIn(){pusher&&pusher.signin()}function pusherConnect(){pusher&&pusher.connect()}function pusherReconnect(e){try_reconnect=e.data.try_reconnect}function send_channels(){sendMessages("pusher_channels",{channels:Object.keys(pusher.channels.channels)})}function bind_connections(){pusher&&(pusher.connection.bind("disconnected",function(){sendMessages("pusher_disconnected",{}),!0===try_reconnect&&pusherConnect()}),pusher.connection.bind("connected",function(){!0===try_reconnect&&(try_reconnect=!1)}))}function pusherSubscribe(s){current_user_id===s.current_user_id&&(void 0===channels[s.data.channel_name]||void 0!==channels[s.data.channel_name]&&void 0!==channels[s.data.channel_name].subscribed&&!1===channels[s.data.channel_name].subscribed)&&(channels[s.data.channel_name]=pusher.subscribe(s.data.channel_name),channels[s.data.channel_name].bind_global(function(e,n){sendMessages("pusher_event",{channel_name:s.data.channel_name,event:e,data:n,user_id:current_user_id})}))}function pusherUnsubscribe(e){current_user_id===e.current_user_id&&void 0!==channels[e.data.channel_name]&&pusher.unsubscribe(e.data.channel_name)}function pusherChannelTrigger(e){current_user_id===e.current_user_id&&void 0!==channels[e.data.channel_name]&&channels[e.data.channel_name].trigger(e.data.event,e.data.data)}function sendMessages(n,s){clients.forEach(function(e){e.postMessage({type:n,data:s})})}self.addEventListener("connect",function(e){e=e.ports[0];clients.push(e),e.onmessage=onMessage,e.start()});