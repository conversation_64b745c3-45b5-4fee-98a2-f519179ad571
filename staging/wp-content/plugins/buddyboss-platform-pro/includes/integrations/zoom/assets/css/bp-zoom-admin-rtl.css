.bp-admin-card.section-bp_zoom_gutenberg_section input[type="text"],
.bp-admin-card.section-bp_zoom_gutenberg_section input[type="email"],
.bp-admin-card.section-bp_zoom_gutenberg_section input[type="password"] {
	width: 50%;
}

.bp-group-zoom-settings .bb-field-wrap {
	display: flex;
	flex-flow: row wrap;
}

.bp-group-zoom-settings label.group-setting-label {
	flex: 0 0 180px;
}

.bp-group-zoom-settings .bp-input-wrap {
	flex: 1;
	min-width: 0;
}

.bp-group-zoom-settings .bp-input-wrap input {
	min-width: 70%;
}

#copy-webhook-link {
	text-decoration: none;
}

.bp-zoom-group-button-wrap {
	display: flex;
	align-items: center;
	padding-bottom: 10px;
}

.bp-zoom-group-button-wrap .bp-zoom-group-check-connection {
	margin-left: 20px;
	text-decoration: none;
}

hr.bb-sep-line {
	margin: 20px 0;
}

.bp-radio-wrap {
	display: flex;
	align-items: center;
	margin-bottom: 10px;
}

.bp-radio-wrap:last-child {
	margin: 0;
}

.bp-radio-wrap input {
	margin-top: 1px;
	margin-left: 8px;
}

.bp-radio-wrap label {
	line-height: 1;
}

.radio-button-options .bp-radio-wrap {
	display: block;
}

.radio-button-options .bp-radio-wrap label {
	line-height: 1.5;
}

.bb-description-info {
	font-size: 14px;
	color: #a3a5a9;
	letter-spacing: -0.24px;
	line-height: 24px;
	display: flex;
	align-items: center;
	margin-top: 8px;
}

.bb-description-info .bb-url-text {
	max-width: 70%;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	margin-left: 20px;
}

.bb-checkbox-wrap .components-base-control__field {
	display: flex;
}

.bb-checkbox-wrap .components-base-control__field > span {
	margin-top: 2px;
}

@media only screen and (max-width: 768px) {
	.bp-admin-card.section-bp_zoom_gutenberg_section input[type="text"],
	.bp-admin-card.section-bp_zoom_gutenberg_section input[type="email"],
	.bp-admin-card.section-bp_zoom_gutenberg_section input[type="password"] {
		width: 100%;
	}
}

@media only screen and (max-width: 600px) {
	.bp-group-zoom-settings label.group-setting-label,
	.bp-group-zoom-settings .bp-input-wrap {
		flex: 0 0 100%;
	}
	.bp-group-zoom-settings label.group-setting-label {
		margin-bottom: 8px;
	}
	.bp-group-zoom-settings .bp-input-wrap input {
		min-width: 100%;
	}
	.bb-description-info .bb-url-text {
		max-width: 90%;
	}
}

.edit-occurrences-button.is-link {
	margin: 5px 0 10px 5px;
}

.components-datetime {
	margin: 10px 0;
	padding: 0;
}

.components-panel__body.is-opened > .components-datetime {
	padding: 0;
	margin: 10px 0;
}

/* SelectDropdown Component */
.select_main {
	position: relative;
	z-index: 9;
}

.select_main .select_option {
	cursor: pointer;
}

.select_main .select_option select.components-select-control__input {
	pointer-events: none;
}

.Select_suggestions-wrapper {
	font-size: 14px;
	font-weight: 400;
	position: absolute;
	top: calc(100% - 2px);
	padding: 0;
	background-color: #fff;
	left: -2px;
	border: 1px solid #ccc;
	border-radius: 6px;
	list-style: none;
	z-index: 1;
	width: calc(100% - 110px);
}

.Select_suggestions-wrapper .components-text-control__input {
	border-color: #ccc;
	border-radius: 6px;
	margin: 10px;
	line-height: 1.8;
}

.Select_suggestions-wrapper .Select_suggestions-list {
	margin: 0;
	padding: 0;
	list-style: none;
	max-height: 180px;
	overflow-y: auto;
}

.Select_suggestions-wrapper .Select_suggestions-list-item {
	padding: 5px 10px;
	cursor: pointer;
}

.Select_suggestions-wrapper .Select_suggestions-list-item:last-child {
	border-bottom-width: 0;
}

.Select_suggestions-wrapper .Select_suggestions-list-item.no-result {
	cursor: default;
}

.Select_suggestions-wrapper .Select_suggestions-list-item.active, .Select_suggestions-wrapper .Select_suggestions-list-item:hover:not(.no-result) {
	background-color: #f2f2f2;
}

/* SelectDropdown Component */
/* Admin Zoom Integration Tab */
[class*="section-bp_zoom_"] .hidden-header > th {
	display: none;
}

[class*="section-bp_zoom_"] .hidden-header .bb-error-section,
[class*="section-bp_zoom_"] .hidden-header .bb-warning-section {
	margin-right: 0;
}

[class*="section-bp_zoom_"] .hidden-header #ss-oauth-content .bb-error-section,
[class*="section-bp_zoom_"] .hidden-header #ss-oauth-content .bb-warning-section {
	margin-bottom: 15px;
}

[class*="section-bp_zoom_"] tr.no-padding > td {
	padding-right: 0;
	padding-top: 0;
}

[class*="section-bp_zoom_"] .is-disabled {
	pointer-events: none;
	background-color: #f6f7f7;
	opacity: 0.75;
}

[class*="section-bp_zoom_"] .bb-warning-section a {
	color: inherit;
}

[class*="section-bp_zoom_"] .bb-zoom_account-email select {
	width: 50%;
}

[class*="section-bp_zoom_"] .bb-zoom_account-email .bb-icon-spinner {
	display: none;
}

[class*="section-bp_zoom_"] .bb-zoom_account-email .bb-icon-spinner:before {
	content: "";
	display: inline-block;
	margin: 0;
	box-sizing: border-box;
	width: 20px;
	height: 20px;
	border-radius: 50%;
	border: 2px solid #dcdcde;
	border-top-color: #787c82;
}

[class*="section-bp_zoom_"] .bb-zoom_account-email.loading .bb-icon-spinner {
	display: inline-block;
	vertical-align: middle;
	margin: -5px 10px 0 0;
	width: 20px;
	height: 20px;
}

[class*="section-bp_zoom_"] .bbpro-zoom-status {
	margin-right: 15px;
}

.copy-toggle {
	display: flex;
	position: relative;
	width: 50%;
}

.copy-toggle > input {
	width: 100% !important;
	padding-left: 65px;
	margin-left: 0;
}

.copy-toggle > .button {
	position: absolute;
	left: 0;
	top: 0;
	cursor: pointer;
	border-top-right-radius: 0;
	border-bottom-right-radius: 0;
	background-color: #fff;
	color: #787c82;
	border-color: #8c8f94;
}

@media screen and (max-width: 768px) {
	.copy-toggle {
		width: 100%;
	}
}

#bp-group-zoom-settings-connection-type .bp-radio-wrap.is-disabled {
	pointer-events: none;
	opacity: 0.75;
}

#bp-group-zoom-settings-connection-type .bp-feedback {
	box-shadow: none;
	margin: 15px 0;
}

.bb-warning-section + .bb-pro-tabs {
	margin-top: 20px;
}

.bb-pro-tabs .bb-pro-tabs-list {
	display: flex;
	flex-flow: row wrap;
	margin: 0 0 20px 0;
	padding: 0;
	list-style: none;
}

.bb-pro-tabs .bb-pro-tabs-list li {
	font-size: 15px;
	padding: 5px 0 12px 0;
	margin-left: 24px;
	border-bottom: 1px solid transparent;
	cursor: pointer;
}

.bb-pro-tabs .bb-pro-tabs-list li:last-child {
	margin-left: 0;
}

.bb-pro-tabs .bb-pro-tabs-list li.selected {
	color: #2271b1;
	border-color: #2271b1;
}

.bb-pro-tabs .bb-pro-tabs-content th {
	padding: 20px 0 20px 10px;
}

.bb-pro-tabs .bb-pro-tabs-content td {
	padding: 15px 10px;
}

/* Admin Zoom Integration Tab */
