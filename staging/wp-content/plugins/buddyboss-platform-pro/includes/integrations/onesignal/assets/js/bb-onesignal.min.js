window.bp=window.bp||{},window.OneSignal=window.OneSignal||[];var bb_player_id="";!function(e){bp.OneSignal_FrontCommon={start:function(){this.setupGlobals(),this.addListeners(),e(document).on("ready",function(){e.magnificPopup.defaults.closeOnBgClick=!1,""===bb_player_id&&(bb_player_id=bp.OneSignal_FrontCommon.getCookie("bbpro-player-id"))})},setupGlobals:function(){bp.OneSignal_FrontCommon.is_updated_device_info=!1,window.OneSignal.push(function(){window.OneSignal.on("subscriptionChange",function(o){window.OneSignal.push(function(){window.OneSignal.getUserId(function(n){bb_player_id=n,o?(window.OneSignal.push(["setSubscription",!0]),bp.OneSignal_FrontCommon.updateDeviceInfo(bb_player_id,!0,!0)):bp.OneSignal_FrontCommon.updateDeviceInfo(bb_player_id,!1,!0)})})}),window.OneSignal.isPushNotificationsEnabled(function(o){window.OneSignal.getUserId(function(n){bp.OneSignal_FrontCommon.is_updated_device_info=!0,bb_player_id=n,o?(window.OneSignal.push(["setSubscription",!0]),bp.OneSignal_FrontCommon.updateDeviceInfo(bb_player_id,!0,!0)):bp.OneSignal_FrontCommon.updateDeviceInfo(bb_player_id,!1,!0)})})}),window.OneSignal.push(function(){window.OneSignal.SERVICE_WORKER_UPDATER_PATH="OneSignalSDKUpdaterWorker.js",window.OneSignal.SERVICE_WORKER_PATH="OneSignalSDKWorker.js",window.OneSignal.SERVICE_WORKER_PARAM={scope:bb_onesignal_vars.path+"/sdk_files/push/onesignal/"},window.OneSignal.setDefaultNotificationUrl(bb_onesignal_vars.home_url);var n={};(window._oneSignalInitOptions=n).appId=bb_onesignal_vars.app_id,n.allowLocalhostAsSecureOrigin=!0,n.path=bb_onesignal_vars.http_path+"sdk_files/",n.safari_web_id=bb_onesignal_vars.safari_web_id,bb_onesignal_vars.subDomainName&&(n.subdomainName=bb_onesignal_vars.subDomainName),0<parseInt(bb_onesignal_vars.is_soft_prompt_enabled)&&(n.promptOptions={},n.promptOptions.actionMessage=bb_onesignal_vars.actionMessage,n.promptOptions.acceptButtonText=bb_onesignal_vars.acceptButtonText,n.promptOptions.cancelButtonText=bb_onesignal_vars.cancelButtonText),window.OneSignal.init(window._oneSignalInitOptions),window.OneSignal.setExternalUserId(bb_onesignal_vars.prompt_user_id),0<parseInt(bb_onesignal_vars.auto_prompt_request_permission)&&("visit"===bb_onesignal_vars.auto_prompt_validate||"login"===bb_onesignal_vars.auto_prompt_validate&&"true"!==sessionStorage.getItem("ONESIGNAL_HTTP_PROMPT_SHOWN"))&&bp.OneSignal_FrontCommon.notificationPrompt()}),window.OneSignal.push(function(){window.OneSignal.on("notificationPermissionChange",function(n){n=n.to;bb_player_id&&("granted"===n?(window.OneSignal.push(["setSubscription",!0]),bp.OneSignal_FrontCommon.updateDeviceInfo(bb_player_id,!0,!0)):("denied"===n&&window.OneSignal.push(["setSubscription",!1]),bp.OneSignal_FrontCommon.updateDeviceInfo(bb_player_id,!1,!0)))})})},addListeners:function(){e(document).on("change",".notification-toggle",this.toggleNotifcationOnOff.bind(this)),e(document).on("click","body .mfp-inline-holder",this.disableMagificPopupBackgroundClick.bind(this)),e(document).on("click",".mfp-close",this.enableMagificPopupBackgroundClick.bind(this)),e(document).on("click","#onesignal-slidedown-cancel-button",function(){e("body .notification-toggle").prop("checked",!1)}),e(window).on("load",this.handleNotificationToggle.bind(this))},disableMagificPopupBackgroundClick:function(){1<=e(".mfp-bg").length&&e("body, html").css("overflow","hidden")},enableMagificPopupBackgroundClick:function(){e("body, html").css("overflow","inherit")},toggleNotifcationOnOff:function(n){e(n.target).is(":checked")?"denied"!==bp.OneSignal_FrontCommon.getUserNotificationStatus()?(localStorage.removeItem("onesignal-notification-prompt"),bp.OneSignal_FrontCommon.notificationPrompt()):e("#permission-helper-modal").magnificPopup({items:{src:e("#permission-helper-modal"),type:"inline",closeOnContentClick:!1,closeBtnInside:!0,enableEscapeKey:!1,closeOnBgClick:!1},callbacks:{open:function(){e(".notification-popup .mfp-close").click(function(){bp.OneSignal_FrontCommon.closeMfpPopup()}),"granted"===bp.OneSignal_FrontCommon.getUserNotificationStatus()?e(".notification-popup .turn-on-button").removeAttr("disabled"):window.OneSignal.showNativePrompt(),e(".notification-popup .turn-on-button").click(function(){window.OneSignal.push(["setSubscription",!0]),setTimeout(function(){bp.OneSignal_FrontCommon.closeMfpPopup()},300)})},close:function(){"granted"===bp.OneSignal_FrontCommon.getUserNotificationStatus()?e("body .notification-toggle").prop("checked",!0):e("body .notification-toggle").prop("checked",!1)}}}).magnificPopup("open"):e("#permission-helper-modal-close").magnificPopup({items:{src:e("#permission-helper-modal-close"),type:"inline",closeOnContentClick:!1,closeBtnInside:!0,enableEscapeKey:!1,closeOnBgClick:!1},callbacks:{open:function(){e(".notification-popup-close .mfp-close").click(function(){bp.OneSignal_FrontCommon.closeMfpPopup()}),"disabled"!==bp.OneSignal_FrontCommon.getUserNotificationStatus()&&"default"!==bp.OneSignal_FrontCommon.getUserNotificationStatus()||e(".notification-popup-close .turn-on-button-close").removeAttr("disabled"),e(".notification-popup-close .turn-on-button-close").click(function(){window.OneSignal.push(["setSubscription",!1]),setTimeout(function(){bp.OneSignal_FrontCommon.closeMfpPopup()},300)})},close:function(){"granted"===bp.OneSignal_FrontCommon.getUserNotificationStatus()?e("body .notification-toggle").prop("checked",!0):e("body .notification-toggle").prop("checked",!1)}}}).magnificPopup("open")},closeMfpPopup:function(){0<e(document).find(".notification-popup  button.turn-on-button").length&&e(document).find(".notification-popup  button.turn-on-button").is(":disabled")&&e("body .notification-toggle").prop("checked",!1),e.magnificPopup.close()},notificationPrompt:function(){"undefined"!=typeof Notification&&("http:"===location.protocol||"granted"!==Notification.permission&&"denied"!==Notification.permission)&&0<parseInt(bb_onesignal_vars.is_component_active)&&0<parseInt(bb_onesignal_vars.is_web_push_enable)&&0<parseInt(bb_onesignal_vars.is_valid_licence)&&0<parseInt(bb_onesignal_vars.prompt_user_id)&&(0<parseInt(bb_onesignal_vars.is_soft_prompt_enabled)?window.OneSignal.showSlidedownPrompt():window.OneSignal.showNativePrompt())},getUserNotificationStatus:function(){return"undefined"!=typeof Notification?Notification.permission:""},updateDeviceInfo:function(n,o,i){e.ajax({type:"POST",url:bb_onesignal_vars.ajax_url,data:{action:"onesignal_update_device_info",user_id:bb_onesignal_vars.prompt_user_id,player_id:n,active:o,update_via_curl:i},success:function(n){void 0!==n.data&&n.data.browser_box&&0<e(document).find(".bb-onesignal-render-browser-block").length&&e(document).find(".bb-onesignal-render-browser-block").empty().append(n.data.browser_box).removeClass("bp-hide")},error:function(){return!1}})},detectBrowser:function(){return-1!==navigator.userAgent.indexOf("Opera")||-1!==navigator.userAgent.indexOf("OPR")?"Opera":-1!==navigator.userAgent.indexOf("Edge")||-1!==navigator.userAgent.indexOf("Edg")?"Edge":-1!==navigator.userAgent.indexOf("Chrome")?"Chrome":-1!==navigator.userAgent.indexOf("Safari")?"Safari":-1!==navigator.userAgent.indexOf("Firefox")?"Firefox":-1!==navigator.userAgent.indexOf("MSIE")||void 0!==document.documentMod?"IE":"Unknown"},getCookie:function(n){for(var o=n+"=",i=decodeURIComponent(document.cookie).split(";"),e=0;e<i.length;e++){for(var t=i[e];" "==t.charAt(0);)t=t.substring(1);if(0==t.indexOf(o))return t.substring(o.length,t.length)}return""},setCookie:function(n,o,i){var e=new Date,i=(e.setTime(e.getTime()+24*i*60*60*1e3),"expires="+e.toUTCString());document.cookie=n+"="+o+"; "+i+"; path=/"},handleNotificationToggle:function(){setTimeout(function(){bp.OneSignal_FrontCommon.is_updated_device_info||bp.OneSignal_FrontCommon.updateDeviceInfo("",!1,!1)},1e3)}},bp.OneSignal_FrontCommon.start()}((bp,jQuery));