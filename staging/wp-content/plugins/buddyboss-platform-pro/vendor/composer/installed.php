<?php

namespace BuddyBossPlatformPro;

return array('root' => array('pretty_version' => 'dev-master', 'version' => 'dev-master', 'type' => 'library', 'install_path' => __DIR__ . '/../../', 'aliases' => array(), 'reference' => '14bd29c5b41a390ac06ca0e4e378a486a66d44cf', 'name' => 'buddyboss-platform/buddyboss-platform-pro', 'dev' => \false), 'versions' => array('buddyboss-platform/buddyboss-platform-pro' => array('pretty_version' => 'dev-master', 'version' => 'dev-master', 'type' => 'library', 'install_path' => __DIR__ . '/../../', 'aliases' => array(), 'reference' => '14bd29c5b41a390ac06ca0e4e378a486a66d44cf', 'dev_requirement' => \false), 'guzzlehttp/guzzle' => array('pretty_version' => '7.5.1', 'version' => '7.5.1.0', 'type' => 'library', 'install_path' => __DIR__ . '/../guzzlehttp/guzzle', 'aliases' => array(), 'reference' => 'b964ca597e86b752cd994f27293e9fa6b6a95ed9', 'dev_requirement' => \false), 'guzzlehttp/promises' => array('pretty_version' => '1.5.2', 'version' => '1.5.2.0', 'type' => 'library', 'install_path' => __DIR__ . '/../guzzlehttp/promises', 'aliases' => array(), 'reference' => 'b94b2807d85443f9719887892882d0329d1e2598', 'dev_requirement' => \false), 'guzzlehttp/psr7' => array('pretty_version' => '2.5.0', 'version' => '2.5.0.0', 'type' => 'library', 'install_path' => __DIR__ . '/../guzzlehttp/psr7', 'aliases' => array(), 'reference' => 'b635f279edd83fc275f822a1188157ffea568ff6', 'dev_requirement' => \false), 'paragonie/random_compat' => array('pretty_version' => 'v9.99.100', 'version' => '9.99.100.0', 'type' => 'library', 'install_path' => __DIR__ . '/../paragonie/random_compat', 'aliases' => array(), 'reference' => '996434e5492cb4c3edcb9168db6fbb1359ef965a', 'dev_requirement' => \false), 'paragonie/sodium_compat' => array('pretty_version' => 'v1.19.0', 'version' => '1.19.0.0', 'type' => 'library', 'install_path' => __DIR__ . '/../paragonie/sodium_compat', 'aliases' => array(), 'reference' => 'cb15e403ecbe6a6cc515f855c310eb6b1872a933', 'dev_requirement' => \false), 'psr/http-client' => array('pretty_version' => '1.0.2', 'version' => '1.0.2.0', 'type' => 'library', 'install_path' => __DIR__ . '/../psr/http-client', 'aliases' => array(), 'reference' => '0955afe48220520692d2d09f7ab7e0f93ffd6a31', 'dev_requirement' => \false), 'psr/http-client-implementation' => array('dev_requirement' => \false, 'provided' => array(0 => '1.0')), 'psr/http-factory' => array('pretty_version' => '1.0.2', 'version' => '1.0.2.0', 'type' => 'library', 'install_path' => __DIR__ . '/../psr/http-factory', 'aliases' => array(), 'reference' => 'e616d01114759c4c489f93b099585439f795fe35', 'dev_requirement' => \false), 'psr/http-factory-implementation' => array('dev_requirement' => \false, 'provided' => array(0 => '1.0')), 'psr/http-message' => array('pretty_version' => '2.0', 'version' => '2.0.0.0', 'type' => 'library', 'install_path' => __DIR__ . '/../psr/http-message', 'aliases' => array(), 'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71', 'dev_requirement' => \false), 'psr/http-message-implementation' => array('dev_requirement' => \false, 'provided' => array(0 => '1.0')), 'psr/log' => array('pretty_version' => '1.1.4', 'version' => '1.1.4.0', 'type' => 'library', 'install_path' => __DIR__ . '/../psr/log', 'aliases' => array(), 'reference' => 'd49695b909c3b7628b6289db5479a1c204601f11', 'dev_requirement' => \false), 'pusher/pusher-php-server' => array('pretty_version' => '7.2.2', 'version' => '7.2.2.0', 'type' => 'library', 'install_path' => __DIR__ . '/../pusher/pusher-php-server', 'aliases' => array(), 'reference' => '4ace4873873b06c25cecb2dd6d9fdcbf2f20b640', 'dev_requirement' => \false), 'ralouphie/getallheaders' => array('pretty_version' => '3.0.3', 'version' => '3.0.3.0', 'type' => 'library', 'install_path' => __DIR__ . '/../ralouphie/getallheaders', 'aliases' => array(), 'reference' => '120b605dfeb996808c31b6477290a714d356e822', 'dev_requirement' => \false), 'symfony/deprecation-contracts' => array('pretty_version' => 'v2.5.2', 'version' => '2.5.2.0', 'type' => 'library', 'install_path' => __DIR__ . '/../symfony/deprecation-contracts', 'aliases' => array(), 'reference' => 'e8b495ea28c1d97b5e0c121748d6f9b53d075c66', 'dev_requirement' => \false)));
