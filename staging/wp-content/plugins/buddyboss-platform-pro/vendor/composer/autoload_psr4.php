<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(dirname(__FILE__));
$baseDir = dirname($vendorDir);

return array(
    'BuddyBossPlatformPro\\Pusher\\' => array($vendorDir . '/pusher/pusher-php-server/src'),
    'BuddyBossPlatformPro\\Psr\\Log\\' => array($vendorDir . '/psr/log/Psr/Log'),
    'BuddyBossPlatformPro\\Psr\\Http\\Message\\' => array($vendorDir . '/psr/http-factory/src', $vendorDir . '/psr/http-message/src'),
    'BuddyBossPlatformPro\\Psr\\Http\\Client\\' => array($vendorDir . '/psr/http-client/src'),
    'BuddyBossPlatformPro\\GuzzleHttp\\Psr7\\' => array($vendorDir . '/guzzlehttp/psr7/src'),
    'BuddyBossPlatformPro\\GuzzleHttp\\Promise\\' => array($vendorDir . '/guzzlehttp/promises/src'),
    'BuddyBossPlatformPro\\GuzzleHttp\\' => array($vendorDir . '/guzzlehttp/guzzle/src'),
);
