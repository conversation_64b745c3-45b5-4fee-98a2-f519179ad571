/*------------------------------------------------------------------------------
Loaded in wp-admin for query string `hello=buddyboss` and `hello=buddyboss-app`.

@since BuddyPress 3.0.0

--------------------------------------------------------------------------------
TABLE OF CONTENTS:
--------------------------------------------------------------------------------
1.0 - Typography and colour
2.0 - Icons
	2.1 - Social Icons
3.0 - Elements
	3.1 - Backdrop and container
	3.2 - Modal footer
	3.3 - Modal header
	3.4 - Modal content
4.0 - Content styles
	4.1 - Backdrop and container
	4.2 - Footer content
	4.3 - Header content
	4.4 - Content content
6.0 - Media Queries
	6.1 - Mobile
	6.2 - Desktop Small
	6.3 - Desktop Medium
	6.4 - Desktop Large
7.0 - Document Mime Type check
8.0 - Update Modal
9.0 - Tabs
------------------------------------------------------------------------------*/
/*------------------------------------------------------------------------------
 * 1.0 - Typography and colour
 *----------------------------------------------------------------------------*/
:root {
  --bp-hello-color-primary: #0073aa;
  --bp-hello-color-secondary: #e5e5e5;
  --bp-hello-container-size: 15%;
}

#bp-hello-container a,
#bb-hello-container a {
  color: var(--bp-hello-color-primary);
}

#bp-hello-container a:hover,
#bb-hello-container a:hover {
  -webkit-transition: all 0.1s ease-in-out;
  -o-transition: all 0.1s ease-in-out;
  transition: all 0.1s ease-in-out;
  color: black;
}

#bp-hello-container .bp-hello-header h1,
#bp-hello-container .bb-hello-header h1,
#bb-hello-container .bp-hello-header h1,
#bb-hello-container .bb-hello-header h1 {
  line-height: 1.7;
  font-size: 21px;
  font-weight: 400;
}

.bp-hello-content p,
.bb-hello-content p {
  font-size: 16px;
}

.bp-hello-content .bb-release-changelog ul,
.bp-hello-content ul.bp-hello-list,
.bp-hello-content ul.bb-hello-list,
.bb-hello-content .bb-release-changelog ul,
.bb-hello-content ul.bp-hello-list,
.bb-hello-content ul.bb-hello-list {
  list-style: disc;
  margin-left: 25px;
  font-size: 16px;
  line-height: 1.3;
}

.bp-hello-content .video-wrapper,
.bb-hello-content .video-wrapper {
  margin: 0 auto;
  height: calc(100%);
}

.bp-hello-content .video-wrapper .video-container,
.bb-hello-content .video-wrapper .video-container {
  position: relative;
  padding-bottom: 56.25%;
  padding-top: 0;
  height: 0;
  overflow: hidden;
}

.bp-hello-content .video-wrapper .video-container iframe,
.bp-hello-content .video-wrapper .video-container object,
.bp-hello-content .video-wrapper .video-container embed,
.bb-hello-content .video-wrapper .video-container iframe,
.bb-hello-content .video-wrapper .video-container object,
.bb-hello-content .video-wrapper .video-container embed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/*------------------------------------------------------------------------------
 * 2.0 - Icons
 *----------------------------------------------------------------------------*/
/*
 * 2.1 - Social Icons
 */
.bp-hello-social li a i:before,
.bb-hello-social li a i:before {
  font-size: 23px;
  vertical-align: middle;
  margin: 0;
  background-color: #f6f6f6;
  color: #333;
  border-radius: 50px;
  padding: 7px;
}

/*------------------------------------------------------------------------------
 * 3.0 - Elements
 *----------------------------------------------------------------------------*/
/*
 * 3.1 - Backdrop and container
 */
#bp-hello-backdrop,
#bb-hello-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9998;
  display: none;
}

#bp-hello-container,
#bb-hello-container {
  position: fixed;
  top: 0;
  bottom: 80px;
  z-index: 99999;
}

.bp-hello-buddyboss#bp-hello-container,
.bp-hello-buddyboss#bb-hello-container,
.bb-hello-buddyboss#bb-hello-container {
  bottom: 0;
}

.bp-disable-scroll {
  overflow: hidden;
}

@media screen and (max-width: 1023px) {
  .admin-bar #bp-hello-container,
  .admin-bar #bb-hello-container {
    top: 32px;
  }
}

@media screen and (max-width: 782px) {
  .admin-bar #bp-hello-container,
  .admin-bar #bb-hello-container {
    top: 46px;
  }
}

/*
 * 3.2 - Modal footer
 */
.bp-hello-footer,
.bb-hello-footer {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  height: 58px;
  max-height: 58px;
}

.bp-hello-social-cta,
.bp-hello-social-links,
.bb-hello-social-cta,
.bb-hello-social-links {
  -ms-flex-preferred-size: 50%;
  flex-basis: 50%;
}

.bp-hello-social-links ul,
.bb-hello-social-links ul {
  display: inline-block;
  margin: 2px 0 0;
}

.bp-hello-social li,
.bb-hello-social li {
  position: relative;
  bottom: -5px;
  display: inline-block;
  list-style-type: none;
  margin-bottom: 0;
  margin-left: 5px;
}

/*
 * 3.3 - Modal header
 */
.bp-hello-header,
.bb-hello-header {
  height: 58px;
  max-height: 58px;
}

/*
 * 3.4 - Modal content
 */
.bp-hello-content,
.bb-hello-content {
  padding: 0 25px;
  height: calc(100% - 58px);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/*------------------------------------------------------------------------------
 * 4.0 - Content styles
 *----------------------------------------------------------------------------*/
/*
 * 4.1 - Backdrop and container
 */
#bp-hello-backdrop,
#bb-hello-backdrop {
  background-color: rgba(0, 0, 0, 0.8);
  -webkit-transition: opacity 0.15s ease-out;
  -o-transition: opacity 0.15s ease-out;
  transition: opacity 0.15s ease-out;
}

#bp-hello-container,
#bb-hello-container {
  background-color: white;
}

/*
 * 4.2 - Footer content
 */
.bp-hello-footer,
.bb-hello-footer {
  border-radius: 0 0 3px 3px;
  background-color: white;
  border-top: 1px solid var(--bp-hello-color-secondary);
  padding: 6px 25px;
}

.bp-hello-footer p,
.bb-hello-footer p {
  font-size: 15px;
}

.bp-hello-buddyboss-app .bp-hello-footer,
.bp-hello-buddyboss-app .bb-hello-footer,
.bb-hello-buddyboss-app .bb-hello-footer {
  border-top: none;
}

.bp-hello-social-cta,
.bb-hello-social-cta {
  text-align: left;
}

.bp-hello-social-links,
.bb-hello-social-links {
  text-align: right;
}

.bp-hello-social li a,
.bb-hello-social li a {
  text-decoration: none;
}

/*
 * 4.3 - Header content
 */
.bp-hello-header,
.bb-hello-header {
  padding: 6px 25px;
}

.bp-hello-header h1,
.bb-hello-header h1 {
  width: calc(100% - 51px);
}

#bp-hello-container .bp-hello-header,
#bp-hello-container .bb-hello-header,
#bb-hello-container .bp-hello-header,
#bb-hello-container .bb-hello-header {
  border-bottom: 1px solid var(--bp-hello-color-secondary);
}

.bp-hello-title,
.bb-hello-title {
  text-align: left;
}

.bp-hello-close,
.bb-hello-close {
  position: absolute;
  top: 15px;
  right: 25px;
  text-align: right;
}

/*
 * 4.4 - Content content
 */
.bp-hello-content,
.bb-hello-content {
  background-color: white;
}

.bp-hello-buddyboss .bp-hello-content,
.bp-hello-buddyboss .bb-hello-content,
.bb-hello-buddyboss .bb-hello-content {
  max-height: calc(100vh - 200px);
  overflow: auto;
}

.bp-hello-buddyboss-app .bp-hello-content,
.bp-hello-buddyboss-app .bb-hello-content,
.bb-hello-buddyboss-app .bb-hello-content {
  padding: 0;
}

.bp-hello-content img,
.bb-hello-content img {
  border-radius: 2px;
  max-width: 100%;
}

/*------------------------------------------------------------------------------
 * 6.0 - Media Queries
 *----------------------------------------------------------------------------*/
/**
 * 6.1 - Mobile
 */
@media screen and (max-width: 500px) {
  .bp-hello-social-cta p span,
  .bb-hello-social-cta p span {
    display: none;
  }
}

/**
 * 6.2 - Desktop Small
 */
@media screen and (max-width: 1024px) {
  #bp-hello-container,
  #bb-hello-container {
    width: 100%;
  }
}

/*
 * 6.3 - Desktop Medium
 */
@media only screen and (min-width: 1024px) {
  #bp-hello-backdrop,
  #bb-hello-backdrop {
    display: block;
  }
  #bp-hello-container,
  #bb-hello-container {
    position: fixed;
    top: 60px;
    left: var(--bp-hello-container-size);
    right: var(--bp-hello-container-size);
    bottom: 23px;
    z-index: 9999;
    border-radius: 3px;
  }
  #bp-hello-container.bp-hello-buddyboss, #bp-hello-container.bb-hello-buddyboss,
  #bb-hello-container.bp-hello-buddyboss,
  #bb-hello-container.bb-hello-buddyboss {
    top: 50%;
    bottom: initial;
    max-height: calc(100% - 90px);
    transform: translateY(-50%);
  }
  #bp-hello-container .bp-hello-header h1,
  #bp-hello-container .bb-hello-header h1,
  #bb-hello-container .bp-hello-header h1,
  #bb-hello-container .bb-hello-header h1 {
    line-height: inherit;
  }
  #bp-hello-container.bp-hello-buddyboss-app, #bp-hello-container.bb-hello-buddyboss-app,
  #bb-hello-container.bp-hello-buddyboss-app,
  #bb-hello-container.bb-hello-buddyboss-app {
    bottom: auto;
    width: 105vmin;
    max-width: 900px;
    left: 0;
    right: 0;
    margin-left: auto;
    margin-right: auto;
    top: 50%;
    transform: translateY(-50%);
  }
  #bp-hello-container.bp-hello-buddyboss-app .bp-hello-footer,
  #bp-hello-container.bp-hello-buddyboss-app .bb-hello-footer, #bp-hello-container.bb-hello-buddyboss-app .bp-hello-footer,
  #bp-hello-container.bb-hello-buddyboss-app .bb-hello-footer,
  #bb-hello-container.bp-hello-buddyboss-app .bp-hello-footer,
  #bb-hello-container.bp-hello-buddyboss-app .bb-hello-footer,
  #bb-hello-container.bb-hello-buddyboss-app .bp-hello-footer,
  #bb-hello-container.bb-hello-buddyboss-app .bb-hello-footer {
    height: 60px;
    position: static;
  }
  .bp-hello-header,
  .bb-hello-header {
    height: auto;
    max-height: inherit;
    padding: 6px 23px;
  }
  .bp-hello-close,
  .bb-hello-close {
    right: 23px;
  }
  .bp-hello-close .close-modal:before,
  .bb-hello-close .close-modal:before {
    line-height: 0.7;
  }
  .bp-hello-footer,
  .bb-hello-footer {
    position: fixed;
    left: var(--bp-hello-container-size);
    right: var(--bp-hello-container-size);
    bottom: 23px;
    z-index: 10000;
    height: auto;
    max-height: inherit;
    padding: 6px 23px;
  }
  .bp-hello-buddyboss .bp-hello-footer,
  .bp-hello-buddyboss .bb-hello-footer,
  .bb-hello-buddyboss .bb-hello-footer {
    bottom: initial;
    left: 0;
    right: 0;
  }
  .bp-hello-content,
  .bb-hello-content {
    height: calc(100% - 90px);
    padding: 0 23px;
  }
  .bp-hello-buddyboss-app .bp-hello-content,
  .bp-hello-buddyboss-app .bb-hello-content,
  .bb-hello-buddyboss-app .bb-hello-content {
    height: calc(100% - 120px);
    padding: 0;
  }
  #bp-hello-container p,
  #bb-hello-container p {
    font-size: 14px;
  }
}

/**
 * 6.4 - Desktop Large
 */
@media screen and (min-width: 1280px) {
  #bp-hello-container,
  .bp-hello-footer,
  #bb-hello-container,
  .bb-hello-footer {
    left: calc((100% - 896px) / 2);
    right: calc((100% - 896px) / 2);
  }
  .bp-hello-buddyboss-app .bp-hello-content,
  .bp-hello-buddyboss-app .bb-hello-content,
  .bb-hello-buddyboss-app .bb-hello-content {
    height: calc(100% - 120px);
    padding: 0;
  }
}

/**
 * 7.0 - Document Mime Type check
 */
#document-upload-check-mime-type #bp-document-file-input,
#document-upload-check-mime-type input[type="submit"],
.show-document-mime-type .mime-copy {
  color: #0071a1;
  border: 1px solid #0071a1;
  background: #f3f5f6;
  font-size: 13px;
  border-radius: 3px;
  padding: 8px 10px;
  display: block;
  margin: 15px 0;
  cursor: pointer;
}

.show-document-mime-type .type {
  font-weight: 500;
  font-size: 14px;
}

.show-document-mime-type .mime-copy {
  display: inline-block;
}

/**
 * 8.0 - Update Modal
 */
.bb-update-modal#bp-hello-container, .bb-update-modal#bb-hello-container {
  bottom: initial;
  margin-top: 15px;
  max-height: calc(100% - 70px);
  overflow-x: hidden;
  overflow-y: auto;
  top: 50%;
  transform: translateY(-50%);
}

.bb-update-modal#bp-hello-container h1#bp-hello-title,
.bb-update-modal#bp-hello-container h1#bb-hello-title, .bb-update-modal#bb-hello-container h1#bp-hello-title,
.bb-update-modal#bb-hello-container h1#bb-hello-title {
  font-weight: 600;
}

.bb-update-modal .bp-hello-content,
.bb-update-modal .bb-hello-content {
  border-radius: 0 0 3px 3px;
  height: auto;
  min-height: 200px;
  max-height: calc(100% - 90px);
}

.bb-update-modal .bb-release-changelog ul,
.bb-update-modal ul.bp-hello-list,
.bb-update-modal ul.bb-hello-list {
  font-size: 14px;
}

.bb-update-modal .bb-release-changelog h2 {
  font-weight: 400;
  font-size: 14px;
  margin-bottom: 25px;
}

.bb-update-modal .video-wrapper {
  height: auto;
}

.bb-update-modal .bp-hello-header,
.bb-update-modal .bb-hello-header {
  height: auto;
  max-height: initial;
}

.bb-update-modal #bb-release-content a {
  text-decoration: none;
}

.bb-release-content {
  margin-bottom: 20px;
}

/**
 * 9.0 - Tabs
 */
.bp-hello-title h1,
.bb-hello-title h1 {
  display: inline-block;
  font-weight: 600;
  margin-right: 10px;
  vertical-align: middle;
  width: initial;
}

.bp-hello-title .bb-version,
.bb-hello-title .bb-version {
  background-color: #f1f6fb;
  border-radius: 3px;
  color: #0073aa;
  font-size: 13px;
  font-weight: 600;
  display: inline-block;
  padding: 5px 10px;
  vertical-align: middle;
}

.bb-hello-tabs {
  display: flex;
  margin: 7px 0 -13px;
}

.bb-hello-tabs li {
  margin-right: 15px;
}

.bb-hello-tabs .bb-hello-tabs_anchor {
  border-bottom: 1px solid transparent;
  display: inline-block;
  font-size: 14px;
  padding: 10px 0;
  position: relative;
  text-decoration: none;
  font-weight: 600;
}

#bp-hello-container .bb-hello-tabs .bb-hello-tabs_anchor,
#bb-hello-container .bb-hello-tabs .bb-hello-tabs_anchor {
  box-shadow: none;
  color: #1d2327;
  transition: none;
}

#bp-hello-container .bb-hello-tabs .bb-hello-tabs_anchor.is_active,
#bb-hello-container .bb-hello-tabs .bb-hello-tabs_anchor.is_active {
  border-color: var(--bp-hello-color-primary);
  color: var(--bp-hello-color-primary);
}

.bb-hello-tabs_content {
  padding-top: 10px;
}

.bb-hello-tabs_content ul,
.bb-hello-tabs_content p {
  margin-bottom: 20px;
}

.bb-hello-tabs_content:not(.is_active) {
  display: none;
}
