(o=>{wp.customize("bp_email_options[email_bg]",function(t){t.bind(function(t){t.length&&(o(".email_bg").attr("bgcolor",t),o("hr").attr("color",t))})}),wp.customize("bp_email_options[site_title_text_size]",function(t){t.bind(function(t){t.length&&o(".site_title_text_size").css("font-size",t+"px")})}),wp.customize("bp_email_options[site_title_logo_size]",function(t){t.bind(function(t){t.length&&o(".site_title_text_size img").css("width",t+"px")})}),wp.customize("bp_email_options[recipient_text_size]",function(t){t.bind(function(t){t.length&&o(".recipient_text_size").css("font-size",t+"px")})}),wp.customize("bp_email_options[site_title_text_color]",function(t){t.bind(function(t){t.length&&o(".site_title_text_color").css("color",t)})}),wp.customize("bp_email_options[recipient_text_color]",function(t){t.bind(function(t){t.length&&o(".recipient_text_color").css("color",t)})}),wp.customize("bp_email_options[highlight_color]",function(t){t.bind(function(i){i.length&&(o("a:not(.ab-item)").css("color",i),o("a:not(.ab-item)").attr("style",function(t,o){return o+"color:"+i+" !important;"}),o("hr").attr("color",i),o(".button_outline").css("border-color",i))})}),wp.customize("bp_email_options[body_bg]",function(t){t.bind(function(t){t.length&&o(".body_bg").attr("bgcolor",t)})}),wp.customize("bp_email_options[quote_bg]",function(t){t.bind(function(t){t.length&&o(".quote_bg").attr("bgcolor",t)})}),wp.customize("bp_email_options[body_border_color]",function(t){t.bind(function(t){t.length&&o(".body_border_color").css("border-color",t)})}),wp.customize("bp_email_options[body_text_size]",function(t){t.bind(function(t){t.length&&(o(".body_text_size").css("font-size",t+"px").css("line-height",Math.floor(1.618*t)+"px"),o(".button_outline").css("width",Math.floor(5.25*t)+"px").css("height",Math.floor(2.125*t)+"px").css("line-height",Math.floor(2.125*t)+"px"),o(".welcome").css("font-size",Math.floor(1.35*t)+"px"))})}),wp.customize("bp_email_options[body_text_color]",function(t){t.bind(function(t){t.length&&o(".body_text_color").css("color",t)})}),wp.customize("bp_email_options[body_secondary_text_color]",function(t){t.bind(function(t){t.length&&o(".body_secondary_text_color").css("color",t)})}),wp.customize("bp_email_options[footer_text_size]",function(t){t.bind(function(t){t.length&&o(".footer_text_size").css("font-size",t+"px").css("line-height",Math.floor(1.618*t)+"px")})}),wp.customize("bp_email_options[footer_text_color]",function(t){t.bind(function(t){t.length&&o(".footer_text_color").css("color",t)})}),wp.customize("bp_email_options[footer_text]",function(t){t.bind(function(t){o(".footer_text").text(t)})})})(jQuery);