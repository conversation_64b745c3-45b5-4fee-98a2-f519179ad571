window.bp=window.bp||{},(()=>{var e,t,o,a,r,n,i="https://www.buddyboss.com/",s=null;function l(a){var e={per_page:r.per_page,page:r.page,orderby:"category_name",order:"asc",exclude:"390262"};r.searchQuery&&(e.search=r.searchQuery),r.collectionId&&"all"!==r.collectionId?e.integrations_collection=r.collectionId:r.categoryHeadings=!0,r.categoryId&&"all"!==r.categoryId&&(e.integrations_category=r.categoryId),s&&s.abort(),s=jQuery.ajax({method:"GET",url:i+"wp-json/wp/v2/integrations",data:e,success:function(e,t,o){r.data=a?r.data.concat(e):e,r.totalpages=parseInt(o.getResponseHeader("X-WP-TotalPages")),c(r),s=null},error:function(e){console.log("Error fetching integrations"),e&&400===e.status?jQuery(".bb-integrations_loadmore").remove():s=null}})}function c(e){var t=jQuery("#tmpl-bb-integrations").html(),t=_.template(t)(e);e.previewParent&&e.previewParent.html(t)}function g(){return(new Date).getTime()}jQuery(".bb-integrations-section-listing").length&&(r={previewParent:jQuery(".bb-integrations-section-listing"),data:null,collections:null,categoriesArr:null,categoriesObj:null,searchQuery:"",collectionId:"",categoryId:"all",page:1,per_page:20,categoryHeadings:!1,totalpages:1},n=null,c(r),e=localStorage.getItem("bb-integrations-collections"),t=localStorage.getItem("bb-integrations-categories-obj"),o=localStorage.getItem("bb-integrations-categories-arr"),a=localStorage.getItem("bb-integrations-time"),e&&t&&o&&a&&g()-parseInt(a,10)<6048e5?(r.collections=JSON.parse(e),r.categoriesObj=JSON.parse(t),r.categoriesArr=JSON.parse(o),c(r),l(!1)):(a=jQuery.ajax({method:"GET",url:i+"wp-json/wp/v2/integrations_collection?per_page=99&orderby=id"}),e=jQuery.ajax({method:"GET",url:i+"wp-json/wp/v2/integrations_category?per_page=99&orderby=name&hide_empty=1"}),jQuery.when(a,e).done(function(e,t){r.collections=e[0],r.categoriesObj={},r.categoriesArr=[];for(var o=0;o<t[0].length;o++){var a=t[0][o];r.categoriesObj[a.id]=a.name,r.categoriesArr.push([a.id,a.name])}localStorage.setItem("bb-integrations-collections",JSON.stringify(r.collections)),localStorage.setItem("bb-integrations-categories-obj",JSON.stringify(r.categoriesObj)),localStorage.setItem("bb-integrations-categories-arr",JSON.stringify(r.categoriesArr)),localStorage.setItem("bb-integrations-time",g().toString()),c(r),l(!1)}).fail(function(){console.log("Error fetching collections or categories")})),jQuery(document).on("change",'input[name="integrations_collection"]',function(){jQuery(this).closest(".bb-integrations_filters").addClass("loading"),"all"===jQuery(this).siblings("span").text().toLowerCase()?r.collectionId="all":r.collectionId=jQuery(this).val(),r.page=1,l(!1)}),jQuery(document).on("keyup",'input[name="search_integrations"]',function(){n&&clearTimeout(n),n=setTimeout(function(){jQuery(this).closest(".bb-integrations_search").addClass("loading"),r.searchQuery=jQuery(this).val(),r.page=1,l(!1)}.bind(this),500)}),jQuery(document).on("change",'select[name="categories_integrations"]',function(){jQuery(this).closest(".bb-integrations_filters").addClass("loading"),r.categoryId=jQuery(this).val(),r.page=1,l(!1)}),jQuery(document).on("click",".bb-integrations_loadmore",function(e){e.preventDefault(),jQuery(this).addClass("loading"),r.page+=1,l(!0)}),jQuery(document).on("click",".bb-integrations_search .clear-search",function(e){e.preventDefault(),r.page=1,r.searchQuery="",jQuery(this).closest(".bb-integrations_search").addClass("loading"),l(!1)}))})(jQuery);