(()=>{"use strict";var e={146:(e,t,n)=>{var r=n(363),o={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},a={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},i={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},l={};function s(e){return r.isMemo(e)?i:l[e.$$typeof]||o}l[r.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},l[r.Memo]=i;var c=Object.defineProperty,u=Object.getOwnPropertyNames,d=Object.getOwnPropertySymbols,p=Object.getOwnPropertyDescriptor,f=Object.getPrototypeOf,m=Object.prototype;e.exports=function e(t,n,r){if("string"!=typeof n){if(m){var o=f(n);o&&o!==m&&e(t,o,r)}var i=u(n);d&&(i=i.concat(d(n)));for(var l=s(t),b=s(n),g=0;g<i.length;++g){var v=i[g];if(!(a[v]||r&&r[v]||b&&b[v]||l&&l[v])){var h=p(n,v);try{c(t,v,h)}catch(e){}}}}return t}},363:(e,t,n)=>{e.exports=n(799)},737:(e,t,n)=>{e.exports=n(989)},799:(e,t)=>{var n="function"==typeof Symbol&&Symbol.for,r=n?Symbol.for("react.element"):60103,o=n?Symbol.for("react.portal"):60106,a=n?Symbol.for("react.fragment"):60107,i=n?Symbol.for("react.strict_mode"):60108,l=n?Symbol.for("react.profiler"):60114,s=n?Symbol.for("react.provider"):60109,c=n?Symbol.for("react.context"):60110,u=n?Symbol.for("react.async_mode"):60111,d=n?Symbol.for("react.concurrent_mode"):60111,p=n?Symbol.for("react.forward_ref"):60112,f=n?Symbol.for("react.suspense"):60113,m=n?Symbol.for("react.suspense_list"):60120,b=n?Symbol.for("react.memo"):60115,g=n?Symbol.for("react.lazy"):60116,v=n?Symbol.for("react.block"):60121,h=n?Symbol.for("react.fundamental"):60117,y=n?Symbol.for("react.responder"):60118,E=n?Symbol.for("react.scope"):60119;function _(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case u:case d:case a:case l:case i:case f:return e;default:switch(e=e&&e.$$typeof){case c:case p:case g:case b:case s:return e;default:return t}}case o:return t}}}function w(e){return _(e)===d}t.AsyncMode=u,t.ConcurrentMode=d,t.ContextConsumer=c,t.ContextProvider=s,t.Element=r,t.ForwardRef=p,t.Fragment=a,t.Lazy=g,t.Memo=b,t.Portal=o,t.Profiler=l,t.StrictMode=i,t.Suspense=f,t.isAsyncMode=function(e){return w(e)||_(e)===u},t.isConcurrentMode=w,t.isContextConsumer=function(e){return _(e)===c},t.isContextProvider=function(e){return _(e)===s},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===r},t.isForwardRef=function(e){return _(e)===p},t.isFragment=function(e){return _(e)===a},t.isLazy=function(e){return _(e)===g},t.isMemo=function(e){return _(e)===b},t.isPortal=function(e){return _(e)===o},t.isProfiler=function(e){return _(e)===l},t.isStrictMode=function(e){return _(e)===i},t.isSuspense=function(e){return _(e)===f},t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===a||e===d||e===l||e===i||e===f||e===m||"object"==typeof e&&null!==e&&(e.$$typeof===g||e.$$typeof===b||e.$$typeof===s||e.$$typeof===c||e.$$typeof===p||e.$$typeof===h||e.$$typeof===y||e.$$typeof===E||e.$$typeof===v)},t.typeOf=_},989:(e,t)=>{var n=60103,r=60106,o=60107,a=60108,i=60114,l=60109,s=60110,c=60112,u=60113,d=60120,p=60115,f=60116;if("function"==typeof Symbol&&Symbol.for){var m=Symbol.for;n=m("react.element"),r=m("react.portal"),o=m("react.fragment"),a=m("react.strict_mode"),i=m("react.profiler"),l=m("react.provider"),s=m("react.context"),c=m("react.forward_ref"),u=m("react.suspense"),d=m("react.suspense_list"),p=m("react.memo"),f=m("react.lazy"),m("react.block"),m("react.server.block"),m("react.fundamental"),m("react.debug_trace_mode"),m("react.legacy_hidden")}t.isContextConsumer=function(e){return function(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case o:case i:case a:case u:case d:return e;default:switch(e=e&&e.$$typeof){case s:case c:case f:case p:case l:return e;default:return t}}case r:return t}}}(e)===s}}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var a=t[r]={exports:{}};return e[r](a,a.exports,n),a.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);const r=window.React;var o=n.n(r);const a=window.wp.element,i=window.wp.i18n,l=window.wp.components;function s(e,t){return s=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},s(e,t)}function c(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,s(e,t)}function u(){return u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},u.apply(null,arguments)}function d(e){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},d(e)}function p(e,t,n){return(t=function(e){var t=function(e){if("object"!=d(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=d(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==d(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function m(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){p(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function b(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}var g="function"==typeof Symbol&&Symbol.observable||"@@observable",v=function(){return Math.random().toString(36).substring(7).split("").join(".")},h={INIT:"@@redux/INIT"+v(),REPLACE:"@@redux/REPLACE"+v(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+v()}};function y(e,t,n){var r;if("function"==typeof t&&"function"==typeof n||"function"==typeof n&&"function"==typeof arguments[3])throw new Error(b(0));if("function"==typeof t&&void 0===n&&(n=t,t=void 0),void 0!==n){if("function"!=typeof n)throw new Error(b(1));return n(y)(e,t)}if("function"!=typeof e)throw new Error(b(2));var o=e,a=t,i=[],l=i,s=!1;function c(){l===i&&(l=i.slice())}function u(){if(s)throw new Error(b(3));return a}function d(e){if("function"!=typeof e)throw new Error(b(4));if(s)throw new Error(b(5));var t=!0;return c(),l.push(e),function(){if(t){if(s)throw new Error(b(6));t=!1,c();var n=l.indexOf(e);l.splice(n,1),i=null}}}function p(e){if(!function(e){if("object"!=typeof e||null===e)return!1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}(e))throw new Error(b(7));if(void 0===e.type)throw new Error(b(8));if(s)throw new Error(b(9));try{s=!0,a=o(a,e)}finally{s=!1}for(var t=i=l,n=0;n<t.length;n++)(0,t[n])();return e}return p({type:h.INIT}),(r={dispatch:p,subscribe:d,getState:u,replaceReducer:function(e){if("function"!=typeof e)throw new Error(b(10));o=e,p({type:h.REPLACE})}})[g]=function(){var e,t=d;return(e={subscribe:function(e){if("object"!=typeof e||null===e)throw new Error(b(11));function n(){e.next&&e.next(u())}return n(),{unsubscribe:t(n)}}})[g]=function(){return this},e},r}function E(e,t){return function(){return t(e.apply(this,arguments))}}function _(e,t){if("function"==typeof e)return E(e,t);if("object"!=typeof e||null===e)throw new Error(b(16));var n={};for(var r in e){var o=e[r];"function"==typeof o&&(n[r]=E(o,t))}return n}function w(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return 0===t.length?function(e){return e}:1===t.length?t[0]:t.reduce((function(e,t){return function(){return e(t.apply(void 0,arguments))}}))}var x=o().createContext(null),I=function(e){e()},N=function(){return I},D={notify:function(){},get:function(){return[]}};function C(e,t){var n,r=D;function o(){i.onStateChange&&i.onStateChange()}function a(){n||(n=t?t.addNestedSub(o):e.subscribe(o),r=function(){var e=N(),t=null,n=null;return{clear:function(){t=null,n=null},notify:function(){e((function(){for(var e=t;e;)e.callback(),e=e.next}))},get:function(){for(var e=[],n=t;n;)e.push(n),n=n.next;return e},subscribe:function(e){var r=!0,o=n={callback:e,next:null,prev:n};return o.prev?o.prev.next=o:t=o,function(){r&&null!==t&&(r=!1,o.next?o.next.prev=o.prev:n=o.prev,o.prev?o.prev.next=o.next:t=o.next)}}}}())}var i={addNestedSub:function(e){return a(),r.subscribe(e)},notifyNestedSubs:function(){r.notify()},handleChangeWrapper:o,isSubscribed:function(){return Boolean(n)},trySubscribe:a,tryUnsubscribe:function(){n&&(n(),n=void 0,r.clear(),r=D)},getListeners:function(){return r}};return i}var S="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?r.useLayoutEffect:r.useEffect;const P=function(e){var t=e.store,n=e.context,a=e.children,i=(0,r.useMemo)((function(){var e=C(t);return{store:t,subscription:e}}),[t]),l=(0,r.useMemo)((function(){return t.getState()}),[t]);S((function(){var e=i.subscription;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),l!==t.getState()&&e.notifyNestedSubs(),function(){e.tryUnsubscribe(),e.onStateChange=null}}),[i,l]);var s=n||x;return o().createElement(s.Provider,{value:i},a)};function A(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}var B=n(146),O=n.n(B),R=n(737),T=["getDisplayName","methodName","renderCountProp","shouldHandleStateChanges","storeKey","withRef","forwardRef","context"],k=["reactReduxForwardedRef"],M=[],L=[null,null];function G(e,t){var n=e[1];return[t.payload,n+1]}function F(e,t,n){S((function(){return e.apply(void 0,t)}),n)}function j(e,t,n,r,o,a,i){e.current=r,t.current=o,n.current=!1,a.current&&(a.current=null,i())}function W(e,t,n,r,o,a,i,l,s,c){if(e){var u=!1,d=null,p=function(){if(!u){var e,n,p=t.getState();try{e=r(p,o.current)}catch(e){n=e,d=e}n||(d=null),e===a.current?i.current||s():(a.current=e,l.current=e,i.current=!0,c({type:"STORE_UPDATED",payload:{error:n}}))}};return n.onStateChange=p,n.trySubscribe(),p(),function(){if(u=!0,n.tryUnsubscribe(),n.onStateChange=null,d)throw d}}}var U=function(){return[null,0]};function H(e,t){void 0===t&&(t={});var n=t,a=n.getDisplayName,i=void 0===a?function(e){return"ConnectAdvanced("+e+")"}:a,l=n.methodName,s=void 0===l?"connectAdvanced":l,c=n.renderCountProp,d=void 0===c?void 0:c,p=n.shouldHandleStateChanges,f=void 0===p||p,m=n.storeKey,b=void 0===m?"store":m,g=(n.withRef,n.forwardRef),v=void 0!==g&&g,h=n.context,y=void 0===h?x:h,E=A(n,T),_=y;return function(t){var n=t.displayName||t.name||"Component",a=i(n),l=u({},E,{getDisplayName:i,methodName:s,renderCountProp:d,shouldHandleStateChanges:f,storeKey:b,displayName:a,wrappedComponentName:n,WrappedComponent:t}),c=E.pure,p=c?r.useMemo:function(e){return e()};function m(n){var a=(0,r.useMemo)((function(){var e=n.reactReduxForwardedRef,t=A(n,k);return[n.context,e,t]}),[n]),i=a[0],s=a[1],c=a[2],d=(0,r.useMemo)((function(){return i&&i.Consumer&&(0,R.isContextConsumer)(o().createElement(i.Consumer,null))?i:_}),[i,_]),m=(0,r.useContext)(d),b=Boolean(n.store)&&Boolean(n.store.getState)&&Boolean(n.store.dispatch);Boolean(m)&&Boolean(m.store);var g=b?n.store:m.store,v=(0,r.useMemo)((function(){return function(t){return e(t.dispatch,l)}(g)}),[g]),h=(0,r.useMemo)((function(){if(!f)return L;var e=C(g,b?null:m.subscription),t=e.notifyNestedSubs.bind(e);return[e,t]}),[g,b,m]),y=h[0],E=h[1],w=(0,r.useMemo)((function(){return b?m:u({},m,{subscription:y})}),[b,m,y]),x=(0,r.useReducer)(G,M,U),I=x[0][0],N=x[1];if(I&&I.error)throw I.error;var D=(0,r.useRef)(),S=(0,r.useRef)(c),P=(0,r.useRef)(),B=(0,r.useRef)(!1),O=p((function(){return P.current&&c===S.current?P.current:v(g.getState(),c)}),[g,I,c]);F(j,[S,D,B,c,O,P,E]),F(W,[f,g,y,v,S,D,B,P,E,N],[g,y,v]);var T=(0,r.useMemo)((function(){return o().createElement(t,u({},O,{ref:s}))}),[s,t,O]);return(0,r.useMemo)((function(){return f?o().createElement(d.Provider,{value:w},T):T}),[d,T,w])}var g=c?o().memo(m):m;if(g.WrappedComponent=t,g.displayName=m.displayName=a,v){var h=o().forwardRef((function(e,t){return o().createElement(g,u({},e,{reactReduxForwardedRef:t}))}));return h.displayName=a,h.WrappedComponent=t,O()(h,t)}return O()(g,t)}}function q(e,t){return e===t?0!==e||0!==t||1/e==1/t:e!=e&&t!=t}function $(e,t){if(q(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(var o=0;o<n.length;o++)if(!Object.prototype.hasOwnProperty.call(t,n[o])||!q(e[n[o]],t[n[o]]))return!1;return!0}function V(e){return function(t,n){var r=e(t,n);function o(){return r}return o.dependsOnOwnProps=!1,o}}function z(e){return null!==e.dependsOnOwnProps&&void 0!==e.dependsOnOwnProps?Boolean(e.dependsOnOwnProps):1!==e.length}function Y(e,t){return function(t,n){n.displayName;var r=function(e,t){return r.dependsOnOwnProps?r.mapToProps(e,t):r.mapToProps(e)};return r.dependsOnOwnProps=!0,r.mapToProps=function(t,n){r.mapToProps=e,r.dependsOnOwnProps=z(e);var o=r(t,n);return"function"==typeof o&&(r.mapToProps=o,r.dependsOnOwnProps=z(o),o=r(t,n)),o},r}}const J=[function(e){return"function"==typeof e?Y(e):void 0},function(e){return e?void 0:V((function(e){return{dispatch:e}}))},function(e){return e&&"object"==typeof e?V((function(t){return function(e,t){var n={},r=function(r){var o=e[r];"function"==typeof o&&(n[r]=function(){return t(o.apply(void 0,arguments))})};for(var o in e)r(o);return n}(e,t)})):void 0}],X=[function(e){return"function"==typeof e?Y(e):void 0},function(e){return e?void 0:V((function(){return{}}))}];function K(e,t,n){return u({},n,e,t)}const Q=[function(e){return"function"==typeof e?function(e){return function(t,n){n.displayName;var r,o=n.pure,a=n.areMergedPropsEqual,i=!1;return function(t,n,l){var s=e(t,n,l);return i?o&&a(s,r)||(r=s):(i=!0,r=s),r}}}(e):void 0},function(e){return e?void 0:function(){return K}}];var Z=["initMapStateToProps","initMapDispatchToProps","initMergeProps"];function ee(e,t,n,r){return function(o,a){return n(e(o,a),t(r,a),a)}}function te(e,t,n,r,o){var a,i,l,s,c,u=o.areStatesEqual,d=o.areOwnPropsEqual,p=o.areStatePropsEqual,f=!1;return function(o,m){return f?function(o,f){var m,b,g=!d(f,i),v=!u(o,a,f,i);return a=o,i=f,g&&v?(l=e(a,i),t.dependsOnOwnProps&&(s=t(r,i)),c=n(l,s,i)):g?(e.dependsOnOwnProps&&(l=e(a,i)),t.dependsOnOwnProps&&(s=t(r,i)),c=n(l,s,i)):v?(m=e(a,i),b=!p(m,l),l=m,b&&(c=n(l,s,i)),c):c}(o,m):(l=e(a=o,i=m),s=t(r,i),c=n(l,s,i),f=!0,c)}}function ne(e,t){var n=t.initMapStateToProps,r=t.initMapDispatchToProps,o=t.initMergeProps,a=A(t,Z),i=n(e,a),l=r(e,a),s=o(e,a);return(a.pure?te:ee)(i,l,s,e,a)}var re=["pure","areStatesEqual","areOwnPropsEqual","areStatePropsEqual","areMergedPropsEqual"];function oe(e,t,n){for(var r=t.length-1;r>=0;r--){var o=t[r](e);if(o)return o}return function(t,r){throw new Error("Invalid value of type "+typeof e+" for "+n+" argument when connecting component "+r.wrappedComponentName+".")}}function ae(e,t){return e===t}function ie(e){var t=void 0===e?{}:e,n=t.connectHOC,r=void 0===n?H:n,o=t.mapStateToPropsFactories,a=void 0===o?X:o,i=t.mapDispatchToPropsFactories,l=void 0===i?J:i,s=t.mergePropsFactories,c=void 0===s?Q:s,d=t.selectorFactory,p=void 0===d?ne:d;return function(e,t,n,o){void 0===o&&(o={});var i=o,s=i.pure,d=void 0===s||s,f=i.areStatesEqual,m=void 0===f?ae:f,b=i.areOwnPropsEqual,g=void 0===b?$:b,v=i.areStatePropsEqual,h=void 0===v?$:v,y=i.areMergedPropsEqual,E=void 0===y?$:y,_=A(i,re),w=oe(e,a,"mapStateToProps"),x=oe(t,l,"mapDispatchToProps"),I=oe(n,c,"mergeProps");return r(p,u({methodName:"connect",getDisplayName:function(e){return"Connect("+e+")"},shouldHandleStateChanges:Boolean(e),initMapStateToProps:w,initMapDispatchToProps:x,initMergeProps:I,pure:d,areStatesEqual:m,areOwnPropsEqual:g,areStatePropsEqual:h,areMergedPropsEqual:E},_))}}const le=ie(),se=window.ReactDOM;var ce,ue=n.n(se);function de(e,t){var n=(0,r.useState)((function(){return{inputs:t,result:e()}}))[0],o=(0,r.useRef)(!0),a=(0,r.useRef)(n),i=o.current||Boolean(t&&a.current.inputs&&function(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(t,a.current.inputs))?a.current:{inputs:t,result:e()};return(0,r.useEffect)((function(){o.current=!1,a.current=i}),[i]),i.result}ce=se.unstable_batchedUpdates,I=ce;var pe=de,fe=function(e,t){return de((function(){return e}),t)},me=function(e){var t=e.top,n=e.right,r=e.bottom,o=e.left;return{top:t,right:n,bottom:r,left:o,width:n-o,height:r-t,x:o,y:t,center:{x:(n+o)/2,y:(r+t)/2}}},be=function(e,t){return{top:e.top-t.top,left:e.left-t.left,bottom:e.bottom+t.bottom,right:e.right+t.right}},ge=function(e,t){return{top:e.top+t.top,left:e.left+t.left,bottom:e.bottom-t.bottom,right:e.right-t.right}},ve={top:0,right:0,bottom:0,left:0},he=function(e){var t=e.borderBox,n=e.margin,r=void 0===n?ve:n,o=e.border,a=void 0===o?ve:o,i=e.padding,l=void 0===i?ve:i,s=me(be(t,r)),c=me(ge(t,a)),u=me(ge(c,l));return{marginBox:s,borderBox:me(t),paddingBox:c,contentBox:u,margin:r,border:a,padding:l}},ye=function(e){var t=e.slice(0,-2);if("px"!==e.slice(-2))return 0;var n=Number(t);return isNaN(n)&&function(){throw new Error("Invariant failed")}(),n},Ee=function(e,t){var n,r,o=e.borderBox,a=e.border,i=e.margin,l=e.padding,s=(r=t,{top:(n=o).top+r.y,left:n.left+r.x,bottom:n.bottom+r.y,right:n.right+r.x});return he({borderBox:s,border:a,margin:i,padding:l})},_e=function(e,t){return void 0===t&&(t={x:window.pageXOffset,y:window.pageYOffset}),Ee(e,t)},we=function(e,t){var n={top:ye(t.marginTop),right:ye(t.marginRight),bottom:ye(t.marginBottom),left:ye(t.marginLeft)},r={top:ye(t.paddingTop),right:ye(t.paddingRight),bottom:ye(t.paddingBottom),left:ye(t.paddingLeft)},o={top:ye(t.borderTopWidth),right:ye(t.borderRightWidth),bottom:ye(t.borderBottomWidth),left:ye(t.borderLeftWidth)};return he({borderBox:e,margin:n,padding:r,border:o})},xe=function(e){var t=e.getBoundingClientRect(),n=window.getComputedStyle(e);return we(t,n)},Ie=Number.isNaN||function(e){return"number"==typeof e&&e!=e};function Ne(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(!((r=e[n])===(o=t[n])||Ie(r)&&Ie(o)))return!1;var r,o;return!0}const De=function(e,t){var n;void 0===t&&(t=Ne);var r,o=[],a=!1;return function(){for(var i=[],l=0;l<arguments.length;l++)i[l]=arguments[l];return a&&n===this&&t(i,o)||(r=e.apply(this,i),a=!0,n=this,o=i),r}},Ce=function(e){var t=[],n=null,r=function(){for(var r=arguments.length,o=new Array(r),a=0;a<r;a++)o[a]=arguments[a];t=o,n||(n=requestAnimationFrame((function(){n=null,e.apply(void 0,t)})))};return r.cancel=function(){n&&(cancelAnimationFrame(n),n=null)},r};function Se(e,t){}function Pe(){}function Ae(e,t,n){var r=t.map((function(t){var r,o,a=(r=n,o=t.options,u({},r,{},o));return e.addEventListener(t.eventName,t.fn,a),function(){e.removeEventListener(t.eventName,t.fn,a)}}));return function(){r.forEach((function(e){e()}))}}Se.bind(null,"warn"),Se.bind(null,"error");function Be(e){this.message=e}function Oe(e,t){if(!e)throw new Be("Invariant failed")}Be.prototype.toString=function(){return this.message};var Re=function(e){function t(){for(var t,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return(t=e.call.apply(e,[this].concat(r))||this).callbacks=null,t.unbind=Pe,t.onWindowError=function(e){var n=t.getCallbacks();n.isDragging()&&n.tryAbort(),e.error instanceof Be&&e.preventDefault()},t.getCallbacks=function(){if(!t.callbacks)throw new Error("Unable to find AppCallbacks in <ErrorBoundary/>");return t.callbacks},t.setCallbacks=function(e){t.callbacks=e},t}c(t,e);var n=t.prototype;return n.componentDidMount=function(){this.unbind=Ae(window,[{eventName:"error",fn:this.onWindowError}])},n.componentDidCatch=function(e){if(!(e instanceof Be))throw e;this.setState({})},n.componentWillUnmount=function(){this.unbind()},n.render=function(){return this.props.children(this.setCallbacks)},t}(o().Component),Te=function(e){return e+1},ke=function(e,t){var n=e.droppableId===t.droppableId,r=Te(e.index),o=Te(t.index);return n?"\n      You have moved the item from position "+r+"\n      to position "+o+"\n    ":"\n    You have moved the item from position "+r+"\n    in list "+e.droppableId+"\n    to list "+t.droppableId+"\n    in position "+o+"\n  "},Me=function(e,t,n){return t.droppableId===n.droppableId?"\n      The item "+e+"\n      has been combined with "+n.draggableId:"\n      The item "+e+"\n      in list "+t.droppableId+"\n      has been combined with "+n.draggableId+"\n      in list "+n.droppableId+"\n    "},Le=function(e){return"\n  The item has returned to its starting position\n  of "+Te(e.index)+"\n"},Ge=function(e){return"\n  You have lifted an item in position "+Te(e.source.index)+"\n"},Fe=function(e){var t=e.destination;if(t)return ke(e.source,t);var n=e.combine;return n?Me(e.draggableId,e.source,n):"You are over an area that cannot be dropped on"},je=function(e){if("CANCEL"===e.reason)return"\n      Movement cancelled.\n      "+Le(e.source)+"\n    ";var t=e.destination,n=e.combine;return t?"\n      You have dropped the item.\n      "+ke(e.source,t)+"\n    ":n?"\n      You have dropped the item.\n      "+Me(e.draggableId,e.source,n)+"\n    ":"\n    The item has been dropped while not over a drop area.\n    "+Le(e.source)+"\n  "},We={x:0,y:0},Ue=function(e,t){return{x:e.x+t.x,y:e.y+t.y}},He=function(e,t){return{x:e.x-t.x,y:e.y-t.y}},qe=function(e,t){return e.x===t.x&&e.y===t.y},$e=function(e){return{x:0!==e.x?-e.x:0,y:0!==e.y?-e.y:0}},Ve=function(e,t,n){var r;return void 0===n&&(n=0),(r={})[e]=t,r["x"===e?"y":"x"]=n,r},ze=function(e,t){return Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))},Ye=function(e,t){return Math.min.apply(Math,t.map((function(t){return ze(e,t)})))},Je=function(e){return function(t){return{x:e(t.x),y:e(t.y)}}},Xe=function(e,t){return{top:e.top+t.y,left:e.left+t.x,bottom:e.bottom+t.y,right:e.right+t.x}},Ke=function(e){return[{x:e.left,y:e.top},{x:e.right,y:e.top},{x:e.left,y:e.bottom},{x:e.right,y:e.bottom}]},Qe=function(e,t){return t&&t.shouldClipSubject?function(e,t){var n=me({top:Math.max(t.top,e.top),right:Math.min(t.right,e.right),bottom:Math.min(t.bottom,e.bottom),left:Math.max(t.left,e.left)});return n.width<=0||n.height<=0?null:n}(t.pageMarginBox,e):me(e)},Ze=function(e){var t=e.page,n=e.withPlaceholder,r=e.axis,o=e.frame,a=function(e,t){return t?Xe(e,t.scroll.diff.displacement):e}(t.marginBox,o),i=function(e,t,n){var r;return n&&n.increasedBy?u({},e,((r={})[t.end]=e[t.end]+n.increasedBy[t.line],r)):e}(a,r,n);return{page:t,withPlaceholder:n,active:Qe(i,o)}},et=function(e,t){e.frame||Oe(!1);var n=e.frame,r=He(t,n.scroll.initial),o=$e(r),a=u({},n,{scroll:{initial:n.scroll.initial,current:t,diff:{value:r,displacement:o},max:n.scroll.max}});return u({},e,{frame:a,subject:Ze({page:e.subject.page,withPlaceholder:e.subject.withPlaceholder,axis:e.axis,frame:a})})};function tt(e){return Object.values?Object.values(e):Object.keys(e).map((function(t){return e[t]}))}function nt(e,t){if(e.findIndex)return e.findIndex(t);for(var n=0;n<e.length;n++)if(t(e[n]))return n;return-1}function rt(e,t){if(e.find)return e.find(t);var n=nt(e,t);return-1!==n?e[n]:void 0}function ot(e){return Array.prototype.slice.call(e)}var at=De((function(e){return e.reduce((function(e,t){return e[t.descriptor.id]=t,e}),{})})),it=De((function(e){return e.reduce((function(e,t){return e[t.descriptor.id]=t,e}),{})})),lt=De((function(e){return tt(e)})),st=De((function(e){return tt(e)})),ct=De((function(e,t){var n=st(t).filter((function(t){return e===t.descriptor.droppableId})).sort((function(e,t){return e.descriptor.index-t.descriptor.index}));return n}));function ut(e){return e.at&&"REORDER"===e.at.type?e.at.destination:null}function dt(e){return e.at&&"COMBINE"===e.at.type?e.at.combine:null}var pt=De((function(e,t){return t.filter((function(t){return t.descriptor.id!==e.descriptor.id}))})),ft=function(e,t){return e.descriptor.droppableId===t.descriptor.id},mt={point:We,value:0},bt={invisible:{},visible:{},all:[]},gt={displaced:bt,displacedBy:mt,at:null},vt=function(e,t){return function(n){return e<=n&&n<=t}},ht=function(e){var t=vt(e.top,e.bottom),n=vt(e.left,e.right);return function(r){if(t(r.top)&&t(r.bottom)&&n(r.left)&&n(r.right))return!0;var o=t(r.top)||t(r.bottom),a=n(r.left)||n(r.right);if(o&&a)return!0;var i=r.top<e.top&&r.bottom>e.bottom,l=r.left<e.left&&r.right>e.right;return!(!i||!l)||i&&a||l&&o}},yt=function(e){var t=vt(e.top,e.bottom),n=vt(e.left,e.right);return function(e){return t(e.top)&&t(e.bottom)&&n(e.left)&&n(e.right)}},Et={direction:"vertical",line:"y",crossAxisLine:"x",start:"top",end:"bottom",size:"height",crossAxisStart:"left",crossAxisEnd:"right",crossAxisSize:"width"},_t={direction:"horizontal",line:"x",crossAxisLine:"y",start:"left",end:"right",size:"width",crossAxisStart:"top",crossAxisEnd:"bottom",crossAxisSize:"height"},wt=function(e){var t=e.target,n=e.destination,r=e.viewport,o=e.withDroppableDisplacement,a=e.isVisibleThroughFrameFn,i=o?function(e,t){var n=t.frame?t.frame.scroll.diff.displacement:We;return Xe(e,n)}(t,n):t;return function(e,t,n){return!!t.subject.active&&n(t.subject.active)(e)}(i,n,a)&&function(e,t,n){return n(t)(e)}(i,r,a)},xt=function(e){return wt(u({},e,{isVisibleThroughFrameFn:yt}))};function It(e){var t=e.afterDragging,n=e.destination,r=e.displacedBy,o=e.viewport,a=e.forceShouldAnimate,i=e.last;return t.reduce((function(e,t){var l=function(e,t){var n=e.page.marginBox,r={top:t.point.y,right:0,bottom:0,left:t.point.x};return me(be(n,r))}(t,r),s=t.descriptor.id;if(e.all.push(s),!wt(u({},{target:l,destination:n,viewport:o,withDroppableDisplacement:!0},{isVisibleThroughFrameFn:ht})))return e.invisible[t.descriptor.id]=!0,e;var c=function(e,t,n){if("boolean"==typeof n)return n;if(!t)return!0;var r=t.invisible,o=t.visible;if(r[e])return!1;var a=o[e];return!a||a.shouldAnimate}(s,i,a),d={draggableId:s,shouldAnimate:c};return e.visible[s]=d,e}),{all:[],visible:{},invisible:{}})}function Nt(e){var t=e.insideDestination,n=e.inHomeList,r=e.displacedBy,o=e.destination,a=function(e,t){if(!e.length)return 0;var n=e[e.length-1].descriptor.index;return t.inHomeList?n:n+1}(t,{inHomeList:n});return{displaced:bt,displacedBy:r,at:{type:"REORDER",destination:{droppableId:o.descriptor.id,index:a}}}}function Dt(e){var t=e.draggable,n=e.insideDestination,r=e.destination,o=e.viewport,a=e.displacedBy,i=e.last,l=e.index,s=e.forceShouldAnimate,c=ft(t,r);if(null==l)return Nt({insideDestination:n,inHomeList:c,displacedBy:a,destination:r});var u=rt(n,(function(e){return e.descriptor.index===l}));if(!u)return Nt({insideDestination:n,inHomeList:c,displacedBy:a,destination:r});var d=pt(t,n),p=n.indexOf(u);return{displaced:It({afterDragging:d.slice(p),destination:r,displacedBy:a,last:i,viewport:o.frame,forceShouldAnimate:s}),displacedBy:a,at:{type:"REORDER",destination:{droppableId:r.descriptor.id,index:l}}}}function Ct(e,t){return Boolean(t.effected[e])}var St=function(e,t){return t.margin[e.start]+t.borderBox[e.size]/2},Pt=function(e,t,n){return t[e.crossAxisStart]+n.margin[e.crossAxisStart]+n.borderBox[e.crossAxisSize]/2},At=function(e){var t=e.axis,n=e.moveRelativeTo,r=e.isMoving;return Ve(t.line,n.marginBox[t.end]+St(t,r),Pt(t,n.marginBox,r))},Bt=function(e){var t=e.axis,n=e.moveRelativeTo,r=e.isMoving;return Ve(t.line,n.marginBox[t.start]-function(e,t){return t.margin[e.end]+t.borderBox[e.size]/2}(t,r),Pt(t,n.marginBox,r))},Ot=function(e,t){var n=e.frame;return n?Ue(t,n.scroll.diff.displacement):t},Rt=function(e){var t=function(e){var t=e.impact,n=e.draggable,r=e.droppable,o=e.draggables,a=e.afterCritical,i=n.page.borderBox.center,l=t.at;return r&&l?"REORDER"===l.type?function(e){var t=e.impact,n=e.draggable,r=e.draggables,o=e.droppable,a=e.afterCritical,i=ct(o.descriptor.id,r),l=n.page,s=o.axis;if(!i.length)return function(e){var t=e.axis,n=e.moveInto,r=e.isMoving;return Ve(t.line,n.contentBox[t.start]+St(t,r),Pt(t,n.contentBox,r))}({axis:s,moveInto:o.page,isMoving:l});var c=t.displaced,u=t.displacedBy,d=c.all[0];if(d){var p=r[d];if(Ct(d,a))return Bt({axis:s,moveRelativeTo:p.page,isMoving:l});var f=Ee(p.page,u.point);return Bt({axis:s,moveRelativeTo:f,isMoving:l})}var m=i[i.length-1];if(m.descriptor.id===n.descriptor.id)return l.borderBox.center;if(Ct(m.descriptor.id,a)){var b=Ee(m.page,$e(a.displacedBy.point));return At({axis:s,moveRelativeTo:b,isMoving:l})}return At({axis:s,moveRelativeTo:m.page,isMoving:l})}({impact:t,draggable:n,draggables:o,droppable:r,afterCritical:a}):function(e){var t=e.afterCritical,n=e.impact,r=e.draggables,o=dt(n);o||Oe(!1);var a=o.draggableId,i=r[a].page.borderBox.center,l=function(e){var t=e.displaced,n=e.afterCritical,r=e.combineWith,o=e.displacedBy,a=Boolean(t.visible[r]||t.invisible[r]);return Ct(r,n)?a?We:$e(o.point):a?o.point:We}({displaced:n.displaced,afterCritical:t,combineWith:a,displacedBy:n.displacedBy});return Ue(i,l)}({impact:t,draggables:o,afterCritical:a}):i}(e),n=e.droppable;return n?Ot(n,t):t},Tt=function(e,t){var n=He(t,e.scroll.initial),r=$e(n);return{frame:me({top:t.y,bottom:t.y+e.frame.height,left:t.x,right:t.x+e.frame.width}),scroll:{initial:e.scroll.initial,max:e.scroll.max,current:t,diff:{value:n,displacement:r}}}};function kt(e,t){return e.map((function(e){return t[e]}))}var Mt=function(e){var t,n,r=e.pageBorderBoxCenter,o=e.draggable,a=(t=e.viewport,n=r,Ue(t.scroll.diff.displacement,n)),i=He(a,o.page.borderBox.center);return Ue(o.client.borderBox.center,i)},Lt=function(e){var t=e.draggable,n=e.destination,r=e.newPageBorderBoxCenter,o=e.viewport,a=e.withDroppableDisplacement,i=e.onlyOnMainAxis,l=void 0!==i&&i,s=He(r,t.page.borderBox.center),c={target:Xe(t.page.borderBox,s),destination:n,withDroppableDisplacement:a,viewport:o};return l?function(e){return wt(u({},e,{isVisibleThroughFrameFn:(t=e.destination.axis,function(e){var n=vt(e.top,e.bottom),r=vt(e.left,e.right);return function(e){return t===Et?n(e.top)&&n(e.bottom):r(e.left)&&r(e.right)}})}));var t}(c):xt(c)},Gt=function(e){var t=e.isMovingForward,n=e.draggable,r=e.destination,o=e.draggables,a=e.previousImpact,i=e.viewport,l=e.previousPageBorderBoxCenter,s=e.previousClientSelection,c=e.afterCritical;if(!r.isEnabled)return null;var d=ct(r.descriptor.id,o),p=ft(n,r),f=function(e){var t=e.isMovingForward,n=e.draggable,r=e.destination,o=e.insideDestination,a=e.previousImpact;if(!r.isCombineEnabled)return null;if(!ut(a))return null;function i(e){var t={type:"COMBINE",combine:{draggableId:e,droppableId:r.descriptor.id}};return u({},a,{at:t})}var l=a.displaced.all,s=l.length?l[0]:null;if(t)return s?i(s):null;var c=pt(n,o);if(!s)return c.length?i(c[c.length-1].descriptor.id):null;var d=nt(c,(function(e){return e.descriptor.id===s}));-1===d&&Oe(!1);var p=d-1;return p<0?null:i(c[p].descriptor.id)}({isMovingForward:t,draggable:n,destination:r,insideDestination:d,previousImpact:a})||function(e){var t=e.isMovingForward,n=e.isInHomeList,r=e.draggable,o=e.draggables,a=e.destination,i=e.insideDestination,l=e.previousImpact,s=e.viewport,c=e.afterCritical,u=l.at;if(u||Oe(!1),"REORDER"===u.type){var d=function(e){var t=e.isMovingForward,n=e.isInHomeList,r=e.insideDestination,o=e.location;if(!r.length)return null;var a=o.index,i=t?a+1:a-1,l=r[0].descriptor.index,s=r[r.length-1].descriptor.index;return i<l||i>(n?s:s+1)?null:i}({isMovingForward:t,isInHomeList:n,location:u.destination,insideDestination:i});return null==d?null:Dt({draggable:r,insideDestination:i,destination:a,viewport:s,last:l.displaced,displacedBy:l.displacedBy,index:d})}var p=function(e){var t=e.isMovingForward,n=e.draggables,r=e.combine,o=e.afterCritical;if(!e.destination.isCombineEnabled)return null;var a=r.draggableId,i=n[a].descriptor.index;return Ct(a,o)?t?i:i-1:t?i+1:i}({isMovingForward:t,destination:a,displaced:l.displaced,draggables:o,combine:u.combine,afterCritical:c});return null==p?null:Dt({draggable:r,insideDestination:i,destination:a,viewport:s,last:l.displaced,displacedBy:l.displacedBy,index:p})}({isMovingForward:t,isInHomeList:p,draggable:n,draggables:o,destination:r,insideDestination:d,previousImpact:a,viewport:i,afterCritical:c});if(!f)return null;var m=Rt({impact:f,draggable:n,droppable:r,draggables:o,afterCritical:c});if(Lt({draggable:n,destination:r,newPageBorderBoxCenter:m,viewport:i.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0}))return{clientSelection:Mt({pageBorderBoxCenter:m,draggable:n,viewport:i}),impact:f,scrollJumpRequest:null};var b=He(m,l),g=function(e){var t=e.impact,n=e.viewport,r=e.destination,o=e.draggables,a=e.maxScrollChange,i=Tt(n,Ue(n.scroll.current,a)),l=r.frame?et(r,Ue(r.frame.scroll.current,a)):r,s=t.displaced,c=It({afterDragging:kt(s.all,o),destination:r,displacedBy:t.displacedBy,viewport:i.frame,last:s,forceShouldAnimate:!1}),d=It({afterDragging:kt(s.all,o),destination:l,displacedBy:t.displacedBy,viewport:n.frame,last:s,forceShouldAnimate:!1}),p={},f={},m=[s,c,d];return s.all.forEach((function(e){var t=function(e,t){for(var n=0;n<t.length;n++){var r=t[n].visible[e];if(r)return r}return null}(e,m);t?f[e]=t:p[e]=!0})),u({},t,{displaced:{all:s.all,invisible:p,visible:f}})}({impact:f,viewport:i,destination:r,draggables:o,maxScrollChange:b});return{clientSelection:s,impact:g,scrollJumpRequest:b}},Ft=function(e){var t=e.subject.active;return t||Oe(!1),t},jt=function(e,t){var n=e.page.borderBox.center;return Ct(e.descriptor.id,t)?He(n,t.displacedBy.point):n},Wt=function(e,t){var n=e.page.borderBox;return Ct(e.descriptor.id,t)?Xe(n,$e(t.displacedBy.point)):n},Ut=De((function(e,t){var n=t[e.line];return{value:n,point:Ve(e.line,n)}})),Ht=function(e,t){return u({},e,{scroll:u({},e.scroll,{max:t})})},qt=function(e,t,n){var r=e.frame;ft(t,e)&&Oe(!1),e.subject.withPlaceholder&&Oe(!1);var o=Ut(e.axis,t.displaceBy).point,a=function(e,t,n){var r=e.axis;if("virtual"===e.descriptor.mode)return Ve(r.line,t[r.line]);var o=e.subject.page.contentBox[r.size],a=ct(e.descriptor.id,n).reduce((function(e,t){return e+t.client.marginBox[r.size]}),0)+t[r.line]-o;return a<=0?null:Ve(r.line,a)}(e,o,n),i={placeholderSize:o,increasedBy:a,oldFrameMaxScroll:e.frame?e.frame.scroll.max:null};if(!r)return u({},e,{subject:Ze({page:e.subject.page,withPlaceholder:i,axis:e.axis,frame:e.frame})});var l=a?Ue(r.scroll.max,a):r.scroll.max,s=Ht(r,l);return u({},e,{subject:Ze({page:e.subject.page,withPlaceholder:i,axis:e.axis,frame:s}),frame:s})},$t=function(e){var t=e.at;return t?"REORDER"===t.type?t.destination.droppableId:t.combine.droppableId:null},Vt=function(e){var t=e.state,n=e.type,r=function(e,t){var n=$t(e);return n?t[n]:null}(t.impact,t.dimensions.droppables),o=Boolean(r),a=t.dimensions.droppables[t.critical.droppable.id],i=r||a,l=i.axis.direction,s="vertical"===l&&("MOVE_UP"===n||"MOVE_DOWN"===n)||"horizontal"===l&&("MOVE_LEFT"===n||"MOVE_RIGHT"===n);if(s&&!o)return null;var c="MOVE_DOWN"===n||"MOVE_RIGHT"===n,u=t.dimensions.draggables[t.critical.draggable.id],d=t.current.page.borderBoxCenter,p=t.dimensions,f=p.draggables,m=p.droppables;return s?Gt({isMovingForward:c,previousPageBorderBoxCenter:d,draggable:u,destination:i,draggables:f,viewport:t.viewport,previousClientSelection:t.current.client.selection,previousImpact:t.impact,afterCritical:t.afterCritical}):function(e){var t=e.isMovingForward,n=e.previousPageBorderBoxCenter,r=e.draggable,o=e.isOver,a=e.draggables,i=e.droppables,l=e.viewport,s=e.afterCritical,c=function(e){var t=e.isMovingForward,n=e.pageBorderBoxCenter,r=e.source,o=e.droppables,a=e.viewport,i=r.subject.active;if(!i)return null;var l=r.axis,s=vt(i[l.start],i[l.end]),c=lt(o).filter((function(e){return e!==r})).filter((function(e){return e.isEnabled})).filter((function(e){return Boolean(e.subject.active)})).filter((function(e){return ht(a.frame)(Ft(e))})).filter((function(e){var n=Ft(e);return t?i[l.crossAxisEnd]<n[l.crossAxisEnd]:n[l.crossAxisStart]<i[l.crossAxisStart]})).filter((function(e){var t=Ft(e),n=vt(t[l.start],t[l.end]);return s(t[l.start])||s(t[l.end])||n(i[l.start])||n(i[l.end])})).sort((function(e,n){var r=Ft(e)[l.crossAxisStart],o=Ft(n)[l.crossAxisStart];return t?r-o:o-r})).filter((function(e,t,n){return Ft(e)[l.crossAxisStart]===Ft(n[0])[l.crossAxisStart]}));if(!c.length)return null;if(1===c.length)return c[0];var u=c.filter((function(e){return vt(Ft(e)[l.start],Ft(e)[l.end])(n[l.line])}));return 1===u.length?u[0]:u.length>1?u.sort((function(e,t){return Ft(e)[l.start]-Ft(t)[l.start]}))[0]:c.sort((function(e,t){var r=Ye(n,Ke(Ft(e))),o=Ye(n,Ke(Ft(t)));return r!==o?r-o:Ft(e)[l.start]-Ft(t)[l.start]}))[0]}({isMovingForward:t,pageBorderBoxCenter:n,source:o,droppables:i,viewport:l});if(!c)return null;var u=ct(c.descriptor.id,a),d=function(e){var t=e.pageBorderBoxCenter,n=e.viewport,r=e.destination,o=e.afterCritical,a=e.insideDestination.filter((function(e){return xt({target:Wt(e,o),destination:r,viewport:n.frame,withDroppableDisplacement:!0})})).sort((function(e,n){var a=ze(t,Ot(r,jt(e,o))),i=ze(t,Ot(r,jt(n,o)));return a<i?-1:i<a?1:e.descriptor.index-n.descriptor.index}));return a[0]||null}({pageBorderBoxCenter:n,viewport:l,destination:c,insideDestination:u,afterCritical:s}),p=function(e){var t=e.previousPageBorderBoxCenter,n=e.moveRelativeTo,r=e.insideDestination,o=e.draggable,a=e.draggables,i=e.destination,l=e.viewport,s=e.afterCritical;if(!n){if(r.length)return null;var c={displaced:bt,displacedBy:mt,at:{type:"REORDER",destination:{droppableId:i.descriptor.id,index:0}}},u=Rt({impact:c,draggable:o,droppable:i,draggables:a,afterCritical:s}),d=ft(o,i)?i:qt(i,o,a);return Lt({draggable:o,destination:d,newPageBorderBoxCenter:u,viewport:l.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0})?c:null}var p,f=Boolean(t[i.axis.line]<=n.page.borderBox.center[i.axis.line]),m=(p=n.descriptor.index,n.descriptor.id===o.descriptor.id||f?p:p+1);return Dt({draggable:o,insideDestination:r,destination:i,viewport:l,displacedBy:Ut(i.axis,o.displaceBy),last:bt,index:m})}({previousPageBorderBoxCenter:n,destination:c,draggable:r,draggables:a,moveRelativeTo:d,insideDestination:u,viewport:l,afterCritical:s});if(!p)return null;var f=Rt({impact:p,draggable:r,droppable:c,draggables:a,afterCritical:s});return{clientSelection:Mt({pageBorderBoxCenter:f,draggable:r,viewport:l}),impact:p,scrollJumpRequest:null}}({isMovingForward:c,previousPageBorderBoxCenter:d,draggable:u,isOver:i,draggables:f,droppables:m,viewport:t.viewport,afterCritical:t.afterCritical})};function zt(e){return"DRAGGING"===e.phase||"COLLECTING"===e.phase}function Yt(e){var t=vt(e.top,e.bottom),n=vt(e.left,e.right);return function(e){return t(e.y)&&n(e.x)}}var Jt=function(e,t){return me(Xe(e,t))};function Xt(e){var t=e.displaced,n=e.id;return Boolean(t.visible[n]||t.invisible[n])}var Kt=function(e){var t=e.pageOffset,n=e.draggable,r=e.draggables,o=e.droppables,a=e.previousImpact,i=e.viewport,l=e.afterCritical,s=Jt(n.page.borderBox,t),c=function(e){var t=e.pageBorderBox,n=e.draggable,r=e.droppables,o=lt(r).filter((function(e){if(!e.isEnabled)return!1;var n,r,o=e.subject.active;if(!o)return!1;if(r=o,!((n=t).left<r.right&&n.right>r.left&&n.top<r.bottom&&n.bottom>r.top))return!1;if(Yt(o)(t.center))return!0;var a=e.axis,i=o.center[a.crossAxisLine],l=t[a.crossAxisStart],s=t[a.crossAxisEnd],c=vt(o[a.crossAxisStart],o[a.crossAxisEnd]),u=c(l),d=c(s);return!u&&!d||(u?l<i:s>i)}));return o.length?1===o.length?o[0].descriptor.id:function(e){var t=e.pageBorderBox,n=e.candidates,r=e.draggable.page.borderBox.center,o=n.map((function(e){var n=e.axis,o=Ve(e.axis.line,t.center[n.line],e.page.borderBox.center[n.crossAxisLine]);return{id:e.descriptor.id,distance:ze(r,o)}})).sort((function(e,t){return t.distance-e.distance}));return o[0]?o[0].id:null}({pageBorderBox:t,draggable:n,candidates:o}):null}({pageBorderBox:s,draggable:n,droppables:o});if(!c)return gt;var u=o[c],d=ct(u.descriptor.id,r),p=function(e,t){var n=e.frame;return n?Jt(t,n.scroll.diff.value):t}(u,s);return function(e){var t=e.draggable,n=e.pageBorderBoxWithDroppableScroll,r=e.previousImpact,o=e.destination,a=e.insideDestination,i=e.afterCritical;if(!o.isCombineEnabled)return null;var l=o.axis,s=Ut(o.axis,t.displaceBy),c=s.value,u=n[l.start],d=n[l.end],p=rt(pt(t,a),(function(e){var t=e.descriptor.id,n=e.page.borderBox,o=n[l.size]/4,a=Ct(t,i),s=Xt({displaced:r.displaced,id:t});return a?s?d>n[l.start]+o&&d<n[l.end]-o:u>n[l.start]-c+o&&u<n[l.end]-c-o:s?d>n[l.start]+c+o&&d<n[l.end]+c-o:u>n[l.start]+o&&u<n[l.end]-o}));return p?{displacedBy:s,displaced:r.displaced,at:{type:"COMBINE",combine:{draggableId:p.descriptor.id,droppableId:o.descriptor.id}}}:null}({pageBorderBoxWithDroppableScroll:p,draggable:n,previousImpact:a,destination:u,insideDestination:d,afterCritical:l})||function(e){var t=e.pageBorderBoxWithDroppableScroll,n=e.draggable,r=e.destination,o=e.insideDestination,a=e.last,i=e.viewport,l=e.afterCritical,s=r.axis,c=Ut(r.axis,n.displaceBy),u=c.value,d=t[s.start],p=t[s.end],f=function(e){var t=e.draggable,n=e.closest;return n?e.inHomeList&&n.descriptor.index>t.descriptor.index?n.descriptor.index-1:n.descriptor.index:null}({draggable:n,closest:rt(pt(n,o),(function(e){var t=e.descriptor.id,n=e.page.borderBox.center[s.line],r=Ct(t,l),o=Xt({displaced:a,id:t});return r?o?p<=n:d<n-u:o?p<=n+u:d<n})),inHomeList:ft(n,r)});return Dt({draggable:n,insideDestination:o,destination:r,viewport:i,last:a,displacedBy:c,index:f})}({pageBorderBoxWithDroppableScroll:p,draggable:n,destination:u,insideDestination:d,last:a.displaced,viewport:i,afterCritical:l})},Qt=function(e,t){var n;return u({},e,((n={})[t.descriptor.id]=t,n))},Zt=function(e){var t=e.state,n=e.clientSelection,r=e.dimensions,o=e.viewport,a=e.impact,i=e.scrollJumpRequest,l=o||t.viewport,s=r||t.dimensions,c=n||t.current.client.selection,d=He(c,t.initial.client.selection),p={offset:d,selection:c,borderBoxCenter:Ue(t.initial.client.borderBoxCenter,d)},f={selection:Ue(p.selection,l.scroll.current),borderBoxCenter:Ue(p.borderBoxCenter,l.scroll.current),offset:Ue(p.offset,l.scroll.diff.value)},m={client:p,page:f};if("COLLECTING"===t.phase)return u({phase:"COLLECTING"},t,{dimensions:s,viewport:l,current:m});var b=s.draggables[t.critical.draggable.id],g=a||Kt({pageOffset:f.offset,draggable:b,draggables:s.draggables,droppables:s.droppables,previousImpact:t.impact,viewport:l,afterCritical:t.afterCritical}),v=function(e){var t=e.draggable,n=e.draggables,r=e.droppables,o=e.impact,a=function(e){var t=e.previousImpact,n=e.impact,r=e.droppables,o=$t(t),a=$t(n);if(!o)return r;if(o===a)return r;var i=r[o];if(!i.subject.withPlaceholder)return r;var l=function(e){var t=e.subject.withPlaceholder;t||Oe(!1);var n=e.frame;if(!n)return u({},e,{subject:Ze({page:e.subject.page,axis:e.axis,frame:null,withPlaceholder:null})});var r=t.oldFrameMaxScroll;r||Oe(!1);var o=Ht(n,r);return u({},e,{subject:Ze({page:e.subject.page,axis:e.axis,frame:o,withPlaceholder:null}),frame:o})}(i);return Qt(r,l)}({previousImpact:e.previousImpact,impact:o,droppables:r}),i=$t(o);if(!i)return a;var l=r[i];if(ft(t,l))return a;if(l.subject.withPlaceholder)return a;var s=qt(l,t,n);return Qt(a,s)}({draggable:b,impact:g,previousImpact:t.impact,draggables:s.draggables,droppables:s.droppables});return u({},t,{current:m,dimensions:{draggables:s.draggables,droppables:v},impact:g,viewport:l,scrollJumpRequest:i||null,forceShouldAnimate:!i&&null})},en=function(e){var t=e.impact,n=e.viewport,r=e.draggables,o=e.destination,a=e.forceShouldAnimate,i=t.displaced,l=function(e,t){return e.map((function(e){return t[e]}))}(i.all,r);return u({},t,{displaced:It({afterDragging:l,destination:o,displacedBy:t.displacedBy,viewport:n.frame,forceShouldAnimate:a,last:i})})},tn=function(e){var t=e.impact,n=e.draggable,r=e.droppable,o=e.draggables,a=e.viewport,i=e.afterCritical,l=Rt({impact:t,draggable:n,draggables:o,droppable:r,afterCritical:i});return Mt({pageBorderBoxCenter:l,draggable:n,viewport:a})},nn=function(e){var t=e.state,n=e.dimensions,r=e.viewport;"SNAP"!==t.movementMode&&Oe(!1);var o=t.impact,a=r||t.viewport,i=n||t.dimensions,l=i.draggables,s=i.droppables,c=l[t.critical.draggable.id],u=$t(o);u||Oe(!1);var d=s[u],p=en({impact:o,viewport:a,destination:d,draggables:l}),f=tn({impact:p,draggable:c,droppable:d,draggables:l,viewport:a,afterCritical:t.afterCritical});return Zt({impact:p,clientSelection:f,state:t,dimensions:i,viewport:a})},rn=function(e){var t=e.draggable,n=e.home,r=e.draggables,o=e.viewport,a=Ut(n.axis,t.displaceBy),i=ct(n.descriptor.id,r),l=i.indexOf(t);-1===l&&Oe(!1);var s,c=i.slice(l+1),u=c.reduce((function(e,t){return e[t.descriptor.id]=!0,e}),{}),d={inVirtualList:"virtual"===n.descriptor.mode,displacedBy:a,effected:u};return{impact:{displaced:It({afterDragging:c,destination:n,displacedBy:a,last:null,viewport:o.frame,forceShouldAnimate:!1}),displacedBy:a,at:{type:"REORDER",destination:(s=t.descriptor,{index:s.index,droppableId:s.droppableId})}},afterCritical:d}},on=function(e){return"SNAP"===e.movementMode},an=function(e,t,n){var r=function(e,t){return{draggables:e.draggables,droppables:Qt(e.droppables,t)}}(e.dimensions,t);return!on(e)||n?Zt({state:e,dimensions:r}):nn({state:e,dimensions:r})};function ln(e){return e.isDragging&&"SNAP"===e.movementMode?u({phase:"DRAGGING"},e,{scrollJumpRequest:null}):e}var sn={phase:"IDLE",completed:null,shouldFlush:!1},cn=function(e,t){if(void 0===e&&(e=sn),"FLUSH"===t.type)return u({},sn,{shouldFlush:!0});if("INITIAL_PUBLISH"===t.type){"IDLE"!==e.phase&&Oe(!1);var n=t.payload,r=n.critical,o=n.clientSelection,a=n.viewport,i=n.dimensions,l=n.movementMode,s=i.draggables[r.draggable.id],c=i.droppables[r.droppable.id],d={selection:o,borderBoxCenter:s.client.borderBox.center,offset:We},p={client:d,page:{selection:Ue(d.selection,a.scroll.initial),borderBoxCenter:Ue(d.selection,a.scroll.initial),offset:Ue(d.selection,a.scroll.diff.value)}},f=lt(i.droppables).every((function(e){return!e.isFixedOnPage})),m=rn({draggable:s,home:c,draggables:i.draggables,viewport:a}),b=m.impact;return{phase:"DRAGGING",isDragging:!0,critical:r,movementMode:l,dimensions:i,initial:p,current:p,isWindowScrollAllowed:f,impact:b,afterCritical:m.afterCritical,onLiftImpact:b,viewport:a,scrollJumpRequest:null,forceShouldAnimate:null}}if("COLLECTION_STARTING"===t.type)return"COLLECTING"===e.phase||"DROP_PENDING"===e.phase?e:("DRAGGING"!==e.phase&&Oe(!1),u({phase:"COLLECTING"},e,{phase:"COLLECTING"}));if("PUBLISH_WHILE_DRAGGING"===t.type)return"COLLECTING"!==e.phase&&"DROP_PENDING"!==e.phase&&Oe(!1),function(e){var t=e.state,n=e.published,r=n.modified.map((function(e){var n=t.dimensions.droppables[e.droppableId];return et(n,e.scroll)})),o=u({},t.dimensions.droppables,{},at(r)),a=it(function(e){var t=e.additions,n=e.updatedDroppables,r=e.viewport,o=r.scroll.diff.value;return t.map((function(e){var t=e.descriptor.droppableId,a=function(e){var t=e.frame;return t||Oe(!1),t}(n[t]),i=a.scroll.diff.value,l=function(e){var t=e.draggable,n=e.offset,r=e.initialWindowScroll,o=Ee(t.client,n),a=_e(o,r);return u({},t,{placeholder:u({},t.placeholder,{client:o}),client:o,page:a})}({draggable:e,offset:Ue(o,i),initialWindowScroll:r.scroll.initial});return l}))}({additions:n.additions,updatedDroppables:o,viewport:t.viewport})),i=u({},t.dimensions.draggables,{},a);n.removals.forEach((function(e){delete i[e]}));var l={droppables:o,draggables:i},s=$t(t.impact),c=s?l.droppables[s]:null,d=l.draggables[t.critical.draggable.id],p=l.droppables[t.critical.droppable.id],f=rn({draggable:d,home:p,draggables:i,viewport:t.viewport}),m=f.impact,b=f.afterCritical,g=c&&c.isCombineEnabled?t.impact:m,v=Kt({pageOffset:t.current.page.offset,draggable:l.draggables[t.critical.draggable.id],draggables:l.draggables,droppables:l.droppables,previousImpact:g,viewport:t.viewport,afterCritical:b}),h=u({phase:"DRAGGING"},t,{phase:"DRAGGING",impact:v,onLiftImpact:m,dimensions:l,afterCritical:b,forceShouldAnimate:!1});return"COLLECTING"===t.phase?h:u({phase:"DROP_PENDING"},h,{phase:"DROP_PENDING",reason:t.reason,isWaiting:!1})}({state:e,published:t.payload});if("MOVE"===t.type){if("DROP_PENDING"===e.phase)return e;zt(e)||Oe(!1);var g=t.payload.client;return qe(g,e.current.client.selection)?e:Zt({state:e,clientSelection:g,impact:on(e)?e.impact:null})}if("UPDATE_DROPPABLE_SCROLL"===t.type){if("DROP_PENDING"===e.phase)return ln(e);if("COLLECTING"===e.phase)return ln(e);zt(e)||Oe(!1);var v=t.payload,h=v.id,y=v.newScroll,E=e.dimensions.droppables[h];if(!E)return e;var _=et(E,y);return an(e,_,!1)}if("UPDATE_DROPPABLE_IS_ENABLED"===t.type){if("DROP_PENDING"===e.phase)return e;zt(e)||Oe(!1);var w=t.payload,x=w.id,I=w.isEnabled,N=e.dimensions.droppables[x];N||Oe(!1),N.isEnabled===I&&Oe(!1);var D=u({},N,{isEnabled:I});return an(e,D,!0)}if("UPDATE_DROPPABLE_IS_COMBINE_ENABLED"===t.type){if("DROP_PENDING"===e.phase)return e;zt(e)||Oe(!1);var C=t.payload,S=C.id,P=C.isCombineEnabled,A=e.dimensions.droppables[S];A||Oe(!1),A.isCombineEnabled===P&&Oe(!1);var B=u({},A,{isCombineEnabled:P});return an(e,B,!0)}if("MOVE_BY_WINDOW_SCROLL"===t.type){if("DROP_PENDING"===e.phase||"DROP_ANIMATING"===e.phase)return e;zt(e)||Oe(!1),e.isWindowScrollAllowed||Oe(!1);var O=t.payload.newScroll;if(qe(e.viewport.scroll.current,O))return ln(e);var R=Tt(e.viewport,O);return on(e)?nn({state:e,viewport:R}):Zt({state:e,viewport:R})}if("UPDATE_VIEWPORT_MAX_SCROLL"===t.type){if(!zt(e))return e;var T=t.payload.maxScroll;if(qe(T,e.viewport.scroll.max))return e;var k=u({},e.viewport,{scroll:u({},e.viewport.scroll,{max:T})});return u({phase:"DRAGGING"},e,{viewport:k})}if("MOVE_UP"===t.type||"MOVE_DOWN"===t.type||"MOVE_LEFT"===t.type||"MOVE_RIGHT"===t.type){if("COLLECTING"===e.phase||"DROP_PENDING"===e.phase)return e;"DRAGGING"!==e.phase&&Oe(!1);var M=Vt({state:e,type:t.type});return M?Zt({state:e,impact:M.impact,clientSelection:M.clientSelection,scrollJumpRequest:M.scrollJumpRequest}):e}if("DROP_PENDING"===t.type){var L=t.payload.reason;return"COLLECTING"!==e.phase&&Oe(!1),u({phase:"DROP_PENDING"},e,{phase:"DROP_PENDING",isWaiting:!0,reason:L})}if("DROP_ANIMATE"===t.type){var G=t.payload,F=G.completed,j=G.dropDuration,W=G.newHomeClientOffset;return"DRAGGING"!==e.phase&&"DROP_PENDING"!==e.phase&&Oe(!1),{phase:"DROP_ANIMATING",completed:F,dropDuration:j,newHomeClientOffset:W,dimensions:e.dimensions}}return"DROP_COMPLETE"===t.type?{phase:"IDLE",completed:t.payload.completed,shouldFlush:!1}:e},un=function(e){return{type:"PUBLISH_WHILE_DRAGGING",payload:e}},dn=function(){return{type:"COLLECTION_STARTING",payload:null}},pn=function(e){return{type:"UPDATE_DROPPABLE_SCROLL",payload:e}},fn=function(e){return{type:"UPDATE_DROPPABLE_IS_ENABLED",payload:e}},mn=function(e){return{type:"UPDATE_DROPPABLE_IS_COMBINE_ENABLED",payload:e}},bn=function(e){return{type:"MOVE",payload:e}},gn=function(){return{type:"MOVE_UP",payload:null}},vn=function(){return{type:"MOVE_DOWN",payload:null}},hn=function(){return{type:"MOVE_RIGHT",payload:null}},yn=function(){return{type:"MOVE_LEFT",payload:null}},En=function(e){return{type:"DROP_COMPLETE",payload:e}},wn=function(e){return{type:"DROP",payload:e}},xn="cubic-bezier(.2,1,.1,1)",In=0,Nn=.7,Dn=.75,Cn="0.2s cubic-bezier(0.2, 0, 0, 1)",Sn={fluid:"opacity "+Cn,snap:"transform "+Cn+", opacity "+Cn,drop:function(e){var t=e+"s "+xn;return"transform "+t+", opacity "+t},outOfTheWay:"transform "+Cn,placeholder:"height "+Cn+", width "+Cn+", margin "+Cn},Pn=function(e){return qe(e,We)?null:"translate("+e.x+"px, "+e.y+"px)"},An=Pn,Bn=.33,On=.55-Bn,Rn=function(e){var t=e.getState,n=e.dispatch;return function(e){return function(r){if("DROP"===r.type){var o=t(),a=r.payload.reason;if("COLLECTING"!==o.phase){if("IDLE"!==o.phase){"DROP_PENDING"===o.phase&&o.isWaiting&&Oe(!1),"DRAGGING"!==o.phase&&"DROP_PENDING"!==o.phase&&Oe(!1);var i=o.critical,l=o.dimensions,s=l.draggables[o.critical.draggable.id],c=function(e){var t=e.draggables,n=e.reason,r=e.lastImpact,o=e.home,a=e.viewport,i=e.onLiftImpact;return r.at&&"DROP"===n?"REORDER"===r.at.type?{impact:r,didDropInsideDroppable:!0}:{impact:u({},r,{displaced:bt}),didDropInsideDroppable:!0}:{impact:en({draggables:t,impact:i,destination:o,viewport:a,forceShouldAnimate:!0}),didDropInsideDroppable:!1}}({reason:a,lastImpact:o.impact,afterCritical:o.afterCritical,onLiftImpact:o.onLiftImpact,home:o.dimensions.droppables[o.critical.droppable.id],viewport:o.viewport,draggables:o.dimensions.draggables}),d=c.impact,p=c.didDropInsideDroppable,f=p?ut(d):null,m=p?dt(d):null,b={index:i.draggable.index,droppableId:i.droppable.id},g={draggableId:s.descriptor.id,type:s.descriptor.type,source:b,reason:a,mode:o.movementMode,destination:f,combine:m},v=function(e){var t=e.impact,n=e.draggable,r=e.dimensions,o=e.viewport,a=e.afterCritical,i=r.draggables,l=r.droppables,s=$t(t),c=s?l[s]:null,u=l[n.descriptor.droppableId],d=tn({impact:t,draggable:n,draggables:i,afterCritical:a,droppable:c||u,viewport:o});return He(d,n.client.borderBox.center)}({impact:d,draggable:s,dimensions:l,viewport:o.viewport,afterCritical:o.afterCritical}),h={critical:o.critical,afterCritical:o.afterCritical,result:g,impact:d};if(!qe(o.current.client.offset,v)||Boolean(g.combine)){var y=function(e){var t=e.reason,n=ze(e.current,e.destination);if(n<=0)return Bn;if(n>=1500)return.55;var r=Bn+On*(n/1500);return Number(("CANCEL"===t?.6*r:r).toFixed(2))}({current:o.current.client.offset,destination:v,reason:a});n({type:"DROP_ANIMATE",payload:{newHomeClientOffset:v,dropDuration:y,completed:h}})}else n(En({completed:h}))}}else n(function(e){return{type:"DROP_PENDING",payload:e}}({reason:a}))}else e(r)}}},Tn=function(){return{x:window.pageXOffset,y:window.pageYOffset}};var kn=function(e){var t=function(e){var t=e.onWindowScroll,n=Ce((function(){t(Tn())})),r=function(e){return{eventName:"scroll",options:{passive:!0,capture:!1},fn:function(t){t.target!==window&&t.target!==window.document||e()}}}(n),o=Pe;function a(){return o!==Pe}return{start:function(){a()&&Oe(!1),o=Ae(window,[r])},stop:function(){a()||Oe(!1),n.cancel(),o(),o=Pe},isActive:a}}({onWindowScroll:function(t){e.dispatch({type:"MOVE_BY_WINDOW_SCROLL",payload:{newScroll:t}})}});return function(e){return function(n){t.isActive()||"INITIAL_PUBLISH"!==n.type||t.start(),t.isActive()&&function(e){return"DROP_COMPLETE"===e.type||"DROP_ANIMATE"===e.type||"FLUSH"===e.type}(n)&&t.stop(),e(n)}}},Mn=function(e,t){t()},Ln=function(e,t){return{draggableId:e.draggable.id,type:e.droppable.type,source:{droppableId:e.droppable.id,index:e.draggable.index},mode:t}},Gn=function(e,t,n,r){if(e){var o=function(e){var t=!1,n=!1,r=setTimeout((function(){n=!0})),o=function(o){t||n||(t=!0,e(o),clearTimeout(r))};return o.wasCalled=function(){return t},o}(n);e(t,{announce:o}),o.wasCalled()||n(r(t))}else n(r(t))},Fn=function(e,t){var n=function(e,t){var n,r=(n=[],{add:function(e){var t=setTimeout((function(){return function(e){var t=nt(n,(function(t){return t.timerId===e}));-1===t&&Oe(!1),n.splice(t,1)[0].callback()}(t)})),r={timerId:t,callback:e};n.push(r)},flush:function(){if(n.length){var e=[].concat(n);n.length=0,e.forEach((function(e){clearTimeout(e.timerId),e.callback()}))}}}),o=null,a=function(n){o||Oe(!1),o=null,Mn(0,(function(){return Gn(e().onDragEnd,n,t,je)}))};return{beforeCapture:function(t,n){o&&Oe(!1),Mn(0,(function(){var r=e().onBeforeCapture;r&&r({draggableId:t,mode:n})}))},beforeStart:function(t,n){o&&Oe(!1),Mn(0,(function(){var r=e().onBeforeDragStart;r&&r(Ln(t,n))}))},start:function(n,a){o&&Oe(!1);var i=Ln(n,a);o={mode:a,lastCritical:n,lastLocation:i.source,lastCombine:null},r.add((function(){Mn(0,(function(){return Gn(e().onDragStart,i,t,Ge)}))}))},update:function(n,a){var i=ut(a),l=dt(a);o||Oe(!1);var s=!function(e,t){if(e===t)return!0;var n=e.draggable.id===t.draggable.id&&e.draggable.droppableId===t.draggable.droppableId&&e.draggable.type===t.draggable.type&&e.draggable.index===t.draggable.index,r=e.droppable.id===t.droppable.id&&e.droppable.type===t.droppable.type;return n&&r}(n,o.lastCritical);s&&(o.lastCritical=n);var c,d,p=(d=i,!(null==(c=o.lastLocation)&&null==d||null!=c&&null!=d&&c.droppableId===d.droppableId&&c.index===d.index));p&&(o.lastLocation=i);var f=!function(e,t){return null==e&&null==t||null!=e&&null!=t&&e.draggableId===t.draggableId&&e.droppableId===t.droppableId}(o.lastCombine,l);if(f&&(o.lastCombine=l),s||p||f){var m=u({},Ln(n,o.mode),{combine:l,destination:i});r.add((function(){Mn(0,(function(){return Gn(e().onDragUpdate,m,t,Fe)}))}))}},flush:function(){o||Oe(!1),r.flush()},drop:a,abort:function(){if(o){var e=u({},Ln(o.lastCritical,o.mode),{combine:null,destination:null,reason:"CANCEL"});a(e)}}}}(e,t);return function(e){return function(t){return function(r){if("BEFORE_INITIAL_CAPTURE"!==r.type){if("INITIAL_PUBLISH"===r.type){var o=r.payload.critical;return n.beforeStart(o,r.payload.movementMode),t(r),void n.start(o,r.payload.movementMode)}if("DROP_COMPLETE"===r.type){var a=r.payload.completed.result;return n.flush(),t(r),void n.drop(a)}if(t(r),"FLUSH"!==r.type){var i=e.getState();"DRAGGING"===i.phase&&n.update(i.critical,i.impact)}else n.abort()}else n.beforeCapture(r.payload.draggableId,r.payload.movementMode)}}}},jn=function(e){return function(t){return function(n){if("DROP_ANIMATION_FINISHED"===n.type){var r=e.getState();"DROP_ANIMATING"!==r.phase&&Oe(!1),e.dispatch(En({completed:r.completed}))}else t(n)}}},Wn=function(e){var t=null,n=null;return function(r){return function(o){if("FLUSH"!==o.type&&"DROP_COMPLETE"!==o.type&&"DROP_ANIMATION_FINISHED"!==o.type||(n&&(cancelAnimationFrame(n),n=null),t&&(t(),t=null)),r(o),"DROP_ANIMATE"===o.type){var a={eventName:"scroll",options:{capture:!0,passive:!1,once:!0},fn:function(){"DROP_ANIMATING"===e.getState().phase&&e.dispatch({type:"DROP_ANIMATION_FINISHED",payload:null})}};n=requestAnimationFrame((function(){n=null,t=Ae(window,[a])}))}}}},Un=function(e){return function(t){return function(n){if(t(n),"PUBLISH_WHILE_DRAGGING"===n.type){var r=e.getState();"DROP_PENDING"===r.phase&&(r.isWaiting||e.dispatch(wn({reason:r.reason})))}}}},Hn=w,qn=function(e){var t=e.scrollHeight,n=e.scrollWidth,r=e.height,o=e.width,a=He({x:n,y:t},{x:o,y:r});return{x:Math.max(0,a.x),y:Math.max(0,a.y)}},$n=function(){var e=document.documentElement;return e||Oe(!1),e},Vn=function(){var e=$n();return qn({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,width:e.clientWidth,height:e.clientHeight})};function zn(e,t,n){return n.descriptor.id!==t.id&&n.descriptor.type===t.type&&"virtual"===e.droppable.getById(n.descriptor.droppableId).descriptor.mode}var Yn,Jn,Xn=function(e,t){var n=null,r=function(e){var t=e.registry,n=e.callbacks,r={additions:{},removals:{},modified:{}},o=null,a=function(){o||(n.collectionStarting(),o=requestAnimationFrame((function(){o=null;var e=r,a=e.additions,i=e.removals,l=e.modified,s=Object.keys(a).map((function(e){return t.draggable.getById(e).getDimension(We)})).sort((function(e,t){return e.descriptor.index-t.descriptor.index})),c=Object.keys(l).map((function(e){return{droppableId:e,scroll:t.droppable.getById(e).callbacks.getScrollWhileDragging()}})),u={additions:s,removals:Object.keys(i),modified:c};r={additions:{},removals:{},modified:{}},n.publish(u)})))};return{add:function(e){var t=e.descriptor.id;r.additions[t]=e,r.modified[e.descriptor.droppableId]=!0,r.removals[t]&&delete r.removals[t],a()},remove:function(e){var t=e.descriptor;r.removals[t.id]=!0,r.modified[t.droppableId]=!0,r.additions[t.id]&&delete r.additions[t.id],a()},stop:function(){o&&(cancelAnimationFrame(o),o=null,r={additions:{},removals:{},modified:{}})}}}({callbacks:{publish:t.publishWhileDragging,collectionStarting:t.collectionStarting},registry:e}),o=function(t){n||Oe(!1);var o=n.critical.draggable;"ADDITION"===t.type&&zn(e,o,t.value)&&r.add(t.value),"REMOVAL"===t.type&&zn(e,o,t.value)&&r.remove(t.value)};return{updateDroppableIsEnabled:function(r,o){e.droppable.exists(r)||Oe(!1),n&&t.updateDroppableIsEnabled({id:r,isEnabled:o})},updateDroppableIsCombineEnabled:function(r,o){n&&(e.droppable.exists(r)||Oe(!1),t.updateDroppableIsCombineEnabled({id:r,isCombineEnabled:o}))},scrollDroppable:function(t,r){n&&e.droppable.getById(t).callbacks.scroll(r)},updateDroppableScroll:function(r,o){n&&(e.droppable.exists(r)||Oe(!1),t.updateDroppableScroll({id:r,newScroll:o}))},startPublishing:function(t){n&&Oe(!1);var r=e.draggable.getById(t.draggableId),a=e.droppable.getById(r.descriptor.droppableId),i={draggable:r.descriptor,droppable:a.descriptor},l=e.subscribe(o);return n={critical:i,unsubscribe:l},function(e){var t,n,r,o,a,i,l,s=e.critical,c=e.scrollOptions,u=e.registry,d=(t=Tn(),n=Vn(),r=t.y,o=t.x,i=(a=$n()).clientWidth,l=a.clientHeight,{frame:me({top:r,left:o,right:o+i,bottom:r+l}),scroll:{initial:t,current:t,max:n,diff:{value:We,displacement:We}}}),p=d.scroll.current,f=s.droppable,m=u.droppable.getAllByType(f.type).map((function(e){return e.callbacks.getDimensionAndWatchScroll(p,c)})),b=u.draggable.getAllByType(s.draggable.type).map((function(e){return e.getDimension(p)}));return{dimensions:{draggables:it(b),droppables:at(m)},critical:s,viewport:d}}({critical:i,registry:e,scrollOptions:t.scrollOptions})},stopPublishing:function(){if(n){r.stop();var t=n.critical.droppable;e.droppable.getAllByType(t.type).forEach((function(e){return e.callbacks.dragStopped()})),n.unsubscribe(),n=null}}}},Kn=function(e,t){return"IDLE"===e.phase||"DROP_ANIMATING"===e.phase&&e.completed.result.draggableId!==t&&"DROP"===e.completed.result.reason},Qn=function(e){window.scrollBy(e.x,e.y)},Zn=De((function(e){return lt(e).filter((function(e){return!!e.isEnabled&&!!e.frame}))})),er=function(e){return Math.pow(e,2)},tr=function(e){var t=e.startOfRange,n=e.endOfRange,r=e.current,o=n-t;return 0===o?0:(r-t)/o},nr=360,rr=1200,or=function(e){var t=e.distanceToEdge,n=e.thresholds,r=e.dragStartTime,o=e.shouldUseTimeDampening,a=function(e,t){if(e>t.startScrollingFrom)return 0;if(e<=t.maxScrollValueAt)return 28;if(e===t.startScrollingFrom)return 1;var n=tr({startOfRange:t.maxScrollValueAt,endOfRange:t.startScrollingFrom,current:e}),r=28*er(1-n);return Math.ceil(r)}(t,n);return 0===a?0:o?Math.max(function(e,t){var n=t,r=rr,o=Date.now()-n;if(o>=rr)return e;if(o<nr)return 1;var a=tr({startOfRange:nr,endOfRange:r,current:o}),i=e*er(a);return Math.ceil(i)}(a,r),1):a},ar=function(e){var t=e.container,n=e.distanceToEdges,r=e.dragStartTime,o=e.axis,a=e.shouldUseTimeDampening,i=function(e,t){return{startScrollingFrom:.25*e[t.size],maxScrollValueAt:.05*e[t.size]}}(t,o);return n[o.end]<n[o.start]?or({distanceToEdge:n[o.end],thresholds:i,dragStartTime:r,shouldUseTimeDampening:a}):-1*or({distanceToEdge:n[o.start],thresholds:i,dragStartTime:r,shouldUseTimeDampening:a})},ir=Je((function(e){return 0===e?0:e})),lr=function(e){var t=e.dragStartTime,n=e.container,r=e.subject,o=e.center,a=e.shouldUseTimeDampening,i={top:o.y-n.top,right:n.right-o.x,bottom:n.bottom-o.y,left:o.x-n.left},l=ar({container:n,distanceToEdges:i,dragStartTime:t,axis:Et,shouldUseTimeDampening:a}),s=ar({container:n,distanceToEdges:i,dragStartTime:t,axis:_t,shouldUseTimeDampening:a}),c=ir({x:s,y:l});if(qe(c,We))return null;var u=function(e){var t=e.container,n=e.subject,r=e.proposedScroll,o=n.height>t.height,a=n.width>t.width;return a||o?a&&o?null:{x:a?0:r.x,y:o?0:r.y}:r}({container:n,subject:r,proposedScroll:c});return u?qe(u,We)?null:u:null},sr=Je((function(e){return 0===e?0:e>0?1:-1})),cr=(Yn=function(e,t){return e<0?e:e>t?e-t:0},function(e){var t=e.current,n=e.max,r=e.change,o=Ue(t,r),a={x:Yn(o.x,n.x),y:Yn(o.y,n.y)};return qe(a,We)?null:a}),ur=function(e){var t=e.max,n=e.current,r=e.change,o={x:Math.max(n.x,t.x),y:Math.max(n.y,t.y)},a=sr(r),i=cr({max:o,current:n,change:a});return!i||0!==a.x&&0===i.x||0!==a.y&&0===i.y},dr=function(e,t){return ur({current:e.scroll.current,max:e.scroll.max,change:t})},pr=function(e,t){var n=e.frame;return!!n&&ur({current:n.scroll.current,max:n.scroll.max,change:t})},fr=function(e){var t=e.state,n=e.dragStartTime,r=e.shouldUseTimeDampening,o=e.scrollWindow,a=e.scrollDroppable,i=t.current.page.borderBoxCenter,l=t.dimensions.draggables[t.critical.draggable.id].page.marginBox;if(t.isWindowScrollAllowed){var s=function(e){var t=e.viewport,n=e.subject,r=e.center,o=e.shouldUseTimeDampening,a=lr({dragStartTime:e.dragStartTime,container:t.frame,subject:n,center:r,shouldUseTimeDampening:o});return a&&dr(t,a)?a:null}({dragStartTime:n,viewport:t.viewport,subject:l,center:i,shouldUseTimeDampening:r});if(s)return void o(s)}var c=function(e){var t=e.center,n=e.destination,r=e.droppables;if(n){var o=r[n];return o.frame?o:null}var a=function(e,t){var n=rt(Zn(t),(function(t){return t.frame||Oe(!1),Yt(t.frame.pageMarginBox)(e)}));return n}(t,r);return a}({center:i,destination:$t(t.impact),droppables:t.dimensions.droppables});if(c){var u=function(e){var t=e.droppable,n=e.subject,r=e.center,o=e.dragStartTime,a=e.shouldUseTimeDampening,i=t.frame;if(!i)return null;var l=lr({dragStartTime:o,container:i.pageMarginBox,subject:n,center:r,shouldUseTimeDampening:a});return l&&pr(t,l)?l:null}({dragStartTime:n,droppable:c,subject:l,center:i,shouldUseTimeDampening:r});u&&a(c.descriptor.id,u)}},mr=function(e){var t=e.move,n=e.scrollDroppable,r=e.scrollWindow;return function(e){var o=e.scrollJumpRequest;if(o){var a=$t(e.impact);a||Oe(!1);var i=function(e,t){if(!pr(e,t))return t;var r=function(e,t){var n=e.frame;return n&&pr(e,t)?cr({current:n.scroll.current,max:n.scroll.max,change:t}):null}(e,t);if(!r)return n(e.descriptor.id,t),null;var o=He(t,r);return n(e.descriptor.id,o),He(t,o)}(e.dimensions.droppables[a],o);if(i){var l=e.viewport,s=function(e,t,n){if(!e)return n;if(!dr(t,n))return n;var o=function(e,t){if(!dr(e,t))return null;var n=e.scroll.max,r=e.scroll.current;return cr({current:r,max:n,change:t})}(t,n);if(!o)return r(n),null;var a=He(n,o);return r(a),He(n,a)}(e.isWindowScrollAllowed,l,i);s&&function(e,n){var r=Ue(e.current.client.selection,n);t({client:r})}(e,s)}}}},br="data-rbd",gr={base:Jn=br+"-drag-handle",draggableId:Jn+"-draggable-id",contextId:Jn+"-context-id"},vr=function(){var e=br+"-draggable";return{base:e,contextId:e+"-context-id",id:e+"-id"}}(),hr=function(){var e=br+"-droppable";return{base:e,contextId:e+"-context-id",id:e+"-id"}}(),yr={contextId:br+"-scroll-container-context-id"},Er=function(e,t){return e.map((function(e){var n=e.styles[t];return n?e.selector+" { "+n+" }":""})).join(" ")},_r="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?r.useLayoutEffect:r.useEffect,wr=function(){var e=document.querySelector("head");return e||Oe(!1),e},xr=function(e){var t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.type="text/css",t};var Ir=function(e){return e&&e.ownerDocument?e.ownerDocument.defaultView:window};function Nr(e){return e instanceof Ir(e).HTMLElement}function Dr(){var e={draggables:{},droppables:{}},t=[];function n(e){t.length&&t.forEach((function(t){return t(e)}))}function r(t){return e.draggables[t]||null}function o(t){return e.droppables[t]||null}return{draggable:{register:function(t){e.draggables[t.descriptor.id]=t,n({type:"ADDITION",value:t})},update:function(t,n){var r=e.draggables[n.descriptor.id];r&&r.uniqueId===t.uniqueId&&(delete e.draggables[n.descriptor.id],e.draggables[t.descriptor.id]=t)},unregister:function(t){var o=t.descriptor.id,a=r(o);a&&t.uniqueId===a.uniqueId&&(delete e.draggables[o],n({type:"REMOVAL",value:t}))},getById:function(e){var t=r(e);return t||Oe(!1),t},findById:r,exists:function(e){return Boolean(r(e))},getAllByType:function(t){return tt(e.draggables).filter((function(e){return e.descriptor.type===t}))}},droppable:{register:function(t){e.droppables[t.descriptor.id]=t},unregister:function(t){var n=o(t.descriptor.id);n&&t.uniqueId===n.uniqueId&&delete e.droppables[t.descriptor.id]},getById:function(e){var t=o(e);return t||Oe(!1),t},findById:o,exists:function(e){return Boolean(o(e))},getAllByType:function(t){return tt(e.droppables).filter((function(e){return e.descriptor.type===t}))}},subscribe:function(e){return t.push(e),function(){var n=t.indexOf(e);-1!==n&&t.splice(n,1)}},clean:function(){e.draggables={},e.droppables={},t.length=0}}}var Cr=o().createContext(null),Sr=function(){var e=document.body;return e||Oe(!1),e},Pr={position:"absolute",width:"1px",height:"1px",margin:"-1px",border:"0",padding:"0",overflow:"hidden",clip:"rect(0 0 0 0)","clip-path":"inset(100%)"},Ar=0,Br={separator:"::"};function Or(e,t){return void 0===t&&(t=Br),pe((function(){return""+e+t.separator+Ar++}),[t.separator,e])}var Rr=o().createContext(null);function Tr(e){var t=(0,r.useRef)(e);return(0,r.useEffect)((function(){t.current=e})),t}var kr,Mr,Lr=((kr={})[13]=!0,kr[9]=!0,kr),Gr=function(e){Lr[e.keyCode]&&e.preventDefault()},Fr=function(){var e="visibilitychange";return"undefined"==typeof document?e:rt([e,"ms"+e,"webkit"+e,"moz"+e,"o"+e],(function(e){return"on"+e in document}))||e}(),jr={type:"IDLE"};function Wr(){}var Ur=((Mr={})[34]=!0,Mr[33]=!0,Mr[36]=!0,Mr[35]=!0,Mr);var Hr={type:"IDLE"},qr={input:!0,button:!0,textarea:!0,select:!0,option:!0,optgroup:!0,video:!0,audio:!0};function $r(e,t){if(null==t)return!1;if(Boolean(qr[t.tagName.toLowerCase()]))return!0;var n=t.getAttribute("contenteditable");return"true"===n||""===n||t!==e&&$r(e,t.parentElement)}function Vr(e,t){var n=t.target;return!!Nr(n)&&$r(e,n)}var zr=function(e){return me(e.getBoundingClientRect()).center},Yr=function(){var e="matches";return"undefined"==typeof document?e:rt([e,"msMatchesSelector","webkitMatchesSelector"],(function(e){return e in Element.prototype}))||e}();function Jr(e,t){return null==e?null:e[Yr](t)?e:Jr(e.parentElement,t)}function Xr(e,t){return e.closest?e.closest(t):Jr(e,t)}function Kr(e){e.preventDefault()}function Qr(e){var t=e.expected,n=e.phase,r=e.isLockActive;return e.shouldWarn,!!r()&&t===n}function Zr(e){var t=e.lockAPI,n=e.store,r=e.registry,o=e.draggableId;if(t.isClaimed())return!1;var a=r.draggable.findById(o);return!!a&&!!a.options.isEnabled&&!!Kn(n.getState(),o)}var eo=[function(e){var t=(0,r.useRef)(jr),n=(0,r.useRef)(Pe),o=pe((function(){return{eventName:"mousedown",fn:function(t){if(!t.defaultPrevented&&0===t.button&&!(t.ctrlKey||t.metaKey||t.shiftKey||t.altKey)){var r=e.findClosestDraggableId(t);if(r){var o=e.tryGetLock(r,l,{sourceEvent:t});if(o){t.preventDefault();var a={x:t.clientX,y:t.clientY};n.current(),u(o,a)}}}}}}),[e]),a=pe((function(){return{eventName:"webkitmouseforcewillbegin",fn:function(t){if(!t.defaultPrevented){var n=e.findClosestDraggableId(t);if(n){var r=e.findOptionsForDraggable(n);r&&(r.shouldRespectForcePress||e.canGetLock(n)&&t.preventDefault())}}}}}),[e]),i=fe((function(){n.current=Ae(window,[a,o],{passive:!1,capture:!0})}),[a,o]),l=fe((function(){"IDLE"!==t.current.type&&(t.current=jr,n.current(),i())}),[i]),s=fe((function(){var e=t.current;l(),"DRAGGING"===e.type&&e.actions.cancel({shouldBlockNextClick:!0}),"PENDING"===e.type&&e.actions.abort()}),[l]),c=fe((function(){var e=function(e){var t=e.cancel,n=e.completed,r=e.getPhase,o=e.setPhase;return[{eventName:"mousemove",fn:function(e){var t=e.button,n=e.clientX,a=e.clientY;if(0===t){var i={x:n,y:a},l=r();if("DRAGGING"===l.type)return e.preventDefault(),void l.actions.move(i);if("PENDING"!==l.type&&Oe(!1),s=l.point,c=i,Math.abs(c.x-s.x)>=5||Math.abs(c.y-s.y)>=5){var s,c;e.preventDefault();var u=l.actions.fluidLift(i);o({type:"DRAGGING",actions:u})}}}},{eventName:"mouseup",fn:function(e){var o=r();"DRAGGING"===o.type?(e.preventDefault(),o.actions.drop({shouldBlockNextClick:!0}),n()):t()}},{eventName:"mousedown",fn:function(e){"DRAGGING"===r().type&&e.preventDefault(),t()}},{eventName:"keydown",fn:function(e){if("PENDING"!==r().type)return 27===e.keyCode?(e.preventDefault(),void t()):void Gr(e);t()}},{eventName:"resize",fn:t},{eventName:"scroll",options:{passive:!0,capture:!1},fn:function(){"PENDING"===r().type&&t()}},{eventName:"webkitmouseforcedown",fn:function(e){var n=r();"IDLE"===n.type&&Oe(!1),n.actions.shouldRespectForcePress()?t():e.preventDefault()}},{eventName:Fr,fn:t}]}({cancel:s,completed:l,getPhase:function(){return t.current},setPhase:function(e){t.current=e}});n.current=Ae(window,e,{capture:!0,passive:!1})}),[s,l]),u=fe((function(e,n){"IDLE"!==t.current.type&&Oe(!1),t.current={type:"PENDING",point:n,actions:e},c()}),[c]);_r((function(){return i(),function(){n.current()}}),[i])},function(e){var t=(0,r.useRef)(Wr),n=pe((function(){return{eventName:"keydown",fn:function(n){if(!n.defaultPrevented&&32===n.keyCode){var r=e.findClosestDraggableId(n);if(r){var a=e.tryGetLock(r,s,{sourceEvent:n});if(a){n.preventDefault();var i=!0,l=a.snapLift();t.current(),t.current=Ae(window,function(e,t){function n(){t(),e.cancel()}return[{eventName:"keydown",fn:function(r){return 27===r.keyCode?(r.preventDefault(),void n()):32===r.keyCode?(r.preventDefault(),t(),void e.drop()):40===r.keyCode?(r.preventDefault(),void e.moveDown()):38===r.keyCode?(r.preventDefault(),void e.moveUp()):39===r.keyCode?(r.preventDefault(),void e.moveRight()):37===r.keyCode?(r.preventDefault(),void e.moveLeft()):void(Ur[r.keyCode]?r.preventDefault():Gr(r))}},{eventName:"mousedown",fn:n},{eventName:"mouseup",fn:n},{eventName:"click",fn:n},{eventName:"touchstart",fn:n},{eventName:"resize",fn:n},{eventName:"wheel",fn:n,options:{passive:!0}},{eventName:Fr,fn:n}]}(l,s),{capture:!0,passive:!1})}}}function s(){i||Oe(!1),i=!1,t.current(),o()}}}}),[e]),o=fe((function(){t.current=Ae(window,[n],{passive:!1,capture:!0})}),[n]);_r((function(){return o(),function(){t.current()}}),[o])},function(e){var t=(0,r.useRef)(Hr),n=(0,r.useRef)(Pe),o=fe((function(){return t.current}),[]),a=fe((function(e){t.current=e}),[]),i=pe((function(){return{eventName:"touchstart",fn:function(t){if(!t.defaultPrevented){var r=e.findClosestDraggableId(t);if(r){var o=e.tryGetLock(r,s,{sourceEvent:t});if(o){var a=t.touches[0],i={x:a.clientX,y:a.clientY};n.current(),p(o,i)}}}}}}),[e]),l=fe((function(){n.current=Ae(window,[i],{capture:!0,passive:!1})}),[i]),s=fe((function(){var e=t.current;"IDLE"!==e.type&&("PENDING"===e.type&&clearTimeout(e.longPressTimerId),a(Hr),n.current(),l())}),[l,a]),c=fe((function(){var e=t.current;s(),"DRAGGING"===e.type&&e.actions.cancel({shouldBlockNextClick:!0}),"PENDING"===e.type&&e.actions.abort()}),[s]),u=fe((function(){var e={capture:!0,passive:!1},t={cancel:c,completed:s,getPhase:o},r=Ae(window,function(e){var t=e.cancel,n=e.completed,r=e.getPhase;return[{eventName:"touchmove",options:{capture:!1},fn:function(e){var n=r();if("DRAGGING"===n.type){n.hasMoved=!0;var o=e.touches[0],a={x:o.clientX,y:o.clientY};e.preventDefault(),n.actions.move(a)}else t()}},{eventName:"touchend",fn:function(e){var o=r();"DRAGGING"===o.type?(e.preventDefault(),o.actions.drop({shouldBlockNextClick:!0}),n()):t()}},{eventName:"touchcancel",fn:function(e){"DRAGGING"===r().type?(e.preventDefault(),t()):t()}},{eventName:"touchforcechange",fn:function(e){var n=r();"IDLE"===n.type&&Oe(!1);var o=e.touches[0];if(o&&o.force>=.15){var a=n.actions.shouldRespectForcePress();if("PENDING"!==n.type)return a?n.hasMoved?void e.preventDefault():void t():void e.preventDefault();a&&t()}}},{eventName:Fr,fn:t}]}(t),e),a=Ae(window,function(e){var t=e.cancel,n=e.getPhase;return[{eventName:"orientationchange",fn:t},{eventName:"resize",fn:t},{eventName:"contextmenu",fn:function(e){e.preventDefault()}},{eventName:"keydown",fn:function(e){"DRAGGING"===n().type?(27===e.keyCode&&e.preventDefault(),t()):t()}},{eventName:Fr,fn:t}]}(t),e);n.current=function(){r(),a()}}),[c,o,s]),d=fe((function(){var e=o();"PENDING"!==e.type&&Oe(!1);var t=e.actions.fluidLift(e.point);a({type:"DRAGGING",actions:t,hasMoved:!1})}),[o,a]),p=fe((function(e,t){"IDLE"!==o().type&&Oe(!1);var n=setTimeout(d,120);a({type:"PENDING",point:t,actions:e,longPressTimerId:n}),u()}),[u,o,a,d]);_r((function(){return l(),function(){n.current();var e=o();"PENDING"===e.type&&(clearTimeout(e.longPressTimerId),a(Hr))}}),[o,l,a]),_r((function(){return Ae(window,[{eventName:"touchmove",fn:function(){},options:{capture:!1,passive:!1}}])}),[])}];function to(e){var t=e.contextId,n=e.store,o=e.registry,a=e.customSensors,i=e.enableDefaultSensors,l=[].concat(i?eo:[],a||[]),s=(0,r.useState)((function(){return function(){var e=null;function t(){e||Oe(!1),e=null}return{isClaimed:function(){return Boolean(e)},isActive:function(t){return t===e},claim:function(t){e&&Oe(!1);var n={abandon:t};return e=n,n},release:t,tryAbandon:function(){e&&(e.abandon(),t())}}}()}))[0],c=fe((function(e,t){e.isDragging&&!t.isDragging&&s.tryAbandon()}),[s]);_r((function(){var e=n.getState();return n.subscribe((function(){var t=n.getState();c(e,t),e=t}))}),[s,n,c]),_r((function(){return s.tryAbandon}),[s.tryAbandon]);for(var d=fe((function(e){return Zr({lockAPI:s,registry:o,store:n,draggableId:e})}),[s,o,n]),p=fe((function(e,r,a){return function(e){var t=e.lockAPI,n=e.contextId,r=e.store,o=e.registry,a=e.draggableId,i=e.forceSensorStop,l=e.sourceEvent;if(!Zr({lockAPI:t,store:r,registry:o,draggableId:a}))return null;var s=o.draggable.getById(a),c=function(e,t){var n="["+vr.contextId+'="'+e+'"]',r=rt(ot(document.querySelectorAll(n)),(function(e){return e.getAttribute(vr.id)===t}));return r&&Nr(r)?r:null}(n,s.descriptor.id);if(!c)return null;if(l&&!s.options.canDragInteractiveElements&&Vr(c,l))return null;var d=t.claim(i||Pe),p="PRE_DRAG";function f(){return s.options.shouldRespectForcePress}function m(){return t.isActive(d)}var b=function(e,t){Qr({expected:e,phase:p,isLockActive:m,shouldWarn:!0})&&r.dispatch(t())}.bind(null,"DRAGGING");function g(e){function n(){t.release(),p="COMPLETED"}function o(t,o){if(void 0===o&&(o={shouldBlockNextClick:!1}),e.cleanup(),o.shouldBlockNextClick){var a=Ae(window,[{eventName:"click",fn:Kr,options:{once:!0,passive:!1,capture:!0}}]);setTimeout(a)}n(),r.dispatch(wn({reason:t}))}return"PRE_DRAG"!==p&&(n(),"PRE_DRAG"!==p&&Oe(!1)),r.dispatch(function(e){return{type:"LIFT",payload:e}}(e.liftActionArgs)),p="DRAGGING",u({isActive:function(){return Qr({expected:"DRAGGING",phase:p,isLockActive:m,shouldWarn:!1})},shouldRespectForcePress:f,drop:function(e){return o("DROP",e)},cancel:function(e){return o("CANCEL",e)}},e.actions)}return{isActive:function(){return Qr({expected:"PRE_DRAG",phase:p,isLockActive:m,shouldWarn:!1})},shouldRespectForcePress:f,fluidLift:function(e){var t=Ce((function(e){b((function(){return bn({client:e})}))}));return u({},g({liftActionArgs:{id:a,clientSelection:e,movementMode:"FLUID"},cleanup:function(){return t.cancel()},actions:{move:t}}),{move:t})},snapLift:function(){var e={moveUp:function(){return b(gn)},moveRight:function(){return b(hn)},moveDown:function(){return b(vn)},moveLeft:function(){return b(yn)}};return g({liftActionArgs:{id:a,clientSelection:zr(c),movementMode:"SNAP"},cleanup:Pe,actions:e})},abort:function(){Qr({expected:"PRE_DRAG",phase:p,isLockActive:m,shouldWarn:!0})&&t.release()}}}({lockAPI:s,registry:o,contextId:t,store:n,draggableId:e,forceSensorStop:r,sourceEvent:a&&a.sourceEvent?a.sourceEvent:null})}),[t,s,o,n]),f=fe((function(e){return function(e,t){var n=function(e,t){var n,r=t.target;if(!((n=r)instanceof Ir(n).Element))return null;var o=function(e){return"["+gr.contextId+'="'+e+'"]'}(e),a=Xr(r,o);return a&&Nr(a)?a:null}(e,t);return n?n.getAttribute(gr.draggableId):null}(t,e)}),[t]),m=fe((function(e){var t=o.draggable.findById(e);return t?t.options:null}),[o.draggable]),b=fe((function(){s.isClaimed()&&(s.tryAbandon(),"IDLE"!==n.getState().phase&&n.dispatch({type:"FLUSH",payload:null}))}),[s,n]),g=fe(s.isClaimed,[s]),v=pe((function(){return{canGetLock:d,tryGetLock:p,findClosestDraggableId:f,findOptionsForDraggable:m,tryReleaseLock:b,isLockClaimed:g}}),[d,p,f,m,b,g]),h=0;h<l.length;h++)l[h](v)}function no(e){return e.current||Oe(!1),e.current}function ro(e){var t=e.contextId,n=e.setCallbacks,a=e.sensors,i=e.nonce,l=e.dragHandleUsageInstructions,s=(0,r.useRef)(null),c=Tr(e),d=fe((function(){return function(e){return{onBeforeCapture:e.onBeforeCapture,onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragEnd:e.onDragEnd,onDragUpdate:e.onDragUpdate}}(c.current)}),[c]),p=function(e){var t=pe((function(){return function(e){return"rbd-announcement-"+e}(e)}),[e]),n=(0,r.useRef)(null);return(0,r.useEffect)((function(){var e=document.createElement("div");return n.current=e,e.id=t,e.setAttribute("aria-live","assertive"),e.setAttribute("aria-atomic","true"),u(e.style,Pr),Sr().appendChild(e),function(){setTimeout((function(){var t=Sr();t.contains(e)&&t.removeChild(e),e===n.current&&(n.current=null)}))}}),[t]),fe((function(e){var t=n.current;t&&(t.textContent=e)}),[])}(t),f=function(e){var t=e.contextId,n=e.text,o=Or("hidden-text",{separator:"-"}),a=pe((function(){return"rbd-hidden-text-"+(e={contextId:t,uniqueId:o}).contextId+"-"+e.uniqueId;var e}),[o,t]);return(0,r.useEffect)((function(){var e=document.createElement("div");return e.id=a,e.textContent=n,e.style.display="none",Sr().appendChild(e),function(){var t=Sr();t.contains(e)&&t.removeChild(e)}}),[a,n]),a}({contextId:t,text:l}),g=function(e,t){var n=pe((function(){return function(e){var t,n,r,o=(t=e,function(e){return"["+e+'="'+t+'"]'}),a=(n="\n      cursor: -webkit-grab;\n      cursor: grab;\n    ",{selector:o(gr.contextId),styles:{always:"\n          -webkit-touch-callout: none;\n          -webkit-tap-highlight-color: rgba(0,0,0,0);\n          touch-action: manipulation;\n        ",resting:n,dragging:"pointer-events: none;",dropAnimating:n}}),i=[(r="\n      transition: "+Sn.outOfTheWay+";\n    ",{selector:o(vr.contextId),styles:{dragging:r,dropAnimating:r,userCancel:r}}),a,{selector:o(hr.contextId),styles:{always:"overflow-anchor: none;"}},{selector:"body",styles:{dragging:"\n        cursor: grabbing;\n        cursor: -webkit-grabbing;\n        user-select: none;\n        -webkit-user-select: none;\n        -moz-user-select: none;\n        -ms-user-select: none;\n        overflow-anchor: none;\n      "}}];return{always:Er(i,"always"),resting:Er(i,"resting"),dragging:Er(i,"dragging"),dropAnimating:Er(i,"dropAnimating"),userCancel:Er(i,"userCancel")}}(e)}),[e]),o=(0,r.useRef)(null),a=(0,r.useRef)(null),i=fe(De((function(e){var t=a.current;t||Oe(!1),t.textContent=e})),[]),l=fe((function(e){var t=o.current;t||Oe(!1),t.textContent=e}),[]);_r((function(){(o.current||a.current)&&Oe(!1);var r=xr(t),s=xr(t);return o.current=r,a.current=s,r.setAttribute(br+"-always",e),s.setAttribute(br+"-dynamic",e),wr().appendChild(r),wr().appendChild(s),l(n.always),i(n.resting),function(){var e=function(e){var t=e.current;t||Oe(!1),wr().removeChild(t),e.current=null};e(o),e(a)}}),[t,l,i,n.always,n.resting,e]);var s=fe((function(){return i(n.dragging)}),[i,n.dragging]),c=fe((function(e){i("DROP"!==e?n.userCancel:n.dropAnimating)}),[i,n.dropAnimating,n.userCancel]),u=fe((function(){a.current&&i(n.resting)}),[i,n.resting]);return pe((function(){return{dragging:s,dropping:c,resting:u}}),[s,c,u])}(t,i),v=fe((function(e){no(s).dispatch(e)}),[]),h=pe((function(){return _({publishWhileDragging:un,updateDroppableScroll:pn,updateDroppableIsEnabled:fn,updateDroppableIsCombineEnabled:mn,collectionStarting:dn},v)}),[v]),E=function(){var e=pe(Dr,[]);return(0,r.useEffect)((function(){return function(){requestAnimationFrame(e.clean)}}),[e]),e}(),x=pe((function(){return Xn(E,h)}),[E,h]),I=pe((function(){return function(e){var t=e.scrollDroppable,n=e.scrollWindow,r=e.move,o=function(e){var t=e.scrollDroppable,n=Ce(e.scrollWindow),r=Ce(t),o=null,a=function(e){o||Oe(!1);var t=o,a=t.shouldUseTimeDampening,i=t.dragStartTime;fr({state:e,scrollWindow:n,scrollDroppable:r,dragStartTime:i,shouldUseTimeDampening:a})};return{start:function(e){o&&Oe(!1);var t=Date.now(),n=!1,r=function(){n=!0};fr({state:e,dragStartTime:0,shouldUseTimeDampening:!1,scrollWindow:r,scrollDroppable:r}),o={dragStartTime:t,shouldUseTimeDampening:n},n&&a(e)},stop:function(){o&&(n.cancel(),r.cancel(),o=null)},scroll:a}}({scrollWindow:n,scrollDroppable:t}),a=mr({move:r,scrollWindow:n,scrollDroppable:t});return{scroll:function(e){"DRAGGING"===e.phase&&("FLUID"!==e.movementMode?e.scrollJumpRequest&&a(e):o.scroll(e))},start:o.start,stop:o.stop}}(u({scrollWindow:Qn,scrollDroppable:x.scrollDroppable},_({move:bn},v)))}),[x.scrollDroppable,v]),N=function(e){var t=(0,r.useRef)({}),n=(0,r.useRef)(null),o=(0,r.useRef)(null),a=(0,r.useRef)(!1),i=fe((function(e,n){var r={id:e,focus:n};return t.current[e]=r,function(){var n=t.current;n[e]!==r&&delete n[e]}}),[]),l=fe((function(t){var n=function(e,t){var n="["+gr.contextId+'="'+e+'"]',r=ot(document.querySelectorAll(n));if(!r.length)return null;var o=rt(r,(function(e){return e.getAttribute(gr.draggableId)===t}));return o&&Nr(o)?o:null}(e,t);n&&n!==document.activeElement&&n.focus()}),[e]),s=fe((function(e,t){n.current===e&&(n.current=t)}),[]),c=fe((function(){o.current||a.current&&(o.current=requestAnimationFrame((function(){o.current=null;var e=n.current;e&&l(e)})))}),[l]),u=fe((function(e){n.current=null;var t=document.activeElement;t&&t.getAttribute(gr.draggableId)===e&&(n.current=e)}),[]);return _r((function(){return a.current=!0,function(){a.current=!1;var e=o.current;e&&cancelAnimationFrame(e)}}),[]),pe((function(){return{register:i,tryRecordFocus:u,tryRestoreFocusRecorded:c,tryShiftRecord:s}}),[i,u,c,s])}(t),D=pe((function(){return function(e){var t,n=e.dimensionMarshal,r=e.focusMarshal,o=e.styleMarshal,a=e.getResponders,i=e.announce,l=e.autoScroller;return y(cn,Hn(function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){return function(){var n=e.apply(void 0,arguments),r=function(){throw new Error(b(15))},o={getState:n.getState,dispatch:function(){return r.apply(void 0,arguments)}},a=t.map((function(e){return e(o)}));return r=w.apply(void 0,a)(n.dispatch),m(m({},n),{},{dispatch:r})}}}((t=o,function(){return function(e){return function(n){"INITIAL_PUBLISH"===n.type&&t.dragging(),"DROP_ANIMATE"===n.type&&t.dropping(n.payload.completed.result.reason),"FLUSH"!==n.type&&"DROP_COMPLETE"!==n.type||t.resting(),e(n)}}}),function(e){return function(){return function(t){return function(n){"DROP_COMPLETE"!==n.type&&"FLUSH"!==n.type&&"DROP_ANIMATE"!==n.type||e.stopPublishing(),t(n)}}}}(n),function(e){return function(t){var n=t.getState,r=t.dispatch;return function(t){return function(o){if("LIFT"===o.type){var a=o.payload,i=a.id,l=a.clientSelection,s=a.movementMode,c=n();"DROP_ANIMATING"===c.phase&&r(En({completed:c.completed})),"IDLE"!==n().phase&&Oe(!1),r({type:"FLUSH",payload:null}),r({type:"BEFORE_INITIAL_CAPTURE",payload:{draggableId:i,movementMode:s}});var u={draggableId:i,scrollOptions:{shouldPublishImmediately:"SNAP"===s}},d=e.startPublishing(u),p=d.critical,f=d.dimensions,m=d.viewport;r({type:"INITIAL_PUBLISH",payload:{critical:p,dimensions:f,clientSelection:l,movementMode:s,viewport:m}})}else t(o)}}}}(n),Rn,jn,Wn,Un,function(e){return function(t){return function(n){return function(r){if(function(e){return"DROP_COMPLETE"===e.type||"DROP_ANIMATE"===e.type||"FLUSH"===e.type}(r))return e.stop(),void n(r);if("INITIAL_PUBLISH"===r.type){n(r);var o=t.getState();return"DRAGGING"!==o.phase&&Oe(!1),void e.start(o)}n(r),e.scroll(t.getState())}}}}(l),kn,function(e){var t=!1;return function(){return function(n){return function(r){if("INITIAL_PUBLISH"===r.type)return t=!0,e.tryRecordFocus(r.payload.critical.draggable.id),n(r),void e.tryRestoreFocusRecorded();if(n(r),t){if("FLUSH"===r.type)return t=!1,void e.tryRestoreFocusRecorded();if("DROP_COMPLETE"===r.type){t=!1;var o=r.payload.completed.result;o.combine&&e.tryShiftRecord(o.draggableId,o.combine.draggableId),e.tryRestoreFocusRecorded()}}}}}}(r),Fn(a,i))))}({announce:p,autoScroller:I,dimensionMarshal:x,focusMarshal:N,getResponders:d,styleMarshal:g})}),[p,I,x,N,d,g]);s.current=D;var C=fe((function(){var e=no(s);"IDLE"!==e.getState().phase&&e.dispatch({type:"FLUSH",payload:null})}),[]),S=fe((function(){var e=no(s).getState();return e.isDragging||"DROP_ANIMATING"===e.phase}),[]);n(pe((function(){return{isDragging:S,tryAbort:C}}),[S,C]));var A=fe((function(e){return Kn(no(s).getState(),e)}),[]),B=fe((function(){return zt(no(s).getState())}),[]),O=pe((function(){return{marshal:x,focus:N,contextId:t,canLift:A,isMovementAllowed:B,dragHandleUsageInstructionsId:f,registry:E}}),[t,x,f,N,A,B,E]);return to({contextId:t,store:D,registry:E,customSensors:a,enableDefaultSensors:!1!==e.enableDefaultSensors}),(0,r.useEffect)((function(){return C}),[C]),o().createElement(Rr.Provider,{value:O},o().createElement(P,{context:Cr,store:D},e.children))}var oo=0;function ao(e){var t=pe((function(){return""+oo++}),[]),n=e.dragHandleUsageInstructions||"\n  Press space bar to start a drag.\n  When dragging you can use the arrow keys to move the item around and escape to cancel.\n  Some screen readers may require you to be in focus mode or to use your pass through key\n";return o().createElement(Re,null,(function(r){return o().createElement(ro,{nonce:e.nonce,contextId:t,setCallbacks:r,dragHandleUsageInstructions:n,enableDefaultSensors:e.enableDefaultSensors,sensors:e.sensors,onBeforeCapture:e.onBeforeCapture,onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragUpdate:e.onDragUpdate,onDragEnd:e.onDragEnd},e.children)}))}var io=function(e){return function(t){return e===t}},lo=io("scroll"),so=io("auto"),co=(io("visible"),function(e,t){return t(e.overflowX)||t(e.overflowY)}),uo=function e(t){return null==t||t===document.body||t===document.documentElement?null:function(e){var t=window.getComputedStyle(e),n={overflowX:t.overflowX,overflowY:t.overflowY};return co(n,lo)||co(n,so)}(t)?t:e(t.parentElement)},po=function(e){return{x:e.scrollLeft,y:e.scrollTop}},fo=function e(t){return!!t&&("fixed"===window.getComputedStyle(t).position||e(t.parentElement))},mo={passive:!1},bo={passive:!0},go=function(e){return e.shouldPublishImmediately?mo:bo};function vo(e){var t=(0,r.useContext)(e);return t||Oe(!1),t}var ho=function(e){return e&&e.env.closestScrollable||null};function yo(){}var Eo={width:0,height:0,margin:{top:0,right:0,bottom:0,left:0}},_o=o().memo((function(e){var t=(0,r.useRef)(null),n=fe((function(){t.current&&(clearTimeout(t.current),t.current=null)}),[]),a=e.animate,i=e.onTransitionEnd,l=e.onClose,s=e.contextId,c=(0,r.useState)("open"===e.animate),u=c[0],d=c[1];(0,r.useEffect)((function(){return u?"open"!==a?(n(),d(!1),yo):t.current?yo:(t.current=setTimeout((function(){t.current=null,d(!1)})),n):yo}),[a,u,n]);var p=fe((function(e){"height"===e.propertyName&&(i(),"close"===a&&l())}),[a,l,i]),f=function(e){var t=e.isAnimatingOpenOnMount,n=e.placeholder,r=e.animate,o=function(e){var t=e.placeholder;return e.isAnimatingOpenOnMount||"close"===e.animate?Eo:{height:t.client.borderBox.height,width:t.client.borderBox.width,margin:t.client.margin}}({isAnimatingOpenOnMount:t,placeholder:n,animate:r});return{display:n.display,boxSizing:"border-box",width:o.width,height:o.height,marginTop:o.margin.top,marginRight:o.margin.right,marginBottom:o.margin.bottom,marginLeft:o.margin.left,flexShrink:"0",flexGrow:"0",pointerEvents:"none",transition:"none"!==r?Sn.placeholder:null}}({isAnimatingOpenOnMount:u,animate:e.animate,placeholder:e.placeholder});return o().createElement(e.placeholder.tagName,{style:f,"data-rbd-placeholder-context-id":s,onTransitionEnd:p,ref:e.innerRef})})),wo=o().createContext(null),xo=function(e){function t(){for(var t,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return(t=e.call.apply(e,[this].concat(r))||this).state={isVisible:Boolean(t.props.on),data:t.props.on,animate:t.props.shouldAnimate&&t.props.on?"open":"none"},t.onClose=function(){"close"===t.state.animate&&t.setState({isVisible:!1})},t}return c(t,e),t.getDerivedStateFromProps=function(e,t){return e.shouldAnimate?e.on?{isVisible:!0,data:e.on,animate:"open"}:t.isVisible?{isVisible:!0,data:t.data,animate:"close"}:{isVisible:!1,animate:"close",data:null}:{isVisible:Boolean(e.on),data:e.on,animate:"none"}},t.prototype.render=function(){if(!this.state.isVisible)return null;var e={onClose:this.onClose,data:this.state.data,animate:this.state.animate};return this.props.children(e)},t}(o().PureComponent),Io=function(e,t){return t?Sn.drop(t.duration):e?Sn.snap:Sn.fluid},No=function(e,t){return e?t?In:Nn:null};function Do(e){return"DRAGGING"===e.type?function(e){var t=e.dimension.client,n=e.offset,r=e.combineWith,o=e.dropping,a=Boolean(r),i=function(e){return null!=e.forceShouldAnimate?e.forceShouldAnimate:"SNAP"===e.mode}(e),l=Boolean(o),s=l?function(e,t){var n=Pn(e);return n?t?n+" scale("+Dn+")":n:null}(n,a):An(n);return{position:"fixed",top:t.marginBox.top,left:t.marginBox.left,boxSizing:"border-box",width:t.borderBox.width,height:t.borderBox.height,transition:Io(i,o),transform:s,opacity:No(a,l),zIndex:l?4500:5e3,pointerEvents:"none"}}(e):{transform:An((t=e).offset),transition:t.shouldAnimateDisplacement?null:"none"};var t}function Co(e){e.preventDefault()}var So=function(e,t){return e===t},Po=function(e){var t=e.combine,n=e.destination;return n?n.droppableId:t?t.droppableId:null};function Ao(e){return{isDragging:!1,isDropAnimating:!1,isClone:!1,dropAnimation:null,mode:null,draggingOver:null,combineTargetFor:e,combineWith:null}}var Bo={mapped:{type:"SECONDARY",offset:We,combineTargetFor:null,shouldAnimateDisplacement:!0,snapshot:Ao(null)}},Oo=le((function(){var e,t,n,r=(e=De((function(e,t){return{x:e,y:t}})),t=De((function(e,t,n,r,o){return{isDragging:!0,isClone:t,isDropAnimating:Boolean(o),dropAnimation:o,mode:e,draggingOver:n,combineWith:r,combineTargetFor:null}})),n=De((function(e,n,r,o,a,i,l){return{mapped:{type:"DRAGGING",dropping:null,draggingOver:a,combineWith:i,mode:n,offset:e,dimension:r,forceShouldAnimate:l,snapshot:t(n,o,a,i,null)}}})),function(r,o){if(r.isDragging){if(r.critical.draggable.id!==o.draggableId)return null;var a=r.current.client.offset,i=r.dimensions.draggables[o.draggableId],l=$t(r.impact),s=(u=r.impact).at&&"COMBINE"===u.at.type?u.at.combine.draggableId:null,c=r.forceShouldAnimate;return n(e(a.x,a.y),r.movementMode,i,o.isClone,l,s,c)}var u;if("DROP_ANIMATING"===r.phase){var d=r.completed;if(d.result.draggableId!==o.draggableId)return null;var p=o.isClone,f=r.dimensions.draggables[o.draggableId],m=d.result,b=m.mode,g=Po(m),v=function(e){return e.combine?e.combine.draggableId:null}(m),h={duration:r.dropDuration,curve:xn,moveTo:r.newHomeClientOffset,opacity:v?In:null,scale:v?Dn:null};return{mapped:{type:"DRAGGING",offset:r.newHomeClientOffset,dimension:f,dropping:h,draggingOver:g,combineWith:v,mode:b,forceShouldAnimate:null,snapshot:t(b,p,g,v,h)}}}return null}),o=function(){var e=De((function(e,t){return{x:e,y:t}})),t=De(Ao),n=De((function(e,n,r){return void 0===n&&(n=null),{mapped:{type:"SECONDARY",offset:e,combineTargetFor:n,shouldAnimateDisplacement:r,snapshot:t(n)}}})),r=function(e){return e?n(We,e,!0):null},o=function(t,o,a,i){var l=a.displaced.visible[t],s=Boolean(i.inVirtualList&&i.effected[t]),c=dt(a),u=c&&c.draggableId===t?o:null;if(!l){if(!s)return r(u);if(a.displaced.invisible[t])return null;var d=$e(i.displacedBy.point),p=e(d.x,d.y);return n(p,u,!0)}if(s)return r(u);var f=a.displacedBy.point,m=e(f.x,f.y);return n(m,u,l.shouldAnimate)};return function(e,t){if(e.isDragging)return e.critical.draggable.id===t.draggableId?null:o(t.draggableId,e.critical.draggable.id,e.impact,e.afterCritical);if("DROP_ANIMATING"===e.phase){var n=e.completed;return n.result.draggableId===t.draggableId?null:o(t.draggableId,n.result.draggableId,n.impact,n.afterCritical)}return null}}();return function(e,t){return r(e,t)||o(e,t)||Bo}}),{dropAnimationFinished:function(){return{type:"DROP_ANIMATION_FINISHED",payload:null}}},null,{context:Cr,pure:!0,areStatePropsEqual:So})((function(e){var t=(0,r.useRef)(null),n=fe((function(e){t.current=e}),[]),o=fe((function(){return t.current}),[]),a=vo(Rr),i=a.contextId,l=a.dragHandleUsageInstructionsId,s=a.registry,c=vo(wo),u=c.type,d=c.droppableId,p=pe((function(){return{id:e.draggableId,index:e.index,type:u,droppableId:d}}),[e.draggableId,e.index,u,d]),f=e.children,m=e.draggableId,b=e.isEnabled,g=e.shouldRespectForcePress,v=e.canDragInteractiveElements,h=e.isClone,y=e.mapped,E=e.dropAnimationFinished;h||function(e){var t=Or("draggable"),n=e.descriptor,o=e.registry,a=e.getDraggableRef,i=e.canDragInteractiveElements,l=e.shouldRespectForcePress,s=e.isEnabled,c=pe((function(){return{canDragInteractiveElements:i,shouldRespectForcePress:l,isEnabled:s}}),[i,s,l]),u=fe((function(e){var t=a();return t||Oe(!1),function(e,t,n){void 0===n&&(n=We);var r=window.getComputedStyle(t),o=t.getBoundingClientRect(),a=we(o,r),i=_e(a,n);return{descriptor:e,placeholder:{client:a,tagName:t.tagName.toLowerCase(),display:r.display},displaceBy:{x:a.marginBox.width,y:a.marginBox.height},client:a,page:i}}(n,t,e)}),[n,a]),d=pe((function(){return{uniqueId:t,descriptor:n,options:c,getDimension:u}}),[n,u,c,t]),p=(0,r.useRef)(d),f=(0,r.useRef)(!0);_r((function(){return o.draggable.register(p.current),function(){return o.draggable.unregister(p.current)}}),[o.draggable]),_r((function(){if(f.current)f.current=!1;else{var e=p.current;p.current=d,o.draggable.update(d,e)}}),[d,o.draggable])}(pe((function(){return{descriptor:p,registry:s,getDraggableRef:o,canDragInteractiveElements:v,shouldRespectForcePress:g,isEnabled:b}}),[p,s,o,v,g,b]));var _=pe((function(){return b?{tabIndex:0,role:"button","aria-describedby":l,"data-rbd-drag-handle-draggable-id":m,"data-rbd-drag-handle-context-id":i,draggable:!1,onDragStart:Co}:null}),[i,l,m,b]),w=fe((function(e){"DRAGGING"===y.type&&y.dropping&&"transform"===e.propertyName&&E()}),[E,y]),x=pe((function(){var e=Do(y),t="DRAGGING"===y.type&&y.dropping?w:null;return{innerRef:n,draggableProps:{"data-rbd-draggable-context-id":i,"data-rbd-draggable-id":m,style:e,onTransitionEnd:t},dragHandleProps:_}}),[i,_,m,y,w,n]),I=pe((function(){return{draggableId:p.id,type:p.type,source:{index:p.index,droppableId:p.droppableId}}}),[p.droppableId,p.id,p.index,p.type]);return f(x,y.snapshot,I)}));function Ro(e){return vo(wo).isUsingCloneFor!==e.draggableId||e.isClone?o().createElement(Oo,e):null}function To(e){var t="boolean"!=typeof e.isDragDisabled||!e.isDragDisabled,n=Boolean(e.disableInteractiveElementBlocking),r=Boolean(e.shouldRespectForcePress);return o().createElement(Ro,u({},e,{isClone:!1,isEnabled:t,canDragInteractiveElements:n,shouldRespectForcePress:r}))}var ko=function(e,t){return e===t.droppable.type},Mo=function(e,t){return t.draggables[e.draggable.id]},Lo={mode:"standard",type:"DEFAULT",direction:"vertical",isDropDisabled:!1,isCombineEnabled:!1,ignoreContainerClipping:!1,renderClone:null,getContainerForClone:function(){return document.body||Oe(!1),document.body}},Go=le((function(){var e={placeholder:null,shouldAnimatePlaceholder:!0,snapshot:{isDraggingOver:!1,draggingOverWith:null,draggingFromThisWith:null,isUsingPlaceholder:!1},useClone:null},t=u({},e,{shouldAnimatePlaceholder:!1}),n=De((function(e){return{draggableId:e.id,type:e.type,source:{index:e.index,droppableId:e.droppableId}}})),r=De((function(r,o,a,i,l,s){var c=l.descriptor.id;if(l.descriptor.droppableId===r){var u=s?{render:s,dragging:n(l.descriptor)}:null,d={isDraggingOver:a,draggingOverWith:a?c:null,draggingFromThisWith:c,isUsingPlaceholder:!0};return{placeholder:l.placeholder,shouldAnimatePlaceholder:!1,snapshot:d,useClone:u}}if(!o)return t;if(!i)return e;var p={isDraggingOver:a,draggingOverWith:c,draggingFromThisWith:null,isUsingPlaceholder:!0};return{placeholder:l.placeholder,shouldAnimatePlaceholder:!0,snapshot:p,useClone:null}}));return function(n,o){var a=o.droppableId,i=o.type,l=!o.isDropDisabled,s=o.renderClone;if(n.isDragging){var c=n.critical;if(!ko(i,c))return t;var u=Mo(c,n.dimensions),d=$t(n.impact)===a;return r(a,l,d,d,u,s)}if("DROP_ANIMATING"===n.phase){var p=n.completed;if(!ko(i,p.critical))return t;var f=Mo(p.critical,n.dimensions);return r(a,l,Po(p.result)===a,$t(p.impact)===a,f,s)}if("IDLE"===n.phase&&n.completed&&!n.shouldFlush){var m=n.completed;if(!ko(i,m.critical))return t;var b=$t(m.impact)===a,g=Boolean(m.impact.at&&"COMBINE"===m.impact.at.type),v=m.critical.droppable.id===a;return b?g?e:t:v?e:t}return t}}),{updateViewportMaxScroll:function(e){return{type:"UPDATE_VIEWPORT_MAX_SCROLL",payload:e}}},null,{context:Cr,pure:!0,areStatePropsEqual:So})((function(e){var t=(0,r.useContext)(Rr);t||Oe(!1);var n=t.contextId,a=t.isMovementAllowed,i=(0,r.useRef)(null),l=(0,r.useRef)(null),s=e.children,c=e.droppableId,u=e.type,d=e.mode,p=e.direction,f=e.ignoreContainerClipping,m=e.isDropDisabled,b=e.isCombineEnabled,g=e.snapshot,v=e.useClone,h=e.updateViewportMaxScroll,y=e.getContainerForClone,E=fe((function(){return i.current}),[]),_=fe((function(e){i.current=e}),[]),w=(fe((function(){return l.current}),[]),fe((function(e){l.current=e}),[])),x=fe((function(){a()&&h({maxScroll:Vn()})}),[a,h]);!function(e){var t=(0,r.useRef)(null),n=vo(Rr),o=Or("droppable"),a=n.registry,i=n.marshal,l=Tr(e),s=pe((function(){return{id:e.droppableId,type:e.type,mode:e.mode}}),[e.droppableId,e.mode,e.type]),c=(0,r.useRef)(s),u=pe((function(){return De((function(e,n){t.current||Oe(!1);var r={x:e,y:n};i.updateDroppableScroll(s.id,r)}))}),[s.id,i]),d=fe((function(){var e=t.current;return e&&e.env.closestScrollable?po(e.env.closestScrollable):We}),[]),p=fe((function(){var e=d();u(e.x,e.y)}),[d,u]),f=pe((function(){return Ce(p)}),[p]),m=fe((function(){var e=t.current,n=ho(e);e&&n||Oe(!1),e.scrollOptions.shouldPublishImmediately?p():f()}),[f,p]),b=fe((function(e,r){t.current&&Oe(!1);var o=l.current,a=o.getDroppableRef();a||Oe(!1);var i=function(e){return{closestScrollable:uo(e),isFixedOnPage:fo(e)}}(a),c={ref:a,descriptor:s,env:i,scrollOptions:r};t.current=c;var u=function(e){var t=e.ref,n=e.descriptor,r=e.env,o=e.windowScroll,a=e.direction,i=e.isDropDisabled,l=e.isCombineEnabled,s=e.shouldClipSubject,c=r.closestScrollable,u=function(e,t){var n=xe(e);if(!t)return n;if(e!==t)return n;var r=n.paddingBox.top-t.scrollTop,o=n.paddingBox.left-t.scrollLeft,a=r+t.scrollHeight,i=o+t.scrollWidth,l=be({top:r,right:i,bottom:a,left:o},n.border);return he({borderBox:l,margin:n.margin,border:n.border,padding:n.padding})}(t,c),d=_e(u,o),p=function(){if(!c)return null;var e=xe(c),t={scrollHeight:c.scrollHeight,scrollWidth:c.scrollWidth};return{client:e,page:_e(e,o),scroll:po(c),scrollSize:t,shouldClipSubject:s}}(),f=function(e){var t=e.descriptor,n=e.isEnabled,r=e.isCombineEnabled,o=e.isFixedOnPage,a=e.direction,i=e.client,l=e.page,s=e.closest,c=function(){if(!s)return null;var e=s.scrollSize,t=s.client,n=qn({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,height:t.paddingBox.height,width:t.paddingBox.width});return{pageMarginBox:s.page.marginBox,frameClient:t,scrollSize:e,shouldClipSubject:s.shouldClipSubject,scroll:{initial:s.scroll,current:s.scroll,max:n,diff:{value:We,displacement:We}}}}(),u="vertical"===a?Et:_t;return{descriptor:t,isCombineEnabled:r,isFixedOnPage:o,axis:u,isEnabled:n,client:i,page:l,frame:c,subject:Ze({page:l,withPlaceholder:null,axis:u,frame:c})}}({descriptor:n,isEnabled:!i,isCombineEnabled:l,isFixedOnPage:r.isFixedOnPage,direction:a,client:u,page:d,closest:p});return f}({ref:a,descriptor:s,env:i,windowScroll:e,direction:o.direction,isDropDisabled:o.isDropDisabled,isCombineEnabled:o.isCombineEnabled,shouldClipSubject:!o.ignoreContainerClipping}),d=i.closestScrollable;return d&&(d.setAttribute(yr.contextId,n.contextId),d.addEventListener("scroll",m,go(c.scrollOptions))),u}),[n.contextId,s,m,l]),g=fe((function(){var e=t.current,n=ho(e);return e&&n||Oe(!1),po(n)}),[]),v=fe((function(){var e=t.current;e||Oe(!1);var n=ho(e);t.current=null,n&&(f.cancel(),n.removeAttribute(yr.contextId),n.removeEventListener("scroll",m,go(e.scrollOptions)))}),[m,f]),h=fe((function(e){var n=t.current;n||Oe(!1);var r=ho(n);r||Oe(!1),r.scrollTop+=e.y,r.scrollLeft+=e.x}),[]),y=pe((function(){return{getDimensionAndWatchScroll:b,getScrollWhileDragging:g,dragStopped:v,scroll:h}}),[v,b,g,h]),E=pe((function(){return{uniqueId:o,descriptor:s,callbacks:y}}),[y,s,o]);_r((function(){return c.current=E.descriptor,a.droppable.register(E),function(){t.current&&v(),a.droppable.unregister(E)}}),[y,s,v,E,i,a.droppable]),_r((function(){t.current&&i.updateDroppableIsEnabled(c.current.id,!e.isDropDisabled)}),[e.isDropDisabled,i]),_r((function(){t.current&&i.updateDroppableIsCombineEnabled(c.current.id,e.isCombineEnabled)}),[e.isCombineEnabled,i])}({droppableId:c,type:u,mode:d,direction:p,isDropDisabled:m,isCombineEnabled:b,ignoreContainerClipping:f,getDroppableRef:E});var I=o().createElement(xo,{on:e.placeholder,shouldAnimate:e.shouldAnimatePlaceholder},(function(e){var t=e.onClose,r=e.data,a=e.animate;return o().createElement(_o,{placeholder:r,onClose:t,innerRef:w,animate:a,contextId:n,onTransitionEnd:x})})),N=pe((function(){return{innerRef:_,placeholder:I,droppableProps:{"data-rbd-droppable-id":c,"data-rbd-droppable-context-id":n}}}),[n,c,I,_]),D=v?v.dragging.draggableId:null,C=pe((function(){return{droppableId:c,type:u,isUsingCloneFor:D}}),[c,D,u]);return o().createElement(wo.Provider,{value:C},s(N,g),function(){if(!v)return null;var e=v.dragging,t=v.render,n=o().createElement(Ro,{draggableId:e.draggableId,index:e.source.index,isClone:!0,isEnabled:!0,shouldRespectForcePress:!1,canDragInteractiveElements:!0},(function(n,r){return t(n,r,e)}));return ue().createPortal(n,y())}())}));Go.defaultProps=Lo;const Fo=({activeTab:e,setActiveTab:t})=>{const n=[{id:"activation",label:(0,i.__)("Activation Settings","buddyboss"),icon:"toggle-right"},{id:"styles",label:(0,i.__)("Styles","buddyboss"),icon:"palette"},{id:"pages",label:(0,i.__)("Pages & Sidebars","buddyboss"),icon:"file-text"},{id:"menus",label:(0,i.__)("Menus","buddyboss"),icon:"list-dashes"}];return(0,r.createElement)(r.Fragment,null,(0,r.createElement)("div",{className:"bb-readylaunch-sidebar"},(0,r.createElement)("ul",null,n.map((n=>(0,r.createElement)("li",{key:n.id,className:e===n.id?"active":"",onClick:()=>t(n.id)},(0,r.createElement)("i",{className:`bb-icons-rl-${n.icon}`}),n.label))))))},jo=window.wp.apiFetch;var Wo=n.n(jo);let Uo=null;const Ho=e=>{const t=localStorage.getItem(e);if(t){const{timestamp:e,data:n}=JSON.parse(t);if((new Date).getTime()-e<2592e5)return n}return null},qo=(e,t)=>{const n={timestamp:(new Date).getTime(),data:t};localStorage.setItem(e,JSON.stringify(n))},$o=({onClick:e,contentId:t})=>(0,r.createElement)("button",{className:"bb-rl-help-icon",onClick:()=>{e&&e(t)},"aria-label":"Help"},(0,r.createElement)("i",{className:"bb-icons-rl-question"})),Vo=({title:e,isExpanded:t,onToggle:n,onHelpClick:o,children:a})=>(0,r.createElement)("div",{className:"settings-accordion "+(t?"expanded":"collapsed")},(0,r.createElement)("div",{className:"accordion-header"},(0,r.createElement)("div",{className:"bb-rl-accordion-toggle",onClick:n},(0,r.createElement)("h3",null,e)),(0,r.createElement)($o,{onClick:o})),t&&(0,r.createElement)("div",{className:"bb-rl-accordion-content"},a)),zo=({link:e,onEdit:t,onDelete:n,innerRef:o,draggableProps:a,dragHandleProps:s,isDragging:c})=>(0,r.createElement)("div",{className:"link-item bb-rl-link-item "+(c?"is-dragging":""),ref:o,...a,...s},(0,r.createElement)("div",{className:"link-item-content"},(0,r.createElement)("i",{className:"bb-icons-rl-list"}),(0,r.createElement)("div",{className:"link-details"},(0,r.createElement)("span",{className:"link-icon"},(0,r.createElement)("i",{className:"bb-icons-rl-link"}),(0,r.createElement)("span",{className:"link-title"},e.title),(0,r.createElement)("div",{className:"link-actions"},(0,r.createElement)(l.Button,{className:"edit-link-button",icon:(0,r.createElement)("i",{className:"bb-icons-rl-pencil-simple"}),onClick:t,label:(0,i.__)("Edit","buddyboss"),isSmall:!0}),(0,r.createElement)(l.Button,{className:"delete-link-button",icon:(0,r.createElement)("i",{className:"bb-icons-rl-trash"}),onClick:n,label:(0,i.__)("Delete","buddyboss"),isSmall:!0}))),(0,r.createElement)("span",{className:"link-url"},e.url)))),Yo=({isOpen:e,onClose:t,linkData:n={},onSave:o})=>{const[s,c]=(0,a.useState)(""),[u,d]=(0,a.useState)(""),[p,f]=(0,a.useState)({});(0,a.useEffect)((()=>{e&&(c(n.title||""),d(n.url||""),f({}))}),[e,n]);return e?(0,r.createElement)(l.Modal,{title:n.id?(0,i.__)("Edit Link","buddyboss"):(0,i.__)("Add Link","buddyboss"),onRequestClose:t,className:"link-modal bb-rl-modal"},(0,r.createElement)("div",{className:"link-modal-content"},(0,r.createElement)("div",{className:"link-modal-form"},(0,r.createElement)(l.TextControl,{label:(0,i.__)("Title","buddyboss"),value:s,onChange:c,help:p.title,placeholder:(0,i.__)("Type a title","buddyboss"),className:"bb-rl-input-field "+(p.title?"has-error":"")}),(0,r.createElement)(l.TextControl,{label:(0,i.__)("URL","buddyboss"),value:u,onChange:d,help:p.url,placeholder:(0,i.__)("Paste link","buddyboss"),className:"bb-rl-input-field "+(p.url?"has-error":"")})),(0,r.createElement)("div",{className:"link-modal-actions"},(0,r.createElement)(l.Button,{onClick:t,className:"bb-rl-button bb-rl-button--secondary bb-rl-button--small"},(0,i.__)("Cancel","buddyboss")),(0,r.createElement)(l.Button,{onClick:()=>{const e={};s.trim()||(e.title=(0,i.__)("Title is required","buddyboss")),u.trim()?(e=>{try{return new URL(e),!0}catch(e){return!1}})(u)||(e.url=(0,i.__)("Please enter a valid URL","buddyboss")):e.url=(0,i.__)("URL is required","buddyboss"),Object.keys(e).length>0?f(e):(o({id:n.id,title:s.trim(),url:u.trim()}),t())},className:"bb-rl-button bb-rl-button--primary bb-rl-button--small"},(0,i.__)("Save","buddyboss"))))):null},Jo=({isOpen:e,onClose:t,children:n,title:o})=>{const a=(0,r.useRef)(null),[l,s]=(0,r.useState)([]);return(0,r.useEffect)((()=>{if(e&&a.current){const e=function(e){if(!e)return[];const t=Array.from(e.querySelectorAll("h2")),n={};return t.map((e=>{let t=(e.textContent||e.innerText||"").trim(),r=t.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/^-+|-+$/g,"");return n[r]?(n[r]++,r=`${r}-${n[r]}`):n[r]=1,{el:e,text:t,anchor:r}}))}(a.current);e.forEach((({el:e,anchor:t})=>{e.id=t})),s(e)}}),[e,n]),e?(0,r.createElement)("div",{className:"bb-rl-help-modal-overlay",onClick:t},(0,r.createElement)("div",{className:"bb-rl-help-modal",onClick:e=>e.stopPropagation()},(0,r.createElement)("div",{className:"bb-rl-help-modal-header"},(0,r.createElement)("h2",null,o),(0,r.createElement)("button",{className:"bb-rl-help-modal-close",onClick:t,"aria-label":"Close"},(0,r.createElement)("span",null,"×"))),(0,r.createElement)("div",{className:"bb-rl-help-modal-content"},l.length>0&&(0,r.createElement)("nav",{className:"bb-rl-help-modal-toc",style:{marginBottom:20,padding:15,background:"#f8f9fa",borderRadius:0,borderLeft:"4px solid #007cba"}},(0,r.createElement)("h3",{style:{margin:"0 0 10px 0",fontSize:16}},(0,i.__)("Contents","buddyboss")),(0,r.createElement)("ol",{style:{listStyle:"none",padding:0,margin:0}},l.map((({text:e,anchor:t})=>(0,r.createElement)("li",{key:t,style:{marginBottom:8}},(0,r.createElement)("a",{href:`#${t}`,onClick:e=>((e,t)=>{t.preventDefault();const n=document.getElementById(e);n&&n.scrollIntoView({behavior:"smooth"})})(t,e),style:{color:"#007cba",textDecoration:"none",padding:"6px 8px",borderRadius:4,display:"block",fontWeight:500}},e)))))),(0,r.createElement)("div",{ref:a,className:"bb-rl-help-modal-main-content"},n))),(0,r.createElement)("style",null,"\n        .bb-rl-help-modal-header {\n          display: flex; align-items: center; justify-content: space-between; padding: 16px 24px 0 24px;\n        }\n        .bb-rl-help-modal-close {\n          background: none; border: none; font-size: 28px; cursor: pointer; color: #888;\n        }\n        .bb-rl-help-modal-content {\n          padding: 24px;\n        }\n        .bb-rl-help-modal-main-content h2 {\n          scroll-margin-top: 10px;\n        }\n      ")):null},Xo=({status:e,message:t,onDismiss:n,showIcon:o=!0})=>(0,r.createElement)("div",{className:`bb-rl-toast bb-rl-toast--${e}`},o&&(0,r.createElement)("div",{className:"bb-rl-toast-icon"},(()=>{switch(e){case"saving":return(0,r.createElement)(l.Spinner,null);case"success":return(0,r.createElement)("i",{className:"bb-icons-rl-fill bb-icons-rl-check-circle"});case"error":return(0,r.createElement)("i",{className:"bb-icons-rl-warning-circle"});default:return null}})()),(0,r.createElement)("div",{className:"bb-rl-toast-message"},t),"error"===e&&n&&(0,r.createElement)(l.Button,{onClick:n,className:"bb-rl-toast-dismiss",icon:(0,r.createElement)("i",{className:"bb-icons-rl-x"})})),Ko=[{id:"members",label:(0,i.__)("Members","buddyboss"),icon:"users",enabled:!0,order:1}];"1"===window?.BP_ADMIN?.courses_integration&&Ko.unshift({id:"courses",label:(0,i.__)("Courses","buddyboss"),icon:"graduation-cap",enabled:!0,order:1});const Qo={light:(0,i.__)("The site will be shown in light mode.","buddyboss"),dark:(0,i.__)("The site will be shown in dark mode.","buddyboss"),choice:(0,i.__)("Users will be able switch between the modes.","buddyboss")},Zo=document.getElementById("bb-rl-field-wrap");Zo&&(0,a.render)((0,r.createElement)((()=>{const[e,t]=(0,a.useState)("activation"),[n,o]=(0,a.useState)({}),[s,c]=(0,a.useState)(!0),[u,d]=(0,a.useState)(null),[p,f]=(0,a.useState)(!0),[m,b]=(0,a.useState)(!1),[g,v]=(0,a.useState)({}),[h,y]=(0,a.useState)({pages:!0,sidebars:!0,menus:!0}),[E,_]=(0,a.useState)(!1),[w,x]=(0,a.useState)(null),[I,N]=(0,a.useState)(Ko),[D,C]=(0,a.useState)([]),[S,P]=(0,a.useState)(!1),[A,B]=(0,a.useState)(null),[O,R]=(0,a.useState)(!1),[T,k]=(0,a.useState)(null),M=(0,a.useRef)();(0,a.useRef)(),(0,a.useEffect)((()=>{(async()=>{c(!0);const e=await(async()=>{try{const e=await Wo()({path:"/buddyboss/v1/settings",method:"GET"});return Uo=JSON.parse(JSON.stringify(e)),e}catch(e){return console.error("Error fetching settings:",e),null}})();if(e&&e.platform){o(e.platform);const t=(()=>{const e=[...Ko];let t=0;return 1===window?.BP_ADMIN?.components?.activity&&e.unshift({id:"activity_feed",label:(0,i.__)("Activity Feed","buddyboss"),icon:"pulse",enabled:!0,order:t++}),e.forEach((e=>{"members"===e.id&&(e.order=t++)})),1===window?.BP_ADMIN?.components?.groups&&e.push({id:"groups",label:(0,i.__)("Groups","buddyboss"),icon:"users-three",enabled:!0,order:t++}),1===window?.BP_ADMIN?.components?.forums&&e.push({id:"forums",label:(0,i.__)("Forums","buddyboss"),icon:"chat-text",enabled:!0,order:t++}),e.forEach((e=>{"courses"===e.id&&(e.order=t++)})),1===window?.BP_ADMIN?.components?.messages&&e.push({id:"messages",label:(0,i.__)("Messages","buddyboss"),icon:"chat-teardrop-text",enabled:!1,order:t++}),1===window?.BP_ADMIN?.components?.notifications&&e.push({id:"notifications",label:(0,i.__)("Notifications","buddyboss"),icon:"bell",enabled:!1,order:t}),e})();N((n=>e.platform.bb_rl_side_menu?t.map((t=>{const n=e.platform.bb_rl_side_menu[t.id];return{...t,enabled:n?n.enabled:t.enabled,order:n?n.order:t.order}})).sort(((e,t)=>e.order-t.order)):t))}c(!1),f(!1),b(!1)})(),(async()=>{try{return await Wo()({path:"/wp/v2/menus?per_page=99",method:"GET"})}catch(e){try{return await Wo()({path:"/menus/v1/menus?per_page=99",method:"GET"})}catch(e){return console.error("Error fetching menus:",e),[]}}})().then(C)}),[]),(0,a.useEffect)((()=>(M.current=(()=>{let e;return function(...t){clearTimeout(e),e=setTimeout((()=>{clearTimeout(e),(e=>{0!==Object.keys(e).length&&(async e=>{if(!e)return console.error("No settings provided to save"),null;try{const t=e;if(0===Object.keys(t).length)return e;const n={...t};n.hasOwnProperty("_tempIds")&&delete n._tempIds;const r=await Wo()({path:"/buddyboss/v1/settings",method:"POST",data:n});return r&&(Uo=JSON.parse(JSON.stringify(r))),r}catch(e){return console.error("Error saving settings:",e),null}})(e).then((e=>{e?(d({status:"success",message:(0,i.__)("Settings saved.","buddyboss")}),v({})):d({status:"error",message:(0,i.__)("Something went wrong. Please try again","buddyboss")})})).catch((()=>{d({status:"error",message:(0,i.__)("Something went wrong. Please try again","buddyboss")})}))})(...t)}),1e3)}})(),()=>{M.current?.cancel&&M.current.cancel()})),[]),(0,a.useEffect)((()=>{!p&&Object.keys(g).length>0&&M.current(g)}),[g,p]),(0,a.useEffect)((()=>{if(u&&"success"===u.status){const e=setTimeout((()=>{d(null)}),3e3);return()=>clearTimeout(e)}}),[u]);const L=e=>t=>{d({status:"saving",message:(0,i.__)("Saving changes...","buddyboss")}),o((n=>({...n,[e]:t}))),v((n=>({...n,[e]:t}))),b(!0)},G=(e,t)=>n=>{b(!0),d({status:"saving",message:(0,i.__)("Saving changes...","buddyboss")}),"bb_rl_side_menu"===e?N((e=>{const r=e.map((e=>e.id===t?{...e,enabled:n}:e)),a={};return r.forEach((e=>{a[e.id]={enabled:e.enabled,order:e.order,icon:e.icon}})),o((e=>({...e,bb_rl_side_menu:a}))),v((e=>({...e,bb_rl_side_menu:a}))),r})):o((r=>{const o={...r[e],[t]:n};return v((t=>({...t,[e]:o}))),{...r,[e]:o}}))},F=e=>{y((t=>({...t,[e]:!t[e]})))},j=()=>{x(null),_(!0)},W=e=>{let t;if(b(!0),d({status:"saving",message:(0,i.__)("Saving changes...","buddyboss")}),w)t=n.bb_rl_custom_links.map((t=>t.id===w.id?{...t,title:e.title,url:e.url}:t));else{const r={id:Date.now(),title:e.title,url:e.url};t=[...n.bb_rl_custom_links||[],r]}o((e=>({...e,bb_rl_custom_links:t}))),v((e=>({...e,bb_rl_custom_links:t}))),_(!1)},U=e=>t=>{b(!0),d({status:"saving",message:(0,i.__)("Saving changes...","buddyboss")});const n=null===t?[]:t;o((t=>({...t,[e]:n}))),v((t=>({...t,[e]:n})))},H=(e,t)=>{if(void 0===window.wp||!window.wp.media)return console.error("WordPress Media API is not available"),void alert("WordPress Media API is not available. Please make sure WordPress Media is properly loaded.");const n=window.wp.media({title:(0,i.__)("Select or Upload Media","buddyboss"),button:{text:(0,i.__)("Use this media","buddyboss")},multiple:!1,library:{type:"image"}});n.on("select",(function(){const e=n.state().get("selection").first().toJSON(),r={id:e.id,url:e.url,alt:e.alt||"",title:e.title||""};t(r)})),n.open()},q=({label:e,value:t,onChange:n,description:o,customClass:a})=>(0,r.createElement)("div",{className:`image-selector-component ${a||""}`},(0,r.createElement)("label",null,e),(0,r.createElement)("div",{className:"image-selector-control"},t&&t.url?(0,r.createElement)("div",{className:"bb-rl-image-preview-wrapper"},(0,r.createElement)("img",{src:t.url,alt:t.alt||"",className:"image-preview"}),(0,r.createElement)("div",{className:"image-actions"},(0,r.createElement)(l.Button,{onClick:()=>H(0,n),className:"change-image-button bb-rl-button bb-rl-button--secondary bb-rl-button--small",icon:(0,r.createElement)("i",{className:"bb-icons-rl-upload-simple"})},(0,i.__)("Replace","buddyboss")),(0,r.createElement)(l.Button,{onClick:()=>n(null),className:"remove-image-button bb-rl-button bb-rl-button--outline bb-rl-button--small",icon:(0,r.createElement)("i",{className:"bb-icons-rl-x"})},(0,i.__)("Remove","buddyboss")))):(0,r.createElement)(l.Button,{onClick:()=>H(0,n),className:"bb-rl-upload-image-button",icon:(0,r.createElement)("i",{className:"bb-icons-rl-plus"})}),o&&(0,r.createElement)("p",{className:"field-description"},o))),$=({label:e,color:t,onChange:n})=>{const[o,s]=(0,a.useState)(!1),[c,u]=(0,a.useState)(t),d=()=>s(!1),p=t||"#3E34FF";return(0,r.createElement)("div",{className:"color-picker-button-component bb-rl-color-picker-button-component"},e&&(0,r.createElement)("span",{className:"color-picker-label"},e),(0,r.createElement)("div",{className:"color-picker-button-wrapper"},(0,r.createElement)(l.Button,{className:"color-picker-button",onClick:()=>{s(!o),u(t)},"aria-expanded":o,"aria-label":(0,i.__)("Select color","buddyboss")},(0,r.createElement)("div",{className:"color-indicator-wrapper"},(0,r.createElement)(l.ColorIndicator,{colorValue:p})),(0,r.createElement)("span",{className:"color-picker-value"},p)),o&&(0,r.createElement)(l.Popover,{className:"color-picker-popover",onClose:d,position:"bottom center"},(0,r.createElement)("div",{className:"color-picker-popover-content"},(0,r.createElement)(l.ColorPicker,{color:c||p,onChange:e=>{u(e)},enableAlpha:!1,copyFormat:"hex"}),(0,r.createElement)("div",{className:"color-picker-popover-footer"},(0,r.createElement)(l.Button,{onClick:()=>{n(c),d()},className:"apply-color-button"},(0,i.__)("Apply","buddyboss")))))))},V=e=>{const{source:t,destination:r,draggableId:a,type:l}=e;if(r&&(r.droppableId!==t.droppableId||r.index!==t.index)){if(b(!0),d({status:"saving",message:(0,i.__)("Saving changes...","buddyboss")}),"sideMenuItems"===t.droppableId){const e=Array.from(I),[n]=e.splice(t.index,1);e.splice(r.index,0,n);const a=e.map(((e,t)=>({...e,order:t})));N(a);const i={};a.forEach((e=>{i[e.id]={enabled:e.enabled,order:e.order,icon:e.icon}})),o((e=>({...e,bb_rl_side_menu:i}))),v((e=>({...e,bb_rl_side_menu:i})))}if("bb_rl_custom_links"===t.droppableId){const e=Array.from(n.bb_rl_custom_links||[]),[a]=e.splice(t.index,1);e.splice(r.index,0,a),o((t=>({...t,bb_rl_custom_links:e}))),v((t=>({...t,bb_rl_custom_links:e})))}}},z=async e=>{P(!0),R(!0),k(null);try{const t=await(async e=>{if(!e)throw new Error("Content ID is required");const t=`bb_help_content_${e}`,n=Ho(t);if(n)return n;try{const n=await fetch(`https://buddyboss.com/wp-json/wp/v2/ht-kb/${e}`);if(!n.ok)throw new Error("Failed to fetch help content");const r=await n.json(),o={title:r.title.rendered,content:r.content.rendered,videoId:r.acf?.video_id||null,imageUrl:r.acf?.featured_image||null};return qo(t,o),o}catch(e){throw console.error("Error fetching help content:",e),e}})(e);B(t)}catch(t){k("Failed to load help content. Please try again later."),((e=null)=>{if(e)localStorage.removeItem(`bb_help_content_${e}`);else for(let e=0;e<localStorage.length;e++){const t=localStorage.key(e);t&&t.startsWith("bb_help_content_")&&localStorage.removeItem(t)}})(e)}finally{R(!1)}},Y=()=>(0,r.createElement)("div",{className:"bb-rl-welcome-section settings-card settings-card--plain"},(0,r.createElement)("div",{className:"bb-rl-welcome-content"},(0,r.createElement)("div",{className:"bb-rl-welcome-text"},(0,r.createElement)("h1",null,(0,i.__)("Welcome to ReadyLaunch","buddyboss")),(0,r.createElement)("p",null,(0,i.__)("We’re excited to reveal the brand new ReadyLaunch system; a page template design allowing you to use BuddyBoss Platform with ANY Theme. It’s easy to get started; just enable ReadyLaunch and configure the next 3 tabs - Style, Sidebars and Menus.","buddyboss")),(0,r.createElement)("p",null,(0,i.__)("This is not a replacement for BuddyBoss Theme, but an alternative solution if you’re looking for an out-the-box community platform with a focus on core functionality without third party customisation. See the video for full details and share your experience on our new roadmap.","buddyboss")),(0,r.createElement)(l.Button,{className:"bb-rl-feedback-btn",href:"https://roadmap.buddyboss.com/p/new-ready-launch-buddyboss-platform-templates-Y8mV6D",target:"_blank",rel:"noopener noreferrer",icon:(0,r.createElement)("i",{className:"bb-icons-rl-rocket-launch"})},(0,i.__)("Leave Feedback","buddyboss")," ",(0,r.createElement)("i",{className:"bb-icons-rl-arrow-right"}))),(0,r.createElement)("div",{className:"bb-rl-welcome-video"},(0,r.createElement)("iframe",{width:"560",height:"315",src:"https://www.youtube.com/embed/3-JhzDr1gLc",title:"YouTube video player",frameborder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",referrerpolicy:"strict-origin-when-cross-origin",allowfullscreen:!0}))));return(0,a.useEffect)((()=>{const e=document.querySelector(".bb-rl-header-actions-button"),t=document.getElementById("bb-rl-help-overlay"),n=document.getElementById("bb-rl-help-overlay-close"),r=document.querySelector(".bb-rl-help-accordion");(async()=>{if(!e||!r)return;const t=e.getAttribute("data-help-cat-id");try{r.innerHTML='<div class="bb-rl-spinner-wrapper"><span class="spinner is-active"></span></div>';const e=await(async(e=null)=>{const t=`bb_rl_help_categories_${e||"root"}`,n=Ho(t);if(n)return n;let r="https://www.buddyboss.com/wp-json/wp/v2/ht-kb-category?orderby=term_order&per_page=99";e&&(r+=`&parent=${e}`);try{const e=await fetch(r);if(!e.ok)throw new Error("Failed to fetch help categories");const n=await e.json();return qo(t,n),n}catch(e){throw console.error("Error fetching help categories:",e),e}})(t);(e=>{r&&(r.innerHTML="",e.forEach((e=>{const t=document.createElement("div");t.className="bb-rl-help-accordion-item";const n=document.createElement("a");n.href=e.link,n.target="_blank",n.rel="noopener noreferrer",n.className="bb-rl-help-accordion-header",n.innerHTML=`\n\t\t\t\t\t<span><i class="bb-icons-rl-folder"></i> ${e.name}</span>\n\t\t\t\t\t<i class="bb-icons-rl-caret-double-right"></i>\n\t\t\t\t`,t.appendChild(n),r.appendChild(t)})))})(e)}catch(e){r.innerHTML=`<p>${(0,i.__)("Error loading help content.","buddyboss")}</p>`,console.error("Error fetching help categories:",e)}})();const o=e=>{e.preventDefault(),t&&(t.style.display="flex",document.body.style.overflow="hidden")},a=()=>{t&&(t.style.display="none",document.body.style.overflow="")};return e&&e.addEventListener("click",o),n&&n.addEventListener("click",a),()=>{e&&e.removeEventListener("click",o),n&&n.removeEventListener("click",a)}}),[]),(0,r.createElement)(r.Fragment,null,(0,r.createElement)("div",{className:"bb-readylaunch-settings-container"},(0,r.createElement)(Fo,{activeTab:e,setActiveTab:t}),(0,r.createElement)("div",{className:"bb-readylaunch-settings-content"},(()=>{if(s)return(0,r.createElement)("div",{className:"settings-loading"},(0,r.createElement)(l.Spinner,null),(0,r.createElement)("p",null,(0,i.__)("Loading settings...","buddyboss")));if(!n.bb_rl_enabled&&"activation"!==e)return(0,r.createElement)("div",{className:"bb-rl-disabled-message"},(0,r.createElement)("div",{className:"bb-rl-disabled-icon"},(0,r.createElement)("span",{className:"bb-icons-rl-info"})),(0,r.createElement)("h3",null,(0,i.__)("ReadyLaunch is disabled","buddyboss")),(0,r.createElement)("p",null,(0,i.__)("To enable ReadyLaunch and access its features, go to the Activation menu.","buddyboss")),(0,r.createElement)(l.Button,{className:"bb-rl-button bb-rl-button--primary bb-rl-button--small",onClick:()=>t("activation")},(0,i.__)("Enable ReadyLaunch","buddyboss")));switch(e){case"activation":return(0,r.createElement)("div",{className:"settings-content"},(0,r.createElement)(Y,null),(0,r.createElement)("div",{className:"settings-card settings-card--plain"},(0,r.createElement)("div",{className:"settings-toggle-container"},(0,r.createElement)("div",{className:"toggle-content"},(0,r.createElement)("h3",null,(0,i.__)("Enable ReadyLaunch","buddyboss")),(0,r.createElement)("p",null,(0,i.__)("Turn on ReadyLaunch to override your theme styles on BuddyBoss pages.","buddyboss"))),(0,r.createElement)(l.ToggleControl,{checked:n.bb_rl_enabled,onChange:L("bb_rl_enabled")}))),n.bb_rl_enabled&&(0,r.createElement)("div",{className:"settings-card"},(0,r.createElement)("div",{className:"settings-header"},(0,r.createElement)("h3",null,(0,i.__)("Site Name","buddyboss")),(0,r.createElement)($o,{onClick:()=>z("459612")})),(0,r.createElement)("div",{className:"settings-form-field"},(0,r.createElement)("div",{className:"field-label"},(0,r.createElement)("label",null,(0,i.__)("Site Name","buddyboss")),(0,r.createElement)("p",null,(0,i.__)("Displays in the browser title, search engine results and site header.","buddyboss"))),(0,r.createElement)("div",{className:"field-input"},(0,r.createElement)(l.TextControl,{placeholder:(0,i.__)("Type your community/site title","buddyboss"),value:n.blogname,onChange:(c="blogname",e=>{d({status:"saving",message:(0,i.__)("Saving changes...","buddyboss")}),o((t=>({...t,[c]:e}))),v((t=>({...t,[c]:e}))),b(!0)})}),(0,r.createElement)("p",{className:"field-description"},(0,i.__)("This matches the WordPress Site Title. Updating it here will update it site-wide.","buddyboss"))))),n.bb_rl_enabled&&(0,r.createElement)("div",{className:"settings-card"},(0,r.createElement)("div",{className:"settings-header"},(0,r.createElement)("h3",null,(0,i.__)("Platform Settings","buddyboss")),(0,r.createElement)($o,{onClick:()=>z("459617")})),(0,r.createElement)("div",{className:"settings-list-items-block"},[{id:"activity",icon:"bb-icons-rl-pulse",title:(0,i.__)("Activity","buddyboss"),description:(0,i.__)("Control activity streams and user engagement settings.","buddyboss"),actionLink:"admin.php?page=bp-settings&tab=bp-activity"},{id:"xprofile",icon:"bb-icons-rl-user-square",title:(0,i.__)("Profiles","buddyboss"),description:(0,i.__)("Manage profile fields, visibility, and user profile options.","buddyboss"),actionLink:"admin.php?page=bp-settings&tab=bp-xprofile"},{id:"groups",icon:"bb-icons-rl-users-three",title:(0,i.__)("Groups","buddyboss"),description:(0,i.__)("Configure group creation, privacy, and member roles.","buddyboss"),actionLink:"admin.php?page=bp-settings&tab=bp-groups"},{id:"media",icon:"bb-icons-rl-image",title:(0,i.__)("Media","buddyboss"),description:(0,i.__)("Enable or restrict user-uploaded media across the platform.","buddyboss"),actionLink:"admin.php?page=bp-settings&tab=bp-media"},{id:"moderation",icon:"bb-icons-rl-flag",title:(0,i.__)("Moderation","buddyboss"),description:(0,i.__)("Set rules and tools for reporting and content moderation.","buddyboss"),actionLink:"admin.php?page=bp-settings&tab=bp-moderation"}].map((e=>window?.BP_ADMIN?.components&&window?.BP_ADMIN?.components[e.id]&&1===window?.BP_ADMIN?.components[e.id]&&(0,r.createElement)("div",{className:"settings-list-item",key:e.id},(0,r.createElement)("div",{className:"settings-list-item-icon"},(0,r.createElement)("span",{className:e.icon})),(0,r.createElement)("div",{className:"settings-list-item-content"},(0,r.createElement)("div",{className:"settings-list-item-title"},(0,r.createElement)("h4",null,e.title)),(0,r.createElement)("div",{className:"settings-list-item-description"},(0,r.createElement)("p",null,e.description))),(0,r.createElement)("div",{className:"settings-list-item-actions"},(0,r.createElement)(l.Button,{className:"bb-rl-button bb-rl-button--outline bb-rl-button--small",icon:(0,r.createElement)("i",{className:"bb-icons-rl-gear"}),href:e.actionLink,target:"_blank",rel:"noopener noreferrer"},(0,i.__)("Settings","buddyboss")))))))));case"styles":return(0,r.createElement)("div",{className:"settings-content"},(0,r.createElement)("h1",null,(0,i.__)("Style Settings","buddyboss")),(0,r.createElement)("p",{className:"settings-description"},(0,i.__)("Customize the appearance of your community to match your brand colors and logo.","buddyboss")),(0,r.createElement)("div",{className:"settings-card"},(0,r.createElement)("div",{className:"settings-header"},(0,r.createElement)("h3",null,(0,i.__)("Branding","buddyboss")),(0,r.createElement)($o,{onClick:()=>z("459621")})),(0,r.createElement)("div",{className:"settings-form-field with-toggle"},(0,r.createElement)("div",{className:"field-label"},(0,r.createElement)("label",null,(0,i.__)("Appearance","buddyboss")),(0,r.createElement)("p",null,(0,i.__)("Choose whether you wish to support light or dark mode.","buddyboss"))),(0,r.createElement)("div",{className:"field-input"},(0,r.createElement)("div",{className:"sub-field-input sub-field-input-inline"},(0,r.createElement)(l.RadioControl,{selected:n.bb_rl_theme_mode,options:[{label:(0,i.__)("Light Mode","buddyboss"),value:"light"},{label:(0,i.__)("Dark Mode","buddyboss"),value:"dark"},{label:(0,i.__)("User Preference","buddyboss"),value:"choice"}],onChange:L("bb_rl_theme_mode")}),Qo[n.bb_rl_theme_mode]&&(0,r.createElement)("p",{className:"field-description"},Qo[n.bb_rl_theme_mode])))),(0,r.createElement)("div",{className:"settings-form-field"},(0,r.createElement)("div",{className:"field-label"},(0,r.createElement)("label",null,(0,i.__)("Logo","buddyboss")),(0,r.createElement)("p",null,(0,i.__)("Upload your logo which appears along the top site header.","buddyboss"))),(0,r.createElement)("div",{className:"field-input logo-uploaders"},"dark"!==n.bb_rl_theme_mode&&(0,r.createElement)(q,{label:(0,i.__)("Logo (Light mode)","buddyboss"),value:n.bb_rl_light_logo,onChange:U("bb_rl_light_logo"),description:(0,i.__)("Recommended to use a dark-colored logo, 280x80 px, in JPG or PNG format.","buddyboss"),customClass:"light-logo-mode"}),"light"!==n.bb_rl_theme_mode&&(0,r.createElement)(q,{label:(0,i.__)("Logo (Dark mode)","buddyboss"),value:n.bb_rl_dark_logo,onChange:U("bb_rl_dark_logo"),description:(0,i.__)("Recommended to use a light-colored logo, 280x80 px, in JPG or PNG format.","buddyboss"),customClass:"dark-logo-mode"}))),(0,r.createElement)("div",{className:"settings-form-field"},(0,r.createElement)("div",{className:"field-label"},(0,r.createElement)("label",null,(0,i.__)("Theme Color","buddyboss")),(0,r.createElement)("p",null,(0,i.__)("Select the primary color of your community. This is used across buttons, links and secondary elements.","buddyboss"))),(0,r.createElement)("div",{className:"field-input color-palettes bb-rl-color-palettes"},"dark"!==n.bb_rl_theme_mode&&(0,r.createElement)("div",{className:"color-palette-item"},(0,r.createElement)($,{label:(0,i.__)("Primary Color (Light Mode)","buddyboss"),color:n.bb_rl_color_light,onChange:L("bb_rl_color_light")})),"light"!==n.bb_rl_theme_mode&&(0,r.createElement)("div",{className:"color-palette-item"},(0,r.createElement)($,{label:(0,i.__)("Primary Color (Dark Mode)","buddyboss"),color:n.bb_rl_color_dark,onChange:L("bb_rl_color_dark")}))))));case"pages":return(0,r.createElement)("div",{className:"settings-content"},(0,r.createElement)("h1",null,(0,i.__)("Pages and Widgets Settings","buddyboss")),(0,r.createElement)("p",{className:"settings-description"},(0,i.__)("Enable or disable page styles, and customize sidebar widgets for different sections of your community.","buddyboss")),(0,r.createElement)("div",{className:"settings-card"},(0,r.createElement)(Vo,{title:(0,i.__)("Pages","buddyboss"),isExpanded:h.pages,onToggle:()=>F("pages"),onHelpClick:()=>z("459627")},(0,r.createElement)("div",{className:"settings-form-field with-multiple-toggles"},(0,r.createElement)("div",{className:"field-label"},(0,r.createElement)("label",null,(0,i.__)("Enable Pages","buddyboss")),(0,r.createElement)("p",null,(0,i.__)("Apply ReadyLaunch styles to the following pages","buddyboss"))),"1"===window?.BP_ADMIN?.register_integration||"1"===window?.BP_ADMIN?.courses_integration?(0,r.createElement)("div",{className:"field-toggles"},"1"===window?.BP_ADMIN?.register_integration&&(0,r.createElement)("div",{className:"toggle-item"},(0,r.createElement)(l.ToggleControl,{label:(0,i.__)("Login & Registration","buddyboss"),checked:n.bb_rl_enabled_pages.registration,onChange:G("bb_rl_enabled_pages","registration")})),"1"===window?.BP_ADMIN?.courses_integration&&(0,r.createElement)("div",{className:"toggle-item"},(0,r.createElement)(l.ToggleControl,{label:(0,i.__)("Courses","buddyboss"),checked:n.bb_rl_enabled_pages.courses,onChange:G("bb_rl_enabled_pages","courses")}))):(0,r.createElement)("div",{className:"bb-rl-notice bb-rl-notice--info"},(0,r.createElement)("span",{className:"bb-rl-notice-icon"},(0,r.createElement)("i",{className:"bb-icons-rl-info"})),(0,r.createElement)("div",{className:"bb-rl-notice-content"},(0,r.createElement)("strong",null,(0,i.__)("No pages found","buddyboss")),(0,r.createElement)("p",null,(0,i.__)("Enable the corresponding pages from your backend settings to manage them here with ReadyLaunch styles.","buddyboss"))))))),(0,r.createElement)("div",{className:"settings-card"},(0,r.createElement)(Vo,{title:(0,i.__)("Sidebar Widgets","buddyboss"),isExpanded:h.sidebars,onToggle:()=>F("sidebars"),onHelpClick:()=>z("459623")},BP_ADMIN.components&&BP_ADMIN.components.activity&&1===BP_ADMIN.components.activity&&(0,r.createElement)("div",{className:"settings-form-field with-multiple-toggles"},(0,r.createElement)("div",{className:"field-label"},(0,r.createElement)("label",null,(0,i.__)("Activity Feed","buddyboss")),(0,r.createElement)("p",null,(0,a.createInterpolateElement)((0,i.__)("Enable or disable widgets to appear on the <a>activity feed</a>.","buddyboss"),{a:(0,r.createElement)("a",{href:window?.BP_ADMIN?.component_pages?.activity||"",target:"_blank"})}))),(0,r.createElement)("div",{className:"field-toggles"},(0,r.createElement)("div",{className:"toggle-item"},(0,r.createElement)(l.ToggleControl,{label:(0,i.__)("Complete Profile","buddyboss"),checked:n.bb_rl_activity_sidebars.complete_profile,onChange:G("bb_rl_activity_sidebars","complete_profile")})),(0,r.createElement)("div",{className:"toggle-item"},(0,r.createElement)(l.ToggleControl,{label:(0,i.__)("Latest Updates","buddyboss"),checked:n.bb_rl_activity_sidebars.latest_updates,onChange:G("bb_rl_activity_sidebars","latest_updates")})),(0,r.createElement)("div",{className:"toggle-item"},(0,r.createElement)(l.ToggleControl,{label:(0,i.__)("Recent Blog Posts","buddyboss"),checked:n.bb_rl_activity_sidebars.recent_blog_posts,onChange:G("bb_rl_activity_sidebars","recent_blog_posts")})),(0,r.createElement)("div",{className:"toggle-item"},(0,r.createElement)(l.ToggleControl,{label:(0,i.__)("Active Members","buddyboss"),checked:n.bb_rl_activity_sidebars.active_members,onChange:G("bb_rl_activity_sidebars","active_members")})))),(0,r.createElement)("div",{className:"settings-form-field with-multiple-toggles"},(0,r.createElement)("div",{className:"field-label"},(0,r.createElement)("label",null,(0,i.__)("Member Profile","buddyboss")),(0,r.createElement)("p",null,(0,a.createInterpolateElement)((0,i.__)("Enable or disable widgets to appear on the <a>member profile</a>.","buddyboss"),{a:(0,r.createElement)("a",{href:window?.BP_ADMIN?.component_pages?.xprofile||"",target:"_blank"})}))),(0,r.createElement)("div",{className:"field-toggles"},(0,r.createElement)("div",{className:"toggle-item"},(0,r.createElement)(l.ToggleControl,{label:(0,i.__)("Complete Profile","buddyboss"),checked:n.bb_rl_member_profile_sidebars.complete_profile,onChange:G("bb_rl_member_profile_sidebars","complete_profile")})),BP_ADMIN.components&&BP_ADMIN.components.friends&&1===BP_ADMIN.components.friends&&(0,r.createElement)("div",{className:"toggle-item"},(0,r.createElement)(l.ToggleControl,{label:(0,i.__)("Connections","buddyboss"),checked:n.bb_rl_member_profile_sidebars.connections,onChange:G("bb_rl_member_profile_sidebars","connections")})),BP_ADMIN.components&&BP_ADMIN.components.activity&&1===BP_ADMIN.components.activity&&!0===n.bp_enable_activity_follow&&(0,r.createElement)("div",{className:"toggle-item"},(0,r.createElement)(l.ToggleControl,{label:(0,i.__)("My Network (Follow, Followers)","buddyboss"),checked:n.bb_rl_member_profile_sidebars.my_network,onChange:G("bb_rl_member_profile_sidebars","my_network")})))),BP_ADMIN.components&&BP_ADMIN.components.groups&&1===BP_ADMIN.components.groups&&(0,r.createElement)("div",{className:"settings-form-field with-multiple-toggles"},(0,r.createElement)("div",{className:"field-label"},(0,r.createElement)("label",null,(0,i.__)("Group","buddyboss")),(0,r.createElement)("p",null,(0,a.createInterpolateElement)((0,i.__)("Enable or disable widgets to appear on the <a>group single</a> page.","buddyboss"),{a:(0,r.createElement)("a",{href:window?.BP_ADMIN?.component_pages?.single_group||"",target:"_blank"})}))),(0,r.createElement)("div",{className:"field-toggles"},(0,r.createElement)("div",{className:"toggle-item"},(0,r.createElement)(l.ToggleControl,{label:(0,i.__)("About Group","buddyboss"),checked:n.bb_rl_groups_sidebars.about_group,onChange:G("bb_rl_groups_sidebars","about_group")})),(0,r.createElement)("div",{className:"toggle-item"},(0,r.createElement)(l.ToggleControl,{label:(0,i.__)("Group Members","buddyboss"),checked:n.bb_rl_groups_sidebars.group_members,onChange:G("bb_rl_groups_sidebars","group_members")})))))));case"menus":return(0,r.createElement)("div",{className:"settings-content"},(0,r.createElement)("h1",null,(0,i.__)("Menu Settings","buddyboss")),(0,r.createElement)("p",{className:"settings-description"},(0,i.__)("Configure header, sidebar, and custom link menus to control navigation across your community.","buddyboss")),(0,r.createElement)(ao,{onDragEnd:V}," ",(0,r.createElement)("div",{className:"settings-card"},(0,r.createElement)(Vo,{title:(0,i.__)("Menus","buddyboss"),isExpanded:h.menus,onToggle:()=>F("menus"),onHelpClick:()=>z("459625")},(0,r.createElement)("div",{className:"settings-form-field menu-header-field"},(0,r.createElement)("div",{className:"field-label"},(0,r.createElement)("label",null,(0,i.__)("Header","buddyboss")),(0,r.createElement)("p",null,(0,i.__)("Choose a menu which displays in the top navigation bar","buddyboss"))),(0,r.createElement)("div",{className:"field-input"},(0,r.createElement)(l.SelectControl,{value:n.bb_rl_header_menu,options:[{label:(0,i.__)("Select Menu","buddyboss"),value:""},...D.map((e=>({label:e.name,value:e.slug})))],onChange:L("bb_rl_header_menu"),className:"bb-rl-input-field"}),(0,r.createElement)("p",{className:"field-note"},(0,a.createInterpolateElement)((0,i.__)("Update your header menu from Appearance > <a>Menus</a>. There you will find a Display Option of ReadyLaunch","buddyboss"),{a:(0,r.createElement)("a",{href:"/wp-admin/nav-menus.php"})})))),(0,r.createElement)("div",{className:"settings-form-field with-icon-toggles"},(0,r.createElement)("div",{className:"field-label"},(0,r.createElement)("label",null,(0,i.__)("Side","buddyboss")),(0,r.createElement)("p",null,(0,i.__)("Enable and re-order menu items shown on the left sidebar","buddyboss"))," "),(0,r.createElement)(Go,{droppableId:"sideMenuItems"},(e=>(0,r.createElement)("div",{className:"field-toggles",...e.droppableProps,ref:e.innerRef},I.map(((e,t)=>(0,r.createElement)(To,{key:e.id,draggableId:e.id,index:t},((t,n)=>(0,r.createElement)("div",{ref:t.innerRef,...t.draggableProps,...t.dragHandleProps,className:"side-menu-item-draggable toggle-item "+(n.isDragging?"is-dragging":"")},(0,r.createElement)("i",{className:"bb-icons-rl-list"}),(0,r.createElement)(l.ToggleControl,{checked:e.enabled,onChange:t=>G("bb_rl_side_menu",e.id)(t),label:(0,r.createElement)(r.Fragment,null,(0,r.createElement)("span",{className:`menu-icon bb-icons-rl-${e.icon}`})," ",e.label)})))))),e.placeholder)))),(0,r.createElement)("div",{className:"settings-form-field custom-links-field"},(0,r.createElement)("div",{className:"field-label"},(0,r.createElement)("label",null,(0,i.__)("Link","buddyboss")),(0,r.createElement)("p",null,(0,i.__)("Add and re-order custom links which are shown on the left sidebar","buddyboss"))," "),(0,r.createElement)(Go,{droppableId:"bb_rl_custom_links"},(e=>(0,r.createElement)("div",{className:"field-input custom-links-wrapper",...e.droppableProps,ref:e.innerRef},n.bb_rl_custom_links&&n.bb_rl_custom_links.map(((e,t)=>(0,r.createElement)(To,{key:e.id,draggableId:e.id.toString(),index:t},((t,a)=>(0,r.createElement)(zo,{link:e,onEdit:()=>(e=>{x(e),_(!0)})(e),onDelete:()=>(e=>{b(!0),d({status:"saving",message:(0,i.__)("Saving changes...","buddyboss")});const t=n.bb_rl_custom_links?n.bb_rl_custom_links.filter((t=>t.id!==e)):[];o((e=>({...e,bb_rl_custom_links:t}))),v((e=>({...e,bb_rl_custom_links:t})))})(e.id),innerRef:t.innerRef,draggableProps:t.draggableProps,dragHandleProps:t.dragHandleProps,isDragging:a.isDragging}))))),e.placeholder,(0,r.createElement)(l.Button,{className:"add-link-button bb-rl-button bb-rl-button--primary bb-rl-button--small",onClick:j,icon:(0,r.createElement)("i",{className:"bb-icons-rl-plus"})},(0,i.__)("Add New Link","buddyboss")))))))))," ",(0,r.createElement)(Yo,{isOpen:E,onClose:()=>_(!1),onSave:W,linkData:w?{title:w.title,url:w.url}:{title:"",url:""}}));default:return(0,r.createElement)("div",null,"Select a tab")}var c})())),(0,r.createElement)("div",{className:"bb-rl-toast-container"},u&&(0,r.createElement)(Xo,{status:u.status,message:u.message,onDismiss:()=>d(null)})),(0,r.createElement)(Jo,{isOpen:S,onClose:()=>{P(!1),B(null),k(null)},title:A?.title||"Help"},O?(0,r.createElement)("div",{className:"help-content-loading"},(0,r.createElement)(l.Spinner,null),(0,r.createElement)("p",null,(0,i.__)("Loading help content...","buddyboss"))):T?(0,r.createElement)("div",{className:"help-content-error"},(0,r.createElement)("p",null,T)):A?(0,r.createElement)(r.Fragment,null,A.videoId&&(0,r.createElement)("div",{style:{marginBottom:16}},(0,r.createElement)("iframe",{width:"100%",height:"315",src:`https://www.youtube.com/embed/${A.videoId}`,title:"YouTube video",frameBorder:"0",allowFullScreen:!0})),(0,r.createElement)("div",{className:"help-content",dangerouslySetInnerHTML:{__html:A.content}}),A.imageUrl&&(0,r.createElement)("img",{src:A.imageUrl,alt:"Help content illustration",style:{width:"100%",borderRadius:8,marginBottom:16}})):(0,r.createElement)("p",null,(0,i.__)("No help content available.","buddyboss"))))}),null),Zo)})();