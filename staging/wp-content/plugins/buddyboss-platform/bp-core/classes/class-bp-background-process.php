<?php
/**
 * Abstract WP_Background_Process class.
 *
 * Uses https://github.com/A5hleyRich/wp-background-processing to handle DB
 * updates in the background.
 *
 * @package buddyboss/Classes
 */

defined( 'ABSPATH' ) || exit;

if ( ! class_exists( 'BP_Background_Process' ) ) {

	if ( ! class_exists( 'WP_Async_Request', false ) ) {
		include_once buddypress()->plugin_dir . 'bp-core/libraries/wp-async-request.php';
	}

	if ( ! class_exists( 'WP_Background_Process', false ) ) {
		include_once buddypress()->plugin_dir . 'bp-core/libraries/wp-background-process.php';
	}

	/**
	 * BP_Background_Process class.
	 */
	abstract class BP_Background_Process extends WP_Background_Process {

		/**
		 * Is queue empty.
		 *
		 * @return bool
		 */
		protected function is_queue_empty() {
			global $wpdb;

			$table  = $wpdb->options;
			$column = 'option_name';

			if ( is_multisite() ) {
				$table  = $wpdb->sitemeta;
				$column = 'meta_key';
			}

			$key = $wpdb->esc_like( $this->identifier . '_batch_' ) . '%';

			$count = $wpdb->get_var( $wpdb->prepare( "SELECT COUNT(*) FROM {$table} WHERE {$column} LIKE %s", $key ) ); // @codingStandardsIgnoreLine.

			return ! ( $count > 0 );
		}

		/**
		 * Get batch.
		 *
		 * @return stdClass Return the first batch from the queue.
		 */
		protected function get_batch() {
			global $wpdb;

			$table        = $wpdb->options;
			$column       = 'option_name';
			$key_column   = 'option_id';
			$value_column = 'option_value';

			if ( is_multisite() ) {
				$table        = $wpdb->sitemeta;
				$column       = 'meta_key';
				$key_column   = 'meta_id';
				$value_column = 'meta_value';
			}

			$key = $wpdb->esc_like( $this->identifier . '_batch_' ) . '%';

			$query = $wpdb->get_row( $wpdb->prepare( "SELECT * FROM {$table} WHERE {$column} LIKE %s ORDER BY {$key_column} ASC LIMIT 1", $key ) ); // @codingStandardsIgnoreLine.

			$batch       = new stdClass();
			$batch->key  = $query->$column;
			$batch->data = array_filter( (array) maybe_unserialize( $query->$value_column ) );

			return $batch;
		}

		/**
		 * See if the batch limit has been exceeded.
		 *
		 * @return bool
		 */
		protected function batch_limit_exceeded() {
			return $this->time_exceeded() || $this->memory_exceeded();
		}

		/**
		 * Handle.
		 *
		 * Pass each queue item to the task handler, while remaining
		 * within server memory and time limit constraints.
		 */
		protected function handle() {
			$this->lock_process();

			do {

				/**
				 * Filter to use before start process.
				 *
				 * @since BuddyBoss 2.5.60
				 *
				 * @param object $batch Batch object.
				 */
				$batch = apply_filters( 'bb_bp_bg_process_start', $this->get_batch() );

				foreach ( $batch->data as $key => $value ) {
					$task = $this->task( $value );

					if ( false !== $task ) {
						$batch->data[ $key ] = $task;
					} else {
						unset( $batch->data[ $key ] );
					}

					if ( $this->batch_limit_exceeded() ) {
						// Batch limits reached.
						break;
					}
				}

				// Update or delete current batch.
				if ( ! empty( $batch->data ) ) {
					$this->update( $batch->key, $batch->data );
				} else {
					$this->delete( $batch->key );
				}

				/**
				 * Action to use after end the process.
				 *
				 * @since BuddyBoss 2.5.60
				 *
				 * @param object $batch Batch object.
				 */
				do_action( 'bb_bp_bg_process_end', $batch );

			} while ( ! $this->batch_limit_exceeded() && ! $this->is_queue_empty() );

			$this->unlock_process();

			// Start next batch or complete process.
			if ( ! $this->is_queue_empty() ) {
				$this->dispatch();
			} else {
				$this->complete();
			}
		}

		/**
		 * Get memory limit.
		 *
		 * @return int
		 */
		protected function get_memory_limit() {
			if ( function_exists( 'ini_get' ) ) {
				$memory_limit = ini_get( 'memory_limit' );
			} else {
				// Sensible default.
				$memory_limit = '128M';
			}

			if ( ! $memory_limit || - 1 === intval( $memory_limit ) ) {
				// Unlimited, set to 32GB.
				$memory_limit = '32G';
			}

			return wp_convert_hr_to_bytes( $memory_limit );
		}

		/**
		 * Schedule cron healthcheck.
		 *
		 * @param array $schedules Schedules.
		 *
		 * @return array
		 */
		public function schedule_cron_healthcheck( $schedules ) {
			$interval = apply_filters( $this->identifier . '_cron_interval', 5 );

			if ( property_exists( $this, 'cron_interval' ) ) {
				$interval = apply_filters( $this->identifier . '_cron_interval', $this->cron_interval );
			}

			// Adds every 5 minutes to the existing schedules.
			$schedules[ $this->identifier . '_cron_interval' ] = array(
				'interval' => MINUTE_IN_SECONDS * $interval,
				/* translators: %d: interval */
				'display'  => sprintf( __( 'Every %d minutes', 'buddyboss' ), $interval ),
			);

			return $schedules;
		}

		/**
		 * Delete all batches.
		 *
		 * @return WC_Background_Process
		 */
		public function delete_all_batches() {
			global $wpdb;

			$table  = $wpdb->options;
			$column = 'option_name';

			if ( is_multisite() ) {
				$table  = $wpdb->sitemeta;
				$column = 'meta_key';
			}

			$key = $wpdb->esc_like( $this->identifier . '_batch_' ) . '%';

			$wpdb->query( $wpdb->prepare( "DELETE FROM {$table} WHERE {$column} LIKE %s", $key ) ); // @codingStandardsIgnoreLine.

			return $this;
		}

		/**
		 * Kill process.
		 *
		 * Stop processing queue items, clear cronjob and delete all batches.
		 */
		public function kill_process() {
			if ( ! $this->is_queue_empty() ) {
				$this->delete_all_batches();
				wp_clear_scheduled_hook( $this->cron_hook_identifier );
			}
		}

		/**
		 * Should the process exit with wp_die?
		 *
		 * @param mixed $return What to return if filter says don't die, default is null.
		 *
		 * @return void|mixed
		 */
		protected function maybe_wp_die( $return = null ) {
			/**
			 * Should wp_die be used?
			 *
			 * @return bool
			 */
			if ( apply_filters( $this->identifier . '_wp_die', true ) ) {
				wp_die();
			}

			return $return;
		}
	}
}
