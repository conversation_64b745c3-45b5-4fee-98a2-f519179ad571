/* Displayed User */
#wpadminbar .quicklinks li#wp-admin-bar-user-admin-with-avatar > a img,
#wpadminbar .quicklinks li#wp-admin-bar-group-admin-with-avatar > a img {
	width: 16px;
	height: 16px;
	display: inline;
	border: 1px solid #999;
	vertical-align: middle;
	margin: -2px 10px 0 -5px;
	padding: 0;
	background: #eee;
	float: none;
}

/* Displayed Group */
#wpadminbar .quicklinks li#wp-admin-bar-group-admin-with-avatar ul {
	left: 0;
}

#wpadminbar .quicklinks li#wp-admin-bar-group-admin-with-avatar ul ul {
	left: 0;
}

/* Notifications */
#wpadminbar .quicklinks li#wp-admin-bar-my-account a span.count,
#wpadminbar .quicklinks li#wp-admin-bar-my-account-with-avatar a span.count,
#wpadminbar .quicklinks li#wp-admin-bar-bp-notifications #ab-pending-notifications {
	background: #0073aa;
	color: #fff;
	text-shadow: none;
	display: inline;
	padding: 2px 5px;
	font-size: 10px;
	font-weight: 700;
	-moz-border-radius: 10px;
	-khtml-border-radius: 10px;
	-webkit-border-radius: 10px;
	border-radius: 10px;
}

#wpadminbar .quicklinks li#wp-admin-bar-bp-notifications #ab-pending-notifications {
	background: #ddd;
	color: #333;
	margin: 0;
}

#wpadminbar .quicklinks li#wp-admin-bar-bp-notifications #ab-pending-notifications.alert {
	background-color: #0073aa;
	color: #fff;
}

#wpadminbar .quicklinks li#wp-admin-bar-bp-notifications > a {
	padding: 0 0.5em;
}

#wp-admin-bar-user-info img.avatar {
	height: 64px;
	width: 64px;
}

/* Remove additional arrows from appearing on the BuddyPress' `my_account_menu`
 * on large screens. Add back arrows for screen widths 600px and lower.
 */
#wpadminbar .wp-admin-bar-arrow-right {
	display: none;
}

@media screen and (max-width: 600px) {

	#wpadminbar .wp-admin-bar-arrow-right {
		display: block;
	}
}

/* Member Switching */
#wpadminbar #wp-admin-bar-top-secondary li#wp-admin-bar-switch-back a {
	background: #0073aa;
	color: #fff;
}

li.toplevel_page_buddyboss-platform .wp-submenu li a[href^="admin.php?page=bbp-repair"],
li.toplevel_page_buddyboss-platform .wp-submenu li a[href^="admin.php?page=bbp-converter"],
li.toplevel_page_buddyboss-platform .wp-submenu li a[href^="admin.php?page=bbp-reset"],
li.toplevel_page_buddyboss-platform .wp-submenu li a[href^="admin.php?page=bp-credits"],
li.toplevel_page_buddyboss-platform .wp-submenu li a[href^="admin.php?page=bp-member-type-import"],
li.toplevel_page_buddyboss-platform .wp-submenu li a[href^="admin.php?page=bp-media-import"],
li.toplevel_page_buddyboss-platform .wp-submenu li a[href^="admin.php?page=bp-repair-community"] {
	display: none !important;
}

.nav-settings-subsubsub {
	padding: 3px 13px 22px 0;
}

.nav-tab-wrapper a.bp-buddyboss-app.nav-tab {
	display: none !important;
}
