<?php
// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

return array(
	array(
		'refer_id'     => 1,
		'login'        => 'bb-arianna',
		'display_name' => 'Arianna',
		'first_name'   => '<PERSON><PERSON>',
		'last_name'    => '<PERSON>',
		'email'        => '<EMAIL>',
	),
	array(
		'refer_id'     => 2,
		'login'        => 'bb-charles',
		'display_name' => '<PERSON>',
		'first_name'   => '<PERSON>',
		'last_name'    => 'Brayden',
		'email'        => '<EMAIL>',
	),
	array(
		'refer_id'     => 3,
		'login'        => 'bb-jessica',
		'display_name' => '<PERSON>',
		'first_name'   => '<PERSON>',
		'last_name'    => '<PERSON>',
		'email'        => '<EMAIL>',
	),
	array(
		'refer_id'     => 4,
		'login'        => 'bb-madelyn',
		'display_name' => '<PERSON><PERSON>',
		'first_name'   => '<PERSON><PERSON>',
		'last_name'    => '<PERSON>',
		'email'        => '<EMAIL>',
	),
	array(
		'refer_id'     => 5,
		'login'        => 'bb-ni<PERSON><PERSON>',
		'display_name' => '<PERSON><PERSON>',
		'first_name'   => '<PERSON>lina',
		'last_name'    => 'Victoria',
		'email'        => '<EMAIL>',
	),
	array(
		'refer_id'     => 6,
		'login'        => 'bb-luna',
		'display_name' => 'Luna',
		'first_name'   => 'Luna',
		'last_name'    => 'Stella',
		'email'        => '<EMAIL>',
	),
	array(
		'refer_id'     => 7,
		'login'        => 'bb-margaret',
		'display_name' => 'Margaret',
		'first_name'   => 'Margaret',
		'last_name'    => 'Mia',
		'email'        => '<EMAIL>',
	),
	array(
		'refer_id'     => 8,
		'login'        => 'bb-mateo',
		'display_name' => 'Mateo',
		'first_name'   => 'Mateo',
		'last_name'    => 'Gabriel',
		'email'        => '<EMAIL>',
	),
	array(
		'refer_id'     => 9,
		'login'        => 'bb-trent',
		'display_name' => 'Trent',
		'first_name'   => 'Trent',
		'last_name'    => 'Watson',
		'email'        => '<EMAIL>',
	),
	array(
		'refer_id'     => 10,
		'login'        => 'bb-maverick',
		'display_name' => 'Maverick',
		'first_name'   => 'Maverick',
		'last_name'    => 'Cooper',
		'email'        => '<EMAIL>',
	),
	array(
		'refer_id'     => 11,
		'login'        => 'bb-neville',
		'display_name' => 'Neville',
		'first_name'   => 'Neville',
		'last_name'    => 'Griffin',
		'email'        => '<EMAIL>',
	),
);
