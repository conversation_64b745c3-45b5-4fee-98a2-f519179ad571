function add_field(){var e=document.getElementById("field_box"),t=document.getElementById("field_next").value,o=document.createElement("div"),n=(o.setAttribute("id","field_div"+t),o.setAttribute("class","sortable"),document.createElement("span")),i=(n.setAttribute("class","bp_ps_col1"),n.setAttribute("title",window.bp_ps_strings.drag),n.appendChild(document.createTextNode(" ⇅")),jQuery("<select>",{name:"bp_ps_options[field_name]["+t+"]",id:"field_name"+t}));i.addClass("bp_ps_col2"),i.addClass("new_field");jQuery("<option>",{text:window.bp_ps_strings.field,value:0}).appendTo(i),jQuery.each(window.bp_ps_groups,function(e,t){jQuery.each(t,function(e,t){var o=jQuery("<optgroup>",{label:e});o.appendTo(i),jQuery.each(t,function(e,t){jQuery("<option>",{text:t.name,value:t.id}).appendTo(o)})})});var d=document.createElement("a");d.setAttribute("href",'javascript:remove("field_div'+t+'");'),d.setAttribute("class","delete"),d.appendChild(document.createTextNode(window.bp_ps_strings.remove)),e.appendChild(o),o.appendChild(n),o.appendChild(document.createTextNode("\n")),i.appendTo("#field_div"+t),o.appendChild(document.createTextNode("\n")),o.appendChild(d),enableSortableFieldOptions(),disableAlreadySelectedOption(),document.getElementById("field_name"+t).focus(),document.getElementById("field_next").value=++t}function remove(e){var t,e=document.getElementById(e);if(0==document.querySelectorAll("body.post-type-bp_ps_form #postbox-container-2 #normal-sortables #bp_ps_fields_box .inside #field_box .sortable").length-1)return t=document.getElementById("empty-box-alert").value,window.alert(t),!1;e.parentNode.removeChild(e)}function enableSortableFieldOptions(){jQuery(".field_box").sortable({items:"div.sortable",tolerance:"pointer",axis:"y",handle:"span"})}function disableAlreadySelectedOption(){jQuery("#field_box select.bp_ps_col2 option").prop("disabled",!1);var e=jQuery.map(jQuery("#field_box select.bp_ps_col2 option:selected"),function(e){if("heading"!==e.value)return e.value});jQuery("#field_box select.bp_ps_col2 option").filter(function(){if(0==jQuery(this).prop("selected"))return-1<jQuery.inArray(jQuery(this).val(),e)}).prop("disabled",!0),jQuery("#field_box select.bp_ps_col2 option").filter(function(){return-1==jQuery.inArray(jQuery(this).val(),e)}).prop("disabled",!1)}jQuery(document).ready(function(){enableSortableFieldOptions(),disableAlreadySelectedOption()}),jQuery(document).ready(function(n){n("#template").change(function(){var t=n("#bp_ps_template .spinner"),o=n("input[type=submit]"),e={action:"template_options",form:n("#form_id").val(),template:n("#template option:selected").val()};o.attr("disabled","disabled"),t.addClass("is-active"),n.post(ajaxurl,e,function(e){n("#template_options").html(e),t.removeClass("is-active"),o.removeAttr("disabled")})}),jQuery(document).on("change","body.post-type-bp_ps_form #postbox-container-2 #normal-sortables #bp_ps_fields_box .inside #field_box .sortable .bp_ps_col2.new_field",function(){var e=this.value,t=n("body.post-type-bp_ps_form #postbox-container-2 #normal-sortables #bp_ps_fields_box .inside #field_next").val();n.post(ajaxurl,{action:"bp_search_ajax_option",field_id:e,count:t},function(e){n("body.post-type-bp_ps_form #postbox-container-2 #normal-sortables #bp_ps_fields_box .inside #field_box #field_div"+(t-1)).remove(),n("body.post-type-bp_ps_form #postbox-container-2 #normal-sortables #bp_ps_fields_box .inside #field_box").append(e),disableAlreadySelectedOption()})}),jQuery(document).on("change","body.post-type-bp_ps_form #postbox-container-2 #normal-sortables #bp_ps_fields_box .inside #field_box .sortable .bp_ps_col2.existing",function(){var e=jQuery(this).find("option:selected").text(),t=jQuery(this).parent().closest("div").attr("id");jQuery("#"+t+" .bp_ps_col3").attr("placeholder",e)})});