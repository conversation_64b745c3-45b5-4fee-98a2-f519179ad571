function bp_ps_clear_radio(e){for(var t=new Event("change"),o=(e=document.getElementById(e)).getElementsByTagName("input"),a=0;a<o.length;a++)o[a].checked="",o[a].dispatchEvent(t)}function bp_ps_autocomplete(e,t,o){e=document.getElementById(e);var a=new google.maps.places.Autocomplete(e,{types:["geocode"]});google.maps.event.addListener(a,"place_changed",function(){var e=a.getPlace();document.getElementById(t).value=e.geometry.location.lat(),document.getElementById(o).value=e.geometry.location.lng()})}function bp_ps_locate(t,o,a){navigator.geolocation?navigator.geolocation.getCurrentPosition(function(e){document.getElementById(o).value=e.coords.latitude,document.getElementById(a).value=e.coords.longitude,bp_ps_address(e,t)},function(e){alert("ERROR "+e.code+": "+e.message)},{timeout:5e3}):alert("ERROR: Geolocation is not supported by this browser")}function bp_ps_address(e,o){var t=new google.maps.Geocoder,e={lat:e.coords.latitude,lng:e.coords.longitude};t.geocode({location:e},function(e,t){"OK"===t?e[0]?document.getElementById(o).value=e[0].formatted_address:alert("ERROR: Geocoder found no results"):alert("ERROR: Geocoder status: "+t)})}function bp_ps_clear_form_elements(e){var e=jQuery(e).closest("form"),t=new Event("change");e.find(":input").each(function(){switch(this.type){case"password":case"select-multiple":case"select-one":case"text":case"email":case"date":case"url":case"search":case"textarea":jQuery(this).val("");break;case"checkbox":case"radio":this.checked=!1,this.dispatchEvent(t)}}),jQuery.removeCookie("bp_ps_request",{path:"/"}),e.find(".submit").trigger("click")}