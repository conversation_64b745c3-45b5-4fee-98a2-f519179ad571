jQuery(document).ready(function(){var t,e,i,r,n,a,d=jQuery("body .layout-wrap #profile-details-section #signup_profile_field_ids"),s=jQuery('<input type="hidden" class="onloadfields" value="" />'),l=jQuery('<input type="hidden" name="signup_profile_field_ids" id="signup_profile_field_ids" value="" />'),u=jQuery('<input type="hidden" name="signup_profile_field_id_prev" id="signup_profile_field_id_prev" value="" />'),o=(jQuery("body").append(s),jQuery("body").append(u),jQuery("body .onloadfields")),p=0,s=(o.val(d.val()),void 0!==window.tinymce&&jQuery(window.tinymce.editors).each(function(e){window.tinymce.editors[e].on("change",function(){window.tinymce.editors[e].save()})}),jQuery("body #buddypress #register-page #signup-form .layout-wrap #profile-details-section .editfield fieldset select#"+BP_Register.field_id));function y(){var e,i,t;9!==window.event.keyCode&&(e=r.val().toLowerCase(),i=n.val().toLowerCase(),t=/^([a-zA-Z0-9_\.\-\+])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/,(a=jQuery("#email-strength-result")).removeClass("show mismatch bad"),""===e&&""===i||""===i||t.test(i)&&t.test(e)&&e===i?a.html(""):e!==i&&n.length&&a.addClass("show mismatch").html(BP_Register.mismatch_email))}void 0!==s.val()&&s.val().length&&(1===p&&(jQuery("body .ajax_added").remove(),d.val(jQuery(".onloadfields").val()),u.val(s.val())),i=d.val(),s=s.val(),t=jQuery(".register-section.extended-profile"),e=o.val(),i={action:"xprofile_get_field",_wpnonce:BP_Register.nonce,fields:i,fixedIds:e,type:s},jQuery.ajax({type:"GET",url:BP_Register.ajaxurl,data:i,success:function(e){var i;e.success&&(p=1,d.val(""),d.val(e.data.field_ids),t.append(e.data.field_html),(i=jQuery("body .layout-wrap #profile-details-section > .editfield")).sort(function(e,i){return jQuery(e).data("index")-jQuery(i).data("index")}),jQuery(".layout-wrap #profile-details-section").html(i),jQuery("body .layout-wrap #profile-details-section").append(l),l.val(e.data.field_ids),jQuery(".register-section textarea.wp-editor-area").each(function(){wp.editor.remove(jQuery(this).attr("id")),wp.editor.initialize(jQuery(this).attr("id"),{tinymce:{wpautop:!0,branding:!1,menubar:!1,statusbar:!0,elementpath:!0,plugins:"lists fullscreen link",toolbar1:"bold italic underline blockquote strikethrough bullist numlist alignleft aligncenter alignright undo redo link fullscreen",setup:function(e){e.on("change",function(){e.save()})}},quicktags:{buttons:"strong,em,link,block,del,ins,img,ul,ol,li,code,more,close"}})}))}})),jQuery(document).on("change","body #buddypress #register-page #signup-form .layout-wrap #profile-details-section .editfield fieldset select#"+BP_Register.field_id,function(){var n=jQuery("body #buddypress #register-page #signup-form .submit #signup_submit"),e=(n.prop("disabled",!0),1===p&&d.val(jQuery(".onloadfields").val()),d.val()),i=this.value,a=jQuery(".register-section.extended-profile"),t=o.val(),e={action:"xprofile_get_field",_wpnonce:BP_Register.nonce,fields:e,fixedIds:t,type:i,prevId:u.val()};u.val(this.value),jQuery.ajax({type:"GET",url:BP_Register.ajaxurl,data:e,success:function(e){var i,t,r;e.success?(r=[],l.val()&&(r=l.val().split(",")),i=e.data.field_ids.split(","),t=[],r&&(jQuery.grep(r,function(e){-1==jQuery.inArray(e,i)&&t.push(e)}),0!==t.length)&&jQuery.each(t,function(e,i){a.find(".field_"+i).remove()}),n.prop("disabled",!1),p=1,d.val(""),d.val(e.data.field_ids),a.append(e.data.field_html),(r=jQuery("body .layout-wrap #profile-details-section > .editfield")).sort(function(e,i){return jQuery(e).data("index")-jQuery(i).data("index")}),jQuery(".layout-wrap #profile-details-section").html(r),jQuery("body .layout-wrap #profile-details-section").append(l),l.val(e.data.field_ids),jQuery(".register-section textarea.wp-editor-area").each(function(){wp.editor.remove(jQuery(this).attr("id")),wp.editor.initialize(jQuery(this).attr("id"),{tinymce:{wpautop:!0,branding:!1,menubar:!1,statusbar:!0,elementpath:!0,plugins:"lists fullscreen link",toolbar1:"bold italic underline blockquote strikethrough bullist numlist alignleft aligncenter alignright undo redo link fullscreen",setup:function(e){e.on("change",function(){e.save()})}},quicktags:{buttons:"strong,em,link,block,del,ins,img,ul,ol,li,code,more,close"}})})):n.prop("disabled",!1)}})}),jQuery(document).on("click","body #buddypress #register-page #signup-form #signup_submit",function(){var t='<div class="bp-messages bp-feedback error">',r=(t=(t+='<span class="bp-icon" aria-hidden="true"></span>')+("<p>"+BP_Register.required_field+"</p>")+"</div>",jQuery("#signup_email")),e=jQuery("#signup_email_confirm"),i=jQuery("#signup_password"),n=jQuery("#signup_password_confirm"),a=!0;if(jQuery(".register-page .error").remove(),jQuery(document).find(e).length&&""==jQuery(document).find(e).val()&&(jQuery(document).find(e).before(t),a=!1),jQuery(document).find(i).length&&""==jQuery(document).find(i).val()&&(jQuery(document).find(i).before(t),a=!1),jQuery(document).find(n).length&&""==jQuery(document).find(n).val()&&(jQuery(document).find(n).before(t),a=!1),jQuery(".required-field").each(function(){var e,i;jQuery(this).find('input[type="text"]').length&&""==jQuery(this).find('input[type="text"] ').val()&&(jQuery(this).find('input[type="text"]').before(t),a=!1),jQuery(this).find('input[type="number"]').length&&""==jQuery(this).find('input[type="number"] ').val()&&(jQuery(this).find('input[type="number"]').before(t),a=!1),jQuery(this).find('input[type="tel"]').length&&""==jQuery(this).find('input[type="tel"] ').val()&&(jQuery(this).find('input[type="tel"]').before(t),a=!1),jQuery(this).find("textarea").length&&""==jQuery(this).find("textarea").val()&&(jQuery(this).find("textarea").before(t),a=!1),jQuery(this).find("select").length&&""==jQuery(this).find("select").val()&&(jQuery(this).find("select").before(t),a=!1),jQuery(this).find('input[type="checkbox"]').length&&(e=0,jQuery(this).find('input[type="checkbox"]').each(function(){1==jQuery(this).prop("checked")&&e++}),e<=0)&&(jQuery(this).find("legend").next().append(t),a=!1),jQuery(this).find('input[type="radio"]').length&&(i=0,jQuery(this).find('input[type="radio"]').each(function(){1==jQuery(this).prop("checked")&&i++}),i<=0)&&(jQuery(this).find("legend").next().append(t),a=!1)}),jQuery(document).find(r).length&&""==jQuery(document).find(r).val()?(jQuery(document).find(r).before(t),a=!1):(y(),jQuery.ajax({type:"POST",url:ajaxurl,dataType:"json",async:!1,data:jQuery("body #buddypress #register-page #signup-form").serialize()+"&action=check_email",success:function(e){e.signup_email&&(t='<div class="bp-messages bp-feedback error">',t=(t+='<span class="bp-icon" aria-hidden="true"></span>')+"<p>"+e.signup_email+"</p></div>",jQuery(document).find(r).before(t),a=!1);var i,t="field_"+e.field_id;return e.signup_username&&(i='<div class="bp-messages bp-feedback error">',i=(i+='<span class="bp-icon" aria-hidden="true"></span>')+"<p>"+e.signup_username+"</p></div>",jQuery(document).find("#"+t).before(i),a=!1),!0}})),!a){e=jQuery(".error").first();if(e.length)return jQuery("html,body").animate({scrollTop:e.offset().top},1e3),!1}return a}),(r=jQuery("#signup_email")).length&&r.on("focusout",function(){var e=r.val().toLowerCase(),i=n.val().toLowerCase(),t=/^([a-zA-Z0-9_\.\-\+])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/;(a=jQuery("#email-strength-result")).removeClass("show mismatch bad"),""===e&&""===i&&t.test(e)?a.html(""):(a.html(""),""===e&&""===i||t.test(e)?""!==i&&e!==i&&n.length&&a.addClass("show mismatch").html(BP_Register.mismatch_email):a.addClass("show bad").html(BP_Register.valid_email))}),(n=jQuery("#signup_email_confirm")).length&&n.on("keyup change",y)});