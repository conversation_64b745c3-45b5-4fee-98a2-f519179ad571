window.bp=window.bp||{},((r,l)=>{var d,u=[];r.mentions=r.mentions||{},r.mentions.users=window.bp.mentions.users||[],"object"==typeof window.BP_Suggestions&&(window.BP_Suggestions.friends&&0<window.BP_Suggestions.friends.length?r.mentions.users=window.BP_Suggestions.friends:window.BP_Suggestions.members&&0<window.BP_Suggestions.members.length?r.mentions.users=window.BP_Suggestions.members:r.mentions.users&&0<r.mentions.users.length&&(r.mentions.users=r.mentions.users)),r.mentions.xhr=null,r.mentions.xhr_scroll=null,r.mentions.clearCache=function(){u=[]},l.fn.bp_mentions=function(e,t,n){l.isArray(e)&&(e={data:e}),t=t||{},n=n||{};var i=navigator.userAgent.toLowerCase(),s=-1<i.indexOf("android"),a=-1<i.indexOf("chrome"),i=l.extend(!0,{},{delay:500,hideWithoutSuffix:!0,insertTpl:BP_Mentions_Options.insert_tpl,limit:100,startWithSpace:!1,callbacks:{filter:function(e,t,n){for(var i,o=[],s=new RegExp("^"+e+"| "+e,"ig"),a=0,r=t.length;a<r;a++)(i=t[a])[n].toLowerCase().match(s)&&o.push(i);return o},highlighter:function(e,t){return t?(t=new RegExp(">(\\s*|[\\w\\s]*)("+this.at.replace("+","\\+")+"?"+t.replace("+","\\+")+")([\\w ]*)\\s*<","ig"),e.replace(t,function(e,t,n,i){return">"+t+"<strong>"+n+"</strong>"+i+"<"})):e},before_reposition:function(e){var t,n=l("#atwho-ground-"+this.id+" .atwho-view"),i=l("body"),o=this.$inputor.data("atwho");"undefined"!==o&&"undefined"!==o.iframe&&null!==o.iframe?(t=this.$inputor.caret("offset",{iframe:o.iframe}),"undefined"!==(o=l(o.iframe).offset())&&(t.left+=o.left,t.top+=o.top)):t=this.$inputor.caret("offset"),o=t.left>i.width()/2?(n.addClass("right"),t.left-e.left-this.view.$el.width()):(n.removeClass("right"),t.left-e.left+1),i.width()<=400&&l(document).scrollTop(t.top-6),n=parseInt(this.$inputor.css("line-height").substr(0,this.$inputor.css("line-height").length-2),10),e.top=t.top+(n=!n||n<5?19:n),e.left+=o},inserting_wrapper:function(e,t){return""+t}}},t),t=l.extend(!0,{},{callbacks:{remoteFilter:function(n,e){function t(){a.scrollTop()+a.innerHeight()>=a[0].scrollHeight&&!0===s.allow_scroll&&(a.hasClass("list-loading")||null!=s.total_pages&&parseInt(s.total_pages)===parseInt(i.page)||r.mentions.xhr_scroll||(a.addClass("list-loading"),a.find("li:last-child").hasClass("list-loader")||a.append('<li class="list-loader">Loading more results…</li>'),i.term=s.s_query,i.page=o.data("page")+1,o.data("page",i.page),r.mentions.xhr_scroll=l.getJSON(ajaxurl,i).done(function(e){e.success&&(r.mentions.xhr_scroll=null,void 0!==e.data.total_pages&&(s.total_pages=parseInt(e.data.total_pages)),u[s.s_query].page=i.page,0==(e=l.map(e.data.results,function(e){return e.search=e.search||e.ID+" "+e.name,e})).length?(a.find(".list-loader").remove(),a.removeClass("list-loading"),s.allow_scroll=!1):(s.data=s.data.concat(e),a.find(".list-loader").remove(),s.render_view(s.data),u[s.s_query].data=s.data,a.removeClass("list-loading")))})))}var i={action:"bp_get_suggestions",term:n,type:"members"},o=this.$el,s=this,a=o.find("ul");s.allow_scroll=!0,s.render_view=e,s.total_pages=1,s.s_query=n,o.data("page",1),r.mentions.xhr&&r.mentions.xhr.abort(),r.mentions.xhr_scroll&&r.mentions.xhr_scroll.abort(),"object"==typeof(d=u[n])&&d.data?(o.data("page",d.page),s.data=d.data,s.render_view(d.data),s.total_pages=d.total_pages,d.page<d.total_pages&&!a.find("li:last-child").hasClass("list-loader")&&a.append('<li class="list-loader">Loading more results…</li>'),r.mentions.xhr_scroll=null):(a.find("li:last-child").hasClass("list-loader")||a.append('<li class="list-loader">Loading more results…</li>'),o.data("page")?i.page=o.data("page"):(i.page=1,o.data("page",1)),l.isNumeric(this.$inputor.data("suggestions-group-id"))&&(i["group-id"]=parseInt(this.$inputor.data("suggestions-group-id"),10)),r.mentions.xhr=l.getJSON(ajaxurl,i).done(function(e){var t;e.success&&(u[n]={},u[n].page=i.page,t=l.map(e.data.results,function(e){return e.search=e.search||e.ID+" "+e.name,e}),void 0!==e.data.total_pages&&(s.total_pages=parseInt(e.data.total_pages),u[n].total_pages=parseInt(e.data.total_pages)),s.data=t,u[n].data=t,a.find(".list-loader").remove(),s.render_view(t),a.removeClass("list-loading"),s.$inputor.trigger("keyup"),r.mentions.xhr_scroll=null)})),o.hasClass("has-events")||(a.on("scroll",function(){t()}),o.addClass("has-events"))},sorter:function(e,t){return t},beforeReposition:function(e){var t;0<l("body.rtl").length&&0<(t=this.$el.find(".atwho-view")).length&&(e.left=this.rect().left-t.width())}},data:l.map(e.data,function(e){return e.search=e.search||e.ID+" "+e.name,e}),at:"@",searchKey:"search",startWithSpace:!0,displayTpl:BP_Mentions_Options.display_tpl},BP_Mentions_Options.extra_options,n),n=(this.on("blur.atwhoInner",function(){jQuery("#atwho-ground-whats-new").data("page",1),jQuery("#atwho-ground-whats-new").find(".list-loader").remove(),jQuery("#atwho-ground-whats-new").find(".list-loading").removeClass("list-loading")}),this.on("inserted.atwho",function(e){if(a&&jQuery(this).on("keyup",function(e){13===e.keyCode&&(jQuery(this).hasClass("medium-editor-element")&&void 0!==(e=jQuery(this).attr("contenteditable"))&&!1!==e&&jQuery(this).attr("contenteditable","false"),setTimeout(function(){l.each(BP_Mentions_Options.selectors,function(e,t){var n;jQuery(t).hasClass("medium-editor-element")&&void 0!==(n=jQuery(t).attr("contenteditable"))&&!1!==n&&(jQuery(t).attr("contenteditable","true"),jQuery(t).find(".atwho-inserted").closest(".medium-editor-element").focus())})},10))}),jQuery(this).on("keydown",function(e){s||(8==e.keyCode?jQuery(this).find(".atwho-inserted").each(function(){jQuery(this).removeAttr("contenteditable")}):jQuery(this).find(".atwho-inserted").each(function(){jQuery(this).attr("contenteditable",!1)}))}),void 0!==e.currentTarget&&void 0!==e.currentTarget.innerHTML){var t=0;if(void 0!==window.forums_medium_reply_editor){var n=Object.keys(window.forums_medium_reply_editor);if(n.length)for(t=0;t<n.length;t++)window.forums_medium_reply_editor[n[t]].checkContentChanged()}if(void 0!==window.forums_medium_topic_editor){var i=Object.keys(window.forums_medium_topic_editor);if(i.length)for(t=0;t<i.length;t++)window.forums_medium_topic_editor[i[t]].checkContentChanged()}if(void 0!==window.forums_medium_forum_editor){var o=Object.keys(window.forums_medium_forum_editor);if(o.length)for(t=0;t<o.length;t++)window.forums_medium_forum_editor[o[t]].checkContentChanged()}}jQuery("#atwho-ground-whats-new").data("page",1),jQuery("#atwho-ground-whats-new").find(".list-loader").remove(),jQuery("#atwho-ground-whats-new").find(".list-loading").removeClass("list-loading"),jQuery(this).focus()}),this.on("keyup",function(){var e;s&&(e=jQuery(this).text().length,localStorage.setItem("charCount",e),jQuery(this).find(".atwho-inserted").each(function(){jQuery(this).removeAttr("contenteditable")}))}),l.extend(!0,{},i,t,e));return l.fn.atwho.call(this,n)},l(document).ready(function(){localStorage.setItem("charCount",0),l(document).on("focus",BP_Mentions_Options.selectors.join(","),function(){l(this).data("bp_mentions_activated")||void 0!==r&&(l(this).bp_mentions(r.mentions.users),l(this).data("bp_mentions_activated",!0))})}),r.mentions.tinyMCEinit=function(){void 0!==window.tinyMCE&&null!=window.tinyMCE.activeEditor&&l(window.tinyMCE.activeEditor.contentDocument.activeElement).atwho("setIframe",l(".wp-editor-wrap iframe")[0]).bp_mentions(r.mentions.users)}})(bp,jQuery);