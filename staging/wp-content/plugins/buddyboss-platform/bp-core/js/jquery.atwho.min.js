(t=>{"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof exports?module.exports=t(require("jquery")):t(jQuery)})(function(a){var i,s;function t(t){this.currentFlag=null,this.controllers={},this.aliasMaps={},this.$inputor=a(t),this.setupRootElement(),this.listen()}s={ESC:27,TAB:9,ENTER:13,CTRL:17,A:65,P:80,N:78,LEFT:37,UP:38,RIGHT:39,DOWN:40,BACKSPACE:8,SPACE:32},i={beforeSave:function(t){return l.arrayToDefaultHash(t)},matcher:function(t,e,i,n){var r;return t=t.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&"),i&&(t="(?:^|\\s)"+t),i=decodeURI("%C3%80"),r=decodeURI("%C3%BF"),(i=new RegExp(t+"([A-Za-z"+i+"-"+r+"0-9_"+(n?" ":"")+"'.+-]*)$|"+t+"([^\\x00-\\xff]*)$","gi").exec(e))?i[2]||i[1]:null},filter:function(t,e,i){for(var n,r=[],o=0,s=e.length;o<s;o++)n=e[o],~String(n[i]).toLowerCase().indexOf(t.toLowerCase())&&r.push(n);return r},remoteFilter:null,sorter:function(t,e,i){var n,r,o,s;if(!t)return e;for(n=[],r=0,s=e.length;r<s;r++)(o=e[r]).atwho_order=String(o[i]).toLowerCase().indexOf(t.toLowerCase()),-1<o.atwho_order&&n.push(o);return n.sort(function(t,e){return t.atwho_order-e.atwho_order})},tplEval:function(t,i){var e=t;try{return(e="string"!=typeof t?t(i):e).replace(/\$\{([^\}]*)\}/g,function(t,e){return i[e]})}catch(t){return""}},highlighter:function(t,e){return e?(e=new RegExp(">\\s*([^<]*?)("+e.replace("+","\\+")+")([^<]*)\\s*<","ig"),t.replace(e,function(t,e,i,n){return"> "+e+"<strong>"+i+"</strong>"+n+" <"})):t},beforeInsert:function(t){return t},beforeReposition:function(t){return t},afterMatchFailed:function(){}},t.prototype.createContainer=function(t){var e;return null!=(e=this.$el)&&e.remove(),a(t.body).append(this.$el=a('<div class="atwho-container"></div>'))},t.prototype.setupRootElement=function(e,t){if(null==t&&(t=!1),e)this.window=e.contentWindow,this.document=e.contentDocument||this.window.document,this.iframe=e;else{this.document=this.$inputor[0].ownerDocument,this.window=this.document.defaultView||this.document.parentWindow;try{this.iframe=this.window.frameElement}catch(t){if(e=t,this.iframe=null,a.fn.atwho.debug)throw new Error("iframe auto-discovery is failed.\nPlease use `setIframe` to set the target iframe manually.\n"+e)}}return this.createContainer((this.iframeAsRoot=t)?this.document:document)},t.prototype.controller=function(t){var e,i,n,r;if(this.aliasMaps[t])i=this.controllers[this.aliasMaps[t]];else for(n in r=this.controllers)if(e=r[n],n===t){i=e;break}return i||this.controllers[this.currentFlag]},t.prototype.setContextFor=function(t){return this.currentFlag=t,this},t.prototype.reg=function(t,e){var i=(i=this.controllers)[t]||(i[t]=new(this.$inputor.is("[contentEditable]")?g:u)(this,t));return e.alias&&(this.aliasMaps[e.alias]=t),i.init(e),this},t.prototype.listen=function(){return this.$inputor.on("compositionstart",(a=this,function(){var t;return null!=(t=a.controller())&&t.view.hide(),a.isComposing=!0,null})).on("compositionend",(s=this,function(){return s.isComposing=!1,setTimeout(function(t){return s.dispatch(t)}),null})).on("keyup.atwhoInner",(o=this,function(t){return o.onKeyup(t)})).on("keydown.atwhoInner",(n=this,function(t){return n.onKeydown(t)})).on("blur.atwhoInner",(i=this,function(t){var e;if(e=i.controller())return e.expectedQueryCBId=null,e.view.hide(t,e.getOpt("displayTimeout"))})).on("click.atwhoInner",(e=this,function(t){return e.dispatch(t)})).on("scroll.atwhoInner",(r=this,(()=>{var n=r.$inputor.scrollTop();return function(t){var e,i=t.target.scrollTop;return n!==i&&null!=(e=r.controller())&&e.view.hide(t),n=i,!0}})()));var r,e,i,n,o,s,a},t.prototype.shutdown=function(){var t,e=this.controllers;for(t in e)e[t].destroy(),delete this.controllers[t];return this.$inputor.off(".atwhoInner"),this.$el.remove()},t.prototype.dispatch=function(t){var e,i,n=this.controllers,r=[];for(e in n)i=n[e],r.push(i.lookUp(t));return r},t.prototype.onKeyup=function(t){var e;switch(t.keyCode){case s.ESC:t.preventDefault(),null!=(e=this.controller())&&e.view.hide();break;case s.DOWN:case s.UP:case s.CTRL:case s.ENTER:a.noop();break;case s.P:case s.N:t.ctrlKey||this.dispatch(t);break;default:this.dispatch(t)}},t.prototype.onKeydown=function(t){var e,i=null!=(e=this.controller())?e.view:void 0;if(i&&i.visible())switch(t.keyCode){case s.ESC:t.preventDefault(),i.hide(t);break;case s.UP:t.preventDefault(),i.prev();break;case s.DOWN:t.preventDefault(),i.next();break;case s.P:t.ctrlKey&&(t.preventDefault(),i.prev());break;case s.N:t.ctrlKey&&(t.preventDefault(),i.next());break;case s.TAB:case s.ENTER:case s.SPACE:!i.visible()||!this.controller().getOpt("spaceSelectsMatch")&&t.keyCode===s.SPACE||!this.controller().getOpt("tabSelectsMatch")&&t.keyCode===s.TAB||(i.highlighted()?(t.preventDefault(),i.choose(t)):i.hide(t));break;default:a.noop()}};var o=t,n=[].slice;function e(t,e){this.app=t,this.at=e,this.$inputor=this.app.$inputor,this.id=this.$inputor[0].id||this.uid(),this.expectedQueryCBId=null,this.setting=null,this.query=null,this.pos=0,this.range=null,0===(this.$el=a("#atwho-ground-"+this.id,this.app.$el)).length&&this.app.$el.append(this.$el=a('<div id="atwho-ground-'+this.id+'"></div>')),this.model=new p(this),this.view=new f(this)}e.prototype.uid=function(){return(Math.random().toString(16)+"000000000").substr(2,8)+(new Date).getTime()},e.prototype.init=function(t){return this.setting=a.extend({},this.setting||a.fn.atwho.default,t),this.view.init(),this.model.reload(this.setting.data)},e.prototype.destroy=function(){return this.trigger("beforeDestroy"),this.model.destroy(),this.view.destroy(),this.$el.remove()},e.prototype.callDefault=function(){var e=arguments[0],t=2<=arguments.length?n.call(arguments,1):[];try{return i[e].apply(this,t)}catch(t){return a.error(t+" Or maybe At.js doesn't have function "+e)}},e.prototype.trigger=function(t,e){var i;return(e=null==e?[]:e).push(this),i=this.getOpt("alias"),this.$inputor.trigger(i?t+"-"+i+".atwho":t+".atwho",e)},e.prototype.callbacks=function(t){return this.getOpt("callbacks")[t]||i[t]},e.prototype.getOpt=function(t){try{return this.setting[t]}catch(t){return null}},e.prototype.insertContentFor=function(t){var e=this.getOpt("insertTpl"),t=a.extend({},t.data("item-data"),{"atwho-at":this.at});return this.callbacks("tplEval").call(this,e,t,"onInsert")},e.prototype.renderView=function(t){var e=this.getOpt("searchKey");return t=this.callbacks("sorter").call(this,this.query.text,t.slice(0,1001),e),this.view.render(t.slice(0,this.getOpt("limit")))},e.arrayToDefaultHash=function(t){var e,i,n,r;if(!a.isArray(t))return t;for(r=[],e=0,n=t.length;e<n;e++)i=t[e],a.isPlainObject(i)?r.push(i):r.push({name:i});return r},e.prototype.lookUp=function(t){var e;if((!t||"click"!==t.type||this.getOpt("lookUpOnClick"))&&(!this.getOpt("suspendOnComposing")||!this.app.isComposing))return(t=this.catchQuery(t))?(this.app.setContextFor(this.at),(e=this.getOpt("delay"))?this._delayLookUp(t,e):this._lookUp(t)):this.expectedQueryCBId=null,t},e.prototype._delayLookUp=function(t,e){var i,n,r=Date.now?Date.now():(new Date).getTime();return this.previousCallTime||(this.previousCallTime=r),0<(i=e-(r-this.previousCallTime))&&i<e?(this.previousCallTime=r,this._stopDelayedCall(),this.delayedCallTimeout=setTimeout((n=this,function(){return n.previousCallTime=0,n.delayedCallTimeout=null,n._lookUp(t)}),e)):(this._stopDelayedCall(),this.previousCallTime!==r&&(this.previousCallTime=0),this._lookUp(t))},e.prototype._stopDelayedCall=function(){if(this.delayedCallTimeout)return clearTimeout(this.delayedCallTimeout),this.delayedCallTimeout=null},e.prototype._generateQueryCBId=function(){return{}},e.prototype._lookUp=function(t){var e=function(t,e){if(t===this.expectedQueryCBId)return e&&0<e.length?this.renderView(this.constructor.arrayToDefaultHash(e)):this.view.hide()};return this.expectedQueryCBId=this._generateQueryCBId(),this.model.query(t.text,a.proxy(e,this,this.expectedQueryCBId))};var r,l=e,h={}.hasOwnProperty,u=((r=function(t,e){for(var i in e)h.call(e,i)&&(t[i]=e[i]);function n(){this.constructor=t}return n.prototype=e.prototype,t.prototype=new n,t.__super__=e.prototype,t})(c,l),c.prototype.catchQuery=function(){var t=this.$inputor.val(),e=this.$inputor.caret("pos",{iframe:this.app.iframe}),t=t.slice(0,e),t=this.callbacks("matcher").call(this,this.at,t,this.getOpt("startWithSpace"),this.getOpt("acceptSpaceBar")),i="string"==typeof t;if(!(i&&t.length<this.getOpt("minLen",0)))return i&&t.length<=this.getOpt("maxLen",20)?(e=(i=e-t.length)+t.length,this.pos=i,this.trigger("matched",[this.at,(t={text:t,headPos:i,endPos:e}).text])):(t=null,this.view.hide()),this.query=t},c.prototype.rect=function(){var t,e;if(t=this.$inputor.caret("offset",this.pos-1,{iframe:this.app.iframe}))return this.app.iframe&&!this.app.iframeAsRoot&&(e=a(this.app.iframe).offset(),t.left+=e.left,t.top+=e.top),e=this.app.document.selection?0:2,{left:t.left,top:t.top,bottom:t.top+t.height+e}},c.prototype.insert=function(t){var e=this.$inputor,i=e.val(),n=i.slice(0,Math.max(this.query.headPos-this.at.length,0)),r=""===(r=this.getOpt("suffix"))?r:r||" ",r=""+n+(t+=r)+i.slice(this.query.endPos||0);return e.val(r),e.caret("pos",n.length+t.length,{iframe:this.app.iframe}),e.is(":focus")||e.focus(),e.change()},c);function c(){return c.__super__.constructor.apply(this,arguments)}r(y,l),y.prototype._getRange=function(){var t=this.app.window.getSelection();if(0<t.rangeCount)return t.getRangeAt(0)},y.prototype._setRange=function(t,e,i){if((i=null==i?this._getRange():i)&&e)return e=a(e)[0],"after"===t?(i.setEndAfter(e),i.setStartAfter(e)):(i.setEndBefore(e),i.setStartBefore(e)),i.collapse(!1),this._clearRange(i)},y.prototype._clearRange=function(t){var e;if(null==t&&(t=this._getRange()),e=this.app.window.getSelection(),null==this.ctrl_a_pressed)return e.removeAllRanges(),e.addRange(t)},y.prototype._movingEvent=function(t){return"click"===t.type||(t=t.which)===s.RIGHT||t===s.LEFT||t===s.UP||t===s.DOWN},y.prototype._unwrap=function(t){var e;return(e=(t=a(t).unwrap().get(0)).nextSibling)&&e.nodeValue&&(t.nodeValue+=e.nodeValue,a(e).remove()),t},y.prototype.catchQuery=function(t){var e,i,n,r,o;if((o=this._getRange())&&o.collapsed&&null!=t)if(t.which===s.ENTER)(e=a(o.startContainer).closest(".atwho-query")).contents().unwrap(),e.is(":empty")&&e.remove(),(e=a(".atwho-query",this.app.document)).text(e.text()).contents().last().unwrap(),this._clearRange();else{if(/firefox/i.test(navigator.userAgent)){if(a(o.startContainer).is(this.$inputor))return void this._clearRange();t.which===s.BACKSPACE&&o.startContainer.nodeType===document.ELEMENT_NODE&&0<=(r=o.startOffset-1)?((i=o.cloneRange()).setStart(o.startContainer,r),a(i.cloneContents()).contents().last().is(".atwho-inserted")&&(r=a(o.startContainer).contents().get(r),this._setRange("after",a(r).contents().last()))):t.which===s.LEFT&&o.startContainer.nodeType===document.TEXT_NODE&&(r=a(o.startContainer.previousSibling)).is(".atwho-inserted")&&0===o.startOffset&&this._setRange("after",r.contents().last())}if(a(o.startContainer).closest(".atwho-inserted").addClass("atwho-query").siblings().removeClass("atwho-query"),0<(e=a(".atwho-query",this.app.document)).length&&e.is(":empty")&&0===e.text().length&&e.remove(),this._movingEvent(t)||e.removeClass("atwho-inserted"),0<e.length)switch(t.which){case s.LEFT:return this._setRange("before",e.get(0),o),void e.removeClass("atwho-query");case s.RIGHT:return this._setRange("after",e.get(0).nextSibling,o),void e.removeClass("atwho-query")}if(0<e.length&&(r=e.attr("data-atwho-at-query"))&&(e.empty().html(r).attr("data-atwho-at-query",null),this._setRange("after",e.get(0),o)),(i=o.cloneRange()).setStart(o.startContainer,0),i="string"==typeof(r=this.callbacks("matcher").call(this,this.at,i.toString(),this.getOpt("startWithSpace"),this.getOpt("acceptSpaceBar"))),0===e.length&&i&&0<=(n=o.startOffset-this.at.length-r.length)&&(o.setStart(o.startContainer,n),e=a("<span/>",this.app.document).attr(this.getOpt("editableAtwhoQueryAttrs")).addClass("atwho-query"),o.surroundContents(e.get(0)),n=e.contents().last().get(0))&&(/firefox/i.test(navigator.userAgent)?(o.setStart(n,n.length),o.setEnd(n,n.length),this._clearRange(o)):this._setRange("after",n,o)),!(i&&r.length<this.getOpt("minLen",0)))return i&&r.length<=this.getOpt("maxLen",20)?(this.trigger("matched",[this.at,(n={text:r,el:e}).text]),this.query=n):(this.view.hide(),this.query={el:e},0<=e.text().indexOf(this.at)&&(this._movingEvent(t)&&e.hasClass("atwho-inserted")?e.removeClass("atwho-query"):!1!==this.callbacks("afterMatchFailed").call(this,this.at,e)&&this._setRange("after",this._unwrap(e.text(e.text()).contents().first()))),null)}},y.prototype.rect=function(){var t,e=this.query.el.offset();if(e&&this.query.el[0].getClientRects().length)return this.app.iframe&&!this.app.iframeAsRoot&&(t=a(this.app.iframe).offset(),e.left+=t.left-this.$inputor.scrollLeft(),e.top+=t.top-this.$inputor.scrollTop()),e.bottom=e.top+this.query.el.height(),e},y.prototype.insert=function(t,e){var i;return this.$inputor.is(":focus")||this.$inputor.focus(),(i=this.getOpt("functionOverrides")).insert?i.insert.call(this,t,e):(i=""===(i=this.getOpt("suffix"))?i:i||" ",e=e.data("item-data"),this.query.el.removeClass("atwho-query").addClass("atwho-inserted").html(t).attr("data-atwho-at-query",""+e["atwho-at"]+this.query.text).attr("contenteditable","false"),(t=this._getRange())&&(this.query.el.length&&t.setEndAfter(this.query.el[0]),t.collapse(!1),t.insertNode(e=this.app.document.createTextNode(""+i)),this._setRange("after",e,t)),this.$inputor.is(":focus")||this.$inputor.focus(),this.$inputor.change())};var p,f,d,g=y;function y(){return y.__super__.constructor.apply(this,arguments)}function v(t){this.context=t,this.at=this.context.at,this.storage=this.context.$inputor}function w(t){this.context=t,this.$el=a('<div class="atwho-view"><ul class="atwho-view-ul"></ul></div>'),this.$elUl=this.$el.children(),this.timeoutID=null,this.context.$el.append(this.$el),this.bindEvent()}v.prototype.destroy=function(){return this.storage.data(this.at,null)},v.prototype.saved=function(){return 0<this.fetch()},v.prototype.query=function(t,e){var i=this.fetch(),n=this.context.getOpt("searchKey");return i=this.context.callbacks("filter").call(this.context,t,i,n)||[],n=this.context.callbacks("remoteFilter"),0<i.length||!n&&0===i.length?e(i):n.call(this.context,t,e)},v.prototype.fetch=function(){return this.storage.data(this.at)||[]},v.prototype.save=function(t){return this.storage.data(this.at,this.context.callbacks("beforeSave").call(this.context,t||[]))},v.prototype.load=function(t){if(!this.saved()&&t)return this._load(t)},v.prototype.reload=function(t){return this._load(t)},v.prototype._load=function(t){return"string"==typeof t?a.ajax(t,{dataType:"json"}).done((e=this,function(t){return e.save(t)})):this.save(t);var e},p=v,w.prototype.init=function(){var t=this.context.getOpt("alias")||this.context.at.charCodeAt(0),e=this.context.getOpt("headerTpl");return e&&1===this.$el.children().length&&this.$el.prepend(e),this.$el.attr({id:"at-view-"+t})},w.prototype.destroy=function(){return this.$el.remove()},w.prototype.bindEvent=function(){var e,i=this.$el.find("ul"),n=0,r=0;i.on("mousemove.atwho-view","li",function(t){if((n!==t.clientX||r!==t.clientY)&&(n=t.clientX,r=t.clientY,!(t=a(t.currentTarget)).hasClass("cur")))return i.find(".cur").removeClass("cur"),t.addClass("cur")}).on("click.atwho-view","li",(e=this,function(t){return i.find(".cur").removeClass("cur"),a(t.currentTarget).addClass("cur"),e.choose(t),t.preventDefault()}))},w.prototype.visible=function(){return a.expr.filters.visible(this.$el[0])},w.prototype.highlighted=function(){return 0<this.$el.find(".cur").length},w.prototype.choose=function(t){var e,i;if((e=this.$el.find(".cur")).length&&(i=this.context.insertContentFor(e),this.context._stopDelayedCall(),this.context.insert(this.context.callbacks("beforeInsert").call(this.context,i,e,t),e),this.context.trigger("inserted",[e,t]),this.hide(t)),this.context.getOpt("hideWithoutSuffix"))return this.stopShowing=!0},w.prototype.reposition=function(t){var e=window!==window.top,i=e?window:window.top,i=a(i);return e&&(e=a(window.frameElement).offset(),t.top+=e.top,t.bottom-=e.top,t.left-=e.left),t.bottom+this.$el.height()-i.scrollTop()>i.height()&&(t.bottom=t.top-this.$el.height()),t.left>(e=i.width()-this.$el.width()-5)&&(t.left=e),i={left:t.left,top:t.bottom},null!=(e=this.context.callbacks("beforeReposition"))&&e.call(this.context,i),this.$el.offset(i),this.context.trigger("reposition",[i])},w.prototype.next=function(){var t=this.$el.find(".cur").removeClass("cur").next();return(t=t.length?t:this.$el.find("li:first")).addClass("cur"),t=(t=t[0]).offsetTop+t.offsetHeight+(t.nextSibling?t.nextSibling.offsetHeight:0),this.scrollTop(Math.max(0,t-this.$el.height()))},w.prototype.prev=function(){var t=this.$el.find(".cur").removeClass("cur").prev();return(t=t.length?t:this.$el.find("li:last")).addClass("cur"),t=(t=t[0]).offsetTop+t.offsetHeight+(t.nextSibling?t.nextSibling.offsetHeight:0),this.scrollTop(Math.max(0,t-this.$el.height()))},w.prototype.scrollTop=function(t){var e=this.context.getOpt("scrollDuration");return e?this.$elUl.animate({scrollTop:t},e):this.$elUl.scrollTop(t)},w.prototype.show=function(){var t;if(!this.stopShowing)return this.visible()||(this.$el.show(),this.$el.scrollTop(0),this.context.trigger("shown")),(t=this.context.rect())?this.reposition(t):void 0;this.stopShowing=!1},w.prototype.hide=function(t,e){var i;if(this.visible())return isNaN(e)?(this.$el.hide(),this.context.trigger("hidden",[t])):(i=this,t=function(){return i.hide()},clearTimeout(this.timeoutID),this.timeoutID=setTimeout(t,e))},w.prototype.render=function(t){var e,i,n,r,o,s;if(a.isArray(t)&&0<t.length){for(this.$el.find("ul").empty(),e=this.$el.find("ul"),s=this.context.getOpt("displayTpl"),i=0,r=t.length;i<r;i++)n=t[i],n=a.extend({},n,{"atwho-at":this.context.at}),o=this.context.callbacks("tplEval").call(this.context,s,n,"onDisplay"),(o=a(this.context.callbacks("highlighter").call(this.context,o,this.context.query.text))).data("item-data",n),e.append(o);return this.show(),this.context.getOpt("highlightFirst")?e.find("li:first").addClass("cur"):void 0}this.hide()},f=w,d={load:function(t,e){if(t=this.controller(t))return t.model.load(e)},isSelecting:function(){var t;return!(null==(t=this.controller())||!t.view.visible())},hide:function(){var t;return null!=(t=this.controller())?t.view.hide():void 0},reposition:function(){var t;if(t=this.controller())return t.view.reposition(t.rect())},setIframe:function(t,e){return this.setupRootElement(t,e),null},run:function(){return this.dispatch()},destroy:function(){return this.shutdown(),this.$inputor.data("atwho",null)}},a.fn.atwho=function(i){var n=arguments,r=null;return this.filter('textarea, input, [contenteditable=""], [contenteditable=true]').each(function(){var t,e;return(e=(t=a(this)).data("atwho"))||t.data("atwho",e=new o(this)),"object"!=typeof i&&i?d[i]&&e?r=d[i].apply(e,Array.prototype.slice.call(n,1)):a.error("Method "+i+" does not exist on jQuery.atwho"):e.reg(i.at,i)}),null!=r?r:this},a.fn.atwho.default={at:void 0,alias:void 0,data:null,displayTpl:"<li>${name}</li>",insertTpl:"${atwho-at}${name}",headerTpl:null,callbacks:i,functionOverrides:{},searchKey:"name",suffix:void 0,hideWithoutSuffix:!1,startWithSpace:!0,acceptSpaceBar:!1,highlightFirst:!0,limit:5,maxLen:20,minLen:0,displayTimeout:300,delay:null,spaceSelectsMatch:!1,tabSelectsMatch:!0,editableAtwhoQueryAttrs:{},scrollDuration:150,suspendOnComposing:!0,lookUpOnClick:!0},a.fn.atwho.debug=!1});