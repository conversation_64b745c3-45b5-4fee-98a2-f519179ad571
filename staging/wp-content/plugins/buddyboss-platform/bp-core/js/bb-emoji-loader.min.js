((b,m)=>{b.wp=b.wp||{},b.wp.bbemoji=new function(){var r,t,n=b.MutationObserver||b.WebKitMutationObserver||b.MozMutationObserver,o=b.document,a=!1,i=0,s=0<b.navigator.userAgent.indexOf("Trident/7.0");function d(){return!o.implementation.hasFeature||o.implementation.hasFeature("http://www.w3.org/TR/SVG11/feature#Image","1.1")}function c(u){f(u)}function l(u){return!!u&&(/[\uDC00-\uDFFF]/.test(u)||/[\u203C\u2049\u20E3\u2122\u2139\u2194-\u2199\u21A9\u21AA\u2300\u231A\u231B\u2328\u2388\u23CF\u23E9-\u23F3\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB-\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u261D\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638\u2639\u263A\u2648-\u2653\u2660\u2663\u2665\u2666\u2668\u267B\u267F\u2692\u2693\u2694\u2696\u2697\u2699\u269B\u269C\u26A0\u26A1\u26AA\u26AB\u26B0\u26B1\u26BD\u26BE\u26C4\u26C5\u26C8\u26CE\u26CF\u26D1\u26D3\u26D4\u26E9\u26EA\u26F0-\u26F5\u26F7-\u26FA\u26FD\u2702\u2705\u2708-\u270D\u270F\u2712\u2714\u2716\u271D\u2721\u2728\u2733\u2734\u2744\u2747\u274C\u274E\u2753\u2754\u2755\u2757\u2763\u2764\u2795\u2796\u2797\u27A1\u27B0\u27BF\u2934\u2935\u2B05\u2B06\u2B07\u2B1B\u2B1C\u2B50\u2B55\u3030\u303D\u3297\u3299]/.test(u))}function f(u,e){var t;return r&&u&&("string"==typeof u||u.childNodes&&u.childNodes.length)?(e=e||{},t={base:d()?m.svgUrl:m.baseUrl,ext:d()?m.svgExt:m.ext,className:e.className||"emoji",callback:function(u,e){switch(u){case"a9":case"ae":case"2122":case"2194":case"2660":case"2663":case"2665":case"2666":return!1}return"".concat(e.base,u,e.ext)},attributes:function(){return{role:"img"}},onerror:function(){r.parentNode&&(this.setAttribute("data-error","load-failed"),r.parentNode.replaceChild(o.createTextNode(r.alt),r))},doNotParse:function(u){return!(!u||!u.className||"string"!=typeof u.className||-1===u.className.indexOf("wp-exclude-emoji"))}},"object"==typeof e.imgAttr&&(t.attributes=function(){return e.imgAttr}),r.parse(u,t)):u}return{load:function u(){if(!a){if(void 0===b.twemoji)return 600<i?void 0:(b.clearTimeout(t),t=b.setTimeout(u,50),void i++);r=b.twemoji,a=!0;var e=o.querySelectorAll("#buddypress, #bbpress-forums");n&&new n(function(u){for(var e,t=u.length;t--;){var r,n=u[t].addedNodes,o=u[t].removedNodes,a=n.length;if("BODY"===u[t].target.tagName)return void(0<(r=u[t].target.querySelectorAll("#buddypress, #bbpress-forums")).length&&r.forEach(c));if(1===a&&1===o.length&&3===n[0].nodeType&&"IMG"===o[0].nodeName&&n[0].data===o[0].alt&&"load-failed"===o[0].getAttribute("data-error"))return;for(;a--;){if(3===(e=n[a]).nodeType){if(!e.parentNode)continue;if(s)for(;e.nextSibling&&3===e.nextSibling.nodeType;)e.nodeValue=e.nodeValue+e.nextSibling.nodeValue,e.parentNode.removeChild(e.nextSibling);e=e.parentNode}l(e.textContent)&&f(e)}}}).observe(o.body,{childList:!0,subtree:!0}),e.forEach(c)}},parse:f,test:l}},document.addEventListener("DOMContentLoaded",function(){b.wp.bbemoji.load()})})(window,window.bbemojiSettings);