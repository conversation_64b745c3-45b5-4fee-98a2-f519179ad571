function my_network_click_handler(){var t=".widget-bb-rl-follow-my-network-widget",o="div.bb-rl-members-item-options a";jQuery(t+" "+o).on("click",function(){var e=this,n=jQuery(e).parents(t),r=(jQuery(this).addClass("loading selected"),jQuery(n).find(o).removeClass("selected"),jQuery(this).data("see-all-link"));return""!==r&&jQuery(n).find(".bb-rl-see-all").attr("href",r),jQuery.post(ajaxurl,{action:"widget_follow_my_network",cookie:encodeURIComponent(document.cookie),_wpnonce:jQuery("input#_wpnonce-follow-my-network").val(),"max-members":jQuery("input#bb_rl_my_network_widget_max").val(),filter:jQuery(this).attr("id")},function(r){jQuery(e).removeClass("loading");var t=jQuery(n).find(".bb-rl-my-network-members-list");void 0!==r.success&&1===r.success?jQuery(t).fadeOut(200,function(){jQuery(t).html(r.data),jQuery(e).find(".bb-rl-widget-tab-count").html(r.count),jQuery(t).fadeIn(200)}):jQuery(t).fadeOut(200,function(){var e=void 0!==r.data?"<p>"+r.data+"</p>":"";jQuery(t).html(e),jQuery(t).fadeIn(200)})}),!1})}jQuery(document).ready(function(){my_network_click_handler()});