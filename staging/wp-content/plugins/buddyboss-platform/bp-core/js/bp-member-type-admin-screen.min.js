void 0===jq&&(jq=jQuery);var jq,l10n=window._bpmtAdminL10n||{},btnChanged=!1;jq(document).ready(function(){new ClipboardJS(".copy-to-clipboard").on("success",function(){var e=jq(".copy-to-clipboard");e.fadeOut(function(){e.text(l10n.copied),e.fadeIn(),btnChanged=!0})}),jq(".copy-to-clipboard").on("click",function(e){e.preventDefault()}),jq(".copy-to-clipboard").on("mouseout",function(){var e;btnChanged&&(e=jq(".copy-to-clipboard")).fadeOut(function(){e.text(l10n.copytoclipboard),e.fadeIn(),btnChanged=!1})}),jq(".post-type-bp-member-type #post").submit(function(){jq("#title").css({border:"none"}),jq(".bp-member-type-label-name").css({border:"none"}),jq(".bp-member-type-singular-name").css({border:"none"});var e=jq("#title").val(),t=jq(".bp-member-type-label-name").val(),n=jq(".bp-member-type-singular-name").val();return 0==e.length&&jq("#title").css({"border-color":"#d54e21","border-width":"1px","border-style":"solid"}),0==t.length&&jq(".bp-member-type-label-name").css({"border-color":"#d54e21","border-width":"1px","border-style":"solid"}),0==n.length&&jq(".bp-member-type-singular-name").css({"border-color":"#d54e21","border-width":"1px","border-style":"solid"}),0!=e.length&&0!=t.length&&0!=n.length}),jq("a.submitdelete").on("click",function(e){var t=jq(this),n="",r=+t.parents("tr").children(".total_users").text();"trash"===t.parent().attr("class")?n=l10n.warnTrash.formatUnicorn({total_users:r}):"delete"===t.parent().attr("class")&&(n=l10n.warnDelete.formatUnicorn({total_users:r})),0<r&&0<n.length&&!window.confirm(n)&&e.preventDefault()}),jq("#doaction, #doaction2").on("click",function(e){var n="",t="";jq('input[name="post[]"]:checked:not(:first-child):not(:last-child)').each(function(){var e=jq(this),t=e.parents("tr");0<+t.children(".total_users").text()&&(n+="\n"+e.prev().text().trim().substr(6).trim())}),"trash"===jq('select[name^="action"]').val()?t=l10n.warnBulkTrash+"\n"+n:"delete"===jq('select[name^="action"]').val()&&(t=l10n.warnBulkDelete+"\n"+n),0<n.length&&0<t.length&&!window.confirm(t)&&e.preventDefault()}),void 0!==jq(".post-type-bp-member-type #title")&&jq(".post-type-bp-member-type #title").attr("tabindex",1),void 0!==jq(".post-type-bp-member-type #publish")&&jq(".post-type-bp-member-type #publish").attr("tabindex",7)}),String.prototype.formatUnicorn||(String.prototype.formatUnicorn=function(){var e=this.toString();if(arguments.length){var t,n=arguments[0];for(t in n)e=e.replace(RegExp("\\{"+t+"\\}","gi"),n[t])}return e});