((s,_)=>{function a(t,e){var a=t.data("livestampdata");"number"==typeof e&&(e*=1e3),t.removeAttr("data-livestamp").removeData("livestamp"),e=_(e),_.isMoment(e)&&!isNaN(+e)&&((a=s.extend({},{original:t.contents()},a)).moment=_(e),t.data("livestampdata",a).empty(),b.push(t[0]))}function t(){n||(v.update(),setTimeout(t,e))}var e=1e3,n=!1,b=s([]),v={update:function(){s("[data-livestamp]").each(function(){var t=s(this);a(t,t.data("livestamp"))});var i=[];b.each(function(){var t,e,a=s(this),n=a.data("livestampdata");void 0===n?i.push(this):_.isMoment(n.moment)&&(t=a.html())!=(n=v.bbHumanizeFormat(n.moment))&&(e=s.Event("change.livestamp"),a.trigger(e,[t,n]),e.isDefaultPrevented()||a.html(n))}),b=b.not(i)},pause:function(){n=!0},resume:function(){n=!1,t()},interval:function(t){if(void 0===t)return e;e=t},bbHumanizeFormat:function(t){var e=_(),t=_(t).utc(),a=_.duration(e.diff(t)).asSeconds(),n="";if(a<0)n=bb_livestamp.unknown_text;else{for(var i=0,s=0,b=0,r=bb_livestamp.chunks.length;b<r&&(s=bb_livestamp.chunks[b],0===(i=Math.floor(a/s)));++b);var o=Math.floor(i);if(void 0===bb_livestamp.chunks[b])n=bb_livestamp.right_now_text;else{switch(s){case parseInt(bb_livestamp.year_in_seconds):n=i<2?bb_livestamp.year_text:v.bbConcateString(i,bb_livestamp.years_text);break;case parseInt(bb_livestamp.year_in_seconds)/6:var m=Math.floor(a/(30*parseInt(bb_livestamp.day_in_seconds))),n=m<2?bb_livestamp.month_text:v.bbConcateString(m,bb_livestamp.months_text);break;case 30*parseInt(bb_livestamp.day_in_seconds):m=Math.floor(a/parseInt(bb_livestamp.week_in_seconds));n=i<2?m<2?bb_livestamp.week_text:v.bbConcateString(m,bb_livestamp.weeks_text):i<2?bb_livestamp.month_text:v.bbConcateString(o,bb_livestamp.months_text);break;case parseInt(bb_livestamp.week_in_seconds):n=i<2?bb_livestamp.week_text:v.bbConcateString(o,bb_livestamp.weeks_text);break;case parseInt(bb_livestamp.day_in_seconds):n=i<2?bb_livestamp.day_text:v.bbConcateString(o,bb_livestamp.days_text);break;case parseInt(bb_livestamp.hour_in_seconds):n=i<2?bb_livestamp.hour_text:v.bbConcateString(o,bb_livestamp.hours_text);break;case parseInt(bb_livestamp.minute_in_seconds):n=i<2?bb_livestamp.minute_text:v.bbConcateString(o,bb_livestamp.minutes_text);break;default:n=bb_livestamp.right_now_text}parseInt(i)||(n=bb_livestamp.right_now_text)}}return n=n!==bb_livestamp.right_now_text?bb_livestamp.ago_text.replace("%s",n):n},bbConcateString:function(t,e){return t+" "+e}},i={add:function(t,e){return"number"==typeof e&&(e*=1e3),e=_(e),_.isMoment(e)&&!isNaN(+e)&&(t.each(function(){a(s(this),e)}),v.update()),t},destroy:function(a){return b=b.not(a),a.each(function(){var t=s(this),e=t.data("livestampdata");if(void 0===e)return a;t.html(e.original||"").removeData("livestampdata")}),a},isLivestamp:function(t){return void 0!==t.data("livestampdata")}};s.livestamp=v,s(function(){v.resume()}),s.fn.livestamp=function(t,e){return i[t]||(e=t,t="add"),i[t](this,e)}})(jQuery,moment);