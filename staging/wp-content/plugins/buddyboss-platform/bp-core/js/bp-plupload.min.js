window.wp=window.wp||{},window.bp=window.bp||{},(d=>{"undefined"!=typeof BP_Uploader&&(_.extend(bp,_.pick(wp,"Backbone","ajax","template")),bp.Models=bp.Models||{},bp.Collections=bp.Collections||{},bp.Views=bp.Views||{},bp.Uploader={},bp.Uploader.uploader=function(){var n=this,e=-1!==navigator.userAgent.indexOf("Trident/")||-1!==navigator.userAgent.indexOf("MSIE ");this.params=BP_Uploader.settings,this.strings=BP_Uploader.strings,this.supports={upload:this.params.browser.supported},this.supported=this.supports.upload,this.supported?(e||"flash"!==plupload.predictRuntime(this.params.defaults)||this.params.defaults.required_features&&this.params.defaults.required_features.hasOwnProperty("send_binary_string")||(this.params.defaults.required_features=this.params.defaults.required_features||{},this.params.defaults.required_features.send_binary_string=!0),this.uploader=new plupload.Uploader(this.params.defaults),this.uploader.bind("Init",function(e){var i=d("#"+n.params.defaults.container),s=d("#"+n.params.defaults.drop_element);"html4"===e.runtime&&(e.settings.multipart_params.html4=!0),"bp_avatar_upload"===e.settings.multipart_params.action&&(e.settings.multipart_params.bp_params.ui_available_width=i.width()),e.features.dragdrop&&!n.params.browser.mobile?(i.addClass("drag-drop"),s.bind("dragover.wp-uploader",function(){i.addClass("drag-over")}).bind("dragleave.wp-uploader, drop.wp-uploader",function(){i.removeClass("drag-over")})):(i.removeClass("drag-drop"),s.unbind(".wp-uploader"))}),this.uploader.bind("postinit",function(e){e.refresh()}),this.uploader.init(),this.feedback=function(e,i,s){!_.isNull(s)&&s.item&&s.item.clear(),bp.Uploader.filesError.unshift({message:e,data:i,file:s})},this.uploader.bind("FilesAdded",function(s,e){var t=104857600,a=parseInt(s.settings.max_file_size,10),r=this;if(!s.settings.multi_selection&&1<e.length){for(var i in e)s.removeFile(e[i]);d(n).trigger("bp-uploader-warning",n.strings.unique_file_warning)}else s.settings.multi_selection||d(n).trigger("bp-uploader-hide"),_.each(e,function(e){var i;plupload.FAILED===e.status?s.settings.multi_selection||d(n).trigger("bp-uploader-show"):t<a&&e.size>t&&"html5"!==s.runtime?(s.settings.multi_selection||d(n).trigger("bp-uploader-show"),r.uploadSizeError(s,e,!0)):(i=_.extend({id:e.id,file:e,uploading:!0,date:new Date,filename:e.name},_.pick(e,"loaded","size","percent")),e.item=new bp.Models.File(i),bp.Uploader.filesQueue.add(e.item))}),s.refresh(),s.start()}),this.uploader.bind("UploadProgress",function(e,i){i.item.set(_.pick(i,"loaded","percent"))}),this.uploader.bind("FileUploaded",function(e,i,s){var t=n.strings.default_error;try{s=JSON.parse(s.response)}catch(e){return n.feedback(t,e,i)}return!_.isObject(s)||_.isUndefined(s.success)?n.feedback(t,null,i):s.success?(_.each(["file","loaded","size","percent"],function(e){i.item.unset(e)}),i.item.set(_.extend(s.data,{uploading:!1})),bp.Uploader.filesUploaded.add(i.item),void(e.settings.multi_selection||d(n).trigger("bp-uploader-show"))):(s.data&&s.data.message&&(t=s.data.message),n.feedback(t,s.data,i))}),this.uploader.bind("BeforeUpload",function(e,i){d(n).trigger("bp-uploader-new-upload",e,i)}),this.uploader.bind("UploadComplete",function(e,i){d(n).trigger("bp-uploader-upload-complete",e,i),bp.Uploader.filesQueue.reset()}),this.uploader.bind("Error",function(e,i){var s,t=n.params&&n.params.defaults&&n.params.defaults.filters&&n.params.defaults.filters.max_file_size?n.params.defaults.filters.max_file_size:"5120000b",a=(t=((t=t.replace("b",""))/1e6).toFixed(0),t+="MB",n.strings.default_error),r={FAILED:n.strings.upload_failed,FILE_EXTENSION_ERROR:n.strings.invalid_filetype,IMAGE_FORMAT_ERROR:n.strings.not_an_image,IMAGE_MEMORY_ERROR:n.strings.image_memory_exceeded,IMAGE_DIMENSIONS_ERROR:n.strings.image_dimensions_exceeded,GENERIC_ERROR:n.strings.upload_failed,IO_ERROR:n.strings.io_error,HTTP_ERROR:n.strings.http_error,SECURITY_ERROR:n.strings.security_error,FILE_SIZE_ERROR:n.strings.file_exceeds_size_limit.replace("%1$s",d("<span />").text(i.file.name).html()).replace("%2$s",t)};for(s in r)if(i.code===plupload[s]){a=r[s];break}e.settings.multi_selection||d(n).trigger("bp-uploader-show"),d(n).trigger("bp-uploader-warning",a),e.refresh()})):BP_Uploader=void 0},bp.Models.File=Backbone.Model.extend({file:{}}),d.extend(bp.Uploader,{filesQueue:new Backbone.Collection,filesUploaded:new Backbone.Collection,filesError:new Backbone.Collection}),bp.View=bp.Backbone.View.extend({inject:function(e){this.render(),d(e).html(this.el),this.views.ready()},prepare:function(){return!_.isUndefined(this.model)&&_.isFunction(this.model.toJSON)?this.model.toJSON():{}}}),BP_Uploader.settings.defaults.filters=_.extend(BP_Uploader.settings.defaults.filters,{mime_types:[{title:"Image files",extensions:"jpg,jpeg,png,gif"}]}),bp.Views.Uploader=bp.View.extend({className:"bp-uploader-window",template:bp.template("upload-window"),defaults:_.pick(BP_Uploader.settings.defaults,"container","drop_element","browse_button"),initialize:function(){this.warnings=[],this.model=new Backbone.Model(this.defaults),this.on("ready",this.initUploader)},initUploader:function(){this.uploader=new bp.Uploader.uploader,d(this.uploader).on("bp-uploader-warning",_.bind(this.setWarning,this)),d(this.uploader).on("bp-uploader-new-upload",_.bind(this.resetWarning,this)),d(this.uploader).on("bp-uploader-hide",_.bind(this.hideUploader,this)),d(this.uploader).on("bp-uploader-show",_.bind(this.showUploader,this))},setWarning:function(e,i){_.isUndefined(i)||(i=new bp.Views.uploaderWarning({value:i}).render(),this.warnings.push(i),this.$el.after(i.el))},resetWarning:function(){0!==this.warnings.length&&(_.each(this.warnings,function(e){e.remove()}),this.warnings=[])},hideUploader:function(){!_.isUndefined(this.views.view)&&!_.isUndefined(this.views.view.$el)&&0<this.views.view.$el.length&&this.views.view.$el.hide()},showUploader:function(){!_.isUndefined(this.views.view)&&!_.isUndefined(this.views.view.$el)&&0<this.views.view.$el.length&&this.views.view.$el.show()}}),bp.Views.uploaderWarning=bp.View.extend({tagName:"p",className:"warning",initialize:function(){this.value=this.options.value},render:function(){return this.$el.html(this.value),this}}),bp.Views.uploaderStatus=bp.View.extend({className:"files",initialize:function(){_.each(this.collection.models,this.addFile,this),this.collection.on("change:percent",this.progress,this),bp.Uploader.filesError.on("add",this.feedback,this)},addFile:function(e){this.views.add(new bp.Views.uploaderProgress({model:e}))},progress:function(e){_.isUndefined(e.get("percent"))||d("#"+e.get("id")+" .bp-progress .bp-bar").css("width",e.get("percent")+"%")},feedback:function(e){BP_Uploader.settings.multi_selection||_.isUndefined(e.get("message"))||_.isUndefined(e.get("file"))||d("#"+e.get("file").id).parents(".files").parent().parent().find(".bp-uploader-window").show(),_.isUndefined(e.get("message"))||_.isUndefined(e.get("file"))||d("#"+e.get("file").id).html(e.get("message")).addClass("error")}}),bp.Views.uploaderProgress=bp.View.extend({className:"bp-uploader-progress",template:bp.template("progress-window")}))})((bp,jQuery));