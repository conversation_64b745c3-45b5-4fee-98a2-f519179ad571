window.bp=window.bp||{},bp,jQ<PERSON>y,"undefined"!=typeof BP_Uploader&&(bp.Models=bp.Models||{},bp.Collections=bp.Collections||{},bp.Views=bp.Views||{},bp.WebCam={start:function(){this.params={video:null,videoStream:null,capture_enable:!1,capture:null,canvas:null,warning:null,flipped:!1},bp.Avatar.nav.on("bp-avatar-view:changed",_.bind(this.setView,this))},setView:function(e){"camera"!==e?_.isNull(this.params.video)||(this.stop(),this.removeWarning()):(e=new bp.Views.WebCamAvatar({model:new Backbone.Model({user_media:!1})}),this.params.flipped=!1,bp.Avatar.views.add({id:"camera",view:e}),e.inject(".bp-avatar"))},removeView:function(){var e;_.isUndefined(bp.Avatar.views.get("camera"))||((e=bp.Avatar.views.get("camera")).get("view").remove(),bp.Avatar.views.remove({id:"camera",view:e}))},gotStream:function(e){var a=bp.WebCam.params.video;bp.WebCam.params.videoStream=e,bp.WebCam.displayWarning("loaded"),a.onerror=function(){bp.WebCam.displayWarning("videoerror"),a&&bp.WebCam.stop()},e.onended=bp.WebCam.noStream(),"srcObject"in a?a.srcObject=e:a.src=window.URL.createObjectURL(e),a.onloadedmetadata=function(){a.play()},bp.WebCam.params.capture_enable=!0},stop:function(){bp.WebCam.params.capture_enable=!1,bp.WebCam.params.videoStream&&(bp.WebCam.params.videoStream.stop?bp.WebCam.params.videoStream.stop():bp.WebCam.params.videoStream.msStop&&bp.WebCam.params.videoStream.msStop(),bp.WebCam.params.videoStream.onended=null,bp.WebCam.params.videoStream=null),bp.WebCam.params.video&&(bp.WebCam.params.video.onerror=null,bp.WebCam.params.video.pause(),bp.WebCam.params.video.mozSrcObject&&(bp.WebCam.params.video.mozSrcObject=null),bp.WebCam.params.video.src="")},noStream:function(){_.isNull(bp.WebCam.params.videoStream)&&(bp.WebCam.displayWarning("noaccess"),bp.WebCam.removeView())},setAvatar:function(e){e.get("url")||bp.WebCam.displayWarning("nocapture"),bp.WebCam.removeView(),bp.Avatar.setAvatar(e)},removeWarning:function(){_.isNull(this.params.warning)||this.params.warning.remove()},displayWarning:function(e){this.removeWarning(),this.params.warning=new bp.Views.uploaderWarning({value:BP_Uploader.strings.camera_warnings[e]}),this.params.warning.inject(".bp-avatar-status")}},bp.Views.WebCamAvatar=bp.View.extend({tagName:"div",id:"bp-webcam-avatar",template:bp.template("bp-avatar-webcam"),events:{"click .avatar-webcam-capture":"captureStream","click .avatar-webcam-save":"saveCapture"},initialize:function(){var e;navigator.getUserMedia=navigator.getUserMedia||navigator.webkitGetUserMedia||navigator.mozGetUserMedia||navigator.msGetUserMedia||navigator.oGetUserMedia||navigator.mediaDevices.getUserMedia,void 0!==navigator.getUserMedia&&(e=_.extend(_.pick(BP_Uploader.settings.defaults.multipart_params.bp_params,"object","item_id","nonces"),{user_media:!0,w:BP_Uploader.settings.crop.full_w,h:BP_Uploader.settings.crop.full_h,x:0,y:0,type:"camera"}),this.model.set(e)),this.on("ready",this.useStream,this)},useStream:function(){this.model.get("user_media")&&(this.options.video=new bp.Views.WebCamVideo,this.options.canvas=new bp.Views.WebCamCanvas,this.$el.find("#avatar-to-crop").append(this.options.video.el),this.$el.find("#avatar-crop-pane").append(this.options.canvas.el),bp.WebCam.params.video=this.options.video.el,bp.WebCam.params.canvas=this.options.canvas.el,bp.WebCam.displayWarning("requesting"),void 0===navigator.mediaDevices.getUserMedia?navigator.getUserMedia({audio:!1,video:!0},bp.WebCam.gotStream,bp.WebCam.noStream):navigator.mediaDevices.getUserMedia({audio:!1,video:!0}).then(bp.WebCam.gotStream,bp.WebCam.noStream).catch(function(){bp.WebCam.displayWarning("errormsg")}))},captureStream:function(e){var a;e.preventDefault(),bp.WebCam.params.capture_enable?this.model.get("h")>this.options.video.el.videoHeight||this.model.get("w")>this.options.video.el.videoWidth?bp.WebCam.displayWarning("videoerror"):(e=this.options.video.el.videoHeight,a=(this.options.video.el.videoWidth-e)/2,bp.WebCam.params.flipped||(this.options.canvas.el.getContext("2d").translate(this.model.get("w"),0),this.options.canvas.el.getContext("2d").scale(-1,1),bp.WebCam.params.flipped=!0),this.options.canvas.el.getContext("2d").drawImage(this.options.video.el,a,0,e,e,0,0,this.model.get("w"),this.model.get("h")),bp.WebCam.params.capture=this.options.canvas.el.toDataURL("image/png"),this.model.set("url",bp.WebCam.params.capture),bp.WebCam.displayWarning("ready")):bp.WebCam.displayWarning("loading")},saveCapture:function(e){e.preventDefault(),bp.WebCam.params.capture?(bp.WebCam.stop(),bp.WebCam.setAvatar(this.model)):bp.WebCam.displayWarning("nocapture")}}),bp.Views.WebCamVideo=bp.View.extend({tagName:"video",id:"bp-webcam-video",attributes:{autoplay:"autoplay"}}),bp.Views.WebCamCanvas=bp.View.extend({tagName:"canvas",id:"bp-webcam-canvas",attributes:{width:150,height:150},initialize:function(){_.isUndefined(BP_Uploader.settings.crop.full_h)||_.isUndefined(BP_Uploader.settings.crop.full_w)||(this.el.attributes.width.value=BP_Uploader.settings.crop.full_w,this.el.attributes.height.value=BP_Uploader.settings.crop.full_h)}}),bp.WebCam.start());