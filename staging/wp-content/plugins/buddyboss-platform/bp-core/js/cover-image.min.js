window.bp=window.bp||{},(s=>{"undefined"!=typeof BP_Uploader&&(bp.Models=bp.Models||{},bp.Collections=bp.Collections||{},bp.Views=bp.Views||{},bp.CoverImage={start:function(){this.views=new Backbone.Collection,this.warning=null,this.Attachment=new Backbone.Model,this.uploaderView(),this.displayWarning(BP_Uploader.strings.cover_image_warnings.dimensions),!0===BP_Uploader.settings.defaults.multipart_params.bp_params.has_cover_image&&this.deleteView()},uploaderView:function(){bp.Uploader.filesQueue.on("add",this.uploadProgress,this);var e=new bp.Views.Uploader;this.views.add({id:"upload",view:e}),e.inject(".bp-cover-image")},uploadProgress:function(){var e=new bp.Views.coverImageUploadProgress({collection:bp.Uploader.filesQueue});_.isUndefined(this.views.get("status"))?this.views.add({id:"status",view:e}):this.views.set({id:"status",view:e}),e.inject(".bp-cover-image-status")},deleteView:function(){var e=new Backbone.Model(_.pick(BP_Uploader.settings.defaults.multipart_params.bp_params,["object","item_id","nonces"]));_.isUndefined(this.views.get("delete"))&&(e=new bp.Views.DeleteCoverImage({model:e}),this.views.add({id:"delete",view:e}),e.inject(".bp-cover-image-manage"))},deleteCoverImage:function(a){var e,t=this;_.isUndefined(this.views.get("delete"))||((e=this.views.get("delete")).get("view").remove(),this.views.remove({id:"delete",view:e})),bp.ajax.post("bp_cover_image_delete",{json:!0,item_id:a.get("item_id"),object:a.get("object"),nonce:a.get("nonces").remove}).done(function(e){var i=new bp.Views.CoverImageStatus({value:BP_Uploader.strings.feedback_messages[e.feedback_code],type:"success"});t.views.add({id:"status",view:i}),i.inject(".bp-cover-image-status"),""===e.reset_url?(s("#header-cover-image").css({"background-image":"none"}),s(".group-create #header-cover-image").css({display:"none"})):s("#header-cover-image").css({"background-image":"url( "+e.reset_url+" )"}),s("#header-cover-image").removeClass("has-default").addClass(BP_Uploader.settings.defaults.multipart_params.bp_params.has_default_class),s("#header-cover-image .header-cover-img").remove(),s("#header-cover-image  .position-change-cover-image").remove(),BP_Uploader.settings.defaults.multipart_params.bp_params.has_cover_image=!1,t.Attachment.set(_.extend(_.pick(a.attributes,["object","item_id"]),{url:e.reset_url,action:"deleted"}))}).fail(function(e){var i=BP_Uploader.strings.default_error,e=(_.isUndefined(e)||(i=BP_Uploader.strings.feedback_messages[e.feedback_code]),new bp.Views.CoverImageStatus({value:i,type:"error"}));t.views.add({id:"status",view:e}),e.inject(".bp-cover-image-status"),bp.CoverImage.deleteView()})},removeWarning:function(){_.isNull(this.warning)||this.warning.remove()},displayWarning:function(e){this.removeWarning(),this.warning=new bp.Views.uploaderWarning({value:e}),this.warning.inject(".bp-cover-image-status")}},bp.Views.coverImageUploadProgress=bp.Views.uploaderStatus.extend({className:"files",initialize:function(){bp.Views.uploaderStatus.prototype.initialize.apply(this,arguments),this.collection.on("change:url",this.uploadResult,this)},uploadResult:function(e){var i;_.isUndefined(e.get("url"))||(s(".bp-cover-image-status > .warning").remove(),i=BP_Uploader.strings.feedback_messages[1],this.views.set(".bp-uploader-progress",new bp.Views.CoverImageStatus({value:i,type:"success"})),0===e.get("feedback_code")&&s(".bp-cover-image-status").append('<p class="warning">'+BP_Uploader.strings.cover_image_warnings.dimensions+"</p>"),s("#header-cover-image .header-cover-img").length?s("#header-cover-image .header-cover-img").prop("src",e.get("url")):s("#header-cover-image").prepend('<img src="'+e.get("url")+'" class="header-cover-img" />'),s("#header-cover-image .header-cover-reposition-wrap .guillotine-window img").length&&(i=s("#header-cover-image .header-cover-reposition-wrap .guillotine-window img"),s("#header-cover-image .header-cover-reposition-wrap .guillotine-window").remove(),s("#header-cover-image .header-cover-reposition-wrap").append(i)),s("#header-cover-image .header-cover-reposition-wrap img").prop("src",e.get("url")),s("#header-cover-image").removeClass("has-position").find(".header-cover-img").removeAttr("data-top").removeAttr("style"),bp.CoverImage.deleteView(),s(".group-create #header-cover-image").css({"background-image":"url( "+e.get("url")+" )",display:"block"}),bp.CoverImage.Attachment.set(_.extend(_.pick(BP_Uploader.settings.defaults.multipart_params.bp_params,["object","item_id"]),{url:e.get("url"),action:"uploaded"})))}}),bp.Views.CoverImageStatus=bp.View.extend({tagName:"p",className:"updated",id:"bp-cover-image-feedback",initialize:function(){this.el.className+=" "+this.options.type,this.value=this.options.value},render:function(){return this.$el.html(this.value),this}}),bp.Views.DeleteCoverImage=bp.View.extend({tagName:"div",id:"bp-delete-cover-image-container",template:bp.template("bp-cover-image-delete"),events:{"click #bp-delete-cover-image":"deleteCoverImage"},deleteCoverImage:function(e){e.preventDefault(),bp.CoverImage.deleteCoverImage(this.model)}}),bp.CoverImage.start())})((bp,jQuery));