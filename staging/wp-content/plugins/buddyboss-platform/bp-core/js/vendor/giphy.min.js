document,window.Giphy=function(e){var r=this;r.key=e,r.version=1,r.url="https://api.giphy.com/v"+r.version+"/",r.stickers={},r.search=function(e,s,t){var i="gifs/search";if("q"in e)i+="?q="+e.q,delete e.q;else{var n="giphy.js: No query.";t?t(n):console.error(n)}return r.request(i,e,s,t)},r.gif=function(e,s,t){var i="gifs/";if("id"in e)i+=e.id,delete e.id;else{var n="giphy.js: No ID.";t?t(n):console.error(n)}r.request(i,e,s,t)},r.gifs=function(e,s,t){var i="gifs?ids=";if("ids"in e)e.ids.forEach((function(r,s){i+=r,s+1!=e.ids.length&&(i+=",")})),delete e.ids;else{var n="giphy.js: No IDs.";t?t(n):console.error(n)}r.request(i,e,s,t)},r.translate=function(e,s,t){var i="gifs/translate";if("s"in e)i+="?s="+e.s.replace(" ","+"),delete e.s;else{var n="giphy.js: No query.";t?t(n):console.error(n)}r.request(i,e,s,t)},r.random=function(e,s,t){var i="gifs/random";"tag"in e&&(i+="?tag="+e.tag,delete e.tag),r.request(i,e,s,t)},r.trending=function(e,s,t){return r.request("gifs/trending",e,s,t)},r.stickers.search=function(e,s,t){var i="stickers/search";if("q"in e)i+="?q="+e.q,delete e.q;else{var n="giphy.js: No query.";t?t(n):console.error(n)}r.request(i,e,s,t)},r.stickers.roulette=function(e,s,t){var i="stickers/roulette";if("tag"in e)i+="?tag="+e.tag,delete e.tag;else{var n="giphy.js: No query.";t?t(n):console.error(n)}r.request(i,e,s,t)},r.stickers.trending=function(e,s,t){var i="stickers/trending";if("s"in e)i+="?s="+e.s,delete e.s;else{var n="giphy.js: No query.";t?t(n):console.error(n)}r.request(i,e,s,t)},r.stickers.translate=function(e,s,t){var i="stickers/translate";if("s"in e)i+="?s="+e.s,delete e.s;else{var n="giphy.js: No query.";t?t(n):console.error(n)}r.request(i,e,s,t)},r.request=function(e,r,s,t){var i=this,n=i.url,o=!1;for(var a in(n+=e).indexOf("?")>-1&&(o=!0),r)o?n+="&"+a+"="+r[a]:(n+="?"+a+"="+r[a],o=!0);n+=o?"&api_key="+i.key:"?api_key="+i.key;var u=new XMLHttpRequest;return u.open("GET",n,!0),u.responseType="json",u.onload=function(){var e=u.status;200==e?s&&s(u.response):t?t(e):console.log(e)},u.onerror=function(){var e=u.status;t&&t(e)},u.send(),u}};