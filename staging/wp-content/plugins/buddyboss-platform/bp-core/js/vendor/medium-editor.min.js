"classList"in document.createElement("_")||function(e){"use strict";if("Element"in e){var t="classList",n="prototype",i=e.Element[n],o=Object,s=String[n].trim||function(){return this.replace(/^\s+|\s+$/g,"")},r=Array[n].indexOf||function(e){for(var t=0,n=this.length;t<n;t++)if(t in this&&this[t]===e)return t;return-1},a=function(e,t){this.name=e,this.code=DOMException[e],this.message=t},l=function(e,t){if(""===t)throw new a("SYNTAX_ERR","An invalid or illegal string was specified");if(/\s/.test(t))throw new a("INVALID_CHARACTER_ERR","String contains an invalid character");return r.call(e,t)},c=function(e){for(var t=s.call(e.getAttribute("class")||""),n=t?t.split(/\s+/):[],i=0,o=n.length;i<o;i++)this.push(n[i]);this._updateClassName=function(){e.setAttribute("class",this.toString())}},d=c[n]=[],e=function(){return new c(this)};if(a[n]=Error[n],d.item=function(e){return this[e]||null},d.contains=function(e){return-1!==l(this,e+="")},d.add=function(){for(var e,t=arguments,n=0,i=t.length,o=!1;-1===l(this,e=t[n]+"")&&(this.push(e),o=!0),++n<i;);o&&this._updateClassName()},d.remove=function(){var e,t,n=arguments,i=0,o=n.length,s=!1;do{for(t=l(this,e=n[i]+"");-1!==t;)this.splice(t,1),s=!0,t=l(this,e)}while(++i<o);s&&this._updateClassName()},d.toggle=function(e,t){var n=this.contains(e+=""),i=n?!0!==t&&"remove":!1!==t&&"add";return i&&this[i](e),!0===t||!1===t?t:!n},d.toString=function(){return this.join(" ")},o.defineProperty){d={get:e,enumerable:!0,configurable:!0};try{o.defineProperty(i,t,d)}catch(e){-2146823252===e.number&&(d.enumerable=!1,o.defineProperty(i,t,d))}}else o[n].__defineGetter__&&i.__defineGetter__(t,e)}}(self),function(e){"use strict";if(e.URL=e.URL||e.webkitURL,e.Blob&&e.URL)try{return new Blob}catch(e){}var s=e.BlobBuilder||e.WebKitBlobBuilder||e.MozBlobBuilder||function(e){function a(e){return Object.prototype.toString.call(e).match(/^\[object\s(.*)\]$/)[1]}function t(){this.data=[]}function l(e,t,n){this.data=e,this.size=e.length,this.type=t,this.encoding=n}function c(e){this.code=this[this.name=e]}var n=t.prototype,i=l.prototype,d=e.FileReaderSync,o="NOT_FOUND_ERR SECURITY_ERR ABORT_ERR NOT_READABLE_ERR ENCODING_ERR NO_MODIFICATION_ALLOWED_ERR INVALID_STATE_ERR SYNTAX_ERR".split(" "),s=o.length,r=e.URL||e.webkitURL||e,u=r.createObjectURL,h=r.revokeObjectURL,m=r,f=e.btoa,p=e.atob,g=e.ArrayBuffer,b=e.Uint8Array,v=/^[\w-]+:\/*\[?[\w\.:-]+\]?(?::[0-9]+)?/;for(l.fake=i.fake=!0;s--;)c.prototype[o[s]]=s+1;return(m=!r.createObjectURL?e.URL=function(e){var t=document.createElementNS("http://www.w3.org/1999/xhtml","a");return t.href=e,"origin"in t||("data:"===t.protocol.toLowerCase()?t.origin=null:(e=e.match(v),t.origin=e&&e[1])),t}:m).createObjectURL=function(e){var t=e.type;return null===t&&(t="application/octet-stream"),e instanceof l?(t="data:"+t,"base64"===e.encoding?t+";base64,"+e.data:"URI"===e.encoding?t+","+decodeURIComponent(e.data):f?t+";base64,"+f(e.data):t+","+encodeURIComponent(e.data)):u?u.call(r,e):void 0},m.revokeObjectURL=function(e){"data:"!==e.substring(0,5)&&h&&h.call(r,e)},n.append=function(e){var t=this.data;if(b&&(e instanceof g||e instanceof b)){for(var n="",i=new b(e),o=0,s=i.length;o<s;o++)n+=String.fromCharCode(i[o]);t.push(n)}else if("Blob"===a(e)||"File"===a(e)){if(!d)throw new c("NOT_READABLE_ERR");var r=new d;t.push(r.readAsBinaryString(e))}else e instanceof l?"base64"===e.encoding&&p?t.push(p(e.data)):"URI"===e.encoding?t.push(decodeURIComponent(e.data)):"raw"===e.encoding&&t.push(e.data):("string"!=typeof e&&(e+=""),t.push(unescape(encodeURIComponent(e))))},n.getBlob=function(e){return arguments.length||(e=null),new l(this.data.join(""),e,"raw")},n.toString=function(){return"[object BlobBuilder]"},i.slice=function(e,t,n){var i=arguments.length;return i<3&&(n=null),new l(this.data.slice(e,1<i?t:this.data.length),n,this.encoding)},i.toString=function(){return"[object Blob]"},i.close=function(){this.size=0,delete this.data},t}(e);e.Blob=function(e,t){var t=t&&t.type||"",n=new s;if(e)for(var i=0,o=e.length;i<o;i++)Uint8Array&&e[i]instanceof Uint8Array?n.append(e[i].buffer):n.append(e[i]);t=n.getBlob(t);return!t.slice&&t.webkitSlice&&(t.slice=t.webkitSlice),t};var t=Object.getPrototypeOf||function(e){return e.__proto__};e.Blob.prototype=t(new e.Blob)}("undefined"!=typeof self&&self||"undefined"!=typeof window&&window||this.content||this),function(e,t){"use strict";"object"==typeof module&&"undefined"!=typeof process&&process&&process.versions&&process.versions.electron||"object"!=typeof module?"function"==typeof define&&define.amd?define(function(){return t}):e.MediumEditor=t:module.exports=t}(this,function(){"use strict";function b(e,t){return this.init(e,t)}function t(e){b.util.extend(this,e)}function c(e){return b.util.isBlockContainer(e)?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}function s(e,t){return e&&e.some(function(e){if("function"!=typeof e.getInteractionElements)return!1;e=e.getInteractionElements();return!!e&&(e=!Array.isArray(e)?[e]:e).some(function(e){return b.util.isDescendant(e,t,!0)})})}function e(e){this.base=e,this.options=this.base.options,this.events=[],this.disabledEvents={},this.customEvents={},this.listeners={}}var n,r,a,l,i,o,d,u,h,m;function f(e){e=b.util.getContainerEditorElement(e);Array.prototype.slice.call(e.parentElement.querySelectorAll("."+i)).forEach(function(e){e.classList.remove(i)})}function p(e){e.stopPropagation()}function g(e,t,n){var i=e.clipboardData||t.clipboardData||n.dataTransfer,o={};if(!i)return o;if(!i.getData||(n=i.getData("Text"))&&0<n.length&&(o["text/plain"]=n),i.types)for(var s=0;s<i.types.length;s++){var r=i.types[s];o[r]=i.getData(r)}return o}function v(e){return e&&e.clipboardData&&e.clipboardData.items&&e.clipboardData.types.includes("Files")}function E(e,t){this.options.disableReturn||t.getAttribute("data-disable-return")?e.preventDefault():!this.options.disableDoubleReturn&&!t.getAttribute("data-disable-double-return")||((t=b.selection.getSelectionStart(this.options.ownerDocument))&&""===t.textContent.trim()&&"li"!==t.nodeName.toLowerCase()||t.previousElementSibling&&"br"!==t.previousElementSibling.nodeName.toLowerCase()&&""===t.previousElementSibling.textContent.trim())&&e.preventDefault()}function C(e){var t,n,i=b.selection.getSelectionStart(this.options.ownerDocument),o=i.nodeName.toLowerCase(),s=/^(\s+|<br\/?>)?$/i,r=/h\d/i;b.util.isKey(e,[b.util.keyCode.BACKSPACE,b.util.keyCode.ENTER])&&i.previousElementSibling&&r.test(o)&&0===b.selection.getCaretOffsets(i).left?b.util.isKey(e,b.util.keyCode.BACKSPACE)&&s.test(i.previousElementSibling.innerHTML)?(i.previousElementSibling.parentNode.removeChild(i.previousElementSibling),e.preventDefault()):!this.options.disableDoubleReturn&&b.util.isKey(e,b.util.keyCode.ENTER)&&((t=this.options.ownerDocument.createElement("p")).innerHTML="<br>",i.previousElementSibling.parentNode.insertBefore(t,i),e.preventDefault()):b.util.isKey(e,b.util.keyCode.DELETE)&&i.nextElementSibling&&i.previousElementSibling&&!r.test(o)&&s.test(i.innerHTML)&&r.test(i.nextElementSibling.nodeName.toLowerCase())?(b.selection.moveCursor(this.options.ownerDocument,i.nextElementSibling),i.previousElementSibling.parentNode.removeChild(i),e.preventDefault()):b.util.isKey(e,b.util.keyCode.BACKSPACE)&&"li"===o&&s.test(i.innerHTML)&&!i.previousElementSibling&&!i.parentElement.previousElementSibling&&i.nextElementSibling&&"li"===i.nextElementSibling.nodeName.toLowerCase()?((t=this.options.ownerDocument.createElement("p")).innerHTML="<br>",i.parentElement.parentElement.insertBefore(t,i.parentElement),b.selection.moveCursor(this.options.ownerDocument,t),i.parentElement.removeChild(i),e.preventDefault()):b.util.isKey(e,b.util.keyCode.BACKSPACE)&&!1!==b.util.getClosestTag(i,"blockquote")&&0===b.selection.getCaretOffsets(i).left?(e.preventDefault(),b.util.execFormatBlock(this.options.ownerDocument,"p")):b.util.isKey(e,b.util.keyCode.ENTER)&&!1!==b.util.getClosestTag(i,"blockquote")&&0===b.selection.getCaretOffsets(i).right?((t=this.options.ownerDocument.createElement("p")).innerHTML="<br>",i.parentElement.insertBefore(t,i.nextSibling),b.selection.moveCursor(this.options.ownerDocument,t),e.preventDefault()):b.util.isKey(e,b.util.keyCode.BACKSPACE)&&!1!==b.util.getClosestTag(i,"pre")&&0===b.selection.getCaretOffsets(i).left?(e.preventDefault(),b.util.execFormatBlock(this.options.ownerDocument,"p")):b.util.isKey(e,b.util.keyCode.ENTER)&&!1!==b.util.getClosestTag(i,"pre")&&0===b.selection.getCaretOffsets(i).right?((t=this.options.ownerDocument.createElement("p")).innerHTML="<br>",i.parentElement.insertBefore(t,i.nextSibling),b.selection.moveCursor(this.options.ownerDocument,t),e.preventDefault()):b.util.isKey(e,b.util.keyCode.ENTER)&&!1!==b.util.getClosestTag(i,"li")&&0===b.selection.getCaretOffsets(i).left?(e.preventDefault(),s.test(i.innerHTML)?"ul"==i.parentNode.parentNode.parentNode.tagName.toLowerCase()||"ol"==i.parentNode.parentNode.parentNode.tagName.toLowerCase()?(o=this.options.ownerDocument.createElement("li"),o=i.parentNode.parentNode.parentNode.insertBefore(o,i.parentNode.parentNode.nextSibling),i.remove(),b.selection.moveCursor(this.options.ownerDocument,o)):((t=this.options.ownerDocument.createElement("p")).innerHTML="<br>",n=i.parentNode.nextSibling?i.parentNode.parentNode.insertBefore(t,i.parentNode.nextSibling):i.parentNode.parentNode.appendChild(t),i.remove(),b.selection.moveCursor(this.options.ownerDocument,n)):!i.previousElementSibling&&i.nextElementSibling&&"li"!==i.parentNode.parentNode.tagName.toLowerCase()&&((t=this.options.ownerDocument.createElement("p")).innerHTML="<br>",n=i.parentNode.parentNode.prepend(t),b.selection.moveCursor(this.options.ownerDocument,t))):b.util.isKey(e,b.util.keyCode.BACKSPACE)&&b.util.isMediumEditorElement(i.parentElement)&&!i.previousElementSibling&&i.nextElementSibling&&s.test(i.innerHTML)&&(e.preventDefault(),b.selection.moveCursor(this.options.ownerDocument,i.nextSibling),i.parentElement.removeChild(i))}function y(e,t,n){var i=[];if("string"==typeof(e=e||[])&&(e=t.querySelectorAll(e)),b.util.isElement(e)&&(e=[e]),n)for(var o=0;o<e.length;o++){var s=e[o];!b.util.isElement(s)||s.getAttribute("data-medium-editor-element")||s.getAttribute("medium-editor-textarea-id")||i.push(s)}else i=Array.prototype.slice.apply(e);return i}function w(e){var t=e.parentNode.querySelector('textarea[medium-editor-textarea-id="'+e.getAttribute("medium-editor-textarea-id")+'"]');t&&(t.classList.remove("medium-editor-hidden"),t.removeAttribute("medium-editor-textarea-id")),e.parentNode&&e.parentNode.removeChild(e)}function x(e,t,n){var i,o,n={window:n.options.contentWindow,document:n.options.ownerDocument,base:n};return i=e,o=n,Object.keys(o).forEach(function(e){void 0===i[e]&&(i[e]=o[e])}),"function"==typeof(e=i).init&&e.init(),e.name||(e.name=t),e}function T(){return!this.elements.every(function(e){return!!e.getAttribute("data-disable-toolbar")})&&!1!==this.options.toolbar}function k(e,t){var n;return e.getAttribute("data-medium-editor-element")||("textarea"===e.nodeName.toLowerCase()&&(e=function(e){for(var t=this.options.ownerDocument.createElement("div"),n=Date.now(),i="medium-editor-"+n,o=e.attributes;this.options.ownerDocument.getElementById(i);)i="medium-editor-"+ ++n;t.className=e.className,t.id=i,t.innerHTML=e.value,e.setAttribute("medium-editor-textarea-id",i);for(var s=0,r=o.length;s<r;s++)t.hasAttribute(o[s].nodeName)||t.setAttribute(o[s].nodeName,o[s].value);return e.form&&this.on(e.form,"reset",function(e){e.defaultPrevented||this.resetContent(this.options.ownerDocument.getElementById(i))}.bind(this)),e.classList.add("medium-editor-hidden"),e.parentNode.insertBefore(t,e),t}.call(this,e),this.instanceHandleEditableInput||(this.instanceHandleEditableInput=function(e,t){var n=t.parentNode.querySelector('textarea[medium-editor-textarea-id="'+t.getAttribute("medium-editor-textarea-id")+'"]');n&&(n.value=t.innerHTML.trim())}.bind(this),this.subscribe("editableInput",this.instanceHandleEditableInput))),this.options.disableEditing||e.getAttribute("data-disable-editing")||(e.setAttribute("contentEditable",!0),e.setAttribute("spellcheck",this.options.spellcheck)),this.instanceHandleEditableKeydownEnter||(e.getAttribute("data-disable-return")||e.getAttribute("data-disable-double-return"))&&(this.instanceHandleEditableKeydownEnter=E.bind(this),this.subscribe("editableKeydownEnter",this.instanceHandleEditableKeydownEnter)),this.options.disableReturn||e.getAttribute("data-disable-return")||this.on(e,"keyup",function(e){var t;e.isComposing||229===e.keyCode||(t=b.selection.getSelectionStart(this.options.ownerDocument))&&(b.util.isMediumEditorElement(t)&&0===t.children.length&&!b.util.isBlockContainer(t)&&this.options.ownerDocument.execCommand("formatBlock",!1,"p"),!b.util.isKey(e,b.util.keyCode.ENTER)||b.util.isListItem(t)||b.util.isBlockContainer(t)||("a"===t.nodeName.toLowerCase()?this.options.ownerDocument.execCommand("unlink",!1,null):e.shiftKey||e.ctrlKey||this.options.ownerDocument.execCommand("formatBlock",!1,"p")))}.bind(this)),n=b.util.guid(),e.setAttribute("data-medium-editor-element",!0),e.classList.add("medium-editor-element"),e.setAttribute("role","textbox"),e.setAttribute("aria-multiline",!0),e.setAttribute("data-medium-editor-editor-index",t),e.setAttribute("medium-editor-index",n),m[n]=e.innerHTML,this.events.attachAllEventsToElement(e)),e}function S(){this.subscribe("editableKeydownTab",function(e){var t,n,i,o=b.selection.getSelectionStart(this.options.ownerDocument),s=o&&o.nodeName.toLowerCase();parent=o&&o.parentNode.nodeName.toLowerCase(),"pre"===s&&(e.preventDefault(),b.util.insertHTMLCommand(this.options.ownerDocument,"    ")),b.util.isListItem(o)&&(e.preventDefault(),o=b.selection.getSelectionStart(this.options.ownerDocument),e.shiftKey?(t=o.innerHTML.replace("<br>",""),i=this.options.ownerDocument.createElement("li"),e=o.parentNode.parentNode.parentNode.appendChild(i),b.selection.moveCursor(this.options.ownerDocument,e),""===t&&o.remove()):""!==(t=o.innerHTML.replace("<br>",""))&&""!==o.innerText&&("ul"==parent?n=this.options.ownerDocument.createElement("ul"):"ol"==parent&&(n=this.options.ownerDocument.createElement("ol")),i=this.options.ownerDocument.createElement("li"),i=n.appendChild(i),o.appendChild(n),b.selection.moveCursor(this.options.ownerDocument,i)))}.bind(this)),this.subscribe("editableKeydownDelete",C.bind(this)),this.subscribe("editableKeydownEnter",C.bind(this)),this.options.disableExtraSpaces&&this.subscribe("editableKeydownSpace",function(e){var t=(n=b.selection.getSelectionStart(this.options.ownerDocument)).textContent,n=b.selection.getCaretOffsets(n);(void 0===t[n.left-1]||""===t[n.left-1].trim()||void 0!==t[n.left]&&""===t[n.left].trim())&&e.preventDefault()}.bind(this)),this.instanceHandleEditableKeydownEnter||(this.options.disableReturn||this.options.disableDoubleReturn)&&(this.instanceHandleEditableKeydownEnter=E.bind(this),this.subscribe("editableKeydownEnter",this.instanceHandleEditableKeydownEnter))}function N(){this.extensions=[],Object.keys(this.options.extensions).forEach(function(e){"toolbar"!==e&&this.options.extensions[e]&&this.extensions.push(x(this.options.extensions[e],e,this))},this),function(){return!this.options.extensions.imageDragging}.call(this)&&((e=this.options.fileDragging)||(e={},function(){return!1!==this.options.imageDragging}.call(this)||(e.allowedTypes=[])),this.addBuiltInExtension("fileDragging",e));var t={paste:!0,"anchor-preview":function(){return!!T.call(this)&&!1!==this.options.anchorPreview}.call(this),autoLink:function(){return!1!==this.options.autoLink}.call(this),keyboardCommands:function(){return!1!==this.options.keyboardCommands}.call(this),placeholder:function(){return!1!==this.options.placeholder}.call(this)};Object.keys(t).forEach(function(e){t[e]&&this.addBuiltInExtension(e)},this);var e,n=this.options.extensions.toolbar;!n&&T.call(this)&&(e=b.util.extend({},this.options.toolbar,{allowMultiParagraphSelection:this.options.allowMultiParagraphSelection}),n=new b.extensions.toolbar(e)),n&&this.extensions.push(x(n,"toolbar",this))}function A(e,t){var n,i=/^append-(.+)$/gi.exec(e);if(i)return b.util.execFormatBlock(this.options.ownerDocument,i[1]);if("fontSize"===e)return t.size&&b.util.deprecated(".size option for fontSize command",".value","6.0.0"),n=t.value||t.size,this.options.ownerDocument.execCommand("fontSize",!1,n);if("fontName"===e)return t.name&&b.util.deprecated(".name option for fontName command",".value","6.0.0"),n=t.value||t.name,this.options.ownerDocument.execCommand("fontName",!1,n);if("createLink"===e)return this.createLink(t);if("image"===e){var o=this.options.contentWindow.getSelection().toString().trim();return this.options.ownerDocument.execCommand("insertImage",!1,o)}if("html"===e){var s=this.options.contentWindow.getSelection().toString().trim();return b.util.insertHTMLCommand(this.options.ownerDocument,s)}if(/justify([A-Za-z]*)$/g.exec(e)){o=this.options.ownerDocument.execCommand(e,!1,null),s=b.selection.getSelectedParentElement(b.selection.getSelectionRange(this.options.ownerDocument));return s&&function(e){var n,t;!e||(t=Array.prototype.slice.call(e.childNodes).filter(function(e){var t="div"===e.nodeName.toLowerCase();return t&&!n&&(n=e.style.textAlign),t})).length&&(this.saveSelection(),t.forEach(function(e){var t;e.style.textAlign!==n||(t=e.lastChild)&&(b.util.unwrap(e,this.options.ownerDocument),e=this.options.ownerDocument.createElement("BR"),t.parentNode.insertBefore(e,t.nextSibling))},this),e.style.textAlign=n,this.restoreSelection())}.call(this,b.util.getTopBlockContainer(s)),o}return n=t&&t.value,this.options.ownerDocument.execCommand(e,!1,n)}return b.extensions={},function(e){function t(e,t){var n,i=Array.prototype.slice.call(arguments,2);t=t||{};for(var o=0;o<i.length;o++){var s=i[o];if(s)for(n in s)s.hasOwnProperty(n)&&void 0!==s[n]&&(e||!1===t.hasOwnProperty(n))&&(t[n]=s[n])}return t}var o=!1;try{var n=document.createElement("div"),i=document.createTextNode(" ");n.appendChild(i),o=n.contains(i)}catch(e){}var h={isIE:"Microsoft Internet Explorer"===navigator.appName||"Netscape"===navigator.appName&&null!==new RegExp("Trident/.*rv:([0-9]{1,}[.0-9]{0,})").exec(navigator.userAgent),isEdge:null!==/Edge\/\d+/.exec(navigator.userAgent),isFF:-1<navigator.userAgent.toLowerCase().indexOf("firefox"),isMac:0<=e.navigator.platform.toUpperCase().indexOf("MAC"),keyCode:{BACKSPACE:8,TAB:9,ENTER:13,ESCAPE:27,SPACE:32,DELETE:46,K:75,M:77,V:86},isMetaCtrlKey:function(e){return!!(h.isMac&&e.metaKey||!h.isMac&&e.ctrlKey)},isKey:function(e,t){e=h.getKeyCode(e);return!1===Array.isArray(t)?e===t:-1!==t.indexOf(e)},getKeyCode:function(e){var t=e.which;return t=null===t?null!==e.charCode?e.charCode:e.keyCode:t},blockContainerElementNames:["p","h1","h2","h3","h4","h5","h6","blockquote","pre","ul","li","ol","address","article","aside","audio","canvas","dd","dl","dt","fieldset","figcaption","figure","footer","form","header","hgroup","main","nav","noscript","output","section","video","table","thead","tbody","tfoot","tr","th","td"],emptyElementNames:["br","col","colgroup","hr","img","input","source","wbr"],extend:function(){var e=[!0].concat(Array.prototype.slice.call(arguments));return t.apply(this,e)},defaults:function(){var e=[!1].concat(Array.prototype.slice.call(arguments));return t.apply(this,e)},createLink:function(e,t,n,i){e=e.createElement("a");return h.moveTextRangeIntoElement(t[0],t[t.length-1],e),e.setAttribute("href",n),i&&("_blank"===i&&e.setAttribute("rel","noopener noreferrer"),e.setAttribute("target",i)),e},findOrCreateMatchingTextNodes:function(e,t,n){for(var i=e.createTreeWalker(t,NodeFilter.SHOW_ALL,null,!1),o=[],s=0,r=!1,a=null,l=null;null!==(a=i.nextNode());)if(!(3<a.nodeType))if(3===a.nodeType){if(!r&&n.start<s+a.nodeValue.length&&(r=!0,l=h.splitStartNodeIfNeeded(a,n.start,s)),r&&h.splitEndNodeIfNeeded(a,l,n.end,s),r&&s===n.end)break;if(r&&s>n.end+1)throw new Error("PerformLinking overshot the target!");r&&o.push(l||a),s+=a.nodeValue.length,null!==l&&(s+=l.nodeValue.length,i.nextNode()),l=null}else"img"===a.tagName.toLowerCase()&&(r=!r&&n.start<=s||r)&&o.push(a);return o},splitStartNodeIfNeeded:function(e,t,n){return t!==n?e.splitText(t-n):null},splitEndNodeIfNeeded:function(e,t,n,i){var o=i+e.nodeValue.length+(t?t.nodeValue.length:0)-1,s=n-i-(t?e.nodeValue.length:0);n<=o&&i!==o&&0!=s&&(t||e).splitText(s)},splitByBlockElements:function(e){if(3!==e.nodeType&&1!==e.nodeType)return[];var t=[],n=b.util.blockContainerElementNames.join(",");if(3===e.nodeType||0===e.querySelectorAll(n).length)return[e];for(var i=0;i<e.childNodes.length;i++){var o=e.childNodes[i];3===o.nodeType?t.push(o):1===o.nodeType&&(0===o.querySelectorAll(n).length?t.push(o):t=t.concat(b.util.splitByBlockElements(o)))}return t},findAdjacentTextNodeWithContent:function(e,t,n){for(var i=!1,o=n.createNodeIterator(e,NodeFilter.SHOW_TEXT,null,!1),s=o.nextNode();s;){if(s===t)i=!0;else if(i&&3===s.nodeType&&s.nodeValue&&0<s.nodeValue.trim().length)break;s=o.nextNode()}return s},findPreviousSibling:function(e){if(!e||h.isMediumEditorElement(e))return!1;for(var t=e.previousSibling;!t&&!h.isMediumEditorElement(e.parentNode);)t=(e=e.parentNode).previousSibling;return t},isDescendant:function(e,t,n){if(!e||!t)return!1;if(e===t)return!!n;if(1!==e.nodeType)return!1;if(o||3!==t.nodeType)return e.contains(t);for(var i=t.parentNode;null!==i;){if(i===e)return!0;i=i.parentNode}return!1},isElement:function(e){return!(!e||1!==e.nodeType)},throttle:function(n,i){function o(){c=Date.now(),l=null,a=n.apply(s,r),l||(s=r=null)}var s,r,a,l=null,c=0;return i||0===i||(i=50),function(){var e=Date.now(),t=i-(e-c);return s=this,r=arguments,t<=0||i<t?(l&&(clearTimeout(l),l=null),c=e,a=n.apply(s,r),l||(s=r=null)):l=l||setTimeout(o,t),a}},traverseUp:function(e,t){if(!e)return!1;do{if(1===e.nodeType){if(t(e))return e;if(h.isMediumEditorElement(e))return!1}}while(e=e.parentNode);return!1},htmlEntities:function(e){return String(e).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;")},insertHTMLCommand:function(e,t){var n,i,o,s,r,a,l=!1,c=["insertHTML",!1,t];if(!b.util.isEdge&&e.queryCommandSupported("insertHTML"))try{return e.execCommand.apply(e,c)}catch(e){}if((n=e.getSelection()).rangeCount){if(a=(n=n.getRangeAt(0)).commonAncestorContainer,h.isMediumEditorElement(a)&&!a.firstChild)n.selectNode(a.appendChild(e.createTextNode("")));else if(3===a.nodeType&&0===n.startOffset&&n.endOffset===a.nodeValue.length||3!==a.nodeType&&a.innerHTML===n.toString()){for(;!h.isMediumEditorElement(a)&&a.parentNode&&1===a.parentNode.childNodes.length&&!h.isMediumEditorElement(a.parentNode);)a=a.parentNode;n.selectNode(a)}for(n.deleteContents(),(i=e.createElement("div")).innerHTML=t,o=e.createDocumentFragment();i.firstChild;)s=i.firstChild,r=o.appendChild(s);n.insertNode(o),r&&((n=n.cloneRange()).setStartAfter(r),n.collapse(!0),b.selection.selectRange(e,n)),l=!0}return e.execCommand.callListeners&&e.execCommand.callListeners(c,l),l},execFormatBlock:function(e,t){var n=h.getTopBlockContainer(b.selection.getSelectionStart(e));if("pre"===t&&e.execCommand("outdent",!1,t),"blockquote"===t){if(n&&Array.prototype.slice.call(n.childNodes).some(function(e){return h.isBlockContainer(e)}))return e.execCommand("outdent",!1,null);if(h.isIE)return e.execCommand("indent",!1,t)}if(n&&t===n.nodeName.toLowerCase()&&(t="p"),h.isIE&&(t="<"+t+">"),n&&"blockquote"===n.nodeName.toLowerCase()){if(h.isIE&&"<p>"===t)return e.execCommand("outdent",!1,t);if((h.isFF||h.isEdge)&&"p"===t)return Array.prototype.slice.call(n.childNodes).some(function(e){return!h.isBlockContainer(e)})&&e.execCommand("formatBlock",!1,t),e.execCommand("outdent",!1,t)}return e.execCommand("formatBlock",!1,t)},setTargetBlank:function(e,t){var n,i=t||!1;if("a"===e.nodeName.toLowerCase())e.target="_blank",e.rel="noopener noreferrer";else for(e=e.getElementsByTagName("a"),n=0;n<e.length;n+=1)!1!==i&&i!==e[n].attributes.href.value||(e[n].target="_blank",e[n].rel="noopener noreferrer")},removeTargetBlank:function(e,t){var n;if("a"===e.nodeName.toLowerCase())e.removeAttribute("target"),e.removeAttribute("rel");else for(e=e.getElementsByTagName("a"),n=0;n<e.length;n+=1)t===e[n].attributes.href.value&&(e[n].removeAttribute("target"),e[n].removeAttribute("rel"))},addClassToAnchors:function(e,t){var n,i,o=t.split(" ");if("a"===e.nodeName.toLowerCase())for(i=0;i<o.length;i+=1)e.classList.add(o[i]);else{var s=e.getElementsByTagName("a");for(e=0===s.length?(t=h.getClosestTag(e,"a"))?[t]:[]:s,n=0;n<e.length;n+=1)for(i=0;i<o.length;i+=1)e[n].classList.add(o[i])}},isListItem:function(e){if(!e)return!1;if("li"===e.nodeName.toLowerCase())return!0;for(var t=e.parentNode,n=t.nodeName.toLowerCase();"li"===n||!h.isBlockContainer(t)&&"div"!==n;){if("li"===n)return!0;if(!(t=t.parentNode))return!1;n=t.nodeName.toLowerCase()}return!1},cleanListDOM:function(e,t){var n;"li"!==t.nodeName.toLowerCase()||"p"===(n=t.parentElement).parentElement.nodeName.toLowerCase()&&(h.unwrap(n.parentElement,e),b.selection.moveCursor(e,t.firstChild,t.firstChild.textContent.length))},splitOffDOMTree:function(e,t,n){for(var i=t,o=null,s=!n;i!==e;){var r,a=i.parentNode,l=a.cloneNode(!1),c=s?i:a.firstChild;for(o&&(s?l.appendChild(o):r=o),o=l;c;)var d=c.nextSibling,c=c===i?(c.hasChildNodes()?c=c.cloneNode(!1):c.parentNode.removeChild(c),c.textContent&&o.appendChild(c),s?d:null):(c.parentNode.removeChild(c),(c.hasChildNodes()||c.textContent)&&o.appendChild(c),d);r&&o.appendChild(r),i=a}return o},moveTextRangeIntoElement:function(e,t,n){if(!e||!t)return!1;var i=h.findCommonRoot(e,t);if(!i)return!1;if(t===e){var o=e.parentNode,s=e.nextSibling;return o.removeChild(e),n.appendChild(e),s?o.insertBefore(n,s):o.appendChild(n),n.hasChildNodes()}for(var r,a,l,c=[],d=0;d<i.childNodes.length;d++)if(l=i.childNodes[d],r){if(h.isDescendant(l,t,!0)){a=l;break}c.push(l)}else h.isDescendant(l,e,!0)&&(r=l);var o=a.nextSibling,u=i.ownerDocument.createDocumentFragment();return r===e?(r.parentNode.removeChild(r),u.appendChild(r)):u.appendChild(h.splitOffDOMTree(r,e)),c.forEach(function(e){e.parentNode.removeChild(e),u.appendChild(e)}),a===t?(a.parentNode.removeChild(a),u.appendChild(a)):u.appendChild(h.splitOffDOMTree(a,t,!0)),n.appendChild(u),a.parentNode===i?i.insertBefore(n,a):o?i.insertBefore(n,o):i.appendChild(n),n.hasChildNodes()},depthOfNode:function(e){for(var t=0,n=e;null!==n.parentNode;)n=n.parentNode,t++;return t},findCommonRoot:function(e,t){for(var n=h.depthOfNode(e),i=h.depthOfNode(t),o=e,s=t;n!==i;)i<n?(o=o.parentNode,--n):(s=s.parentNode,--i);for(;o!==s;)o=o.parentNode,s=s.parentNode;return o},isElementAtBeginningOfBlock:function(e){for(var t;!h.isBlockContainer(e)&&!h.isMediumEditorElement(e);){for(t=e;t=t.previousSibling;)if(0<(3===t.nodeType?t.nodeValue:t.textContent).length)return!1;e=e.parentNode}return!0},isMediumEditorElement:function(e){return e&&e.getAttribute&&!!e.getAttribute("data-medium-editor-element")},getContainerEditorElement:function(e){return h.traverseUp(e,function(e){return h.isMediumEditorElement(e)})},isBlockContainer:function(e){return e&&3!==e.nodeType&&-1!==h.blockContainerElementNames.indexOf(e.nodeName.toLowerCase())},getClosestBlockContainer:function(e){return h.traverseUp(e,function(e){return h.isBlockContainer(e)||h.isMediumEditorElement(e)})},getTopBlockContainer:function(e){var t=!!h.isBlockContainer(e)&&e;return h.traverseUp(e,function(e){return!((t=h.isBlockContainer(e)?e:t)||!h.isMediumEditorElement(e))&&(t=e,!0)}),t},getFirstSelectableLeafNode:function(e){for(;e&&e.firstChild;)e=e.firstChild;var t;return"table"!==(e=h.traverseUp(e,function(e){return-1===h.emptyElementNames.indexOf(e.nodeName.toLowerCase())})).nodeName.toLowerCase()||(t=e.querySelector("th, td"))&&(e=t),e},getFirstTextNode:function(e){return h.warn("getFirstTextNode is deprecated and will be removed in version 6.0.0"),h._getFirstTextNode(e)},_getFirstTextNode:function(e){if(3===e.nodeType)return e;for(var t=0;t<e.childNodes.length;t++){var n=h._getFirstTextNode(e.childNodes[t]);if(null!==n)return n}return null},ensureUrlHasProtocol:function(e){return-1===e.indexOf("://")?"http://"+e:e},warn:function(){void 0!==e.console&&"function"==typeof e.console.warn&&e.console.warn.apply(e.console,arguments)},deprecated:function(e,t,n){t=e+" is deprecated, please use "+t+" instead.";n&&(t+=" Will be removed in "+n),h.warn(t)},deprecatedMethod:function(e,t,n,i){h.deprecated(e,t,i),"function"==typeof this[t]&&this[t].apply(this,n)},cleanupAttrs:function(t,e){e.forEach(function(e){jQuery(t).hasClass("emojioneemoji")||jQuery(t).hasClass("emoji")||t.removeAttribute(e)})},cleanupTags:function(e,t){-1!==t.indexOf(e.nodeName.toLowerCase())&&e.parentNode.removeChild(e)},unwrapTags:function(e,t){-1!==t.indexOf(e.nodeName.toLowerCase())&&b.util.unwrap(e,document)},getClosestTag:function(e,t){return h.traverseUp(e,function(e){return e.nodeName.toLowerCase()===t.toLowerCase()})},unwrap:function(e,t){for(var n=t.createDocumentFragment(),i=Array.prototype.slice.call(e.childNodes),o=0;o<i.length;o++)n.appendChild(i[o]);n.childNodes.length?e.parentNode.replaceChild(n,e):e.parentNode.removeChild(e)},guid:function(){function e(){return Math.floor(65536*(1+Math.random())).toString(16).substring(1)}return e()+e()+"-"+e()+"-"+e()+"-"+e()+"-"+e()+e()+e()}};b.util=h}(window),t.extend=function(e){var t=this,n=e&&e.hasOwnProperty("constructor")?e.constructor:function(){return t.apply(this,arguments)};b.util.extend(n,t);function i(){this.constructor=n}return i.prototype=t.prototype,n.prototype=new i,e&&b.util.extend(n.prototype,e),n},t.prototype={init:function(){},base:void 0,name:void 0,checkState:void 0,destroy:void 0,queryCommandState:void 0,isActive:void 0,isAlreadyApplied:void 0,setActive:void 0,setInactive:void 0,getInteractionElements:void 0,window:void 0,document:void 0,getEditorElements:function(){return this.base.elements},getEditorId:function(){return this.base.id},getEditorOption:function(e){return this.base.options[e]}},["execAction","on","off","subscribe","trigger"].forEach(function(e){t.prototype[e]=function(){return this.base[e].apply(this.base,arguments)}}),b.Extension=t,b.selection={findMatchingSelectionParent:function(e,t){var t=t.getSelection();return 0!==t.rangeCount&&(t=t.getRangeAt(0).commonAncestorContainer,b.util.traverseUp(t,e))},getSelectionElement:function(e){return this.findMatchingSelectionParent(function(e){return b.util.isMediumEditorElement(e)},e)},exportSelection:function(e,t){if(!e)return null;var n,i,o=null,s=t.getSelection();return 0<s.rangeCount&&((n=(i=s.getRangeAt(0)).cloneRange()).selectNodeContents(e),n.setEnd(i.startContainer,i.startOffset),o={start:s=n.toString().length,end:s+i.toString().length},this.doesRangeStartWithImages(i,t)&&(o.startsWithImage=!0),(n=this.getTrailingImageCount(e,o,i.endContainer,i.endOffset))&&(o.trailingImageCount=n),0===s||-1!==(i=this.getIndexRelativeToAdjacentEmptyBlocks(t,e,i.startContainer,i.startOffset))&&(o.emptyBlocksIndex=i)),o},importSelection:function(e,t,n,i){if(e&&t){var o=n.createRange();o.setStart(t,0),o.collapse(!0);var s,r=t,a=[],l=0,c=!1,d=!1,u=0,h=!1,m=!1,f=null;for((i||e.startsWithImage||void 0!==e.emptyBlocksIndex)&&(m=!0);!h&&r;)if(3<r.nodeType)r=a.pop();else{if(3!==r.nodeType||d){if(e.trailingImageCount&&d&&("img"===r.nodeName.toLowerCase()&&u++,u===e.trailingImageCount)){for(var p=0;r.parentNode.childNodes[p]!==r;)p++;o.setEnd(r.parentNode,p+1),h=!0}if(!h&&1===r.nodeType)for(var g=r.childNodes.length-1;0<=g;)a.push(r.childNodes[g]),--g}else s=l+r.length,!c&&e.start>=l&&e.start<=s&&(m||e.start<s?(o.setStart(r,e.start-l),c=!0):f=r),c&&e.end>=l&&e.end<=s&&(e.trailingImageCount?d=!0:(o.setEnd(r,e.end-l),h=!0)),l=s;h||(r=a.pop())}!c&&f&&(o.setStart(f,f.length),o.setEnd(f,f.length)),void 0!==e.emptyBlocksIndex&&(o=this.importSelectionMoveCursorPastBlocks(n,t,e.emptyBlocksIndex,o)),i&&(o=this.importSelectionMoveCursorPastAnchor(e,o)),this.selectRange(n,o)}},importSelectionMoveCursorPastAnchor:function(e,t){if(e.start===e.end&&3===t.startContainer.nodeType&&t.startOffset===t.startContainer.nodeValue.length&&b.util.traverseUp(t.startContainer,function(e){return"a"===e.nodeName.toLowerCase()})){for(var n=t.startContainer,i=t.startContainer.parentNode;null!==i&&"a"!==i.nodeName.toLowerCase();)i=i.childNodes[i.childNodes.length-1]!==n?null:(n=i).parentNode;if(null!==i&&"a"===i.nodeName.toLowerCase()){for(var o=null,s=0;null===o&&s<i.parentNode.childNodes.length;s++)i.parentNode.childNodes[s]===i&&(o=s);t.setStart(i.parentNode,o+1),t.collapse(!0)}}return t},importSelectionMoveCursorPastBlocks:function(e,t,n,i){var o,s,r=e.createTreeWalker(t,NodeFilter.SHOW_ELEMENT,c,!1),t=i.startContainer,a=0;for(n=n||1,o=3===t.nodeType&&b.util.isBlockContainer(t.previousSibling)?t.previousSibling:b.util.getClosestBlockContainer(t);r.nextNode();)if(s){if(s=r.currentNode,++a===n)break;if(0<s.textContent.length)break}else o===r.currentNode&&(s=r.currentNode);return i.setStart(b.util.getFirstSelectableLeafNode(s=s||o),0),i},getIndexRelativeToAdjacentEmptyBlocks:function(e,t,n,i){if(0<n.textContent.length&&0<i)return-1;var o=n;if(o=3!==o.nodeType?n.childNodes[i]:o){if(!b.util.isElementAtBeginningOfBlock(o))return-1;o=b.util.findPreviousSibling(o);if(!o)return-1;if(o.nodeValue)return-1}for(var s=b.util.getClosestBlockContainer(n),r=e.createTreeWalker(t,NodeFilter.SHOW_ELEMENT,c,!1),a=0;r.nextNode();){var l=""===r.currentNode.textContent;if((l||0<a)&&(a+=1),r.currentNode===s)return a;l||(a=0)}return a},doesRangeStartWithImages:function(e,t){if(0!==e.startOffset||1!==e.startContainer.nodeType)return!1;if("img"===e.startContainer.nodeName.toLowerCase())return!0;var n=e.startContainer.querySelector("img");if(!n)return!1;for(var i=t.createTreeWalker(e.startContainer,NodeFilter.SHOW_ALL,null,!1);i.nextNode();){var o=i.currentNode;if(o===n)break;if(o.nodeValue)return!1}return!0},getTrailingImageCount:function(e,t,n,i){if(0===i||1!==n.nodeType)return 0;if("img"!==n.nodeName.toLowerCase()&&!n.querySelector("img"))return 0;for(var o=n.childNodes[i-1];o.hasChildNodes();)o=o.lastChild;for(var s,r=e,a=[],l=0,c=!1,d=!1,u=!1,h=0;!u&&r;)if(3<r.nodeType)r=a.pop();else{if(3!==r.nodeType||d){if("img"===r.nodeName.toLowerCase()&&h++,r===o)u=!0;else if(1===r.nodeType)for(var m=r.childNodes.length-1;0<=m;)a.push(r.childNodes[m]),--m}else h=0,s=l+r.length,(c=!c&&t.start>=l&&t.start<=s?!0:c)&&t.end>=l&&t.end<=s&&(d=!0),l=s;u||(r=a.pop())}return h},selectionContainsContent:function(e){e=e.getSelection();if(!e||e.isCollapsed||!e.rangeCount)return!1;if(""!==e.toString().trim())return!0;e=this.getSelectedParentElement(e.getRangeAt(0));return!(!e||!("img"===e.nodeName.toLowerCase()||1===e.nodeType&&e.querySelector("img")))},selectionInContentEditableFalse:function(e){var n,e=this.findMatchingSelectionParent(function(e){var t=e&&e.getAttribute("contenteditable");return"true"===t&&(n=!0),"#text"!==e.nodeName&&"false"===t},e);return!n&&e},getSelectionHtml:function(e){var t,n,i,o="",s=e.getSelection();if(s.rangeCount){for(i=e.createElement("div"),t=0,n=s.rangeCount;t<n;t+=1)i.appendChild(s.getRangeAt(t).cloneContents());o=i.innerHTML}return o},getCaretOffsets:function(e,t){var n=(t=t||window.getSelection().getRangeAt(0)).cloneRange(),i=t.cloneRange();return n.selectNodeContents(e),n.setEnd(t.endContainer,t.endOffset),i.selectNodeContents(e),i.setStart(t.endContainer,t.endOffset),{left:n.toString().length,right:i.toString().length}},rangeSelectsSingleNode:function(e){var t=e.startContainer;return t===e.endContainer&&t.hasChildNodes()&&e.endOffset===e.startOffset+1},getSelectedParentElement:function(e){return e?this.rangeSelectsSingleNode(e)&&3!==e.startContainer.childNodes[e.startOffset].nodeType?e.startContainer.childNodes[e.startOffset]:3===e.startContainer.nodeType?e.startContainer.parentNode:e.startContainer:null},getSelectedElements:function(e){var t,n,i=e.getSelection();if(!i.rangeCount||i.isCollapsed||!i.getRangeAt(0).commonAncestorContainer)return[];if(3!==(e=i.getRangeAt(0)).commonAncestorContainer.nodeType)return[].filter.call(e.commonAncestorContainer.getElementsByTagName("*"),function(e){return"function"!=typeof i.containsNode||i.containsNode(e,!0)});for(t=[],n=e.commonAncestorContainer;n.parentNode&&1===n.parentNode.childNodes.length;)t.push(n.parentNode),n=n.parentNode;return t},selectNode:function(e,t){var n=t.createRange();n.selectNodeContents(e),this.selectRange(t,n)},select:function(e,t,n,i,o){var s=e.createRange();return s.setStart(t,n),i?s.setEnd(i,o):s.collapse(!0),this.selectRange(e,s),s},clearSelection:function(e,t){t?e.getSelection().collapseToStart():e.getSelection().collapseToEnd()},moveCursor:function(e,t,n){this.select(e,t,n)},getSelectionRange:function(e){e=e.getSelection();return 0===e.rangeCount?null:e.getRangeAt(0)},selectRange:function(e,t){e=e.getSelection();e.removeAllRanges(),e.addRange(t)},getSelectionStart:function(e){e=e.getSelection().anchorNode;return e&&3===e.nodeType?e.parentNode:e}},e.prototype={InputEventOnContenteditableSupported:!b.util.isIE&&!b.util.isEdge,attachDOMEvent:function(e,t,n,i){var o=this.base.options.contentWindow,s=this.base.options.ownerDocument;e=b.util.isElement(e)||-1<[o,s].indexOf(e)?[e]:e,Array.prototype.forEach.call(e,function(e){e.addEventListener(t,n,i),this.events.push([e,t,n,i])}.bind(this))},detachDOMEvent:function(e,t,n,i){var o,s=this.base.options.contentWindow,r=this.base.options.ownerDocument;e&&(e=b.util.isElement(e)||-1<[s,r].indexOf(e)?[e]:e,Array.prototype.forEach.call(e,function(e){-1!==(o=this.indexOfListener(e,t,n,i))&&(o=this.events.splice(o,1)[0])[0].removeEventListener(o[1],o[2],o[3])}.bind(this)))},indexOfListener:function(e,t,n,i){for(var o,s=0,r=this.events.length;s<r;s+=1)if((o=this.events[s])[0]===e&&o[1]===t&&o[2]===n&&o[3]===i)return s;return-1},detachAllDOMEvents:function(){for(var e=this.events.pop();e;)e[0].removeEventListener(e[1],e[2],e[3]),e=this.events.pop()},detachAllEventsFromElement:function(t){for(var e=this.events.filter(function(e){return e&&e[0].getAttribute&&e[0].getAttribute("medium-editor-index")===t.getAttribute("medium-editor-index")}),n=0,i=e.length;n<i;n++){var o=e[n];this.detachDOMEvent(o[0],o[1],o[2],o[3])}},attachAllEventsToElement:function(t){this.listeners.editableInput&&(this.contentCache[t.getAttribute("medium-editor-index")]=t.innerHTML),this.eventsCache&&this.eventsCache.forEach(function(e){this.attachDOMEvent(t,e.name,e.handler.bind(this))},this)},enableCustomEvent:function(e){void 0!==this.disabledEvents[e]&&delete this.disabledEvents[e]},disableCustomEvent:function(e){this.disabledEvents[e]=!0},attachCustomEvent:function(e,t){this.setupListener(e),this.customEvents[e]||(this.customEvents[e]=[]),this.customEvents[e].push(t)},detachCustomEvent:function(e,t){t=this.indexOfCustomListener(e,t);-1!==t&&this.customEvents[e].splice(t,1)},indexOfCustomListener:function(e,t){return this.customEvents[e]&&this.customEvents[e].length?this.customEvents[e].indexOf(t):-1},detachAllCustomEvents:function(){this.customEvents={}},triggerCustomEvent:function(e,t,n){this.customEvents[e]&&!this.disabledEvents[e]&&this.customEvents[e].forEach(function(e){e(t,n)})},destroy:function(){this.detachAllDOMEvents(),this.detachAllCustomEvents(),this.detachExecCommand(),this.base.elements&&this.base.elements.forEach(function(e){e.removeAttribute("data-medium-focused")})},attachToExecCommand:function(){this.execCommandListener||(this.execCommandListener=function(e){this.handleDocumentExecCommand(e)}.bind(this),this.wrapExecCommand(),this.options.ownerDocument.execCommand.listeners.push(this.execCommandListener))},detachExecCommand:function(){var e,t=this.options.ownerDocument;this.execCommandListener&&t.execCommand.listeners&&(-1!==(e=t.execCommand.listeners.indexOf(this.execCommandListener))&&t.execCommand.listeners.splice(e,1),t.execCommand.listeners.length||this.unwrapExecCommand())},wrapExecCommand:function(){var n,e,i=this.options.ownerDocument;i.execCommand.listeners||(n=function(t,n){i.execCommand.listeners&&i.execCommand.listeners.forEach(function(e){e({command:t[0],value:t[2],args:t,result:n})})},(e=function(){var e=i.execCommand.orig.apply(this,arguments);if(!i.execCommand.listeners)return e;var t=Array.prototype.slice.call(arguments);return n(t,e),e}).orig=i.execCommand,e.listeners=[],e.callListeners=n,i.execCommand=e)},unwrapExecCommand:function(){var e=this.options.ownerDocument;e.execCommand.orig&&(e.execCommand=e.execCommand.orig)},setupListener:function(e){if(!this.listeners[e]){switch(e){case"externalInteraction":this.attachDOMEvent(this.options.ownerDocument.body,"mousedown",this.handleBodyMousedown.bind(this),!0),this.attachDOMEvent(this.options.ownerDocument.body,"click",this.handleBodyClick.bind(this),!0),this.attachDOMEvent(this.options.ownerDocument.body,"focus",this.handleBodyFocus.bind(this),!0);break;case"blur":case"focus":this.setupListener("externalInteraction");break;case"editableInput":this.contentCache={},this.base.elements.forEach(function(e){this.contentCache[e.getAttribute("medium-editor-index")]=e.innerHTML},this),this.InputEventOnContenteditableSupported&&this.attachToEachElement("input",this.handleInput),this.InputEventOnContenteditableSupported||(this.setupListener("editableKeypress"),this.keypressUpdateInput=!0,this.attachDOMEvent(document,"selectionchange",this.handleDocumentSelectionChange.bind(this)),this.attachToExecCommand());break;case"editableClick":this.attachToEachElement("click",this.handleClick);break;case"editableBlur":this.attachToEachElement("blur",this.handleBlur);break;case"editableKeypress":this.attachToEachElement("keypress",this.handleKeypress);break;case"editableKeyup":this.attachToEachElement("keyup",this.handleKeyup);break;case"editableKeydown":this.attachToEachElement("keydown",this.handleKeydown);break;case"editableKeydownSpace":case"editableKeydownEnter":case"editableKeydownTab":case"editableKeydownDelete":this.setupListener("editableKeydown");break;case"editableMouseover":this.attachToEachElement("mouseover",this.handleMouseover);break;case"editableDrag":this.attachToEachElement("dragover",this.handleDragging),this.attachToEachElement("dragleave",this.handleDragging);break;case"editableDrop":this.attachToEachElement("drop",this.handleDrop);break;case"editablePaste":this.attachToEachElement("paste",this.handlePaste)}this.listeners[e]=!0}},attachToEachElement:function(t,n){this.eventsCache||(this.eventsCache=[]),this.base.elements.forEach(function(e){this.attachDOMEvent(e,t,n.bind(this))},this),this.eventsCache.push({name:t,handler:n})},cleanupElement:function(e){var t=e.getAttribute("medium-editor-index");t&&(this.detachAllEventsFromElement(e),this.contentCache&&delete this.contentCache[t])},focusElement:function(e){e.focus(),this.updateFocus(e,{target:e,type:"focus"})},updateFocus:function(t,e){var n,i=this.base.getFocusedElement();(n=i&&"click"===e.type&&this.lastMousedownTarget&&(b.util.isDescendant(i,this.lastMousedownTarget,!0)||s(this.base.extensions,this.lastMousedownTarget))?i:n)||this.base.elements.some(function(e){return!!(n=!n&&b.util.isDescendant(e,t,!0)?e:n)},this);var o=!b.util.isDescendant(i,t,!0)&&!s(this.base.extensions,t);n!==i&&(i&&o&&(i.removeAttribute("data-medium-focused"),this.triggerCustomEvent("blur",e,i)),n&&(n.setAttribute("data-medium-focused",!0),this.triggerCustomEvent("focus",e,n))),o&&this.triggerCustomEvent("externalInteraction",e)},updateInput:function(e,t){var n,i;this.contentCache&&(n=e.getAttribute("medium-editor-index"),(i=e.innerHTML)!==this.contentCache[n]&&this.triggerCustomEvent("editableInput",t,e),this.contentCache[n]=i)},handleDocumentSelectionChange:function(e){var t,n;e.currentTarget&&e.currentTarget.activeElement&&(t=e.currentTarget.activeElement,this.base.elements.some(function(e){return!!b.util.isDescendant(e,t,!0)&&(n=e,!0)},this),n&&this.updateInput(n,{target:t,currentTarget:n}))},handleDocumentExecCommand:function(){var e=this.base.getFocusedElement();e&&this.updateInput(e,{target:e,currentTarget:e})},handleBodyClick:function(e){this.updateFocus(e.target,e)},handleBodyFocus:function(e){this.updateFocus(e.target,e)},handleBodyMousedown:function(e){this.lastMousedownTarget=e.target},handleInput:function(e){this.updateInput(e.currentTarget,e)},handleClick:function(e){this.triggerCustomEvent("editableClick",e,e.currentTarget)},handleBlur:function(e){this.triggerCustomEvent("editableBlur",e,e.currentTarget)},handleKeypress:function(e){var t;this.triggerCustomEvent("editableKeypress",e,e.currentTarget),this.keypressUpdateInput&&(t={target:e.target,currentTarget:e.currentTarget},setTimeout(function(){this.updateInput(t.currentTarget,t)}.bind(this),0))},handleKeyup:function(e){this.triggerCustomEvent("editableKeyup",e,e.currentTarget)},handleMouseover:function(e){this.triggerCustomEvent("editableMouseover",e,e.currentTarget)},handleDragging:function(e){this.triggerCustomEvent("editableDrag",e,e.currentTarget)},handleDrop:function(e){this.triggerCustomEvent("editableDrop",e,e.currentTarget)},handlePaste:function(e){this.triggerCustomEvent("editablePaste",e,e.currentTarget)},handleKeydown:function(e){return this.triggerCustomEvent("editableKeydown",e,e.currentTarget),b.util.isKey(e,b.util.keyCode.SPACE)?this.triggerCustomEvent("editableKeydownSpace",e,e.currentTarget):b.util.isKey(e,b.util.keyCode.ENTER)||e.ctrlKey&&b.util.isKey(e,b.util.keyCode.M)?this.triggerCustomEvent("editableKeydownEnter",e,e.currentTarget):b.util.isKey(e,b.util.keyCode.TAB)?this.triggerCustomEvent("editableKeydownTab",e,e.currentTarget):b.util.isKey(e,[b.util.keyCode.DELETE,b.util.keyCode.BACKSPACE])?this.triggerCustomEvent("editableKeydownDelete",e,e.currentTarget):void 0}},b.Events=e,(n=b.Extension.extend({action:void 0,aria:void 0,tagNames:void 0,style:void 0,useQueryState:void 0,contentDefault:void 0,contentFA:void 0,classList:void 0,attrs:void 0,constructor:function(e){n.isBuiltInButton(e)?b.Extension.call(this,this.defaults[e]):b.Extension.call(this,e)},init:function(){b.Extension.prototype.init.apply(this,arguments),this.button=this.createButton(),this.on(this.button,"click",this.handleClick.bind(this))},getButton:function(){return this.button},getAction:function(){return"function"==typeof this.action?this.action(this.base.options):this.action},getAria:function(){return"function"==typeof this.aria?this.aria(this.base.options):this.aria},getTagNames:function(){return"function"==typeof this.tagNames?this.tagNames(this.base.options):this.tagNames},createButton:function(){var t=this.document.createElement("button"),e=this.contentDefault,n=this.getAria(),i=this.getEditorOption("buttonLabels");return t.classList.add("medium-editor-action"),t.classList.add("medium-editor-action-"+this.name),this.classList&&this.classList.forEach(function(e){t.classList.add(e)}),t.setAttribute("data-action",this.getAction()),n&&(t.setAttribute("title",n),t.setAttribute("aria-label",n)),this.attrs&&Object.keys(this.attrs).forEach(function(e){t.setAttribute(e,this.attrs[e])},this),"fontawesome"===i&&this.contentFA&&(e=this.contentFA),t.innerHTML=e,t},handleClick:function(e){e.preventDefault(),e.stopPropagation();e=this.getAction();e&&this.execAction(e)},isActive:function(){return this.button.classList.contains(this.getEditorOption("activeButtonClass"))},setInactive:function(){this.button.classList.remove(this.getEditorOption("activeButtonClass")),delete this.knownState},setActive:function(){this.button.classList.add(this.getEditorOption("activeButtonClass")),delete this.knownState},queryCommandState:function(){var e=null;return e=this.useQueryState?this.base.queryCommandState(this.getAction()):e},isAlreadyApplied:function(e){var t,n=!1,i=this.getTagNames();return!1===this.knownState||!0===this.knownState?this.knownState:(!(n=i&&0<i.length?-1!==i.indexOf(e.nodeName.toLowerCase()):n)&&this.style&&(i=this.style.value.split("|"),t=this.window.getComputedStyle(e,null).getPropertyValue(this.style.prop),i.forEach(function(e){this.knownState||!(n=-1!==t.indexOf(e))&&"text-decoration"===this.style.prop||(this.knownState=n)},this)),n)}})).isBuiltInButton=function(e){return"string"==typeof e&&b.extensions.button.prototype.defaults.hasOwnProperty(e)},b.extensions.button=n,b.extensions.button.prototype.defaults={bold:{name:"bold",action:"bold",aria:"bold",tagNames:["b","strong"],style:{prop:"font-weight",value:"700|bold"},useQueryState:!0,contentDefault:"<b>B</b>",contentFA:'<i class="fa fa-bold"></i>'},italic:{name:"italic",action:"italic",aria:"italic",tagNames:["i","em"],style:{prop:"font-style",value:"italic"},useQueryState:!0,contentDefault:"<b><i>I</i></b>",contentFA:'<i class="fa fa-italic"></i>'},underline:{name:"underline",action:"underline",aria:"underline",tagNames:["u"],style:{prop:"text-decoration",value:"underline"},useQueryState:!0,contentDefault:"<b><u>U</u></b>",contentFA:'<i class="fa fa-underline"></i>'},strikethrough:{name:"strikethrough",action:"strikethrough",aria:"strike through",tagNames:["strike"],style:{prop:"text-decoration",value:"line-through"},useQueryState:!0,contentDefault:"<s>A</s>",contentFA:'<i class="fa fa-strikethrough"></i>'},superscript:{name:"superscript",action:"superscript",aria:"superscript",tagNames:["sup"],contentDefault:"<b>x<sup>1</sup></b>",contentFA:'<i class="fa fa-superscript"></i>'},subscript:{name:"subscript",action:"subscript",aria:"subscript",tagNames:["sub"],contentDefault:"<b>x<sub>1</sub></b>",contentFA:'<i class="fa fa-subscript"></i>'},image:{name:"image",action:"image",aria:"image",tagNames:["img"],contentDefault:"<b>image</b>",contentFA:'<i class="fa fa-picture-o"></i>'},html:{name:"html",action:"html",aria:"evaluate html",tagNames:["iframe","object"],contentDefault:"<b>html</b>",contentFA:'<i class="fa fa-code"></i>'},orderedlist:{name:"orderedlist",action:"insertorderedlist",aria:"ordered list",tagNames:["ol"],useQueryState:!0,contentDefault:"<b>1.</b>",contentFA:'<i class="fa fa-list-ol"></i>'},unorderedlist:{name:"unorderedlist",action:"insertunorderedlist",aria:"unordered list",tagNames:["ul"],useQueryState:!0,contentDefault:"<b>&bull;</b>",contentFA:'<i class="fa fa-list-ul"></i>'},indent:{name:"indent",action:"indent",aria:"indent",tagNames:[],contentDefault:"<b>&rarr;</b>",contentFA:'<i class="fa fa-indent"></i>'},outdent:{name:"outdent",action:"outdent",aria:"outdent",tagNames:[],contentDefault:"<b>&larr;</b>",contentFA:'<i class="fa fa-outdent"></i>'},justifyCenter:{name:"justifyCenter",action:"justifyCenter",aria:"center justify",tagNames:[],style:{prop:"text-align",value:"center"},contentDefault:"<b>C</b>",contentFA:'<i class="fa fa-align-center"></i>'},justifyFull:{name:"justifyFull",action:"justifyFull",aria:"full justify",tagNames:[],style:{prop:"text-align",value:"justify"},contentDefault:"<b>J</b>",contentFA:'<i class="fa fa-align-justify"></i>'},justifyLeft:{name:"justifyLeft",action:"justifyLeft",aria:"left justify",tagNames:[],style:{prop:"text-align",value:"left"},contentDefault:"<b>L</b>",contentFA:'<i class="fa fa-align-left"></i>'},justifyRight:{name:"justifyRight",action:"justifyRight",aria:"right justify",tagNames:[],style:{prop:"text-align",value:"right"},contentDefault:"<b>R</b>",contentFA:'<i class="fa fa-align-right"></i>'},removeFormat:{name:"removeFormat",aria:"remove formatting",action:"removeFormat",contentDefault:"<b>X</b>",contentFA:'<i class="fa fa-eraser"></i>'},quote:{name:"quote",action:"append-blockquote",aria:"blockquote",tagNames:["blockquote"],contentDefault:"<b>&ldquo;</b>",contentFA:'<i class="fa fa-quote-right"></i>'},pre:{name:"pre",action:"append-pre",aria:"preformatted text",tagNames:["pre"],contentDefault:"<b>0101</b>",contentFA:'<i class="fa fa-code fa-lg"></i>'},h1:{name:"h1",action:"append-h1",aria:"header type one",tagNames:["h1"],contentDefault:"<b>H1</b>",contentFA:'<i class="fa fa-header"><sup>1</sup>'},h2:{name:"h2",action:"append-h2",aria:"header type two",tagNames:["h2"],contentDefault:"<b>H2</b>",contentFA:'<i class="fa fa-header"><sup>2</sup>'},h3:{name:"h3",action:"append-h3",aria:"header type three",tagNames:["h3"],contentDefault:"<b>H3</b>",contentFA:'<i class="fa fa-header"><sup>3</sup>'},h4:{name:"h4",action:"append-h4",aria:"header type four",tagNames:["h4"],contentDefault:"<b>H4</b>",contentFA:'<i class="fa fa-header"><sup>4</sup>'},h5:{name:"h5",action:"append-h5",aria:"header type five",tagNames:["h5"],contentDefault:"<b>H5</b>",contentFA:'<i class="fa fa-header"><sup>5</sup>'},h6:{name:"h6",action:"append-h6",aria:"header type six",tagNames:["h6"],contentDefault:"<b>H6</b>",contentFA:'<i class="fa fa-header"><sup>6</sup>'}},h=b.extensions.button.extend({init:function(){b.extensions.button.prototype.init.apply(this,arguments)},formSaveLabel:"&#10003;",formCloseLabel:"&times;",activeClass:"medium-editor-toolbar-form-active",hasForm:!0,getForm:function(){},isDisplayed:function(){return!!this.hasForm&&this.getForm().classList.contains(this.activeClass)},showForm:function(){this.hasForm&&this.getForm().classList.add(this.activeClass)},hideForm:function(){this.hasForm&&this.getForm().classList.remove(this.activeClass)},showToolbarDefaultActions:function(){var e=this.base.getExtensionByName("toolbar");e&&e.showToolbarDefaultActions()},hideToolbarDefaultActions:function(){var e=this.base.getExtensionByName("toolbar");e&&e.hideToolbarDefaultActions()},setToolbarPosition:function(){var e=this.base.getExtensionByName("toolbar");e&&e.setToolbarPosition()}}),b.extensions.form=h,h=b.extensions.form.extend({customClassOption:null,customClassOptionText:"Button",linkValidation:!1,placeholderText:"Paste or type a link",targetCheckbox:!1,targetCheckboxText:"Open in new window",name:"anchor",action:"createLink",aria:"link",tagNames:["a"],contentDefault:"<b>#</b>",contentFA:'<i class="fa fa-link"></i>',init:function(){b.extensions.form.prototype.init.apply(this,arguments),this.subscribe("editableKeydown",this.handleKeydown.bind(this))},handleClick:function(e){e.preventDefault(),e.stopPropagation();var t=b.selection.getSelectionRange(this.document),n=document.createElement("span");if(n.classList.add("medium-editor-create-link"),"a"===t.startContainer.nodeName.toLowerCase()||"a"===t.endContainer.nodeName.toLowerCase()||b.util.getClosestTag(b.selection.getSelectedParentElement(t),"a"))return this.execAction("unlink");var i=t.startContainer===t.endContainer;return 0<t.startOffset&&-1!==["b","i","em","strong"].indexOf(t.startContainer.parentElement.nodeName.toLowerCase())&&((e=document.createElement(t.startContainer.parentElement.nodeName.toLowerCase())).innerHTML=t.startContainer.textContent.substring(0,t.startOffset),t.startContainer.parentElement.innerHTML=t.startContainer.textContent.substring(t.startOffset),t.startContainer.parentElement.insertBefore(e,t.startContainer)),i||((i=document.createElement("span")).appendChild(t.extractContents()),t.insertNode(i)),window.safari||t.surroundContents(n),this.isDisplayed()||this.showForm(),!1},handleKeydown:function(e){b.util.isKey(e,b.util.keyCode.K)&&b.util.isMetaCtrlKey(e)&&!e.shiftKey&&this.handleClick(e)},getForm:function(){return this.form||(this.form=this.createForm()),this.form},getTemplate:function(){var e=['<input type="text" class="medium-editor-toolbar-input" placeholder="',this.placeholderText,'">'];return e.push('<a href="#" class="medium-editor-toolbar-save">',"fontawesome"===this.getEditorOption("buttonLabels")?'<i class="fa fa-check"></i>':this.formSaveLabel,"</a>"),e.push('<a href="#" class="medium-editor-toolbar-close">',"fontawesome"===this.getEditorOption("buttonLabels")?'<i class="fa fa-times"></i>':this.formCloseLabel,"</a>"),this.targetCheckbox&&e.push('<div class="medium-editor-toolbar-form-row">','<input type="checkbox" class="medium-editor-toolbar-anchor-target" id="medium-editor-toolbar-anchor-target-field-'+this.getEditorId()+'">','<label for="medium-editor-toolbar-anchor-target-field-'+this.getEditorId()+'">',this.targetCheckboxText,"</label>","</div>"),this.customClassOption&&e.push('<div class="medium-editor-toolbar-form-row">','<input type="checkbox" class="medium-editor-toolbar-anchor-button">',"<label>",this.customClassOptionText,"</label>","</div>"),e.join("")},isDisplayed:function(){return b.extensions.form.prototype.isDisplayed.apply(this)},hideForm:function(){b.extensions.form.prototype.hideForm.apply(this),this.getInput().value=""},showForm:function(e){var t=this.getInput(),n=this.getAnchorTargetCheckbox(),i=this.getAnchorButtonCheckbox();"string"==typeof(e=e||{value:""})&&(e={value:e}),this.base.saveSelection(),this.hideToolbarDefaultActions(),b.extensions.form.prototype.showForm.apply(this),this.setToolbarPosition(),t.value=e.value,t.focus(),n&&(n.checked="_blank"===e.target),i&&(e=e.buttonClass?e.buttonClass.split(" "):[],i.checked=-1!==e.indexOf(this.customClassOption))},destroy:function(){if(!this.form)return!1;this.form.parentNode&&this.form.parentNode.removeChild(this.form),delete this.form},getFormOpts:function(){var e=this.getAnchorTargetCheckbox(),t=this.getAnchorButtonCheckbox(),n={value:this.getInput().value.trim()};return this.linkValidation&&(n.value=this.checkLinkFormat(n.value)),n.target="_self",e&&e.checked&&(n.target="_blank"),t&&t.checked&&(n.buttonClass=this.customClassOption),n},doFormSave:function(){var e=this.getFormOpts();this.completeFormSave(e)},completeFormSave:function(e){this.base.restoreSelection(),this.execAction(this.action,e),this.base.checkSelection();this.base.elements[0].innerHTML=this.base.elements[0].innerHTML.replace(/<([a-zA-Z]+)[^>]*><\/\1>/g,"")},ensureEncodedUri:function(e){return e===decodeURI(e)?encodeURI(e):e},ensureEncodedUriComponent:function(e){return e===decodeURIComponent(e)?encodeURIComponent(e):e},ensureEncodedParam:function(e){var t=e.split("="),e=t[0],t=t[1];return e+(void 0===t?"":"="+this.ensureEncodedUriComponent(t))},ensureEncodedQuery:function(e){return e.split("&").map(this.ensureEncodedParam.bind(this)).join("&")},checkLinkFormat:function(e){var t=/^([a-z]+:)?\/\/|^(mailto|tel|maps):|^\#/i.test(e),n="",i=e.match(/^(.*?)(?:\?(.*?))?(?:#(.*))?$/),o=i[1],s=i[2],i=i[3];return/^\+?\s?\(?(?:\d\s?\-?\)?){3,20}$/.test(e)?"tel:"+e:(t||(!(t=o.split("/")[0]).match(/.+(\.|:).+/)&&"localhost"!==t||(n="http://")),n+this.ensureEncodedUri(o)+(void 0===s?"":"?"+this.ensureEncodedQuery(s))+(void 0===i?"":"#"+i))},doFormCancel:function(){this.base.restoreSelection(),this.base.checkSelection()},attachFormEvents:function(e){var t=e.querySelector(".medium-editor-toolbar-close"),n=e.querySelector(".medium-editor-toolbar-save"),i=e.querySelector(".medium-editor-toolbar-input");this.on(e,"click",this.handleFormClick.bind(this)),this.on(i,"keyup",this.handleTextboxKeyup.bind(this)),this.on(t,"click",this.handleCloseClick.bind(this)),this.on(n,"click",this.handleSaveClick.bind(this),!0)},createForm:function(){var e=this.document.createElement("div");return e.className="medium-editor-toolbar-form",e.id="medium-editor-toolbar-form-anchor-"+this.getEditorId(),e.innerHTML=this.getTemplate(),this.attachFormEvents(e),e},getInput:function(){return this.getForm().querySelector("input.medium-editor-toolbar-input")},getAnchorTargetCheckbox:function(){return this.getForm().querySelector(".medium-editor-toolbar-anchor-target")},getAnchorButtonCheckbox:function(){return this.getForm().querySelector(".medium-editor-toolbar-anchor-button")},clearPreCreateLink:function(){jQuery(".medium-editor-create-link").replaceWith(function(){return this.childNodes})},handleTextboxKeyup:function(e){if(e.keyCode===b.util.keyCode.ENTER)return e.preventDefault(),void this.doFormSave();e.keyCode===b.util.keyCode.ESCAPE&&(e.preventDefault(),this.doFormCancel())},handleFormClick:function(e){e.stopPropagation()},handleSaveClick:function(e){var t=e.target.closest(".medium-editor-toolbar-form").querySelector(".medium-editor-toolbar-input");if(t.classList.contains("isNotValid"))return t.classList.add("validate"),!1;t.classList.remove("validate"),this.clearPreCreateLink(),e.preventDefault(),this.doFormSave()},handleCloseClick:function(e){e.target.closest(".medium-editor-toolbar-form").querySelector(".medium-editor-toolbar-input").classList.remove("validate"),e.preventDefault(),this.doFormCancel(),this.clearPreCreateLink()}}),b.extensions.anchor=h,h=b.Extension.extend({name:"anchor-preview",hideDelay:500,previewValueSelector:"a",showWhenToolbarIsVisible:!1,showOnEmptyLinks:!0,init:function(){this.anchorPreview=this.createPreview(),this.getEditorOption("elementsContainer").appendChild(this.anchorPreview),this.attachToEditables()},getInteractionElements:function(){return this.getPreviewElement()},getPreviewElement:function(){return this.anchorPreview},createPreview:function(){var e=this.document.createElement("div");return e.id="medium-editor-anchor-preview-"+this.getEditorId(),e.className="medium-editor-anchor-preview",e.innerHTML=this.getTemplate(),this.on(e,"click",this.handleClick.bind(this)),e},getTemplate:function(){return'<div class="medium-editor-toolbar-anchor-preview" id="medium-editor-toolbar-anchor-preview">    <a class="medium-editor-toolbar-anchor-preview-inner"></a></div>'},destroy:function(){this.anchorPreview&&(this.anchorPreview.parentNode&&this.anchorPreview.parentNode.removeChild(this.anchorPreview),delete this.anchorPreview)},hidePreview:function(){this.anchorPreview&&this.anchorPreview.classList.remove("medium-editor-anchor-preview-active"),this.activeAnchor=null},showPreview:function(e){return!(!this.anchorPreview.classList.contains("medium-editor-anchor-preview-active")&&!e.getAttribute("data-disable-preview"))||(this.previewValueSelector&&(this.anchorPreview.querySelector(this.previewValueSelector).textContent=e.attributes.href.value,this.anchorPreview.querySelector(this.previewValueSelector).href=e.attributes.href.value),this.anchorPreview.classList.add("medium-toolbar-arrow-over"),this.anchorPreview.classList.remove("medium-toolbar-arrow-under"),this.anchorPreview.classList.contains("medium-editor-anchor-preview-active")||this.anchorPreview.classList.add("medium-editor-anchor-preview-active"),this.activeAnchor=e,this.positionPreview(),this.attachPreviewHandlers(),this)},positionPreview:function(e){e=e||this.activeAnchor;var t,n=this.window.innerWidth,i=this.anchorPreview.offsetHeight,o=e.getBoundingClientRect(),s=this.diffLeft,r=this.diffTop,a=this.getEditorOption("elementsContainer"),l=-1<["absolute","fixed"].indexOf(window.getComputedStyle(a).getPropertyValue("position")),c={},d=this.anchorPreview.offsetWidth/2,e=this.base.getExtensionByName("toolbar");e&&(s=e.diffLeft,r=e.diffTop),s=s-d,l=l?(t=a.getBoundingClientRect(),["top","left"].forEach(function(e){c[e]=o[e]-t[e]}),c.width=o.width,c.height=o.height,o=c,n=t.width,a.scrollTop):this.window.pageYOffset,a=o.left+o.width/2,l+=i+o.top+o.height-r-this.anchorPreview.offsetHeight,this.anchorPreview.style.top=Math.round(l)+"px",this.anchorPreview.style.right="initial",a<d?(this.anchorPreview.style.left=s+d+"px",this.anchorPreview.style.right="initial"):n-a<d?(this.anchorPreview.style.left="auto",this.anchorPreview.style.right=0):(this.anchorPreview.style.left=s+a+"px",this.anchorPreview.style.right="initial")},attachToEditables:function(){this.subscribe("editableMouseover",this.handleEditableMouseover.bind(this)),this.subscribe("positionedToolbar",this.handlePositionedToolbar.bind(this))},handlePositionedToolbar:function(){this.showWhenToolbarIsVisible||this.hidePreview()},handleClick:function(e){var t=this.base.getExtensionByName("anchor"),n=this.activeAnchor;t&&n&&(e.preventDefault(),this.base.selectElement(this.activeAnchor),this.base.delay(function(){var e;n&&(e={value:n.attributes.href.value,target:n.getAttribute("target"),buttonClass:n.getAttribute("class")},t.showForm(e),n=null)}.bind(this))),this.hidePreview()},handleAnchorMouseout:function(){this.anchorToPreview=null,this.off(this.activeAnchor,"mouseout",this.instanceHandleAnchorMouseout),this.instanceHandleAnchorMouseout=null},handleEditableMouseover:function(e){var t=b.util.getClosestTag(e.target,"a");if(!1!==t){if(!this.showOnEmptyLinks&&(!/href=["']\S+["']/.test(t.outerHTML)||/href=["']#\S+["']/.test(t.outerHTML)))return!0;e=this.base.getExtensionByName("toolbar");if(!this.showWhenToolbarIsVisible&&e&&e.isDisplayed&&e.isDisplayed())return!0;this.activeAnchor&&this.activeAnchor!==t&&this.detachPreviewHandlers(),this.anchorToPreview=t,this.instanceHandleAnchorMouseout=this.handleAnchorMouseout.bind(this),this.on(this.anchorToPreview,"mouseout",this.instanceHandleAnchorMouseout),this.base.delay(function(){this.anchorToPreview&&this.showPreview(this.anchorToPreview)}.bind(this))}},handlePreviewMouseover:function(){this.lastOver=(new Date).getTime(),this.hovering=!0},handlePreviewMouseout:function(e){e.relatedTarget&&/anchor-preview/.test(e.relatedTarget.className)||(this.hovering=!1)},updatePreview:function(){if(this.hovering)return!0;(new Date).getTime()-this.lastOver>this.hideDelay&&this.detachPreviewHandlers()},detachPreviewHandlers:function(){clearInterval(this.intervalTimer),this.instanceHandlePreviewMouseover&&(this.off(this.anchorPreview,"mouseover",this.instanceHandlePreviewMouseover),this.off(this.anchorPreview,"mouseout",this.instanceHandlePreviewMouseout),this.activeAnchor&&(this.off(this.activeAnchor,"mouseover",this.instanceHandlePreviewMouseover),this.off(this.activeAnchor,"mouseout",this.instanceHandlePreviewMouseout))),this.hidePreview(),this.hovering=this.instanceHandlePreviewMouseover=this.instanceHandlePreviewMouseout=null},attachPreviewHandlers:function(){this.lastOver=(new Date).getTime(),this.hovering=!0,this.instanceHandlePreviewMouseover=this.handlePreviewMouseover.bind(this),this.instanceHandlePreviewMouseout=this.handlePreviewMouseout.bind(this),this.intervalTimer=setInterval(this.updatePreview.bind(this),200),this.on(this.anchorPreview,"mouseover",this.instanceHandlePreviewMouseover),this.on(this.anchorPreview,"mouseout",this.instanceHandlePreviewMouseout),this.on(this.activeAnchor,"mouseover",this.instanceHandlePreviewMouseover),this.on(this.activeAnchor,"mouseout",this.instanceHandlePreviewMouseout)}}),b.extensions.anchorPreview=h,r=[" ","\t","\n","\r"," "," "," "," "," ","\u2028","\u2029"],h="com|net|org|edu|gov|mil|aero|asia|biz|cat|coop|info|int|jobs|mobi|museum|name|post|pro|tel|travel|xxx|ac|ad|ae|af|ag|ai|al|am|an|ao|aq|ar|as|at|au|aw|ax|az|ba|bb|bd|be|bf|bg|bh|bi|bj|bm|bn|bo|br|bs|bt|bv|bw|by|bz|ca|cc|cd|cf|cg|ch|ci|ck|cl|cm|cn|co|cr|cs|cu|cv|cx|cy|cz|dd|de|dj|dk|dm|do|dz|ec|ee|eg|eh|er|es|et|eu|fi|fj|fk|fm|fo|fr|ga|gb|gd|ge|gf|gg|gh|gi|gl|gm|gn|gp|gq|gr|gs|gt|gu|gw|gy|hk|hm|hn|hr|ht|hu|id|ie|il|im|in|io|iq|ir|is|it|je|jm|jo|jp|ke|kg|kh|ki|km|kn|kp|kr|kw|ky|kz|la|lb|lc|li|lk|lr|ls|lt|lu|lv|ly|ma|mc|md|me|mg|mh|mk|ml|mm|mn|mo|mp|mq|mr|ms|mt|mu|mv|mw|mx|my|mz|na|nc|ne|nf|ng|ni|nl|no|np|nr|nu|nz|om|pa|pe|pf|pg|ph|pk|pl|pm|pn|pr|ps|pt|pw|py|qa|re|ro|rs|ru|rw|sa|sb|sc|sd|se|sg|sh|si|sj|ja|sk|sl|sm|sn|so|sr|ss|st|su|sv|sx|sy|sz|tc|td|tf|tg|th|tj|tk|tl|tm|tn|to|tp|tr|tt|tv|tw|tz|ua|ug|uk|us|uy|uz|va|vc|ve|vg|vi|vn|vu|wf|ws|ye|yt|yu|za|zm|zw",a=new RegExp("^("+h+")$","i"),l=new RegExp("(((?:(https?://|ftps?://|nntp://)|www\\d{0,3}[.]|[a-z0-9.\\-]+[.](com|net|org|edu|gov|mil|aero|asia|biz|cat|coop|info|int|jobs|mobi|museum|name|post|pro|tel|travel|xxx|ac|ad|ae|af|ag|ai|al|am|an|ao|aq|ar|as|at|au|aw|ax|az|ba|bb|bd|be|bf|bg|bh|bi|bj|bm|bn|bo|br|bs|bt|bv|bw|by|bz|ca|cc|cd|cf|cg|ch|ci|ck|cl|cm|cn|co|cr|cs|cu|cv|cx|cy|cz|dd|de|dj|dk|dm|do|dz|ec|ee|eg|eh|er|es|et|eu|fi|fj|fk|fm|fo|fr|ga|gb|gd|ge|gf|gg|gh|gi|gl|gm|gn|gp|gq|gr|gs|gt|gu|gw|gy|hk|hm|hn|hr|ht|hu|id|ie|il|im|in|io|iq|ir|is|it|je|jm|jo|jp|ke|kg|kh|ki|km|kn|kp|kr|kw|ky|kz|la|lb|lc|li|lk|lr|ls|lt|lu|lv|ly|ma|mc|md|me|mg|mh|mk|ml|mm|mn|mo|mp|mq|mr|ms|mt|mu|mv|mw|mx|my|mz|na|nc|ne|nf|ng|ni|nl|no|np|nr|nu|nz|om|pa|pe|pf|pg|ph|pk|pl|pm|pn|pr|ps|pt|pw|py|qa|re|ro|rs|ru|rw|sa|sb|sc|sd|se|sg|sh|si|sj|ja|sk|sl|sm|sn|so|sr|ss|st|su|sv|sx|sy|sz|tc|td|tf|tg|th|tj|tk|tl|tm|tn|to|tp|tr|tt|tv|tw|tz|ua|ug|uk|us|uy|uz|va|vc|ve|vg|vi|vn|vu|wf|ws|ye|yt|yu|za|zm|zw)\\/)\\S+(?:[^\\s`!\\[\\]{};:'\".,?«»“”‘’])))|(([a-z0-9\\-]+\\.)?[a-z0-9\\-]+\\.(com|net|org|edu|gov|mil|aero|asia|biz|cat|coop|info|int|jobs|mobi|museum|name|post|pro|tel|travel|xxx|ac|ad|ae|af|ag|ai|al|am|an|ao|aq|ar|as|at|au|aw|ax|az|ba|bb|bd|be|bf|bg|bh|bi|bj|bm|bn|bo|br|bs|bt|bv|bw|by|bz|ca|cc|cd|cf|cg|ch|ci|ck|cl|cm|cn|co|cr|cs|cu|cv|cx|cy|cz|dd|de|dj|dk|dm|do|dz|ec|ee|eg|eh|er|es|et|eu|fi|fj|fk|fm|fo|fr|ga|gb|gd|ge|gf|gg|gh|gi|gl|gm|gn|gp|gq|gr|gs|gt|gu|gw|gy|hk|hm|hn|hr|ht|hu|id|ie|il|im|in|io|iq|ir|is|it|je|jm|jo|jp|ke|kg|kh|ki|km|kn|kp|kr|kw|ky|kz|la|lb|lc|li|lk|lr|ls|lt|lu|lv|ly|ma|mc|md|me|mg|mh|mk|ml|mm|mn|mo|mp|mq|mr|ms|mt|mu|mv|mw|mx|my|mz|na|nc|ne|nf|ng|ni|nl|no|np|nr|nu|nz|om|pa|pe|pf|pg|ph|pk|pl|pm|pn|pr|ps|pt|pw|py|qa|re|ro|rs|ru|rw|sa|sb|sc|sd|se|sg|sh|si|sj|ja|sk|sl|sm|sn|so|sr|ss|st|su|sv|sx|sy|sz|tc|td|tf|tg|th|tj|tk|tl|tm|tn|to|tp|tr|tt|tv|tw|tz|ua|ug|uk|us|uy|uz|va|vc|ve|vg|vi|vn|vu|wf|ws|ye|yt|yu|za|zm|zw))","gi"),h=b.Extension.extend({init:function(){b.Extension.prototype.init.apply(this,arguments),this.disableEventHandling=!1,this.subscribe("editableKeypress",this.onKeypress.bind(this)),this.subscribe("editableBlur",this.onBlur.bind(this)),this.document.execCommand("AutoUrlDetect",!1,!1)},isLastInstance:function(){for(var e=0,t=0;t<this.window._mediumEditors.length;t++){var n=this.window._mediumEditors[t];null!==n&&void 0!==n.getExtensionByName("autoLink")&&e++}return 1===e},destroy:function(){this.document.queryCommandSupported("AutoUrlDetect")&&this.isLastInstance()&&this.document.execCommand("AutoUrlDetect",!1,!0)},onBlur:function(e,t){this.performLinking(t)},onKeypress:function(t){this.disableEventHandling||b.util.isKey(t,[b.util.keyCode.SPACE,b.util.keyCode.ENTER])&&(clearTimeout(this.performLinkingTimeout),this.performLinkingTimeout=setTimeout(function(){try{var e=this.base.exportSelection();this.performLinking(t.target)&&this.base.importSelection(e,!0)}catch(e){window.console&&window.console.error("Failed to perform linking",e),this.disableEventHandling=!0}}.bind(this),0))},performLinking:function(e){var t=b.util.splitByBlockElements(e),n=!1;0===t.length&&(t=[e]);for(var i=0;i<t.length;i++)n=this.removeObsoleteAutoLinkSpans(t[i])||n,n=this.performLinkingWithinElement(t[i])||n;return this.base.events.updateInput(e,{target:e,currentTarget:e}),n},removeObsoleteAutoLinkSpans:function(e){if(!e||3===e.nodeType)return!1;for(var t=e.querySelectorAll('span[data-auto-link="true"]'),n=!1,i=0;i<t.length;i++){var o,s=t[i].textContent;-1===s.indexOf("://")&&(s=b.util.ensureUrlHasProtocol(s)),t[i].getAttribute("data-href")===s||(o=t[i],b.util.getClosestTag(o,"a"))||(n=!0,o=s.replace(/\s+$/,""),t[i].getAttribute("data-href")===o?(o=s.length-o.length,o=b.util.splitOffDOMTree(t[i],this.splitTextBeforeEnd(t[i],o)),t[i].parentNode.insertBefore(o,t[i].nextSibling)):b.util.unwrap(t[i],this.document))}return n},splitTextBeforeEnd:function(e,t){for(var n,i,o,s=this.document.createTreeWalker(e,NodeFilter.SHOW_TEXT,null,!1),r=!0;r;)r=null!==s.lastChild();for(;0<t&&null!==o;)(i=(n=s.currentNode).nodeValue).length>t?(o=n.splitText(i.length-t),t=0):(o=s.previousNode(),t-=i.length);return o},performLinkingWithinElement:function(e){for(var t=this.findLinkableText(e),n=0;n<t.length;n++){var i=b.util.findOrCreateMatchingTextNodes(this.document,e,t[n]);this.shouldNotLink(i)||this.createAutoLink(i,t[n].href)}return!1},shouldNotLink:function(e){for(var t=!1,n=0;n<e.length&&!1===t;n++)t=!!b.util.traverseUp(e[n],function(e){return"a"===e.nodeName.toLowerCase()||e.getAttribute&&"true"===e.getAttribute("data-auto-link")});return t},findLinkableText:function(e){for(var t=e.textContent,n=null,i=[];null!==(n=l.exec(t));){var o=n.index+n[0].length;!(0!==n.index&&-1===r.indexOf(t[n.index-1])||o!==t.length&&-1===r.indexOf(t[o]))&&(-1!==n[0].indexOf("/")||a.test(n[0].split(".").pop().split("?").shift()))&&i.push({href:n[0],start:n.index,end:o})}return i},createAutoLink:function(e,t){t=b.util.ensureUrlHasProtocol(t);var n=b.util.createLink(this.document,e,t,this.getEditorOption("targetBlank")?"_blank":null),i=this.document.createElement("span");for(i.setAttribute("data-auto-link","true"),i.setAttribute("data-href",t),n.insertBefore(i,n.firstChild);1<n.childNodes.length;)i.appendChild(n.childNodes[1])}}),b.extensions.autoLink=h,i="medium-editor-dragover",h=b.Extension.extend({name:"fileDragging",allowedTypes:["image"],init:function(){b.Extension.prototype.init.apply(this,arguments),this.subscribe("editableDrag",this.handleDrag.bind(this)),this.subscribe("editableDrop",this.handleDrop.bind(this))},handleDrag:function(e){e.preventDefault(),e.dataTransfer.dropEffect="copy";var t=e.target.classList?e.target:e.target.parentElement;f(t),"dragover"===e.type&&t.classList.add(i)},handleDrop:function(e){e.preventDefault(),e.stopPropagation(),this.base.selectElement(e.target);var t=this.base.exportSelection();t.start=t.end,this.base.importSelection(t),e.dataTransfer.files&&Array.prototype.slice.call(e.dataTransfer.files).forEach(function(e){this.isAllowedFile(e)&&e.type.match("image")&&this.insertImageFile(e)},this),f(e.target)},isAllowedFile:function(t){return this.allowedTypes.some(function(e){return!!t.type.match(e)})},insertImageFile:function(e){var t;"function"==typeof FileReader&&((t=new FileReader).readAsDataURL(e),t.addEventListener("load",function(e){var t=this.document.createElement("img");t.src=e.target.result,b.util.insertHTMLCommand(this.document,t.outerHTML)}.bind(this)))}}),b.extensions.fileDragging=h,h=b.Extension.extend({name:"keyboard-commands",commands:[{command:"bold",key:"B",meta:!0,shift:!1,alt:!1},{command:"italic",key:"I",meta:!0,shift:!1,alt:!1},{command:"underline",key:"U",meta:!0,shift:!1,alt:!1}],init:function(){b.Extension.prototype.init.apply(this,arguments),this.subscribe("editableKeydown",this.handleKeydown.bind(this)),this.keys={},this.commands.forEach(function(e){var t=e.key.charCodeAt(0);this.keys[t]||(this.keys[t]=[]),this.keys[t].push(e)},this)},handleKeydown:function(t){var n,i,o,e=b.util.getKeyCode(t);this.keys[e]&&(n=b.util.isMetaCtrlKey(t),i=!!t.shiftKey,o=!!t.altKey,this.keys[e].forEach(function(e){e.meta!==n||e.shift!==i||e.alt!==o&&void 0!==e.alt||(t.preventDefault(),t.stopPropagation(),"function"==typeof e.command?e.command.apply(this):!1!==e.command&&this.execAction(e.command))},this))}}),b.extensions.keyboardCommands=h,h=b.extensions.form.extend({name:"fontname",action:"fontName",aria:"change font name",contentDefault:"&#xB1;",contentFA:'<i class="fa fa-font"></i>',fonts:["","Arial","Verdana","Times New Roman"],init:function(){b.extensions.form.prototype.init.apply(this,arguments)},handleClick:function(e){return e.preventDefault(),e.stopPropagation(),this.isDisplayed()||(e=this.document.queryCommandValue("fontName")+"",this.showForm(e)),!1},getForm:function(){return this.form||(this.form=this.createForm()),this.form},isDisplayed:function(){return"block"===this.getForm().style.display},hideForm:function(){this.getForm().style.display="none",this.getSelect().value=""},showForm:function(e){var t=this.getSelect();this.base.saveSelection(),this.hideToolbarDefaultActions(),this.getForm().style.display="block",this.setToolbarPosition(),t.value=e||"",t.focus()},destroy:function(){if(!this.form)return!1;this.form.parentNode&&this.form.parentNode.removeChild(this.form),delete this.form},doFormSave:function(){this.base.restoreSelection(),this.base.checkSelection()},doFormCancel:function(){this.base.restoreSelection(),this.clearFontName(),this.base.checkSelection()},createForm:function(){var e,t=this.document,n=t.createElement("div"),i=t.createElement("select"),o=t.createElement("a"),s=t.createElement("a");n.className="medium-editor-toolbar-form",n.id="medium-editor-toolbar-form-fontname-"+this.getEditorId(),this.on(n,"click",this.handleFormClick.bind(this));for(var r=0;r<this.fonts.length;r++)(e=t.createElement("option")).innerHTML=this.fonts[r],e.value=this.fonts[r],i.appendChild(e);return i.className="medium-editor-toolbar-select",n.appendChild(i),this.on(i,"change",this.handleFontChange.bind(this)),s.setAttribute("href","#"),s.className="medium-editor-toobar-save",s.innerHTML="fontawesome"===this.getEditorOption("buttonLabels")?'<i class="fa fa-check"></i>':"&#10003;",n.appendChild(s),this.on(s,"click",this.handleSaveClick.bind(this),!0),o.setAttribute("href","#"),o.className="medium-editor-toobar-close",o.innerHTML="fontawesome"===this.getEditorOption("buttonLabels")?'<i class="fa fa-times"></i>':"&times;",n.appendChild(o),this.on(o,"click",this.handleCloseClick.bind(this)),n},getSelect:function(){return this.getForm().querySelector("select.medium-editor-toolbar-select")},clearFontName:function(){b.selection.getSelectedElements(this.document).forEach(function(e){"font"===e.nodeName.toLowerCase()&&e.hasAttribute("face")&&e.removeAttribute("face")})},handleFontChange:function(){var e=this.getSelect().value;""===e?this.clearFontName():this.execAction("fontName",{value:e})},handleFormClick:function(e){e.stopPropagation()},handleSaveClick:function(e){e.preventDefault(),this.doFormSave()},handleCloseClick:function(e){e.preventDefault(),this.doFormCancel()}}),b.extensions.fontName=h,h=b.extensions.form.extend({name:"fontsize",action:"fontSize",aria:"increase/decrease font size",contentDefault:"&#xB1;",contentFA:'<i class="fa fa-text-height"></i>',init:function(){b.extensions.form.prototype.init.apply(this,arguments)},handleClick:function(e){return e.preventDefault(),e.stopPropagation(),this.isDisplayed()||(e=this.document.queryCommandValue("fontSize")+"",this.showForm(e)),!1},getForm:function(){return this.form||(this.form=this.createForm()),this.form},isDisplayed:function(){return"block"===this.getForm().style.display},hideForm:function(){this.getForm().style.display="none",this.getInput().value=""},showForm:function(e){var t=this.getInput();this.base.saveSelection(),this.hideToolbarDefaultActions(),this.getForm().style.display="block",this.setToolbarPosition(),t.value=e||"",t.focus()},destroy:function(){if(!this.form)return!1;this.form.parentNode&&this.form.parentNode.removeChild(this.form),delete this.form},doFormSave:function(){this.base.restoreSelection(),this.base.checkSelection()},doFormCancel:function(){this.base.restoreSelection(),this.clearFontSize(),this.base.checkSelection()},createForm:function(){var e=this.document,t=e.createElement("div"),n=e.createElement("input"),i=e.createElement("a"),e=e.createElement("a");return t.className="medium-editor-toolbar-form",t.id="medium-editor-toolbar-form-fontsize-"+this.getEditorId(),this.on(t,"click",this.handleFormClick.bind(this)),n.setAttribute("type","range"),n.setAttribute("min","1"),n.setAttribute("max","7"),n.className="medium-editor-toolbar-input",t.appendChild(n),this.on(n,"change",this.handleSliderChange.bind(this)),e.setAttribute("href","#"),e.className="medium-editor-toobar-save",e.innerHTML="fontawesome"===this.getEditorOption("buttonLabels")?'<i class="fa fa-check"></i>':"&#10003;",t.appendChild(e),this.on(e,"click",this.handleSaveClick.bind(this),!0),i.setAttribute("href","#"),i.className="medium-editor-toobar-close",i.innerHTML="fontawesome"===this.getEditorOption("buttonLabels")?'<i class="fa fa-times"></i>':"&times;",t.appendChild(i),this.on(i,"click",this.handleCloseClick.bind(this)),t},getInput:function(){return this.getForm().querySelector("input.medium-editor-toolbar-input")},clearFontSize:function(){b.selection.getSelectedElements(this.document).forEach(function(e){"font"===e.nodeName.toLowerCase()&&e.hasAttribute("size")&&e.removeAttribute("size")})},handleSliderChange:function(){var e=this.getInput().value;"4"===e?this.clearFontSize():this.execAction("fontSize",{value:e})},handleFormClick:function(e){e.stopPropagation()},handleSaveClick:function(e){e.preventDefault(),this.doFormSave()},handleCloseClick:function(e){e.preventDefault(),this.doFormCancel()}}),b.extensions.fontSize=h,o="%ME_PASTEBIN%",u=d=null,h=b.Extension.extend({forcePlainText:!0,cleanPastedHTML:!1,preCleanReplacements:[],cleanReplacements:[],cleanAttrs:["class","style","dir"],cleanTags:["meta"],unwrapTags:[],init:function(){b.Extension.prototype.init.apply(this,arguments),(this.forcePlainText||this.cleanPastedHTML)&&(this.subscribe("editableKeydown",this.handleKeydown.bind(this)),this.getEditorElements().forEach(function(e){this.on(e,"paste",this.handlePaste.bind(this))},this),this.subscribe("addElement",this.handleAddElement.bind(this)))},handleAddElement:function(e,t){this.on(t,"paste",this.handlePaste.bind(this))},destroy:function(){(this.forcePlainText||this.cleanPastedHTML)&&this.removePasteBin()},handlePaste:function(e,t){var n,i;e.defaultPrevented||(v(e)?e.preventDefault():(n=(i=g(e,this.window,this.document))["text/html"],i=i["text/plain"],((n=this.window.clipboardData&&void 0===e.clipboardData&&!n?i:n)||i)&&(e.preventDefault(),this.doPaste(n,i,t))))},doPaste:function(e,t,n){var i,o,s="";if(this.cleanPastedHTML&&e)return this.cleanPaste(e);if(t){if(this.getEditorOption("disableReturn")||n&&n.getAttribute("data-disable-return"))s=b.util.htmlEntities(t);else if(1<(i=t.split(/[\r\n]+/g)).length)for(o=0;o<i.length;o+=1)""!==i[o]&&(s+="<p>"+b.util.htmlEntities(i[o])+"</p>");else s=b.util.htmlEntities(i[0]);b.util.insertHTMLCommand(this.document,s)}},handlePasteBinPaste:function(e){if(e.defaultPrevented)this.removePasteBin();else if(!v(e)){var t=g(e,this.window,this.document),n=t["text/html"],i=t["text/plain"],o=u;if(!this.cleanPastedHTML||n)return e.preventDefault(),this.removePasteBin(),this.doPaste(n,i,o),void this.trigger("editablePaste",{currentTarget:o,target:o},o);setTimeout(function(){this.cleanPastedHTML&&(n=this.getPasteBinHtml()),this.removePasteBin(),this.doPaste(n,i,o),this.trigger("editablePaste",{currentTarget:o,target:o},o)}.bind(this),0)}},handleKeydown:function(e,t){b.util.isKey(e,b.util.keyCode.V)&&b.util.isMetaCtrlKey(e)&&(e.stopImmediatePropagation(),this.removePasteBin(),this.createPasteBin(t))},createPasteBin:function(e){var t=b.selection.getSelectionRange(this.document),n=this.window.pageYOffset;u=e,t&&((e=t.getClientRects()).length?n+=e[0].top:void 0!==t.startContainer.getBoundingClientRect?n+=t.startContainer.getBoundingClientRect().top:n+=t.getBoundingClientRect().top),d=t;t=this.document.createElement("div");t.id=this.pasteBinId="medium-editor-pastebin-"+ +Date.now(),t.setAttribute("style","border: 1px red solid; position: absolute; top: "+n+"px; width: 10px; height: 10px; overflow: hidden; opacity: 0"),t.setAttribute("contentEditable",!0),t.innerHTML=o,this.document.body.appendChild(t),this.on(t,"focus",p),this.on(t,"focusin",p),this.on(t,"focusout",p),t.focus(),b.selection.selectNode(t,this.document),this.boundHandlePaste||(this.boundHandlePaste=this.handlePasteBinPaste.bind(this)),this.on(t,"paste",this.boundHandlePaste)},removePasteBin:function(){null!==d&&(b.selection.selectRange(this.document,d),d=null),null!==u&&(u=null);var e=this.getPasteBin();e&&(this.off(e,"focus",p),this.off(e,"focusin",p),this.off(e,"focusout",p),this.off(e,"paste",this.boundHandlePaste),e.parentElement.removeChild(e))},getPasteBin:function(){return this.document.getElementById(this.pasteBinId)},getPasteBinHtml:function(){var e=this.getPasteBin();if(!e)return!1;if(e.firstChild&&"mcepastebin"===e.firstChild.id)return!1;e=e.innerHTML;return!(!e||e===o)&&e},cleanPaste:function(e){for(var t,n,i=/<p|<br|<div/.test(e),o=[].concat(this.preCleanReplacements||[],[[new RegExp(/^[\s\S]*<body[^>]*>\s*|\s*<\/body[^>]*>[\s\S]*$/g),""],[new RegExp(/<!--StartFragment-->|<!--EndFragment-->/g),""],[new RegExp(/<br>$/i),""],[new RegExp(/<[^>]*docs-internal-guid[^>]*>/gi),""],[new RegExp(/<\/b>(<br[^>]*>)?$/gi),""],[new RegExp(/<span class="Apple-converted-space">\s+<\/span>/g)," "],[new RegExp(/<br class="Apple-interchange-newline">/g),"<br>"],[new RegExp(/<span[^>]*(font-style:italic;font-weight:(bold|700)|font-weight:(bold|700);font-style:italic)[^>]*>/gi),'<span class="replace-with italic bold">'],[new RegExp(/<span[^>]*font-style:italic[^>]*>/gi),'<span class="replace-with italic">'],[new RegExp(/<span[^>]*font-weight:(bold|700)[^>]*>/gi),'<span class="replace-with bold">'],[new RegExp(/&lt;(\/?)(i|b|a)&gt;/gi),"<$1$2>"],[new RegExp(/&lt;a(?:(?!href).)+href=(?:&quot;|&rdquo;|&ldquo;|"|“|”)(((?!&quot;|&rdquo;|&ldquo;|"|“|”).)*)(?:&quot;|&rdquo;|&ldquo;|"|“|”)(?:(?!&gt;).)*&gt;/gi),'<a href="$1">'],[new RegExp(/<\/p>\n+/gi),"</p>"],[new RegExp(/\n+<p/gi),"<p"],[new RegExp(/<\/?o:[a-z]*>/gi),""],[new RegExp(/<!\[if !supportLists\]>(((?!<!).)*)<!\[endif]\>/gi),"$1"]],this.cleanReplacements||[]),s=0;s<o.length;s+=1)e=e.replace(o[s][0],o[s][1]);if(!i)return this.pasteHTML(e);for((i=this.document.createElement("div")).innerHTML="<p>"+e.split("<br><br>").join("</p><p>")+"</p>",t=i.querySelectorAll("a,p,div,br"),s=0;s<t.length;s+=1)switch((n=t[s]).innerHTML=n.innerHTML.replace(/\n/gi," "),n.nodeName.toLowerCase()){case"p":case"div":this.filterCommonBlocks(n);break;case"br":this.filterLineBreak(n)}this.pasteHTML(i.innerHTML)},pasteHTML:function(e,t){t=b.util.defaults({},t,{cleanAttrs:this.cleanAttrs,cleanTags:this.cleanTags,unwrapTags:this.unwrapTags});var n,i,o,s=this.document.createDocumentFragment();for(s.appendChild(this.document.createElement("body")),s=s.querySelector("body"),e=e.replace(/<\!--.*?-->/g,""),s.innerHTML=e,this.cleanupSpans(s),n=s.querySelectorAll("*"),o=0;o<n.length;o+=1)"a"===(i=n[o]).nodeName.toLowerCase()&&this.getEditorOption("targetBlank")&&b.util.setTargetBlank(i),b.util.cleanupAttrs(i,t.cleanAttrs),b.util.cleanupTags(i,t.cleanTags),b.util.unwrapTags(i,t.unwrapTags);b.util.insertHTMLCommand(this.document,s.innerHTML.replace(/&nbsp;/g," "))},isCommonBlock:function(e){return e&&("p"===e.nodeName.toLowerCase()||"div"===e.nodeName.toLowerCase())},filterCommonBlocks:function(e){/^\s*$/.test(e.textContent)&&e.parentNode&&e.parentNode.removeChild(e)},filterLineBreak:function(e){(this.isCommonBlock(e.previousElementSibling)||this.isCommonBlock(e.parentNode)&&(e.parentNode.firstChild===e||e.parentNode.lastChild===e)||e.parentNode&&1===e.parentNode.childElementCount&&""===e.parentNode.textContent)&&this.removeWithParent(e)},removeWithParent:function(e){e&&e.parentNode&&(e.parentNode.parentNode&&1===e.parentNode.childElementCount?e.parentNode.parentNode.removeChild(e.parentNode):e.parentNode.removeChild(e))},cleanupSpans:function(e){for(var t,n,i=e.querySelectorAll(".replace-with"),o=function(e){return e&&"#text"!==e.nodeName&&"false"===e.getAttribute("contenteditable")},s=0;s<i.length;s+=1)t=i[s],n=this.document.createElement(t.classList.contains("bold")?"b":"i"),t.classList.contains("bold")&&t.classList.contains("italic")?n.innerHTML="<i>"+t.innerHTML+"</i>":n.innerHTML=t.innerHTML,t.parentNode.replaceChild(n,t);for(i=e.querySelectorAll("span"),s=0;s<i.length;s+=1){if(t=i[s],b.util.traverseUp(t,o))return!1;b.util.unwrap(t,this.document)}}}),b.extensions.paste=h,h=b.Extension.extend({name:"placeholder",text:"Type your text",hideOnClick:!0,init:function(){b.Extension.prototype.init.apply(this,arguments),this.initPlaceholders(),this.attachEventHandlers()},initPlaceholders:function(){this.getEditorElements().forEach(this.initElement,this)},handleAddElement:function(e,t){this.initElement(t)},initElement:function(e){e.getAttribute("data-placeholder")||e.setAttribute("data-placeholder",this.text),this.updatePlaceholder(e)},destroy:function(){this.getEditorElements().forEach(this.cleanupElement,this)},handleRemoveElement:function(e,t){this.cleanupElement(t)},cleanupElement:function(e){e.getAttribute("data-placeholder")===this.text&&e.removeAttribute("data-placeholder")},showPlaceholder:function(e){e&&(b.util.isFF&&0===e.childNodes.length?(e.classList.add("medium-editor-placeholder-relative"),e.classList.remove("medium-editor-placeholder")):(e.classList.add("medium-editor-placeholder"),e.classList.remove("medium-editor-placeholder-relative")))},hidePlaceholder:function(e){e&&(e.classList.remove("medium-editor-placeholder"),e.classList.remove("medium-editor-placeholder-relative"))},updatePlaceholder:function(e,t){if(e.querySelector("img, blockquote, ul, ol, table")||""!==e.textContent.replace(/^\s+|\s+$/g,""))return this.hidePlaceholder(e);t||this.showPlaceholder(e)},attachEventHandlers:function(){this.hideOnClick&&this.subscribe("focus",this.handleFocus.bind(this)),this.subscribe("editableInput",this.handleInput.bind(this)),this.subscribe("blur",this.handleBlur.bind(this)),this.subscribe("addElement",this.handleAddElement.bind(this)),this.subscribe("removeElement",this.handleRemoveElement.bind(this))},handleInput:function(e,t){var n=this.hideOnClick&&t===this.base.getFocusedElement();this.updatePlaceholder(t,n)},handleFocus:function(e,t){this.hidePlaceholder(t)},handleBlur:function(e,t){this.updatePlaceholder(t)}}),b.extensions.placeholder=h,h=b.Extension.extend({name:"toolbar",align:"center",allowMultiParagraphSelection:!0,buttons:["bold","italic","underline","anchor","h2","h3","quote"],diffLeft:0,diffTop:-10,firstButtonClass:"medium-editor-button-first",lastButtonClass:"medium-editor-button-last",standardizeSelectionStart:!1,static:!1,sticky:!1,stickyTopOffset:0,updateOnEmptySelection:!1,relativeContainer:null,init:function(){b.Extension.prototype.init.apply(this,arguments),this.initThrottledMethods(),(this.relativeContainer||this.getEditorOption("elementsContainer")).appendChild(this.getToolbarElement())},forEachExtension:function(t,n){return this.base.extensions.forEach(function(e){if(e!==this)return t.apply(n||this,arguments)},this)},createToolbar:function(){var t=this.document.createElement("div");return t.id="medium-editor-toolbar-"+this.getEditorId(),t.className="medium-editor-toolbar",this.static?t.className+=" static-toolbar":this.relativeContainer?t.className+=" medium-editor-relative-toolbar":t.className+=" medium-editor-stalker-toolbar",t.appendChild(this.createToolbarButtons()),this.forEachExtension(function(e){e.hasForm&&t.appendChild(e.getForm())}),this.attachEventHandlers(),t},createToolbarButtons:function(){var e,t,n,i,o=this.document.createElement("ul");return o.id="medium-editor-toolbar-actions"+this.getEditorId(),o.className="medium-editor-toolbar-actions",o.style.display="block",this.buttons.forEach(function(e){i="string"==typeof e?(n=e,null):(n=e.name,e),(t=this.base.addBuiltInExtension(n,i))&&"function"==typeof t.getButton&&(i=t.getButton(this.base),t=this.document.createElement("li"),b.util.isElement(i)?t.appendChild(i):t.innerHTML=i,o.appendChild(t))},this),0<(e=o.querySelectorAll("button")).length&&(e[0].classList.add(this.firstButtonClass),e[e.length-1].classList.add(this.lastButtonClass)),o},destroy:function(){this.toolbar&&(this.toolbar.parentNode&&this.toolbar.parentNode.removeChild(this.toolbar),delete this.toolbar)},getInteractionElements:function(){return this.getToolbarElement()},getToolbarElement:function(){return this.toolbar||(this.toolbar=this.createToolbar()),this.toolbar},getToolbarActionsElement:function(){return this.getToolbarElement().querySelector(".medium-editor-toolbar-actions")},initThrottledMethods:function(){this.throttledPositionToolbar=b.util.throttle(function(){this.base.isActive&&this.positionToolbarIfShown()}.bind(this))},attachEventHandlers:function(){this.subscribe("blur",this.handleBlur.bind(this)),this.subscribe("focus",this.handleFocus.bind(this)),this.subscribe("editableClick",this.handleEditableClick.bind(this)),this.subscribe("editableKeyup",this.handleEditableKeyup.bind(this)),this.on(this.document.documentElement,"mouseup",this.handleDocumentMouseup.bind(this)),this.static&&this.sticky&&this.on(this.window,"scroll",this.handleWindowScroll.bind(this),!0),this.on(this.window,"resize",this.handleWindowResize.bind(this))},handleWindowScroll:function(){this.positionToolbarIfShown()},handleWindowResize:function(){this.throttledPositionToolbar()},handleDocumentMouseup:function(e){if(e&&e.target&&b.util.isDescendant(this.getToolbarElement(),e.target))return!1;this.checkState()},handleEditableClick:function(){setTimeout(function(){this.checkState()}.bind(this),0)},handleEditableKeyup:function(){this.checkState()},handleBlur:function(){clearTimeout(this.hideTimeout),clearTimeout(this.delayShowTimeout),this.hideTimeout=setTimeout(function(){this.hideToolbar()}.bind(this),1)},handleFocus:function(){this.checkState()},isDisplayed:function(){return this.getToolbarElement().classList.contains("medium-editor-toolbar-active")},showToolbar:function(){clearTimeout(this.hideTimeout),this.isDisplayed()||(this.getToolbarElement().classList.add("medium-editor-toolbar-active"),this.trigger("showToolbar",{},this.base.getFocusedElement()))},hideToolbar:function(){this.isDisplayed()&&(this.getToolbarElement().classList.remove("medium-editor-toolbar-active"),this.trigger("hideToolbar",{},this.base.getFocusedElement()))},isToolbarDefaultActionsDisplayed:function(){return"block"===this.getToolbarActionsElement().style.display},hideToolbarDefaultActions:function(){this.isToolbarDefaultActionsDisplayed()&&(this.getToolbarActionsElement().style.display="none")},showToolbarDefaultActions:function(){this.hideExtensionForms(),this.isToolbarDefaultActionsDisplayed()||(this.getToolbarActionsElement().style.display="block"),this.delayShowTimeout=this.base.delay(function(){this.showToolbar()}.bind(this))},hideExtensionForms:function(){this.forEachExtension(function(e){e.hasForm&&e.isDisplayed()&&e.hideForm()})},multipleBlockElementsSelected:function(){var e=new RegExp("<("+b.util.blockContainerElementNames.join("|")+")[^>]*>","g"),e=b.selection.getSelectionHtml(this.document).replace(/<[^\/>][^>]*><\/[^>]+>/gim,"").match(e);return!!e&&1<e.length},modifySelection:function(){var e=this.window.getSelection().getRangeAt(0);if(this.standardizeSelectionStart&&e.startContainer.nodeValue&&e.startOffset===e.startContainer.nodeValue.length){var t=b.util.findAdjacentTextNodeWithContent(b.selection.getSelectionElement(this.window),e.startContainer,this.document);if(t){for(var n=0;0===t.nodeValue.substr(n,1).trim().length;)n+=1;b.selection.select(this.document,t,n,e.endContainer,e.endOffset)}}},checkState:function(){if(!this.base.preventSelectionUpdates){if(!this.base.getFocusedElement()||b.selection.selectionInContentEditableFalse(this.window))return this.hideToolbar();var e=b.selection.getSelectionElement(this.window);return!e||-1===this.getEditorElements().indexOf(e)||e.getAttribute("data-disable-toolbar")?this.hideToolbar():this.updateOnEmptySelection&&this.static?this.showAndUpdateToolbar():!b.selection.selectionContainsContent(this.document)||!1===this.allowMultiParagraphSelection&&this.multipleBlockElementsSelected()?this.hideToolbar():void this.showAndUpdateToolbar()}},showAndUpdateToolbar:function(){this.modifySelection(),this.setToolbarButtonStates(),this.trigger("positionToolbar",{},this.base.getFocusedElement()),this.showToolbarDefaultActions(),this.setToolbarPosition()},setToolbarButtonStates:function(){this.forEachExtension(function(e){"function"==typeof e.isActive&&"function"==typeof e.setInactive&&e.setInactive()}),this.checkActiveButtons()},checkActiveButtons:function(){function e(e){"function"==typeof e.checkState?e.checkState(n):"function"==typeof e.isActive&&"function"==typeof e.isAlreadyApplied&&"function"==typeof e.setActive&&!e.isActive()&&e.isAlreadyApplied(n)&&e.setActive()}var t,n,i=[],o=b.selection.getSelectionRange(this.document);if(o&&(this.forEachExtension(function(e){"function"!=typeof e.queryCommandState||null===(t=e.queryCommandState())?i.push(e):t&&"function"==typeof e.setActive&&e.setActive()}),n=b.selection.getSelectedParentElement(o),this.getEditorElements().some(function(e){return b.util.isDescendant(e,n,!0)})))for(;n&&(i.forEach(e),!b.util.isMediumEditorElement(n));)n=n.parentNode},positionToolbarIfShown:function(){this.isDisplayed()&&this.setToolbarPosition()},setToolbarPosition:function(){var e=this.base.getFocusedElement(),t=this.window.getSelection();if(!e)return this;!this.static&&t.isCollapsed||(this.showToolbar(),this.relativeContainer||(this.static?this.positionStaticToolbar(e):this.positionToolbar(t)),this.trigger("positionedToolbar",{},this.base.getFocusedElement()))},positionStaticToolbar:function(e){this.getToolbarElement().style.left="0";var t,n=this.document.documentElement&&this.document.documentElement.scrollTop||this.document.body.scrollTop,i=this.window.innerWidth,o=this.getToolbarElement(),s=e.getBoundingClientRect(),r=s.top+n,a=s.left+s.width/2,l=o.offsetHeight,c=o.offsetWidth,d=c/2;switch(this.sticky?n>r+e.offsetHeight-l-this.stickyTopOffset?(o.style.top=r+e.offsetHeight-l+"px",o.classList.remove("medium-editor-sticky-toolbar")):n>r-l-this.stickyTopOffset?(o.classList.add("medium-editor-sticky-toolbar"),o.style.top=this.stickyTopOffset+"px"):(o.classList.remove("medium-editor-sticky-toolbar"),o.style.top=r-l+"px"):o.style.top=r-l+"px",this.align){case"left":t=s.left;break;case"right":t=s.right-c;break;case"center":t=a-d}t<0?t=0:i<t+c&&(t=i-Math.ceil(c)-1),o.style.left=t+"px"},positionToolbar:function(e){this.getToolbarElement().style.left="0",this.getToolbarElement().style.right="initial";var t=e.getRangeAt(0),n=t.getBoundingClientRect();n&&(0!==n.height||0!==n.width||t.startContainer!==t.endContainer)||(n=(1===t.startContainer.nodeType&&t.startContainer.querySelector("img")?t.startContainer.querySelector("img"):t.startContainer).getBoundingClientRect());var i,o=this.window.innerWidth,s=this.getToolbarElement(),r=s.offsetHeight,a=s.offsetWidth/2,l=this.diffLeft-a,e=this.getEditorOption("elementsContainer"),t=-1<["absolute","fixed"].indexOf(window.getComputedStyle(e).getPropertyValue("position")),c={},d={};t?(i=e.getBoundingClientRect(),["top","left"].forEach(function(e){d[e]=n[e]-i[e]}),d.width=n.width,d.height=n.height,n=d,o=i.width,c.top=e.scrollTop):c.top=this.window.pageYOffset,e=n.left+n.width/2,c.top+=n.top-r,n.top<50?(s.classList.add("medium-toolbar-arrow-over"),s.classList.remove("medium-toolbar-arrow-under"),c.top+=50+n.height-this.diffTop):(s.classList.add("medium-toolbar-arrow-under"),s.classList.remove("medium-toolbar-arrow-over"),c.top+=this.diffTop),e<a?(c.left=l+a,c.right="initial"):o-e<a?(c.left="auto",c.right=0):(c.left=l+e,c.right="initial"),["top","left","right"].forEach(function(e){s.style[e]=c[e]+(isNaN(c[e])?"":"px")})}}),b.extensions.toolbar=h,h=b.Extension.extend({init:function(){b.Extension.prototype.init.apply(this,arguments),this.subscribe("editableDrag",this.handleDrag.bind(this)),this.subscribe("editableDrop",this.handleDrop.bind(this))},handleDrag:function(e){var t="medium-editor-dragover";e.preventDefault(),e.dataTransfer.dropEffect="copy","dragover"===e.type?e.target.classList.add(t):"dragleave"===e.type&&e.target.classList.remove(t)},handleDrop:function(e){e.preventDefault(),e.stopPropagation(),e.dataTransfer.files&&Array.prototype.slice.call(e.dataTransfer.files,0).some(function(e){var t,n;e.type.match("image")&&((n=new FileReader).readAsDataURL(e),t="medium-img-"+ +new Date,b.util.insertHTMLCommand(this.document,'<img class="medium-editor-image-loading" id="'+t+'" />'),n.onload=function(){var e=this.document.getElementById(t);e&&(e.removeAttribute("id"),e.removeAttribute("class"),e.src=n.result)}.bind(this))}.bind(this)),e.target.classList.remove("medium-editor-dragover")}}),b.extensions.imageDragging=h,m={},b.prototype={init:function(e,t){return this.options=function(e,t){return t&&[["allowMultiParagraphSelection","toolbar.allowMultiParagraphSelection"]].forEach(function(e){t.hasOwnProperty(e[0])&&void 0!==t[e[0]]&&b.util.deprecated(e[0],e[1],"v6.0.0")}),b.util.defaults({},t,e)}.call(this,this.defaults,t),this.origElements=e,this.options.elementsContainer||(this.options.elementsContainer=this.options.ownerDocument.body),this.setup()},setup:function(){this.isActive||(!function(e){e._mediumEditors||(e._mediumEditors=[null]),this.id||(this.id=e._mediumEditors.length),e._mediumEditors[this.id]=this}.call(this,this.options.contentWindow),this.events=new b.Events(this),this.elements=[],this.addElements(this.origElements),0!==this.elements.length&&(this.isActive=!0,N.call(this),S.call(this)))},destroy:function(){this.isActive&&(this.isActive=!1,this.extensions.forEach(function(e){"function"==typeof e.destroy&&e.destroy()},this),this.events.destroy(),this.elements.forEach(function(e){this.options.spellcheck&&(e.innerHTML=e.innerHTML),e.removeAttribute("contentEditable"),e.removeAttribute("spellcheck"),e.removeAttribute("data-medium-editor-element"),e.classList.remove("medium-editor-element"),e.removeAttribute("role"),e.removeAttribute("aria-multiline"),e.removeAttribute("medium-editor-index"),e.removeAttribute("data-medium-editor-editor-index"),e.getAttribute("medium-editor-textarea-id")&&w(e)},this),this.elements=[],this.instanceHandleEditableKeydownEnter=null,this.instanceHandleEditableInput=null,function(e){e._mediumEditors&&e._mediumEditors[this.id]&&(e._mediumEditors[this.id]=null)}.call(this,this.options.contentWindow))},on:function(e,t,n,i){return this.events.attachDOMEvent(e,t,n,i),this},off:function(e,t,n,i){return this.events.detachDOMEvent(e,t,n,i),this},subscribe:function(e,t){return this.events.attachCustomEvent(e,t),this},unsubscribe:function(e,t){return this.events.detachCustomEvent(e,t),this},trigger:function(e,t,n){return this.events.triggerCustomEvent(e,t,n),this},delay:function(e){var t=this;return setTimeout(function(){t.isActive&&e()},this.options.delay)},serialize:function(){for(var e={},t=this.elements.length,n=0;n<t;n+=1)e[""!==this.elements[n].id?this.elements[n].id:"element-"+n]={value:this.elements[n].innerHTML.trim()};return e},getExtensionByName:function(t){var n;return this.extensions&&this.extensions.length&&this.extensions.some(function(e){return e.name===t&&(n=e,!0)}),n},addBuiltInExtension:function(e,t){var n,i=this.getExtensionByName(e);if(i)return i;switch(e){case"anchor":n=b.util.extend({},this.options.anchor,t),i=new b.extensions.anchor(n);break;case"anchor-preview":i=new b.extensions.anchorPreview(this.options.anchorPreview);break;case"autoLink":i=new b.extensions.autoLink;break;case"fileDragging":i=new b.extensions.fileDragging(t);break;case"fontname":i=new b.extensions.fontName(this.options.fontName);break;case"fontsize":i=new b.extensions.fontSize(t);break;case"keyboardCommands":i=new b.extensions.keyboardCommands(this.options.keyboardCommands);break;case"paste":i=new b.extensions.paste(this.options.paste);break;case"placeholder":i=new b.extensions.placeholder(this.options.placeholder);break;default:b.extensions.button.isBuiltInButton(e)&&(i=t?(n=b.util.defaults({},t,b.extensions.button.prototype.defaults[e]),new b.extensions.button(n)):new b.extensions.button(e))}return i&&this.extensions.push(x(i,e,this)),i},stopSelectionUpdates:function(){this.preventSelectionUpdates=!0},startSelectionUpdates:function(){this.preventSelectionUpdates=!1},checkSelection:function(){var e=this.getExtensionByName("toolbar");return e&&e.checkState(),this},queryCommandState:function(e){var t=null,n=/^full-(.+)$/gi.exec(e);n&&(e=n[1]);try{t=this.options.ownerDocument.queryCommandState(e)}catch(e){t=null}return t},execAction:function(e,t){var n,i,o,s=/^full-(.+)$/gi.exec(e);return s?(this.saveSelection(),this.selectAllContents(),n=A.call(this,s[1],t),this.restoreSelection()):n=A.call(this,e,t),"insertunorderedlist"!==e&&"insertorderedlist"!==e||(b.util.cleanListDOM(this.options.ownerDocument,this.getSelectedParentElement()),this.getSelectedParentElement().classList.contains("medium-editor-element")?(o=b.selection.getSelectionRange(this.options.ownerDocument),(i=this.options.ownerDocument.createElement("p")).innerHTML="<br>",o=o.endContainer.nextElementSibling?o.endContainer.insertBefore(i,o.endContainer.nextSibling):o.endContainer.appendChild(i),b.selection.moveCursor(this.options.ownerDocument,o),o.previousElementSibling&&"br"===o.previousElementSibling.tagName.toLowerCase()&&o.previousElementSibling.remove()):this.getSelectedParentElement().parentNode.classList.contains("medium-editor-element")&&(i=this.getSelectedParentElement(),o=document.createElement("p"),i.parentNode.insertBefore(o,i),o.appendChild(i),b.selection.moveCursor(this.options.ownerDocument,i,1),i.nextElementSibling&&"br"===i.nextElementSibling.tagName.toLowerCase()&&i.nextElementSibling.remove())),this.checkSelection(),n},getSelectedParentElement:function(e){return void 0===e&&(e=this.options.contentWindow.getSelection().getRangeAt(0)),b.selection.getSelectedParentElement(e)},selectAllContents:function(){var e=b.selection.getSelectionElement(this.options.contentWindow);if(e){for(;1===e.children.length;)e=e.children[0];this.selectElement(e)}},selectElement:function(e){b.selection.selectNode(e,this.options.ownerDocument);e=b.selection.getSelectionElement(this.options.contentWindow);e&&this.events.focusElement(e)},getFocusedElement:function(){var t;return this.elements.some(function(e){return!!(t=!t&&e.getAttribute("data-medium-focused")?e:t)},this),t},exportSelection:function(){var e=b.selection.getSelectionElement(this.options.contentWindow),t=this.elements.indexOf(e),n=null;return null!==(n=0<=t?b.selection.exportSelection(e,this.options.ownerDocument):n)&&0!==t&&(n.editableElementIndex=t),n},saveSelection:function(){this.selectionState=this.exportSelection()},importSelection:function(e,t){var n;e&&(n=this.elements[e.editableElementIndex||0],b.selection.importSelection(e,n,this.options.ownerDocument,t))},restoreSelection:function(){this.importSelection(this.selectionState)},createLink:function(e){var t,n,i,o,s,r,a,l,c,d,u,h,m=b.selection.getSelectionElement(this.options.contentWindow),f={};if(-1!==this.elements.indexOf(m)){try{if(this.events.disableCustomEvent("editableInput"),e.url&&b.util.deprecated(".url option for createLink",".value","6.0.0"),(t=e.url||e.value)&&0<t.trim().length&&((n=this.options.contentWindow.getSelection())&&(o=(i=n.getRangeAt(0)).commonAncestorContainer,3===i.endContainer.nodeType&&3!==i.startContainer.nodeType&&0===i.startOffset&&i.startContainer.firstChild===i.endContainer&&(o=i.endContainer),r=b.util.getClosestBlockContainer(i.startContainer),a=b.util.getClosestBlockContainer(i.endContainer),3!==o.nodeType&&0!==o.textContent.length&&r===a?(l=r||m,c=this.options.ownerDocument.createDocumentFragment(),this.execAction("unlink"),s=this.exportSelection(),c.appendChild(l.cloneNode(!0)),m===l?b.selection.select(this.options.ownerDocument,l.firstChild,0,l.lastChild,(3===l.lastChild.nodeType?l.lastChild.nodeValue:l.lastChild.childNodes).length):b.selection.select(this.options.ownerDocument,l,0,l,l.childNodes.length),d=this.exportSelection(),0===(u=b.util.findOrCreateMatchingTextNodes(this.options.ownerDocument,c,{start:s.start-d.start,end:s.end-d.start,editableElementIndex:s.editableElementIndex})).length&&((c=this.options.ownerDocument.createDocumentFragment()).appendChild(o.cloneNode(!0)),u=[c.firstChild.firstChild,c.firstChild.lastChild]),b.util.createLink(this.options.ownerDocument,u,t.trim()),h=(c.firstChild.innerHTML.match(/^\s+/)||[""])[0].length,b.util.insertHTMLCommand(this.options.ownerDocument,c.firstChild.innerHTML.replace(/^\s+/,"")),s.start-=h,s.end-=h,this.importSelection(s)):this.options.ownerDocument.execCommand("createLink",!1,t),this.options.targetBlank||"_blank"===e.target?b.util.setTargetBlank(b.selection.getSelectionStart(this.options.ownerDocument),t):b.util.removeTargetBlank(b.selection.getSelectionStart(this.options.ownerDocument),t),e.buttonClass&&b.util.addClassToAnchors(b.selection.getSelectionStart(this.options.ownerDocument),e.buttonClass))),this.options.targetBlank||"_blank"===e.target||e.buttonClass){(f=this.options.ownerDocument.createEvent("HTMLEvents")).initEvent("input",!0,!0,this.options.contentWindow);for(var p=0,g=this.elements.length;p<g;p+=1)this.elements[p].dispatchEvent(f)}}finally{this.events.enableCustomEvent("editableInput")}this.events.triggerCustomEvent("editableInput",f,m)}},cleanPaste:function(e){this.getExtensionByName("paste").cleanPaste(e)},pasteHTML:function(e,t){this.getExtensionByName("paste").pasteHTML(e,t)},setContent:function(e,t){this.elements[t=t||0]&&((t=this.elements[t]).innerHTML=e,this.checkContentChanged(t))},getContent:function(e){return this.elements[e=e||0]?this.elements[e].innerHTML.trim():null},checkContentChanged:function(e){e=e||b.selection.getSelectionElement(this.options.contentWindow),this.events.updateInput(e,{target:e,currentTarget:e})},resetContent:function(e){var t;e?-1!==(t=this.elements.indexOf(e))&&this.setContent(m[e.getAttribute("medium-editor-index")],t):this.elements.forEach(function(e,t){this.setContent(m[e.getAttribute("medium-editor-index")],t)},this)},addElements:function(e){e=y(e,this.options.ownerDocument,!0);if(0===e.length)return!1;e.forEach(function(e){e=k.call(this,e,this.id),this.elements.push(e),this.trigger("addElement",{target:e,currentTarget:e},e)},this)},removeElements:function(e){var t=y(e,this.options.ownerDocument).map(function(e){return e.getAttribute("medium-editor-textarea-id")&&e.parentNode?e.parentNode.querySelector('div[medium-editor-textarea-id="'+e.getAttribute("medium-editor-textarea-id")+'"]'):e});this.elements=this.elements.filter(function(e){return-1===t.indexOf(e)||(this.events.cleanupElement(e),e.getAttribute("medium-editor-textarea-id")&&w(e),this.trigger("removeElement",{target:e,currentTarget:e},e),!1)},this)}},b.getEditorFromElement=function(e){var t=e.getAttribute("data-medium-editor-editor-index"),e=e&&e.ownerDocument&&(e.ownerDocument.defaultView||e.ownerDocument.parentWindow);return e&&e._mediumEditors&&e._mediumEditors[t]?e._mediumEditors[t]:null},b.prototype.defaults={activeButtonClass:"medium-editor-button-active",buttonLabels:!1,delay:0,disableReturn:!1,disableDoubleReturn:!1,disableExtraSpaces:!1,disableEditing:!1,autoLink:!1,elementsContainer:!1,contentWindow:window,ownerDocument:document,targetBlank:!1,extensions:{},spellcheck:!0},b.version=(b.parseVersionString=function(e){var e=e.split("-"),t=e[0].split("."),n=1<e.length?e[1]:"";return{major:parseInt(t[0],10),minor:parseInt(t[1],10),revision:parseInt(t[2],10),preRelease:n,toString:function(){return[t[0],t[1],t[2]].join(".")+(n?"-"+n:"")}}}).call(this,"5.23.3"),b}());