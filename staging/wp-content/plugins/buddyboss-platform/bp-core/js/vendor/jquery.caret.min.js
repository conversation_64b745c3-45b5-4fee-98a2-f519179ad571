!function(t,e){"function"==typeof define&&define.amd?define(["jquery"],function(o){return t.returnExportsGlobal=e(o)}):"object"==typeof exports?module.exports=e(require("jquery")):e(jQuery)}(this,function(t){"use strict";var e,o,n,r,i,s,a,l,p;e=function(){function e(t){this.$inputor=t,this.domInputor=this.$inputor[0]}return e.prototype.setPos=function(t){var e,o,n,r;return(r=l.getSelection())&&(n=0,o=!1,(e=function(t,i){var a,l,p,c,f,u;for(u=[],p=0,c=(f=i.childNodes).length;p<c&&(a=f[p],!o);p++)if(3===a.nodeType){if(n+a.length>=t){o=!0,(l=s.createRange()).setStart(a,t-n),r.remove<PERSON>llRanges(),r.addRange(l);break}u.push(n+=a.length)}else u.push(e(t,a));return u})(t,this.domInputor)),this.domInputor},e.prototype.getIEPosition=function(){return this.getPosition()},e.prototype.getPosition=function(){var t,e;return e=this.getOffset(),t=this.$inputor.offset(),e.left-=t.left,e.top-=t.top,e},e.prototype.getOldIEPos=function(){var t,e;return e=s.selection.createRange(),(t=s.body.createTextRange()).moveToElementText(this.domInputor),t.setEndPoint("EndToEnd",e),t.text.length},e.prototype.getPos=function(){var t,e,o;return(o=this.range())?((t=o.cloneRange()).selectNodeContents(this.domInputor),t.setEnd(o.endContainer,o.endOffset),e=t.toString().length,t.detach(),e):s.selection?this.getOldIEPos():void 0},e.prototype.getOldIEOffset=function(){var t,e;return(t=s.selection.createRange().duplicate()).moveStart("character",-1),e=t.getBoundingClientRect(),{height:e.bottom-e.top,left:e.left,top:e.top}},e.prototype.getOffset=function(e){var o,n,r,i,a;return l.getSelection&&(r=this.range())?(r.endOffset-1>0&&r.endContainer!==this.domInputor&&((o=r.cloneRange()).setStart(r.endContainer,r.endOffset-1),o.setEnd(r.endContainer,r.endOffset),n={height:(i=o.getBoundingClientRect()).height,left:i.left+i.width,top:i.top},o.detach()),n&&0!==(null!=n?n.height:void 0)||(o=r.cloneRange(),a=t(s.createTextNode("|")),o.insertNode(a[0]),o.selectNode(a[0]),n={height:(i=o.getBoundingClientRect()).height,left:i.left,top:i.top},a.remove(),o.detach())):s.selection&&(n=this.getOldIEOffset()),n&&(n.top+=t(l).scrollTop(),n.left+=t(l).scrollLeft()),n},e.prototype.range=function(){var t;if(l.getSelection)return(t=l.getSelection()).rangeCount>0?t.getRangeAt(0):null},e}(),o=function(){function e(t){this.$inputor=t,this.domInputor=this.$inputor[0]}return e.prototype.getIEPos=function(){var t,e,o,n,r,i;return e=this.domInputor,r=s.selection.createRange(),n=0,r&&r.parentElement()===e&&(o=e.value.replace(/\r\n/g,"\n").length,(i=e.createTextRange()).moveToBookmark(r.getBookmark()),(t=e.createTextRange()).collapse(!1),n=i.compareEndPoints("StartToEnd",t)>-1?o:-i.moveStart("character",-o)),n},e.prototype.getPos=function(){return s.selection?this.getIEPos():this.domInputor.selectionStart},e.prototype.setPos=function(t){var e,o;return e=this.domInputor,s.selection?((o=e.createTextRange()).move("character",t),o.select()):e.setSelectionRange&&e.setSelectionRange(t,t),e},e.prototype.getIEOffset=function(t){var e,o,n,r;return o=this.domInputor.createTextRange(),t||(t=this.getPos()),o.move("character",t),n=o.boundingLeft,r=o.boundingTop,e=o.boundingHeight,{left:n,top:r,height:e}},e.prototype.getOffset=function(e){var o,n,r;return o=this.$inputor,s.selection?(n=this.getIEOffset(e),n.top+=t(l).scrollTop()+o.scrollTop(),n.left+=t(l).scrollLeft()+o.scrollLeft(),n):(n=o.offset(),r=this.getPosition(e),n={left:n.left+r.left-o.scrollLeft(),top:n.top+r.top-o.scrollTop(),height:r.height})},e.prototype.getPosition=function(t){var e,o,r,i,s,a;return e=this.$inputor,r=function(t){return t=t.replace(/<|>|`|"|&/g,"?").replace(/\r\n|\r|\n/g,"<br/>"),/firefox/i.test(navigator.userAgent)&&(t=t.replace(/\s/g,"&nbsp;")),t},void 0===t&&(t=this.getPos()),a=e.val().slice(0,t),o=e.val().slice(t),i="<span style='position: relative; display: inline;'>"+r(a)+"</span>",i+="<span id='caret' style='position: relative; display: inline;'>|</span>",i+="<span style='position: relative; display: inline;'>"+r(o)+"</span>",s=new n(e),s.create(i).rect()},e.prototype.getIEPosition=function(t){var e,o,n,r,i;return n=this.getIEOffset(t),o=this.$inputor.offset(),r=n.left-o.left,i=n.top-o.top,e=n.height,{left:r,top:i,height:e}},e}(),n=function(){function e(t){this.$inputor=t}return e.prototype.css_attr=["borderBottomWidth","borderLeftWidth","borderRightWidth","borderTopStyle","borderRightStyle","borderBottomStyle","borderLeftStyle","borderTopWidth","boxSizing","fontFamily","fontSize","fontWeight","height","letterSpacing","lineHeight","marginBottom","marginLeft","marginRight","marginTop","outlineWidth","overflow","overflowX","overflowY","paddingBottom","paddingLeft","paddingRight","paddingTop","textAlign","textOverflow","textTransform","whiteSpace","wordBreak","wordWrap"],e.prototype.mirrorCss=function(){var e,o=this;return e={position:"absolute",left:-9999,top:0,zIndex:-2e4},"TEXTAREA"===this.$inputor.prop("tagName")&&this.css_attr.push("width"),t.each(this.css_attr,function(t,n){return e[n]=o.$inputor.css(n)}),e},e.prototype.create=function(e){return this.$mirror=t("<div></div>"),this.$mirror.css(this.mirrorCss()),this.$mirror.html(e),this.$inputor.after(this.$mirror),this},e.prototype.rect=function(){var t,e,o;return t=this.$mirror.find("#caret"),e=t.position(),o={left:e.left,top:e.top,height:t.height()},this.$mirror.remove(),o},e}(),r={contentEditable:function(t){return!(!t[0].contentEditable||"true"!==t[0].contentEditable)}},i={pos:function(t){return t||0===t?this.setPos(t):this.getPos()},position:function(t){return s.selection?this.getIEPosition(t):this.getPosition(t)},offset:function(t){return this.getOffset(t)}},s=null,l=null,a=null,p=function(t){var e;return(e=null!=t?t.iframe:void 0)?(a=e,l=e.contentWindow,s=e.contentDocument||l.document):(a=void 0,l=window,s=document)},t.fn.caret=function(n,s,a){var l;return i[n]?(t.isPlainObject(s)?(p(s),s=void 0):p(a),l=r.contentEditable(this)?new e(this):new o(this),i[n].apply(l,[s])):t.error("Method "+n+" does not exist on jQuery.caret")},t.fn.caret.EditableCaret=e,t.fn.caret.InputCaret=o,t.fn.caret.Utils=r,t.fn.caret.apis=i});