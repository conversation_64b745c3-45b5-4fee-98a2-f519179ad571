<?php
/**
 * <PERSON><PERSON><PERSON> Invites Template Functions.
 *
 * @package <PERSON><PERSON><PERSON>\Invites\Templates
 * @since Buddy<PERSON>oss 1.0.0
 */

// Exit if accessed directly.
defined( 'ABSPATH' ) || exit;

/**
 * Output the invites component slug.
 *
 * @since Buddy<PERSON>oss 1.0.0
 */
function bp_invites_slug() {
	echo bp_get_invites_slug();
}
	/**
	 * Return the invites component slug.
	 *
	 * @since BuddyBoss 1.0.0
	 *
	 * @return string
	 */
function bp_get_invites_slug() {

	/**
	 * Filters the invites component slug.
	 *
	 * @since <PERSON><PERSON><PERSON> 1.0.0
	 *
	 * @param string $slug Invites component slug.
	 */
	return apply_filters( 'bp_get_invites_slug', buddypress()->invites->slug );
}

/**
 * Output the invites component root slug.
 *
 * @since BuddyBoss 1.0.0
 */
function bp_invites_root_slug() {
	echo bp_get_invites_root_slug();
}
	/**
	 * Return the invites component root slug.
	 *
	 * @since Buddy<PERSON>oss 1.0.0
	 *
	 * @return string
	 */
function bp_get_invites_root_slug() {

	/**
	 * Filters the invites component root slug.
	 *
	 * @since <PERSON><PERSON><PERSON> 1.0.0
	 *
	 * @param string $root_slug Invites component root slug.
	 */
	return apply_filters( 'bp_get_invites_root_slug', buddypress()->invites->root_slug );
}

