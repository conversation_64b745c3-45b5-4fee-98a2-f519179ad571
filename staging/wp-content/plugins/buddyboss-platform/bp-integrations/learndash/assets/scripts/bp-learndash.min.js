(n=>{var e={$report_tables:null,$report_selects:null,init:function(){this.$report_tables=jQuery(".bp_ld_report_table"),this.fetch_table_data()},fetch_table_data:function(){this.$report_tables.each(this.fetch_data.bind(this))},fetch_data:function(t,e){var o=this,a=n('[data-report-filter="step"]').val(),a=BP_LD_REPORTS_DATA.table_columns[a],a={columns:this.adjustTableColumns(a,e),processing:!0,serverSide:!0,searching:!1,lengthChange:!1,info:!1,pageLength:BP_LD_REPORTS_DATA.config.perpage,language:{processing:BP_LD_REPORTS_DATA.text.processing,emptyTable:BP_LD_REPORTS_DATA.text.emptyTable,paginate:{first:BP_LD_REPORTS_DATA.text.paginate_first,last:BP_LD_REPORTS_DATA.text.paginate_last,next:BP_LD_REPORTS_DATA.text.paginate_next,previous:BP_LD_REPORTS_DATA.text.paginate_previous}},ajax:{url:BP_LD_REPORTS_DATA.ajax_url,type:"POST",data:function(a){n("[data-report-filter]").each(function(){var t=n(this).data("report-filter");a[t]=n(this).val()}),a.nonce=BP_LD_REPORTS_DATA.nonce,a.action="bp_ld_group_get_reports",a.group=BP_LD_REPORTS_DATA.current_group,a.completed=n(e).data("completed")?1:0,a.display=!0}}};n(e).on("xhr.dt",function(t,a,e){0<e.data.length?n(t.target).closest(".bp_ld_report_table_wrapper").removeClass("no-data hidden").addClass("has-data"):n(t.target).closest(".bp_ld_report_table_wrapper").removeClass("has-data").addClass("no-data hidden"),n(t.target).data("data_length",e.data.length);var r=0;o.$report_tables.each(function(){0==n(this).data("data_length")&&r++}),r==o.$report_tables.length?n(".ld-report-export-csv, .ld-report-no-data").removeClass("has-data").addClass("no-data hidden"):n(".ld-report-export-csv, .ld-report-no-data").removeClass("no-data hidden").addClass("has-data")}).DataTable(a)},adjustTableColumns:function(t,a){var e=n(a).data("completed")?"updated_date":"completion_date",r=[];return n(t).each(function(t,a){a.name!=e&&r.push(a)}),r},prepareExport:function(t){t.preventDefault();t=n(t.target);if(t.data("exported"))return window.location.href=t.data("export_url"),!1;var a={start:0,length:BP_LD_REPORTS_DATA.config.perpage,nonce:BP_LD_REPORTS_DATA.nonce,action:"bp_ld_group_get_reports",group:BP_LD_REPORTS_DATA.current_group,export:!0};n("[data-report-filter]").each(function(){var t=n(this).data("report-filter");a[t]=n(this).val()}),t.data("export_args",a),e.startExport(t)},startExport:function(a){var e=this,r=a.data("export_args");a.prop("disabled",!0),n.post(BP_LD_REPORTS_DATA.ajax_url,r,function(t){a.prop("disabled",!1),t.success?t.data.has_more?(r.start=r.start+r.length,r.hash=t.data.hash,a.data("export_args",r),n(".export-indicator").show(),n(".export-indicator .export-current-step").text(t.data.page),n(".export-indicator .export-total-step").text(t.data.total),e.startExport(a)):(a.data("exported",!0),a.data("export_url",t.data.url),window.location.href=t.data.url,n(".export-indicator").hide()):n(".export-indicator").text(BP_LD_REPORTS_DATA.text.export_failed)},"json")}};n.fn.dataTable.ext.classes.sPageButton="button",n(function(){e.init(),n(".ld-report-export-csv").on("click",e.prepareExport)})})(jQuery);