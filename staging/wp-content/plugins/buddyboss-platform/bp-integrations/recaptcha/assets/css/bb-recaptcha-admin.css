.bb-recaptcha-settings .bp-admin-card .bb-inactive-field {
  pointer-events: none;
  user-select: none;
  opacity: 0.3;
}

.bb-recaptcha-settings #bb_recaptcha_versions .bb-recaptcha-status {
  margin-left: 15px;
}

.bb-recaptcha-settings #bb_recaptcha_versions .notes-hidden-header > th {
  display: none;
}

.bb-recaptcha-settings #bb_recaptcha_versions .notes-hidden-header > th + td {
  padding-left: 0;
  padding-right: 0;
}

.bb-recaptcha-settings #bb_recaptcha_versions .hidden-header > th {
  opacity: 0;
  visibility: hidden;
  padding: 0;
  margin: 0;
}

.bb-recaptcha-settings #bb_recaptcha_versions .hidden-header:not(.field-button) > td {
  padding: 0;
  margin: 0;
}

.bb-recaptcha-settings #bb_recaptcha_versions .hidden-header .show-full-width {
  margin-left: -210px;
  word-break: break-word;
}

@media (max-width: 782px) {
  .bb-recaptcha-settings #bb_recaptcha_versions .hidden-header .show-full-width {
    margin-left: 0;
  }
}

.bb-recaptcha-settings #bb_recaptcha_versions .hidden-header .bb-success-section {
  margin-left: 0;
  margin-bottom: 15px;
}

.bb-recaptcha-settings #bb_recaptcha_versions .hidden-header .bb-error-section {
  margin-left: 0;
  margin-bottom: 25px;
}

.bb-recaptcha-settings #bb_recaptcha_versions .hidden-header .bb-recaptcha-prompt {
  display: inline-block;
  margin-bottom: 10px;
  line-height: 1.5;
}

.bb-recaptcha-settings #bb_recaptcha_versions input[type="text"],
.bb-recaptcha-settings #bb_recaptcha_versions input[type="email"],
.bb-recaptcha-settings #bb_recaptcha_versions select,
.bb-recaptcha-settings #bb_recaptcha_versions input[type="password"] {
  min-width: 50%;
}

.bb-recaptcha-settings #bb_recaptcha_versions select {
  max-width: none;
}

.bb-recaptcha-settings #bb_recaptcha_versions .password-toggle {
  position: relative;
  display: inline-block;
  width: 50%;
}

@media (max-width: 782px) {
  .bb-recaptcha-settings #bb_recaptcha_versions .password-toggle {
    width: 100%;
  }
}

.bb-recaptcha-settings #bb_recaptcha_versions .password-toggle .bb-hide-pw {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 30px;
  border: 0;
  padding: 0;
  background: none;
  box-shadow: none;
}

.bb-recaptcha-settings #bb_recaptcha_versions .password-toggle .bb-hide-pw .bb-icon {
  font-size: 16px;
  line-height: 1;
}

.bb-recaptcha-settings #bb_recaptcha_versions .password-toggle input[type="text"],
.bb-recaptcha-settings #bb_recaptcha_versions .password-toggle input[type="password"] {
  padding-right: 30px;
  width: 100%;
}

.bb-recaptcha-settings #bb_recaptcha_versions .password-toggle input[type="text"] + .bb-hide-pw .bb-icon::before {
  content: "\ee6a";
}

.bb-recaptcha-settings #bb_recaptcha_versions .checkbox-wrap input[type="checkbox"] {
  margin-top: 0;
}

.bb-recaptcha-settings #bb_recaptcha_versions .checkbox-wrap + .checkbox-wrap {
  margin-top: 15px;
}

.bb-recaptcha-settings #bb_recaptcha_versions .bb-bottom-notice {
  text-align: center;
}

.bb-recaptcha-settings #bb_recaptcha_versions .bp-hello-recaptcha .bp-hello-title h2 {
  color: #1d2327;
  font-size: 1.5em;
  margin: 0.5em 0;
  padding-bottom: 0;
  border-bottom: none;
  font-weight: 600;
}

.bb-recaptcha-settings #bb_recaptcha_versions .bp-hello-recaptcha .bp-hello-content {
  height: auto;
  padding: 0 23px;
  min-height: 180px;
  max-height: calc(100vh - 320px);
  margin-bottom: 62px;
  display: flex;
  align-items: center;
}

.bb-recaptcha-settings #bb_recaptcha_versions .bp-hello-recaptcha .bb-popup-buttons {
  position: absolute;
  bottom: 0;
  padding: 15px;
  text-align: right;
  width: calc(100% - 30px);
  margin: 0 -23px;
  border-top: 1px solid var(--bp-hello-color-secondary);
  background-color: #f0f0f1;
}

@media screen and (orientation: landscape) and (max-height: 480px) {
  .bb-recaptcha-settings #bb_recaptcha_versions .bp-hello-recaptcha .bp-hello-content {
    min-height: 110px;
  }
}

.bb-recaptcha-settings #bb_recaptcha_versions .bp-hello-recaptcha .bp-hello-recaptcha-content-container {
  padding: 15px 0;
  margin: 0 auto;
  text-align: center;
}

.bb-recaptcha-settings #bb_recaptcha_versions .bp-hello-recaptcha .bp-hello-recaptcha-content-container .verifying_token {
  display: inline-block;
}

.bb-recaptcha-settings #bb_recaptcha_versions .bp-hello-recaptcha .bp-hello-recaptcha-content-container .grecaptcha-badge {
  position: static !important;
  margin: 0 auto;
}

.bb-recaptcha-settings #bb_recaptcha_versions #bp-hello-container.bp-hello-recaptcha {
  top: 50%;
  bottom: initial;
  transform: translateY(-50%);
  margin-top: 32px;
}

.bb-recaptcha-settings #bb_recaptcha_versions #bp-hello-container.bp-hello-recaptcha .button-primary {
  color: #fff;
  margin-right: 10px;
}

@media screen and (max-width: 480px) {
  .bb-recaptcha-settings #bb_recaptcha_versions #bp-hello-container.bp-hello-recaptcha .button-primary {
    margin-right: 0;
  }
}

.bb-recaptcha-settings .bb-success-section {
  padding: 10px;
  margin-left: -210px;
  background: #edfaef;
  color: #008a20;
  line-height: 1.5;
  border: 1px solid #68de7c;
  border-radius: 4px;
}

.bb-recaptcha-settings .bp-hello-recaptcha .bp-hello-recaptcha-content-container .recaptcha-status-icon {
  max-width: 56px;
  margin-bottom: 16px;
}

.bb-recaptcha-settings .bp-hello-recaptcha .bp-hello-recaptcha-content-container > p {
  font-weight: 600;
  color: #50575e;
  line-height: 1;
}

.bb-recaptcha-settings .bp-hello-recaptcha .bp-hello-recaptcha-content-container .verified_token,
.bb-recaptcha-settings .bp-hello-recaptcha .bp-hello-recaptcha-content-container .verifying_token {
  font-weight: 600;
}

.bb-recaptcha-settings .bp-hello-recaptcha .bp-hello-recaptcha-content-container .verified_token p,
.bb-recaptcha-settings .bp-hello-recaptcha .bp-hello-recaptcha-content-container .verifying_token p {
  margin-top: 0;
  color: #50575e;
  line-height: 1;
}

.bb-recaptcha-settings .bp-hello-recaptcha .bp-hello-recaptcha-content-container .verifying_token p {
  display: flex;
  align-items: center;
}

.bb-recaptcha-settings .bp-hello-recaptcha .bp-hello-recaptcha-content-container .verifying_token.loading p::after {
  content: "";
  display: inline-block;
  box-sizing: border-box;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid #dcdcde;
  border-top-color: #787c82;
  font-size: 20px;
  line-height: 1;
  margin-left: 10px;
  text-align: center;
  vertical-align: middle;
  animation: spin 2s infinite linear;
}

.bb-recaptcha-settings .bp-hello-recaptcha#bp-hello-container .verified_token p,
.bb-recaptcha-settings .bp-hello-recaptcha#bp-hello-container .verifying_token p {
  font-size: 16px;
}

.bb-recaptcha-settings .bp-hello-recaptcha#bp-hello-container .bp-hello-recaptcha-content-container > p {
  font-size: 16px;
}

.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer {
  display: flex;
}

.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-badge-position,
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-size,
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-theme {
  display: flex;
  flex-direction: column;
  margin-right: 15px;
  text-align: center;
  width: 184px;
}

.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-badge-position input[type="radio"],
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-size input[type="radio"],
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-theme input[type="radio"] {
  height: 0;
  opacity: 0;
  visibility: hidden;
}

.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-badge-position .option,
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-size .option,
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-theme .option {
  display: flex;
  color: #999;
  flex-direction: column;
  position: relative;
}

.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-badge-position .option:before,
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-size .option:before,
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-theme .option:before {
  background: #f9f9f9;
  border: 2px solid #ccd0d4;
  border-radius: 4px;
  box-sizing: border-box;
  content: "";
  display: flex;
  height: 64px;
  margin-bottom: 5px;
  transition: all 0.3s ease;
  width: 100%;
}

.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-badge-position .option:after,
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-size .option:after,
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-theme .option:after {
  background-size: contain !important;
  content: "";
  border-radius: 3px;
  bottom: 30px;
  left: 6px;
  position: absolute;
  right: 6px;
  top: 6px;
  image-rendering: -webkit-optimize-contrast;
}

.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-badge-position .option.opt-light:after,
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-size .option.opt-light:after,
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-theme .option.opt-light:after {
  background: url(../images/reCAPTCHA-light.png) no-repeat center;
}

.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-badge-position .option.opt-dark:after,
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-size .option.opt-dark:after,
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-theme .option.opt-dark:after {
  background: url(../images/reCAPTCHA-dark.png) no-repeat center;
}

.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-badge-position .option.opt-normal.opt-size-light:after,
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-size .option.opt-normal.opt-size-light:after,
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-theme .option.opt-normal.opt-size-light:after {
  background: url(../images/reCAPTCHA-light.png) no-repeat center;
}

.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-badge-position .option.opt-normal.opt-size-dark:after,
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-size .option.opt-normal.opt-size-dark:after,
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-theme .option.opt-normal.opt-size-dark:after {
  background: url(../images/reCAPTCHA-dark.png) no-repeat center;
}

.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-badge-position .option.opt-compact.opt-size-light:after,
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-size .option.opt-compact.opt-size-light:after,
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-theme .option.opt-compact.opt-size-light:after {
  background: url(../images/reCAPTCHA-light-compact.png) no-repeat center;
}

.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-badge-position .option.opt-compact.opt-size-dark:after,
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-size .option.opt-compact.opt-size-dark:after,
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-theme .option.opt-compact.opt-size-dark:after {
  background: url(../images/reCAPTCHA-dark-compact.png) no-repeat center;
}

.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-badge-position .option.opt-bottom-right:after,
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-size .option.opt-bottom-right:after,
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-theme .option.opt-bottom-right:after {
  background: url(../images/reCAPTCHA-bottom-right.png) no-repeat center;
}

.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-badge-position .option.opt-bottom-left:after,
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-size .option.opt-bottom-left:after,
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-theme .option.opt-bottom-left:after {
  background: url(../images/reCAPTCHA-bottom-left.png) no-repeat center;
}

.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-badge-position .option.opt-inline:after,
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-size .option.opt-inline:after,
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-theme .option.opt-inline:after {
  background: url(../images/reCAPTCHA-inline.png) no-repeat center;
}

.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-badge-position .option span,
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-size .option span,
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-theme .option span {
  color: #999;
  font-size: 14px;
  line-height: 2;
}

.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-badge-position input[type="radio"]:checked:not(:disabled) + .option:before,
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-size input[type="radio"]:checked:not(:disabled) + .option:before,
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-theme input[type="radio"]:checked:not(:disabled) + .option:before {
  border-color: #2370b1;
}

.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-badge-position input[type="radio"]:disabled + .option:before,
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-size input[type="radio"]:disabled + .option:before,
.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-theme input[type="radio"]:disabled + .option:before {
  border-color: #dcdcde;
}

.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-size .option:before {
  height: 96px;
}

.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-badge-position {
  width: 120px;
}

.bb-recaptcha-settings #bb_recaptcha_design .bb-grid-style-outer .bb-badge-position .option:before {
  height: 120px;
  background-color: #f0f0f1;
}

.bb-recaptcha-settings #bb_recaptcha_settings .bb_login_require .copy-toggle {
  width: 60%;
  margin-top: 10px;
  display: flex;
  align-items: center;
}

.bb-recaptcha-settings #bb_recaptcha_settings .bb_login_require .copy-toggle .copy-toggle-text {
  margin-right: 5px;
  text-wrap: nowrap;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.bb-recaptcha-settings #bb_recaptcha_settings .bb_login_require .copy-toggle.bb-url-not-allowed {
  color: #f00;
}

.bb-recaptcha-settings #bb_recaptcha_settings .bb_login_require .copy-toggle.bb-url-not-allowed .copy-toggle-text {
  color: #f00;
}

.bb-recaptcha-settings #bb_recaptcha_settings .bb_login_require .copy-toggle .bb-recaptcha-copy-button {
  font-size: 12px;
  color: #999;
  position: relative;
  cursor: pointer;
}

.bb-recaptcha-settings #bb_recaptcha_settings .bb_login_require .copy-toggle .bb-recaptcha-copy-button[data-balloon][data-balloon-pos="up"]:after {
  pointer-events: none;
  opacity: 0;
  transition: all 0.18s ease-out 0.18s;
  font-weight: 500;
  font-size: 13px;
  letter-spacing: -0.24px;
  background: rgba(18, 43, 70, 0.95);
  border-radius: 4px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.22);
  color: #fff;
  content: attr(data-balloon);
  line-height: 1.3;
  padding: 7px 15px;
  white-space: nowrap;
  z-index: 10;
  display: block;
  bottom: 100%;
  left: 50%;
  margin-bottom: 5px;
  transform: translate(-50%, 10px);
  transform-origin: top;
  position: absolute;
}

.bb-recaptcha-settings #bb_recaptcha_settings .bb_login_require .copy-toggle .bb-recaptcha-copy-button[data-balloon][data-balloon-pos="up"]:before {
  background: no-repeat url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http://www.w3.org/2000/svg%22%20width%3D%2236px%22%20height%3D%2212px%22%3E%3Cpath%20fill%3D%22var(--bb-tooltip-background)%22%20transform%3D%22rotate(0)%22%20d%3D%22M2.658,0.000%20C-13.615,0.000%2050.938,0.000%2034.662,0.000%20C28.662,0.000%2023.035,12.002%2018.660,12.002%20C14.285,12.002%208.594,0.000%202.658,0.000%20Z%22/%3E%3C/svg%3E");
  /* stylelint-disable-line */
  background-size: 100% auto;
  width: 18px;
  height: 6px;
  opacity: 0;
  pointer-events: none;
  -webkit-transition: all 0.18s ease-out 0.18s;
  transition: all 0.18s ease-out 0.18s;
  content: "";
  display: block;
  bottom: 100%;
  left: 50%;
  transform: translate(-50%, 10px);
  transform-origin: top;
  position: absolute;
}

.bb-recaptcha-settings #bb_recaptcha_settings .bb_login_require .copy-toggle .bb-recaptcha-copy-button:hover:after, .bb-recaptcha-settings #bb_recaptcha_settings .bb_login_require .copy-toggle .bb-recaptcha-copy-button[data-balloon][data-balloon-pos="up"]:hover:after {
  pointer-events: auto;
  opacity: 1;
  transform: translate(-50%, 0);
}

.bb-recaptcha-settings #bb_recaptcha_settings .bb_login_require .copy-toggle .bb-recaptcha-copy-button:hover:before, .bb-recaptcha-settings #bb_recaptcha_settings .bb_login_require .copy-toggle .bb-recaptcha-copy-button[data-balloon][data-balloon-pos="up"]:hover:before {
  pointer-events: auto;
  opacity: 1;
  transform: translate(-50%, 0);
}

.bb-recaptcha-settings #bb_recaptcha_settings .bb_login_require .copy-toggle .bb-recaptcha-copy-button i {
  font-size: 20px;
}

.bb-recaptcha-settings #bb_recaptcha_settings .bb_login_require .copy-toggle.bp-hide {
  display: none !important;
}

.bb-success-section.bb-recaptcha-errors span, .bb-success-section.bb-recaptcha-success span,
.bb-error-section.bb-recaptcha-errors span,
.bb-error-section.bb-recaptcha-success span {
  display: inline-flex;
  align-items: center;
}

.bb-success-section.bb-recaptcha-errors span::before, .bb-success-section.bb-recaptcha-success span::before,
.bb-error-section.bb-recaptcha-errors span::before,
.bb-error-section.bb-recaptcha-success span::before {
  font-family: bb-icons;
  font-style: normal;
  font-weight: 200;
  speak: none;
  display: inline-block;
  text-decoration: inherit;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  text-align: center;
  font-variant: normal;
  text-transform: none;
  line-height: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
}

.bb-success-section.bb-recaptcha-success span::before {
  content: "\e876";
}

.bb-error-section.bb-recaptcha-errors {
  background-color: #fcf0f1;
  color: #d63638;
  border-color: #ffabaf;
}

.bb-error-section.bb-recaptcha-errors span::before {
  content: "\ee65";
}

#bp-hello-container.bp-hello-recaptcha .bp-hello-header {
  background-color: #f6f7f7;
  border-color: #dcdcde;
}

.section-bb_recaptcha_versions .recaptcha-version-fields input[type="radio"] {
  margin-left: 16px;
}

.section-bb_recaptcha_versions .recaptcha-version-fields input[type="radio"]:first-of-type {
  margin-left: 0;
}

.bp-hello-recaptcha .bp-hello-close .button {
  background-color: transparent;
  border: 0;
  color: #a7aaad;
  height: auto;
  min-height: auto;
  line-height: 1;
  font-size: 28px;
  padding: 0;
  margin: 0;
}

.bp-hello-recaptcha .recaptcha-verify-icon {
  max-width: 80px;
  margin-bottom: 1rem;
}
