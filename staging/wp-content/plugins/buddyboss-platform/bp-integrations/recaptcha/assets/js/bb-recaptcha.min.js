(i=>{var a={init:function(){this.bbrecaptchaData="undefined"!=typeof bbRecaptcha&&void 0!==bbRecaptcha.data?bbRecaptcha.data:"",this.bbrecaptchaAction=this.bbrecaptchaData&&void 0!==this.bbrecaptchaData.action?this.bbrecaptchaData.action:"",this.bbrecaptchaVersion=this.bbrecaptchaData&&void 0!==this.bbrecaptchaData.selected_version?this.bbrecaptchaData.selected_version:"",this.setupGlobals()},setupGlobals:function(){var a,c;this.bbrecaptchaAction&&(a=this.bbrecaptchaAction,c=!1,"bb_login"===a?c="loginform":"bb_lost_password"===a?c="lostpasswordform":"bb_register"===a?c="signup-form":"bb_activate"===a&&(c="activation-form"),"recaptcha_v3"===this.bbrecaptchaVersion&&grecaptcha.ready(function(){grecaptcha.execute(bbRecaptcha.data.site_key,{action:a}).then(function(a){i("#bb_recaptcha_response_id").val(a)})}),"recaptcha_v2"===this.bbrecaptchaVersion)&&void 0!==this.bbrecaptchaData.v2_option&&("v2_checkbox"===this.bbrecaptchaData.v2_option&&grecaptcha.ready(function(){var a={sitekey:bbRecaptcha.data.site_key,theme:bbRecaptcha.data.v2_theme,size:bbRecaptcha.data.v2_size};grecaptcha.render("bb_recaptcha_v2_element",a)}),"v2_invisible_badge"===this.bbrecaptchaData.v2_option)&&grecaptcha.ready(function(){var t=i("#"+c),a={sitekey:bbRecaptcha.data.site_key,tabindex:9999,badge:bbRecaptcha.data.v2_badge_position,size:"invisible",callback:function(a){i("#g-recaptcha-response").val(a),c&&t.find("input[data-click]").trigger("click")}},e=grecaptcha.render("bb_recaptcha_v2_element",a);t.length&&t.on("submit",function(a){""==t.find(".g-recaptcha-response").val()&&(a.preventDefault(),a.stopImmediatePropagation(),grecaptcha.execute(e))}).find("input:submit, button").on("click",function(a){""==t.find(".g-recaptcha-response").val()&&(t.find("input:submit").attr("data-click","bb_recaptcha_submit"),a.preventDefault(),a.stopImmediatePropagation(),grecaptcha.execute(e))})})}};i(function(){a.init()})})(jQuery);