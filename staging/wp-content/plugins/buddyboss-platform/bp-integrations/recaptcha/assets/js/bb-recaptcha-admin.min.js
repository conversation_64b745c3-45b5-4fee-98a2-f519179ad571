(r=>{var e={init:function(){this.setupGlobals(),this.addListeners()},setupGlobals:function(){this.selected_version=r('input[name="bb_recaptcha[recaptcha_version]"]:checked').val(),this.v2_option=r('.recaptcha_v2:visible input[name="bb_recaptcha[v2_option]"]:checked').val(),this.site_key="",this.secret_key="",this.captcha_response="",r(".bb-recaptcha-settings form table.bb-inactive-field").length&&(r(".bb-recaptcha-settings form #bb_recaptcha_settings table").removeClass("bb-inactive-field"),r(".bb-recaptcha-settings form #bb_recaptcha_design table").removeClass("bb-inactive-field")),r(".bb-recaptcha-settings .section-bb_recaptcha_versions .bb-recaptcha-status .not-connected").length&&(r(".bb-recaptcha-settings form #bb_recaptcha_settings table").addClass("bb-inactive-field"),r(".bb-recaptcha-settings form #bb_recaptcha_design table").addClass("bb-inactive-field"));var e=new URLSearchParams(window.location.search);e.has("bb_verified")&&(e.delete("bb_verified"),e=window.location.pathname+"?"+e.toString()+window.location.hash,window.history.replaceState({},document.title,e))},addListeners:function(){var e=r(".buddyboss_page_bp-integrations .section-bb_recaptcha_versions"),e=(e.on("keyup","#bb-recaptcha-site-key, #bb-recaptcha-secret-key",this.enableVerifyButton.bind(this)),e.on("click",".recaptcha-verification",this.recaptchaVerifications.bind(this)),e.on("click","#recaptcha_submit",this.recaptchaSubmit.bind(this)),e.on("click","#recaptcha_verified",this.recaptchaVerified.bind(this)),e.on("click",".close-modal, #recaptcha_cancel",this.recaptchaVerificationPopupClose.bind(this)),e.on("change",'input[name="bb_recaptcha[recaptcha_version]"]',this.recaptchaVersion.bind(this)),e.on("change",'input[name="bb_recaptcha[v2_option]"]',this.recaptchaType.bind(this)),r(".buddyboss_page_bp-integrations .section-bb_recaptcha_design").on("change",'input[name="bb_recaptcha[theme]"]',this.recaptchaTheme.bind(this)),r(".buddyboss_page_bp-integrations .section-bb_recaptcha_settings"));e.on("change","#recaptcha_bb_login",this.allowByPass.bind(this)),e.on("change","#bb_recaptcha_allow_bypass",this.enableBypassInputAndToggle.bind(this)),e.on("keyup",'input[name="bb_recaptcha[bypass_text]"]',this.updateByPassUrl.bind(this)),e.on("click",".bb_login_require .bb-recaptcha-copy-button",this.copyByPassUrl.bind(this))},enableVerifyButton:function(e){e.preventDefault();var e=r("#bb-recaptcha-site-key").val(),t=r("#bb-recaptcha-site-key").attr("data-old-value"),a=r("#bb-recaptcha-secret-key").val(),c=r("#bb-recaptcha-secret-key").attr("data-old-value");""!==e&&""!==a?(r(".verify-row").removeClass("bp-hide"),r(".recaptcha-verification").removeAttr("disabled"),r(".bb-recaptcha-settings form .submit input").attr("disabled","disabled"),""===r('input[name="bb_recaptcha[recaptcha_version]"]:checked').val()||e===t&&a===c?(r(".verify-row").addClass("bp-hide"),r(".recaptcha-verification").attr("disabled","disabled"),r(".bb-recaptcha-settings form .submit input").removeAttr("disabled")):(r(".verify-row").removeClass("bp-hide"),r(".recaptcha-verification").removeAttr("disabled"),r(".bb-recaptcha-settings form .submit input").attr("disabled","disabled"))):(r(".verify-row").removeClass("bp-hide"),r(".recaptcha-verification").attr("disabled","disabled"),r(".bb-recaptcha-settings form .submit input").removeAttr("disabled"))},recaptchaVersion:function(e){var t;e.preventDefault(),"recaptcha_v3"===e.currentTarget.value?(r(".recaptcha_v3").removeClass("bp-hide"),r(".recaptcha_v2").addClass("bp-hide"),r("#bp-hello-content-recaptcha_v2").addClass("bp-hide"),r("#bp-hello-content-recaptcha_v3").removeClass("bp-hide")):(r(".recaptcha_v2").removeClass("bp-hide"),r(".recaptcha_v3").addClass("bp-hide"),void 0!==(t=r('.recaptcha_v2 input[name="bb_recaptcha[v2_option]"]:checked').val())&&"v2_invisible_badge"===t?(r("#bp-hello-content-recaptcha_v2").addClass("bp-hide"),r("#bp-hello-content-recaptcha_v3").removeClass("bp-hide")):(r("#bp-hello-content-recaptcha_v2").removeClass("bp-hide"),r("#bp-hello-content-recaptcha_v3").addClass("bp-hide"))),this.selected_version!==e.currentTarget.value||"recaptcha_v2"===e.currentTarget.value&&this.v2_option!==r('.recaptcha_v2:visible input[name="bb_recaptcha[v2_option]"]:checked').val()?(r(".verify-row").removeClass("bp-hide"),r(".recaptcha-verification").removeAttr("disabled"),r(".bb-recaptcha-settings form .submit input").attr("disabled","disabled")):(r(".verify-row").addClass("bp-hide"),r(".recaptcha-verification").attr("disabled","disabled"),r(".bb-recaptcha-settings form .submit input").removeAttr("disabled"))},recaptchaType:function(e){e.preventDefault(),this.v2_option!==e.currentTarget.value?(r(".verify-row").removeClass("bp-hide"),r(".recaptcha-verification").removeAttr("disabled"),r(".bb-recaptcha-settings form .submit input").attr("disabled","disabled")):(r(".verify-row").addClass("bp-hide"),r(".recaptcha-verification").attr("disabled","disabled"),r(".bb-recaptcha-settings form .submit input").removeAttr("disabled")),r(".recaptcha-v2-fields p.description").addClass("bp-hide"),r("."+e.currentTarget.value+"_description").removeClass("bp-hide"),"v2_checkbox"===e.currentTarget.value?(r(".recaptcha_v2_checkbox").removeClass("bp-hide"),r(".recaptcha_v2_invisible").addClass("bp-hide"),r("#bp-hello-content-recaptcha_v2").removeClass("bp-hide"),r("#bp-hello-content-recaptcha_v3").addClass("bp-hide")):(r(".recaptcha_v2_invisible").removeClass("bp-hide"),r(".recaptcha_v2_checkbox").addClass("bp-hide"),r("#bp-hello-content-recaptcha_v2").addClass("bp-hide"),r("#bp-hello-content-recaptcha_v3").removeClass("bp-hide"))},recaptchaTheme:function(e){e.preventDefault(),"dark"===e.currentTarget.value?r('input[name="bb_recaptcha[size]"] + label').removeClass("opt-size-light").addClass("opt-size-dark"):r('input[name="bb_recaptcha[size]"] + label').removeClass("opt-size-dark").addClass("opt-size-light")},recaptchaVerifications:function(e){e.preventDefault();var t,a=this,e=(r(document).find("#bp-hello-backdrop").length||(e=r(document).find(".bp-hello-recaptcha"),r('<div id="bp-hello-backdrop" style="display: none;"></div>').insertBefore(e)),document.getElementById("bp-hello-backdrop")),c=document.getElementById("bp-hello-container");null!==e&&(document.body.classList.add("bp-disable-scroll"),e.style.display="",c.style.display="",a.selected_version=r('input[name="bb_recaptcha[recaptcha_version]"]:checked').val(),a.site_key=r("#bb-recaptcha-site-key").val(),a.secret_key=r("#bb-recaptcha-secret-key").val(),window.bb_recaptcha_script=document.createElement("script"),r('input[name="bb_recaptcha[v2_option]"]').length&&(a.v2_option=r('input[name="bb_recaptcha[v2_option]"]:checked').val()),t=this.fetchSelector(),"recaptcha_v3"===a.selected_version&&(window.bb_recaptcha_script.src="https://www.google.com/recaptcha/api.js?onload=bb_recaptcha_v3_verify&render="+a.site_key),"recaptcha_v2"===a.selected_version&&a.v2_option&&("v2_checkbox"===a.v2_option&&(window.bb_recaptcha_script.src="https://www.google.com/recaptcha/api.js?onload=bb_recaptcha_v2_verify&render=explicit"),"v2_invisible_badge"===a.v2_option)&&(window.bb_recaptcha_script.src="https://www.google.com/recaptcha/api.js?onload=bb_recaptcha_v2_verify_invisible&render=explicit",r("body").append('<div id="v2_invisible_footer"></div>')),window.bb_recaptcha_script.onerror=function(){console.log("error")},window.bb_recaptcha_v3_verify=function(){"object"==typeof grecaptcha&&grecaptcha.ready(function(){grecaptcha.execute(a.site_key,{action:"bb_recaptcha_admin_verify"}).then(function(e){a.captcha_response=e,r("#"+t+" .verifying_token").hide(),r("#"+t+" .verified_token").show()})})},window.bb_recaptcha_v2_verify=function(){window.bb_recaptcha_box=grecaptcha.render("verifying_token",{sitekey:a.site_key,theme:"light",callback:function(){a.captcha_response=grecaptcha.getResponse(window.bb_recaptcha_box)}})},window.bb_recaptcha_v2_verify_invisible=function(){window.bb_recaptcha_invisible=grecaptcha.render("v2_invisible_footer",{sitekey:a.site_key,tabindex:9999,size:"invisible",callback:function(e){a.captcha_response=e,r("#"+t+" .verifying_token").hide(),r("#"+t+" .verified_token").show()}}),grecaptcha.execute()},r("#recaptcha_submit").removeAttr("disabled"),document.head.appendChild(window.bb_recaptcha_script))},recaptchaSubmit:function(t){t.preventDefault();var e=this,a=(r(t.currentTarget).attr("disabled","disabled"),{action:"bb_recaptcha_verification_admin_settings",nonce:bbRecaptchaAdmin.nonce,selected_version:e.selected_version,site_key:e.site_key,secret_key:e.secret_key,captcha_response:e.captcha_response}),c=(e.v2_option&&(a.v2_option=e.v2_option),this.fetchSelector());r.ajax({type:"POST",url:bbRecaptchaAdmin.ajax_url,data:a,success:function(e){r(t.currentTarget).removeAttr("disabled"),e.success&&void 0!==e.data?(r("#"+c).html(e.data),r(".bb-popup-buttons").html('<button id="recaptcha_verified" class="button">'+bbRecaptchaAdmin.bb_recaptcha_ok+"</button>"),document.head.removeChild(window.bb_recaptcha_script),window.bb_recaptcha_script=null,window.bb_recaptcha_v3_verify=null,window.bb_recaptcha_box=null,window.bb_recaptcha_v2_verify=null,window.bb_recaptcha_invisible=null,window.bb_recaptcha_v2_verify_invisible=null):(r("#"+c).html(e.data),r(".bb-popup-buttons").html('<button id="recaptcha_cancel" class="button">'+bbRecaptchaAdmin.bb_recaptcha_ok+"</button>"))}})},recaptchaVerified:function(e){e.preventDefault();e=window.location.href+(window.location.search?"&":"?")+"bb_verified";window.location.href=e},recaptchaVerificationPopupClose:function(e){e.preventDefault();e=window.location.href+(window.location.search?"&":"?")+"bb_verified";window.location.href=e},fetchSelector:function(){var e=this;return"recaptcha_v3"===e.selected_version||"recaptcha_v2"===e.selected_version&&e.v2_option&&"v2_invisible_badge"===e.v2_option?"bp-hello-content-recaptcha_v3":"bp-hello-content-recaptcha_v2"},allowByPass:function(e){e.currentTarget.checked?r(".bb_login_require").removeClass("bp-hide"):r(".bb_login_require").addClass("bp-hide")},enableBypassInputAndToggle:function(e){e.currentTarget.checked?(r('.bb_login_require input[name="bb_recaptcha[bypass_text]"]').removeAttr("disabled"),r(".bb_login_require .copy-toggle").removeClass("bp-hide")):(r('.bb_login_require input[name="bb_recaptcha[bypass_text]"]').attr("disabled","disabled"),r(".bb_login_require .copy-toggle").addClass("bp-hide"))},updateByPassUrl:function(e){e.preventDefault();var t="xxUNIQUE_STRINGXS",e=((t=e.currentTarget.value?e.currentTarget.value:t).length<6?(r(".bb_login_require .copy-toggle").addClass("bb-url-not-allowed"),r(".bb_login_require .bb-recaptcha-copy-button").addClass("bp-hide")):(r(".bb_login_require .copy-toggle").removeClass("bb-url-not-allowed"),r(".bb_login_require .bb-recaptcha-copy-button").removeClass("bp-hide")),r(".copy-toggle-text").data("domain")+t);r(".copy-toggle-text").attr("href",e),r(".copy-toggle-text").html(e)},copyByPassUrl:function(e){e.preventDefault();var t=r(e.currentTarget).html(),a=r(".copy-toggle-text").attr("href"),c=r("<input>"),i=(r("body").append(c),c.val(a).select(),document.execCommand("copy"),c.remove(),r(e.currentTarget).html(r(e.currentTarget).html()),r(e.currentTarget).data("balloon")),a=r(e.currentTarget).data("copied-text");r(e.currentTarget).attr("data-balloon",a),setTimeout(function(){r(e.currentTarget).html(t),r(e.currentTarget).attr("data-balloon",i)},2e3)}};r(function(){e.init()})})(jQuery);