function clear(e){if(e=document.getElementById(e)){var t=e.getElementsByTagName("INPUT"),i=e.getElementsByTagName("OPTION"),a=0,r=t.length,n=i.length;if(t)for(a=0;a<r;a++)t[a].checked="";if(i)for(a=0;a<n;a++)i[a].selected=!1}}(f=>{f(".visibility-toggle-link").on("click",function(e){e.preventDefault(),f(this).attr("aria-expanded","true").parent().hide().siblings(".field-visibility-settings").show()}),f(".field-visibility-settings-close").on("click",function(e){e.preventDefault(),f(".visibility-toggle-link").attr("aria-expanded","false");var e=f(this).parent(),t=e.find("input:checked").parent().text();e.hide().siblings(".field-visibility-settings-toggle").children(".current-visibility-level").text(t).end().show()}),0<f(".bb_admin_repeater_group").length&&(f(".bb_admin_repeater_group").each(function(){var t,i=f(this),a=i.attr("id").split("-").pop(),r=i.find(".repeater_group_outer").length;0<r&&(t=[],i.find(".repeater_group_outer").each(function(){var e=f(this),a=(t.push(e.data("set_no")),1<r&&e.find(".repeater_group_inner").hide(),"");e.find(".editfield").each(function(){var e=f(this).find("input[type=text], input[type=number], input[type=email], input[type=phone], input:checked, textarea, select"),t=e.val(),i=e.attr("name"),i=-1<(i?i.split("_"):"").indexOf("visibility");if(""!==f.trim(t)&&!i)return e.is("select")?a=f.trim(e.find("option:selected").text()):a=f.trim(t),!1}),""===a?e.find(".repeater_set_title").addClass("repeater_set_title_empty").html(BB_Member_Admin.empty_field):e.find(".repeater_set_title").html(a)}),i.append('<input type="hidden" name="repeater_set_sequence['+a+']" value="'+t.join(",")+'">'),i.find(".repeater_sets_sortable").sortable({items:".repeater_group_outer",update:function(){var e=[];f(this).find(".repeater_group_outer").each(function(){e.push(f(this).data("set_no"))}),i.find('[name="repeater_set_sequence['+a+']"]').val(e.join(","))}}))}),f(".bb_admin_repeater_group").on("click",".repeater_group_outer .repeater_set_edit",function(e){e.preventDefault(),f(this).closest(".repeater_group_outer").find(".repeater_group_inner").slideToggle(),f(this).parents(".repeater_group_outer").toggleClass("active")}),f(".bb_admin_repeater_group").on("click",".repeater_group_outer .repeater_set_delete",function(e){var t=f(this),i=f(this).parents(".bb_admin_repeater_group").attr("id"),a=f("#"+i).find("#group").val();if(e.preventDefault(),!t.hasClass("disabled")){var r=[];if(f("#"+i+' [name="deleted_field_ids['+a+']"]').length&&(e=f("#"+i+' [name="deleted_field_ids['+a+']"]').val(),r.push(e)),confirm(BB_Member_Admin.confirm_delete_set)){t.closest(".repeater_group_outer").find(".editfield").each(function(){var e=f(this),t=e.find("input,textarea,select").attr("name");void 0!==(t=void 0!==t?t:e.find("textarea.wp-editor-area").attr("name"))&&(t=(t=(t=t.replace("field_","")).replace("_day","")).replace("[]",""),r.push(t))}),t.closest(".repeater_group_outer").remove();for(var n=[],s=(f("#"+i+" .repeater_group_outer").each(function(){n.push(f(this).data("set_no"))}),f("#"+i+' [name="repeater_set_sequence['+a+']"]').val(n.join(",")),f("#"+i+' [name="field_ids['+a+']"]').val().split(",")),d=[],l=s.length,p=r.length,o=0;o<l;o++){for(var _=!1,u=0;u<p;u++)if(s[o]===r[u]){_=!0;break}_||d.push(s[o])}d=d.join(","),f("#"+i+' [name="field_ids['+a+']"]').val(d),f("#"+i+' [name="deleted_field_ids['+a+']"]').length||f("#"+i).append('<input type="hidden" name="deleted_field_ids['+a+']" >'),f("#"+i+' [name="deleted_field_ids['+a+']"]').val(r.join(",")),f("#"+i+" .repeater_group_outer").length}}}),f(".bb_admin_repeater_group #btn_add_repeater_set").click(function(e){e.preventDefault();var t=f(this),i=t.data("group"),a=f("#profile-edit-form-"+i);t.hasClass("disabled")||(t.addClass("disabled"),t.attr("disabled","disabled"),t.css("pointer-events","none"),f.ajax({url:ajaxurl,method:"POST",data:{action:"bb_admin_xprofile_add_repeater_set",_wpnonce:t.data("nonce"),group:i,user_id:f("#user_id").val(),set_no:a.find('input[name="repeater_set_sequence['+i+']"]').val(),existing_field_ids:a.find('input[name="field_ids['+i+']"]').val(),deleted_field_ids:a.find('input[name="deleted_field_ids['+i+']"]').val()},success:function(e){e.success&&e.data&&(a.find(".repeater_group_outer").length?a.find(".repeater_group_outer").last().after(e.data.html):a.find(".repeater_sets_sortable").html(e.data.html),a.find('[name="field_ids['+i+']"]').val(e.data.field_ids.join(",")),a.find('[name="repeater_set_sequence['+i+']"]').val(e.data.set_no.join(",")),a.find(".repeater_sets_sortable").sortable("destroy").sortable(),a.find(".repeater_group_outer").last().addClass("active"),a.find(".repeater_group_outer .repeater_group_inner:last").show(),t.removeClass("disabled"),t.removeAttr("disabled","disabled"),t.css("pointer-events","initial"),e=jQuery(e.data.set_no).get(-1),jQuery("#profile-edit-form-"+i+" .repeater_group_outer[data-set_no="+e+"] .field_type_textarea textarea").each(function(){tinymce.EditorManager.execCommand("mceAddEditor",!1,jQuery(this).attr("name")),"undefined"!=typeof quicktags&&quicktags({id:jQuery(this).attr("name")})}))}}))}),f(".field-visibility-settings").addClass("bp-hide"),f(".visibility-toggle-link").attr("aria-expanded","false"),f(".bb_admin_repeater_group").on("click",".repeater_group_outer .visibility-toggle-link",function(e){e.preventDefault(),f(this).attr("aria-expanded","true"),f(this).parent().addClass("field-visibility-settings-hide bp-hide").siblings(".field-visibility-settings").removeClass("bp-hide").addClass("field-visibility-settings-open")}),f(".bb_admin_repeater_group").on("click",".repeater_group_outer .field-visibility-settings-close",function(e){e.preventDefault();var e=f(this).parent(),t=(0<f(".iradio_minimal").length?e.find("input:checked").parent().next(".field-visibility-text"):e.find("input:checked").parent()).text();e.removeClass("field-visibility-settings-open").addClass("bp-hide").siblings(".field-visibility-settings-toggle").find(".current-visibility-level").text(t).closest(".field-visibility-settings-toggle").addClass("bp-show").removeClass("field-visibility-settings-hide bp-hide"),f(".visibility-toggle-link").attr("aria-expanded","false")})),window.clear=function(e){e&&(e=e.replace("[","\\[").replace("]","\\]"),f("#"+e+" option").length?f.each(f("#"+e+" option"),function(e,t){f(t).prop("selected",!1)}):f("#"+e+" [type=radio]").length&&f.each(f("#"+e+" [type=radio]"),function(e,t){f(t).prop("checked",!1)}))}})(jQuery);