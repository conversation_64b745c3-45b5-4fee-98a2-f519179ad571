<?php
/**
 * BuddyPress Members List Table class.
 *
 * @package BuddyBoss\Members\AdminClasses
 * @since BuddyPress 2.3.0
 */

// Exit if accessed directly.
defined( 'ABSPATH' ) || exit;

/**
 * List table class for signups admin page.
 *
 * @since BuddyPress 2.0.0
 */
class BP_Members_List_Table extends WP_Users_List_Table {

	/**
	 * Signup counts.
	 *
	 * @since BuddyPress 2.0.0
	 *
	 * @var int
	 */
	public $signup_counts = 0;

	/**
	 * Constructor.
	 *
	 * @since BuddyPress 2.0.0
	 */
	public function __construct() {
		// Define singular and plural labels, as well as whether we support AJAX.
		parent::__construct(
			array(
				'ajax'     => false,
				'plural'   => 'signups',
				'singular' => 'signup',
				'screen'   => get_current_screen()->id,
			)
		);
	}

	/**
	 * Set up items for display in the list table.
	 *
	 * Handles filtering of data, sorting, pagination, and any other data
	 * manipulation required prior to rendering.
	 *
	 * @since BuddyPress 2.0.0
	 */
	public function prepare_items() {
		global $usersearch;

		$usersearch       = isset( $_REQUEST['s'] ) ? $_REQUEST['s'] : '';
		$signups_per_page = $this->get_items_per_page( str_replace( '-', '_', "{$this->screen->id}_per_page" ) );
		$paged            = $this->get_pagenum();

		$args = array(
			'offset'     => ( $paged - 1 ) * $signups_per_page,
			'number'     => $signups_per_page,
			'usersearch' => $usersearch,
			'orderby'    => 'signup_id',
			'order'      => 'DESC',
		);

		if ( isset( $_REQUEST['orderby'] ) ) {
			$args['orderby'] = $_REQUEST['orderby'];
		}

		if ( isset( $_REQUEST['order'] ) ) {
			$args['order'] = $_REQUEST['order'];
		}

		$signups = BP_Signup::get( $args );

		$this->items         = $signups['signups'];
		$this->signup_counts = $signups['total'];

		$this->set_pagination_args(
			array(
				'total_items' => $this->signup_counts,
				'per_page'    => $signups_per_page,
			)
		);
	}

	/**
	 * Display the users screen views
	 *
	 * @since BuddyPress 2.5.0
	 *
	 * @global string $role The name of role the users screens is filtered by
	 */
	public function views() {
		global $role;

		// Used to reset the role.
		$reset_role = $role;

		// Temporarly set the role to registered.
		$role = 'registered';

		// Used to reset the screen id once views are displayed.
		$reset_screen_id = $this->screen->id;

		// Temporarly set the screen id to the users one.
		$this->screen->id = 'users';

		// Use the parent function so that other plugins can safely add views.
		parent::views();

		// Reset the role.
		$role = $reset_role;

		// Reset the screen id.
		$this->screen->id = $reset_screen_id;
	}

	/**
	 * Get rid of the extra nav.
	 *
	 * WP_Users_List_Table will add an extra nav to change user's role.
	 * As we're dealing with signups, we don't need this.
	 *
	 * @since BuddyPress 2.0.0
	 *
	 * @param array $which Current table nav item.
	 */
	public function extra_tablenav( $which ) {
		return;
	}

	/**
	 * Specific signups columns.
	 *
	 * @since BuddyPress 2.0.0
	 *
	 * @return array
	 */
	public function get_columns() {

		/**
		 * Filters the single site Members signup columns.
		 *
		 * @since BuddyPress 2.0.0
		 *
		 * @param array $value Array of columns to display.
		 */
		return apply_filters(
			'bp_members_signup_columns',
			array(
				'cb'         => '<input type="checkbox" />',
				'username'   => __( 'Username', 'buddyboss' ),
				'name'       => __( 'Name', 'buddyboss' ),
				'email'      => __( 'Email', 'buddyboss' ),
				'registered' => __( 'Registered', 'buddyboss' ),
				'date_sent'  => __( 'Last Sent', 'buddyboss' ),
				'count_sent' => __( 'Emails Sent', 'buddyboss' ),
			)
		);
	}

	/**
	 * Specific bulk actions for signups.
	 *
	 * @since BuddyPress 2.0.0
	 */
	public function get_bulk_actions() {
		$actions = array(
			'activate' => __( 'Activate', 'buddyboss' ),
			'resend'   => __( 'Email', 'buddyboss' ),
		);

		if ( current_user_can( 'delete_users' ) ) {
			$actions['delete'] = __( 'Delete', 'buddyboss' );
		}

		return $actions;
	}

	/**
	 * The text shown when no items are found.
	 *
	 * Nice job, clean sheet!
	 *
	 * @since BuddyPress 2.0.0
	 */
	public function no_items() {

		if ( bp_get_signup_allowed() ) {
			esc_html_e( 'No pending accounts found.', 'buddyboss' );
		} else {
			$link = false;

			// Specific case when BuddyPress is not network activated.
			if ( is_multisite() && current_user_can( 'manage_network_users' ) ) {
				$link = sprintf( '<a href="%1$s">%2$s</a>', esc_url( network_admin_url( 'settings.php' ) ), esc_html__( 'Edit settings', 'buddyboss' ) );
			} elseif ( current_user_can( 'manage_options' ) ) {
				$link = sprintf( '<a href="%1$s">%2$s</a>', esc_url( bp_get_admin_url( 'options-general.php' ) ), esc_html__( 'Edit settings', 'buddyboss' ) );
			}

			printf( __( 'Registration is disabled. %s', 'buddyboss' ), $link );
		}

	}

	/**
	 * The columns signups can be reordered with.
	 *
	 * @since BuddyPress 2.0.0
	 */
	public function get_sortable_columns() {
		return array(
			'username'   => 'login',
			'email'      => 'email',
			'registered' => 'signup_id',
		);
	}

	/**
	 * Display signups rows.
	 *
	 * @since BuddyPress 2.0.0
	 */
	public function display_rows() {
		$style = '';
		foreach ( $this->items as $userid => $signup_object ) {

			// Avoid a notice error appearing since 4.3.0.
			if ( isset( $signup_object->id ) ) {
				$signup_object->ID = $signup_object->id;
			}

			$style = ( ' class="alternate"' == $style ) ? '' : ' class="alternate"';
			echo "\n\t" . $this->single_row( $signup_object, $style );
		}
	}

	/**
	 * Display a signup row.
	 *
	 * @since BuddyPress 2.0.0
	 *
	 * @see WP_List_Table::single_row() for explanation of params.
	 *
	 * @param object|null $signup_object Signup user object.
	 * @param string      $style         Styles for the row.
	 * @param string      $role          Role to be assigned to user.
	 * @param int         $numposts      Numper of posts.
	 * @return void
	 */
	public function single_row( $signup_object = null, $style = '', $role = '', $numposts = 0 ) {
		echo '<tr' . $style . ' id="signup-' . esc_attr( $signup_object->id ) . '">';
		echo $this->single_row_columns( $signup_object );
		echo '</tr>';
	}

	/**
	 * Markup for the checkbox used to select items for bulk actions.
	 *
	 * @since BuddyPress 2.0.0
	 *
	 * @param object|null $signup_object The signup data object.
	 */
	public function column_cb( $signup_object = null ) {
		?>
		<label class="screen-reader-text" for="signup_<?php echo intval( $signup_object->id ); ?>">
																 <?php
																	/* translators: accessibility text */
																	printf( esc_html__( 'Select user: %s', 'buddyboss' ), $signup_object->user_login );
																	?>
		</label>
		<input type="checkbox" id="signup_<?php echo intval( $signup_object->id ); ?>" name="allsignups[]" value="<?php echo esc_attr( $signup_object->id ); ?>" />
		<?php
	}

	/**
	 * The row actions (delete/activate/email).
	 *
	 * @since BuddyPress 2.0.0
	 *
	 * @param object|null $signup_object The signup data object.
	 */
	public function column_username( $signup_object = null ) {
		$avatar = get_avatar( $signup_object->user_email, 32 );

		// Activation email link.
		$email_link = add_query_arg(
			array(
				'page'      => 'bp-signups',
				'signup_id' => $signup_object->id,
				'action'    => 'resend',
			),
			bp_get_admin_url( 'users.php' )
		);

		// Activate link.
		$activate_link = add_query_arg(
			array(
				'page'      => 'bp-signups',
				'signup_id' => $signup_object->id,
				'action'    => 'activate',
			),
			bp_get_admin_url( 'users.php' )
		);

		// Delete link.
		$delete_link = add_query_arg(
			array(
				'page'      => 'bp-signups',
				'signup_id' => $signup_object->id,
				'action'    => 'delete',
			),
			bp_get_admin_url( 'users.php' )
		);

		echo $avatar . sprintf( '<strong><a href="%1$s" class="edit">%2$s</a></strong><br/>', esc_url( $activate_link ), $signup_object->user_login );

		$actions = array();

		$actions['activate'] = sprintf( '<a href="%1$s">%2$s</a>', esc_url( $activate_link ), __( 'Activate', 'buddyboss' ) );
		$actions['resend']   = sprintf( '<a href="%1$s">%2$s</a>', esc_url( $email_link ), __( 'Email', 'buddyboss' ) );

		if ( current_user_can( 'delete_users' ) ) {
			$actions['delete'] = sprintf( '<a href="%1$s" class="delete">%2$s</a>', esc_url( $delete_link ), __( 'Delete', 'buddyboss' ) );
		}

		/**
		 * Filters the multisite row actions for each user in list.
		 *
		 * @since BuddyPress 2.0.0
		 *
		 * @param array  $actions       Array of actions and corresponding links.
		 * @param object $signup_object The signup data object.
		 */
		$actions = apply_filters( 'bp_members_ms_signup_row_actions', $actions, $signup_object );

		echo $this->row_actions( $actions );
	}

	/**
	 * Display user name, if any.
	 *
	 * @since BuddyPress 2.0.0
	 *
	 * @param object|null $signup_object The signup data object.
	 */
	public function column_name( $signup_object = null ) {
		echo esc_html( $signup_object->user_name );
	}

	/**
	 * Display user email.
	 *
	 * @since BuddyPress 2.0.0
	 *
	 * @param object|null $signup_object The signup data object.
	 */
	public function column_email( $signup_object = null ) {
		printf( '<a href="mailto:%1$s">%2$s</a>', esc_attr( $signup_object->user_email ), esc_html( $signup_object->user_email ) );
	}

	/**
	 * Display registration date.
	 *
	 * @since BuddyPress 2.0.0
	 *
	 * @param object|null $signup_object The signup data object.
	 */
	public function column_registered( $signup_object = null ) {
		echo mysql2date( 'Y/m/d', $signup_object->registered );
	}

	/**
	 * Display the last time an activation email has been sent.
	 *
	 * @since BuddyPress 2.0.0
	 *
	 * @param object|null $signup_object The signup data object.
	 */
	public function column_date_sent( $signup_object = null ) {
		echo mysql2date( 'Y/m/d', $signup_object->date_sent );
	}

	/**
	 * Display number of time an activation email has been sent.
	 *
	 * @since BuddyPress 2.0.0
	 *
	 * @param object|null $signup_object Signup object instance.
	 */
	public function column_count_sent( $signup_object = null ) {
		echo absint( $signup_object->count_sent );
	}

	/**
	 * Allow plugins to add their custom column.
	 *
	 * @since BuddyPress 2.1.0
	 *
	 * @param object|null $signup_object The signup data object.
	 * @param string      $column_name   The column name.
	 * @return string
	 */
	function column_default( $signup_object = null, $column_name = '' ) {

		/**
		 * Filters the single site custom columns for plugins.
		 *
		 * @since BuddyPress 2.1.0
		 *
		 * @param string $column_name   The column name.
		 * @param object $signup_object The signup data object.
		 */
		return apply_filters( 'bp_members_signup_custom_column', '', $column_name, $signup_object );
	}
}
