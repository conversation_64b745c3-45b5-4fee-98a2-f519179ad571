<?php
/**
 * The template for members home
 *
 * This template can be overridden by copying it to yourtheme/buddypress/members/single/home.php.
 *
 * @since   BuddyPress 1.0.0
 * @version 1.0.0
 */

bp_nouveau_member_hook( 'before', 'home_content' );
?>

<div id="item-header" role="complementary" data-bp-item-id="<?php echo esc_attr( bp_displayed_user_id() ); ?>" data-bp-item-component="members" class="users-header single-headers">
	<?php bp_nouveau_member_header_template_part(); ?>
</div><!-- #item-header -->

<div class="bp-wrap">
	<?php
	if ( ! bp_nouveau_is_object_nav_in_sidebar() ) {
		bp_get_template_part( 'members/single/parts/item-nav' );
	}
	?>

	<div id="item-body" class="item-body">
		<?php bp_nouveau_member_template_part(); ?>
	</div><!-- #item-body -->
</div><!-- // .bp-wrap -->

<?php bp_nouveau_member_hook( 'after', 'home_content' ); ?>
