// BP default/generic Table styles.
// @version 3.0.0
.buddypress-wrap {

	.bp-tables-user,
	.bp-tables-report,
	table.wp-profile-fields,
	table.forum {
		width: 100%;
	}

	.bp-tables-user,
	.bp-tables-report,
	table.wp-profile-fields,
	table.forum {

		thead {

			tr {
				background: none;
				border-bottom: 2px solid $bp-border-dark;
			}
		}

		tbody {

			tr {
				background: $off-white;
			}
		}

	}

	.bp-tables-user,
	.bp-tables-report,
	table.wp-profile-fields,
	table.forum {

		tr {

			th,
			td {
				padding: $pad-sml;
				vertical-align: middle;
			}
		}
	}

	.bp-tables-user,
	.bp-tables-report,
	table.wp-profile-fields,
	table.forum {

		tr {

			td.label {
				border-right: 1px solid $light-grey;
				font-weight: 600;
				width: 25%;
			}
		}
	}

	.bp-tables-user,
	.bp-tables-report,
	table.wp-profile-fields, {

		tr.alt {

			td {
				background: $off-white;
			}
		}
	}


	table.profile-fields {

		.data {
			padding: $pad-sml $pad-med;
		}

		.repeater-separator {
			background: transparent;
		}

		tr:last-child {
			border-bottom: none;
		}
	}

	// Set various tables th/td cell sizes
	table.notifications {

		// Increase the cell padding to give better separation of lines
		// when some text lines wrap

		td {
			padding: $pad-med $pad-sml;
		}

		.bulk-select-all,
		.bulk-select-check {
			width: 7%;
		}

		.bulk-select-check {
			vertical-align: middle;
		}

		.title,
		.notification-description,
		.date,
		.notification-since {
			width: 39%;
		}

		.actions,
		.notification-actions {
			width: 15%;
		}
	}

	table.notification-settings,
	table.profile-settings {

		th.title {
			width: 80%;
		}
	}
	// end cell sizings

	table.notifications {

		.notification-actions {

			a.delete,
			a.mark-read {
				display: inline-block;
			}
		}
	}

	table.notification-settings {
		margin-bottom: $marg-med;
		text-align: left;
	}

	#groups-notification-settings {
		margin-bottom: 0;
	}

	table.notifications,
	table.notification-settings {

		th.icon,
		td:first-child {
			display: none;
		}
	}

	table.notification-settings {

		.no,
		.yes {
			text-align: center;
			width: 40px;
			vertical-align: middle;
		}
	}

	table#message-threads {
		clear: both;

		.thread-info {
			min-width: 40%;

			p {
				margin: 0;
			}

			p.thread-excerpt {
				color: $form-text;

				@include font-size(12);
				margin-top: 3px;
			}
		}
	}

	table.profile-fields {
		margin-bottom: 20px;

		&:last-child {
			margin-bottom: 0;
		}
	}

	table.profile-fields p {
		margin: 0;

		&:last-child {
			margin-top: 0;
		}
	}

	table.profile-fields td.data > p {
		margin: 0 0 12px;
	}

} // close .buddypress-wrap

