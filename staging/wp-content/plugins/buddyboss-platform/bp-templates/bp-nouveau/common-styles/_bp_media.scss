.bb-activity-media-wrap {
	display: -webkit-box;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	flex-flow: row wrap;
	margin: 0 -3px;
	align-items: flex-start;
	position: relative;
}

.fb-post.fb_iframe_widget {
	margin: 0 -3px;

	+ .bb-activity-media-wrap {
		margin-top: 15px;
	}

}

.act-grid-1-1 {
	flex: 0 0 100%;
	max-width: 100%;
	min-width: 0;

	.bb-media-length-2 & {
		flex: 0 0 50%;
		max-width: 50%;
		min-width: 0;
	}
}

.act-grid-1-2 {
	flex: 0 0 50%;
	max-width: 50%;
	min-width: 0;
}

.bb-media-length-1 .bb-activity-media-elem {

	&.media-activity {
		flex: none;
		min-width: 190px;
	}

	.entry-img {

		img {
			min-height: auto;
			min-width: auto;
			margin: 0 auto;
			display: block;
		}
	}
}

.bb-activity-media-elem {

	padding: 3px;

	.entry-img {
		background: transparent;
		border-radius: 3px;
		display: block;
		margin: 0;
		position: relative;
		padding-top: 42.56%;
		overflow: hidden;

		.bb-media-length-1 & {
			padding-top: 0;

			img {
				position: static;
			}
		}

		.bb-media-length-2 & {
			padding-top: 72.56%;
		}

		.bb-media-length-3 &.act-grid-1-1 {
			padding-top: 52.56%;
		}

		img {
			position: absolute;
			top: 0;
			bottom: 0;
			left: 0;
			right: 0;
			margin: auto;
			z-index: 0;
			min-height: 100%;
			width: auto;
			min-width: 100%;
			object-fit: cover;
		}
	}

	.bb-photos-length {
		position: absolute;
		width: 100%;
		top: 0;
		left: 0;
		height: 100%;
		text-align: center;
		display: flex;
		align-items: center;
		flex-flow: row wrap;
		justify-content: center;
		background: rgba(0, 0, 0, 0.5);
		color: #fff;
		font-size: 16px;
		line-height: 1.31;

		strong {
			color: inherit;
			display: block;
			font-size: 24px;
			font-weight: 400;
			line-height: 1.33;
		}
	}

	&.media-activity {

		.entry-img:before {
			content: " ";
			position: absolute;
			background: rgba(0, 0, 0, 0.25);
			border-radius: 3px;
			width: 100%;
			height: 100%;
			left: 0;
			top: 0;
			z-index: 1;
			transition: 0.3s all;
			opacity: 0;
			visibility: visible;
		}

		&.is-visible .entry-img:before,
		&:hover .entry-img:before {
			opacity: 1;
			visibility: visible;
		}

	}
}

.bb-media-length-4 {

	.act-grid-1-2 {
		flex: 0 0 33.33%;
		max-width: 33.33%;
		min-width: 0;
	}

	.bb-activity-media-elem.act-grid-1-2 .entry-img {
		padding-top: 72.56%;
	}
}

.bb-media-length-5 {

	.bb-activity-media-elem .entry-img {
		padding-top: 72.56%;
	}

	.act-grid-1-1 {
		flex: 0 0 50%;
		max-width: 50%;
		min-width: 0;

		+ .act-grid-1-2 {
			flex: 0 0 50%;
			max-width: 50%;
			min-width: 0;
		}
	}

	.act-grid-1-2 {
		flex: 0 0 33.33%;
		max-width: 33.33%;
		min-width: 0;
	}
}

.bb-activity-media-wrap.bb-media-length-3 {

	.act-grid-1-2 .entry-img {
		padding-top: 55.56%;
	}

	.act-grid-1-1.bb-vertical-layout {
		flex: 0 0 64%;
		max-width: 64%;
		min-width: 0;

		~ .bb-activity-media-elem {
			flex: 0 0 36%;
			max-width: 36%;
			min-width: 0;

			.entry-img {
				padding-top: 105.6%;
			}
		}

		.entry-img {
			padding-top: 118%;
		}
	}

	.act-grid-1-1.bb-vertical-layout + .bb-activity-media-elem + .bb-activity-media-elem {
		position: absolute;
		right: 0;
		bottom: 0;
		width: 36%;
	}
}

.bb-activity-media-wrap.bb-media-length-4 {

	.act-grid-1-1.bb-vertical-layout {
		flex: 0 0 64%;
		max-width: 64%;
		min-width: 0;

		~ .bb-activity-media-elem {
			flex: 0 0 36%;
			max-width: 36%;
			min-width: 0;

			.entry-img {
				padding-top: 69.1%;
			}
		}

		.entry-img {
			padding-top: 119%;
		}
	}

	.act-grid-1-1.bb-vertical-layout + .bb-activity-media-elem,
	.act-grid-1-1.bb-vertical-layout + .bb-activity-media-elem + .bb-activity-media-elem,
	.act-grid-1-1.bb-vertical-layout + .bb-activity-media-elem + .bb-activity-media-elem + .bb-activity-media-elem {
		position: absolute;
		right: 0;
		top: 0;
		width: 36%;
	}

	.act-grid-1-1.bb-vertical-layout + .bb-activity-media-elem + .bb-activity-media-elem {
		top: 33.4%;
	}

	.act-grid-1-1.bb-vertical-layout + .bb-activity-media-elem + .bb-activity-media-elem + .bb-activity-media-elem {
		top: initial;
		bottom: 0;
	}
}

.bb-activity-media-wrap.bb-media-length-more {

	.bb-activity-media-elem .entry-img {
		padding-top: 82.56%;
	}

	.act-grid-1-1 {
		flex: 0 0 50%;
		max-width: 50%;
		min-width: 0;

		+ .act-grid-1-2 {
			flex: 0 0 50%;
			max-width: 50%;
			min-width: 0;
		}
	}

	.act-grid-1-2 {
		flex: 0 0 33.33%;
		max-width: 33.33%;
		min-width: 0;
	}
}

// Modal
.modal-mask {
	position: fixed;
	z-index: 999991;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	background-color: rgba(0, 0, 0, 0.5);
	transition: opacity 0.3s ease;
	display: flex;
	align-items: center;

	&.bb-white {
		background: rgba(250, 251, 253, 0.5);
	}
}

.modal-wrapper {
	max-width: 615px;
	margin: 50px auto;
	vertical-align: middle;
	width: 90%;

	&.bb-medium {
		max-width: 740px;
	}

	&.bb-large {
		max-width: 1080px;
	}
}

.bp-media-photo-uploader .modal-wrapper {
	max-width: 645px;
}

.modal-container {
	width: auto;
	margin: 0 auto;
	padding: 20px 30px 30px;
	background-color: #fff;
	border-radius: 4px;
	border: 1px solid #d2d4d6;
	box-shadow: 0 6px 24px 0 rgba(18, 43, 70, 0.1);
	transition: all 0.3s ease;

	&.has-folderlocationUI {
		max-height: 90vh;
		overflow: auto;
	}

	&#boss-media-create-album-popup,
	&#boss-video-create-album-popup {

		.bb-model-header {
			margin-bottom: 20px;
		}

		#bp-media-create-album-submit.saving:after,
		#bp-video-create-album-submit.saving:after {
			content: "\ef30";
			font-family: "bb-icons";/* stylelint-disable-line */
			font-size: 18px;
			font-weight: 400;
			line-height: 1;
			margin-left: 10px;
			text-align: center;
			display: inline-block;
			-webkit-animation: spin 3s infinite linear;
			animation: spin 3s infinite linear;
		}

	}

	.bp-video-upload-tab.selected {
		color: #122b46;
	}

}

.bp-media-upload-tab-content,
.bp-video-upload-tab-content {
	padding-top: 30px;
}

#bp-media-uploader .popup-on-fly-create-folder-title.error + .error-box,
#boss-media-create-album-popup .popup-on-fly-create-folder-title.error + .error-box {
	display: block;
	font-size: 13px;
}

.bbm-model-wrap {

	input,
	textarea {
		width: 100%;
	}

	label {
		font-size: 16px;
		line-height: 1.5;
		display: block;
		margin-bottom: 7px;
	}

	textarea {
		min-height: 80px;
		resize: none;
		margin-bottom: 20px;
	}
}

.modal-header h3 {
	margin-top: 0;
	color: #42b983;
}

.modal-body {
	margin: 20px 0;
}

.modal-default-button {
	float: right;
}

#bbpress-forums div.bbp-reply-content .bb-model-header,
.bb-model-header {
	background: #fbfbfc;
	box-shadow: 0 1px 0 0 #eef0f3;
	border-radius: 4px 4px 0 0;
	padding: 17px 30px 16px;
	margin: -20px -30px 30px;
	display: flex;
	align-items: center;

	h4 {
		margin: 0;
		font-size: 17px;
		font-weight: 500;
	}

	.bb-model-close-button {
		margin-left: auto;
		line-height: 1;
	}
}

/*
* The following styles are auto-applied to elements with
* transition="modal" when their visibility is toggled
* by Vue.js.
*
* You can easily play with the modal transition by editing
* these styles.
*/

.modal-enter {
	opacity: 0;
}

.modal-leave-active {
	opacity: 0;
}

.modal-enter .modal-container,
.modal-leave-active .modal-container {
	-webkit-transform: scale(1.1);
	transform: scale(1.1);
}

.bb-model-close-button {
	font-size: 24px;
	color: rgba(17, 49, 80, 0.4);
}

.bb-photos-wrap {

	.bb-title {
		font-size: 28px;
		line-height: 1;
		margin: 0;
	}

	.bb-single-bp-header {

		.button {
			margin-left: 5px;
		}
	}
}

.bb-member-photos-list {
	margin: 0;
	list-style: none;
}

.bb-member-media-header {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	margin-top: 20px;

	&.bb-videos-actions {
		margin-top: 30px;

		.bb-videos-meta {
			margin-bottom: 10px;
		}

	}

	+ #video-stream .grid.bp-list {
		padding-top: 0;
	}

}

.bb-media-meta {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	margin-left: auto;
	margin-bottom: 10px;

	a {
		margin-left: 10px;
	}

	.bb-delete {
		line-height: 1;

		i {
			font-size: 21px;
		}

	}

	.bb-select i {
		border: 1px solid;
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		-webkit-box-pack: center;
		-ms-flex-pack: center;
		justify-content: center;
		font-size: 12px;
		border-radius: 50%;
		width: 19px;
		height: 19px;
	}

	.bb-select.selected i {
		background: #000;
		color: #fff;
		border-color: #000;
	}
}

.bb-photos-date {
	font-size: 14px;
	color: #939597;
}

.buddypress-wrap .bp-list.bb-photo-list {
	display: flex;
	flex-flow: row wrap;
	list-style: none;
	padding: 0;
	margin: 0 -5px;

	li {
		flex: 0 0 20%;
		min-width: 0;
		margin: 0;
		padding: 5px;
	}
}

.buddypress-wrap .bp-subnavs .component-navigation {

	li.selected.loading a {

		&:after {
			content: "\ef30";
			font-weight: 400;
			font-family: "bb-icons"; /* stylelint-disable-line */
			font-size: 20px;
			line-height: 1;
			margin-left: 5px;
			text-align: center;
			display: inline-block;
			vertical-align: middle;
			-webkit-animation: spin 2s infinite linear;
			animation: spin 2s infinite linear;
		}

		.count {
			display: none;
		}
	}
}

.bb-item-cover-wrap {
	position: relative;
	overflow: hidden;
	padding-top: 100%;
	display: block;

	&:hover:before {
		opacity: 1;
	}

	img {
		position: absolute;
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
		margin: auto;
		z-index: 0;
		min-height: 100%;
		width: auto;
		min-width: 100%;
		object-fit: cover;
		image-rendering: -webkit-optimize-contrast; // To solve blurry image issue in chrome
	}

	&:after {
		content: " ";
		position: absolute;
		background: rgba(0, 0, 0, 0.35);
		border-radius: 3px;
		width: 100%;
		height: 100%;
		left: 0;
		top: 0;
		transition: 0.3s all;
	}

	&:not(.selected):after {
		visibility: hidden;
		opacity: 0;
	}

	&.bb-video-cover-wrap img {
		max-height: 100%;
	}
}

.bb-item-thumb {
	position: relative;
	z-index: 1;

	&.is-visible {
		z-index: 11;
	}

	&.selected,
	&.is-visible,
	&:hover {

		.bb-item-cover-wrap:after,
		.bb-action-check-wrap {
			visibility: visible;
			opacity: 1;
		}
	}
}

#media-stream .media-list,
#video-stream .video-list {

	.bb-item-thumb {

		&.loading.deleting {
			opacity: 0.6;
			pointer-events: none;
		}

	}
}

.bb-action-check-wrap {
	position: absolute;
	transition: 0.3s all;
	top: 21px;
	left: 15px;
	color: #fff;
	width: 20px;
	height: 20px;
	font-size: 15px;
	text-align: center;
	line-height: 1;
	z-index: 2;
	visibility: hidden;
	opacity: 0;
}

.bb-media-model-wrapper {

	&.bb-internal-model {
		position: fixed;
		z-index: 999990;
		background-color: rgba(0, 0, 0, 0.9);
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		display: flex;
		align-items: center;
		overflow: auto;
	}
}

#bbpress-forums .bb-media-model-container,
#buddypress .bb-media-model-container {
	max-width: 90%;
	min-width: 90%;
	position: relative;
	margin: auto;
	padding: 20px 0;

	.activity-list {
		border: 0;
		margin: 0;
		padding: 0;
	}
}

#buddypress .bb-media-model-container .activity-list .activity-item,
#bbpress-forums .bb-media-model-container .activity-list .activity-item {
	border: 0;
	box-shadow: none;
	margin: 0;
	max-height: 90vh;
	min-height: 90vh;
	overflow: auto;
	box-sizing: border-box;
}

.bb-media-model-inner {
	background: #fff;
	border-radius: 4px;
	display: flex;
	flex-flow: row wrap;
	position: relative;
	overflow: hidden;
}

a.theater-command {
	position: absolute;
	align-self: center;
	left: 0;
	top: 0;
	z-index: 1;
	width: 20%;
	height: 100%;
	display: flex;
	align-items: center;
	padding: 0 20px;
	opacity: 0;
	visibility: hidden;
	transition: 0.2s all;

	.bb-media-section:hover & {
		opacity: 1;
		visibility: visible;
	}

	[dir="rtl"] & {
		left: auto;
		right: 0;
		justify-content: center;
	}
}

a.theater-command.bb-next-media {
	left: auto;
	right: 0;
	width: 80%;
	justify-content: flex-end;

	[dir="rtl"] & {
		right: auto;
		left: 0;
		width: 20%;
		justify-content: center;
	}
}

.bb-media-section {
	display: flex;
	flex: 0 0 100%;
	align-items: center;
	justify-content: center;
	background: #000;
	position: relative;

	figure {
		margin: 0;

		img {
			max-height: 90vh;
			width: auto;
		}
	}
}

a.bb-close-model {
	position: absolute;
	top: 20px;
	right: 20px;
	line-height: 1;
	z-index: 22;
}

.bb-media-info-section {
	flex: 0 0 100%;
	min-width: 0;

	.item-title {
		margin-bottom: 2px;

		a {
			font-size: 13px;
		}
	}

	.item-avatar {
		margin-right: 12px;
	}

	.avatar {
		border-radius: 50%;
		max-width: 36px;
	}

	.activity-media-description {

		.bp-add-media-activity-description > span {
			font-size: 13px;
			font-weight: 400;

			&.bb-icon-edit {
				font-size: 18px;
			}
		}

		.bp-edit-media-activity-description {

			#add-activity-description {
				width: 100%;
				margin-bottom: 10px;
				line-height: 1.5;
				overflow: auto;
				min-height: 75px;

				&:focus {

					@include box-shadow(0 0 6px $med-light-grey);
				}
			}

			.description-new-submit {
				margin-bottom: 17px;

				#bp-activity-description-new-submit {
					padding: 7px 20px;
					height: auto;
					min-height: 10px;
					line-height: 1;
					opacity: 0.4;
					pointer-events: none;
				}

				#bp-activity-description-new-reset {
					background: 0 0;
					box-shadow: none;
					border: 0;
					font-size: 14px;
					color: #a3a5a9;
					font-weight: 500;
					margin: 0;
					padding: 0 10px;
					width: auto;
				}

				#bp-activity-description-new-reset:hover {
					color: #000;
				}
			}

			&.has-content .description-new-submit #bp-activity-description-new-submit {
				opacity: 1;
				pointer-events: initial;
			}
		}
	}

	.activity-video-description {

		.bp-add-video-activity-description > span {
			font-size: 13px;
			font-weight: 400;
		}

		.bp-edit-video-activity-description {

			#add-activity-description {
				width: 100%;
				margin-bottom: 10px;
				line-height: 1.5;
				overflow: auto;
				min-height: 75px;

				&:focus {

					@include box-shadow(0 0 6px $med-light-grey);
				}
			}

			.description-new-submit {
				margin-bottom: 17px;

				#bp-activity-description-new-submit {
					padding: 7px 20px;
					height: auto;
					min-height: 10px;
					line-height: 1;
					opacity: 0.4;
					pointer-events: none;
				}

				#bp-activity-description-new-reset {
					background: 0 0;
					box-shadow: none;
					border: 0;
					font-size: 14px;
					color: #a3a5a9;
					font-weight: 500;
					margin: 0;
					padding: 0 10px;
					width: auto;
				}

				#bp-activity-description-new-reset:hover {
					color: #000;
				}
			}

			&.has-content .description-new-submit #bp-activity-description-new-submit {
				opacity: 1;
				pointer-events: initial;
			}
		}
	}

	.activity-comments {
		clear: both;
		margin: 0;
		overflow: visible;
		position: relative;
		width: auto;

		> ul {
			background: #fbfbfc;
			margin: 0 -15px;
			padding: 15px 15px 0;
			border-top: 1px solid #eef0f3;
			border-radius: 0 0 4px 4px;
			clear: both;
			list-style: none;
		}

		ul li {
			margin: 0;
			padding: 0;
			padding-bottom: 15px;
		}

		div.acomment-avatar {
			margin: 0 15px 0 0;
			width: auto;
			text-align: left;
		}

		.acomment-meta {
			font-size: 13px;
		}

		.action {
			line-height: 1;
			margin-left: 51px;
		}

		form {
			background: #fbfbfc;

			/*margin: 15px -15px;*/
			padding: 15px 0 0;
		}

		.comment-item form {
			padding-left: 0;
			padding-right: 0;
		}
	}
}

.bb-media-info-header {
	font-size: 14px;
	color: #939597;
	letter-spacing: -0.24px;
	line-height: 16px;
	margin-bottom: 15px;

	.item-title a {
		font-size: 14px;
	}
}

.bb-media-desc {
	min-height: 120px;
}

.bb-model-meta {
	padding: 12px 0;

	a {
		color: $dark-grey;
		opacity: 0.4;
		display: flex;
		align-items: center;
		margin-right: 20px;
	}

	i:before {
		font-size: 22px;
		line-height: 1;
		margin: 0 5px 0 0;
	}

	.bb-count {
		font-size: 13px;
		letter-spacing: -0.24px;
	}
}

#boss-media .bb-media-model-inner .bs-item-wrap .item-meta {
	font-size: 12px;
	color: #a3a5a9;
	letter-spacing: -0.18px;
	line-height: 1.666;

	.item-meta a {
		color: inherit;

		&:hover {
			color: $highlight;
		}
	}
}

.bb-media-info-content {
	position: relative;
}

.bb-media-info-footer {
	background: #fbfbfc;
	border-top: 1px solid #eef0f3;
	padding: 12px 15px;

	.add-comment-wrap {
		padding: 0;
	}

	.add-comment-wrap .add-comment {
		background: #fff;
	}
}

a.bb-smile {
	color: rgba(18, 43, 70, 0.4);
	transition: all linear 0.2s;
	font-size: 20px;

	&:hover {
		color: rgba(18, 43, 70, 0.8);
	}
}

/* Remove page scroll after model open */
body.bb-model-open {
	overflow: hidden;
}

.bs-activity-comments:empty {
	display: none;
}

.bb-single-album-header {
	padding-top: 20px;

	.bb-title {
		font-size: 24px;
		font-weight: 500;
		letter-spacing: -0.24px;
		line-height: 1;
		margin: 0 0 5px !important;
	}

	> a {
		font-size: 14px;
		display: block;
	}

	p {
		opacity: 0.7;
		font-size: 13px;
		color: #000;
		letter-spacing: -0.24px;
	}

	span.bb-sep {
		margin: 0 5px;
	}
}

.bb-album-photos-list {
	list-style: none;
	margin-left: 0;
}

.album-single-view.no-photos {

	.modal-mask {
		position: relative;
		width: auto;
		height: auto;
	}

	.modal-wrapper.bb-large {
		max-width: 100%;
		margin: 0;
		width: 100%;
	}

	.bb-model-header {
		display: none;
	}

	.modal-container {
		padding: 0;
		margin: 0;
		box-shadow: none;
	}

	.dropzone .dz-default {
		background: transparent;
	}
}

.bb-photos-wrap .bb-model-footer {
	padding-top: 20px;
}

@media (max-width: 544px) {

	.bb-photos-wrap #boss-media-create-album-popup .bb-model-footer {
		flex-direction: column;

		.button.push-right {
			margin: 20px auto 0;
		}

		.bb-dropdown-wrap {
			width: 100%;

			select {
				width: 100%;
			}
		}
	}
}

.bb-single-album-page .bb-single-bp-header {

	.bb-add-photos {
		display: none !important;
	}
}

.bb-album-actions {
	text-align: center;
	font-size: 12px;
	margin-top: -25px;
	margin-bottom: 20px;
	padding-top: 10px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-ms-flex-flow: row wrap;
	flex-flow: row wrap;

	select {
		margin-left: auto;
		font-size: 14px;
		color: #939597;
		background-position: right 10px center;
		padding-right: 26px;
		width: 160px;
		height: 30px !important;

		@media (max-width: 480px) {
			margin: 20px auto 0;
		}
	}

	> a:first-of-type {
		margin-left: 0;
	}

	@media (max-width: 480px) {
		-webkit-box-pack: center;
		-ms-flex-pack: center;
		justify-content: center;
	}
}

div#buddypress div.bb-album-actions .button {
	min-height: 24px;
	padding: 4px 15px;
	font-size: 12px;
	margin: 0 10px 0 0;
}

div#buddypress .bb-album-actions .button.error.outline {
	background: none;
	color: #ef3e46;
	border-color: #ef3e46;
}

.bb-member-albums-items {
	list-style: none;
	margin: 0;

	> li:not(:last-child) {
		border-bottom: 1px solid #eef0f3;
		padding-bottom: 30px;
		margin-bottom: 30px;
	}
}

ul.bb-albums-list {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-ms-flex-flow: row wrap;
	flex-flow: row wrap;
	list-style: none;
	overflow: hidden;
	margin: 0 -5px;

	li {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 25%;
		flex: 0 0 25%;
		max-width: 25%;
		min-width: 0;
		padding: 5px;
	}

	a {
		position: relative;
		overflow: hidden;
		padding-top: 100%;
		display: block;
		background: #809ab4;
	}

	img {
		position: absolute;
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
		margin: auto;
		z-index: 0;
		min-height: 100%;
		width: auto;
		min-width: 100%;
		object-fit: cover;
		image-rendering: -webkit-optimize-contrast; // To solve blurry image issue in chrome
	}

	.bb-more-photos {
		font-size: 14px;
		font-weight: 300;
		color: #939597;
		letter-spacing: -0.24px;
		position: absolute;
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
		margin: auto;
		display: flex;
		align-items: center;
		flex-flow: column;
		justify-content: center;

		strong {
			line-height: 1;
			font-size: 24px;
			color: #4d5c6d;
			letter-spacing: -0.24px;
			text-align: center;
			font-weight: 400;
		}
	}

	.load-more {
		width: 100%;
		flex: 100%;
		max-width: 100%;
		text-align: center;
	}

}

#buddypress .activity-list li.load-more,
#buddypress .bb-photo-list li.load-more,
#buddypress .bb-video-list li.load-more {
	margin: 20px auto 10px;
	text-align: center;
	-ms-flex-preferred-size: 100%;
	flex-basis: 100%;
	max-width: 100%;
	pointer-events: none;

	> a {
		max-width: 33%;
		pointer-events: auto;
	}

}

.bb-album-list-item {

	.bs-cover-wrap {
		border-radius: 0;
		padding-top: 93.52%;
	}

	.bs-cover-wrap:after {
		content: " ";
		position: absolute;
		background-image: linear-gradient(0deg, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.01) 60%, rgba(0, 0, 0, 0) 100%);
		border-radius: 3px;
		width: 100%;
		height: 100%;
		left: 0;
		top: 0;
	}
}

.bb-album-cover-wrap {
	position: relative;

	h4 {
		font-size: 20px;
		color: inherit;
		letter-spacing: -0.24px;
		line-height: 1;
		margin: 0 0 12px;
	}

	.bb-album-content-wrap {
		position: absolute;
		bottom: 0;
		z-index: 11;
		left: 0;
		padding: 20px;
		font-size: 13px;
		color: #fff;
		letter-spacing: -0.24px;
		line-height: 1;
		width: 100%;
	}
}

#buddypress .bb-album-content-wrap h4 {
	margin-bottom: 8px;
	letter-spacing: 0.2px;
}

.bb-album-content-wrap .bb-album_date {
	display: block;
	margin-bottom: 8px;
	font-size: 14px;
}

.bb-album-content-wrap .bb-album_stats {
	font-size: 14px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;

	[class*=" bb-icon-"] {
		font-size: 20px;
		margin-right: 7px;

		&.bb-icon-video-alt {
			font-size: 18px;
		}

	}

	.bb-album_stats_photos,
	.bb-album_stats_videos {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
	}

	.bb-album_stats_spacer {
		color: #b2b1b0;
		font-size: 20px;
		margin: 0 8px;
	}

}

#buddypress {

	#bp-media-single-album {

		.bb-single-album-header {
			display: flex;
			flex-wrap: wrap;
			justify-content: center;
			align-items: center;

			#bp-single-album-title {
				padding: 10px 0;
				margin: 0 !important;
			}

			#bb-album-title {
				max-width: 202px;
			}

			#bp-edit-album-title {
				margin: 0 0 0 10px;
			}

			#bp-save-album-title {
				margin: 0 10px 0 10px;
			}

			p {
				flex-basis: 100%;
				width: 100%;
			}
		}
	}

	.bb-media-container.member-media {

		.bb-media-actions-wrap {
			margin-bottom: 20px;
		}

		.bp-navs ~ .bb-media-actions-wrap,
		.bb-media-actions-header .bb-media-actions-wrap {
			margin-bottom: 0;
		}

		.bb-media-actions-header .bb-member-media-header {
			margin-top: 0;
		}

	}

}

@media (min-width: 1025px) {

	.bb-media-model-inner {
		max-height: 90vh;
		min-height: 90vh;
	}

	.bb-media-section {
		flex: 1;
		min-width: 0;
	}

	.bb-media-info-section {
		flex: 0 0 380px;
		min-width: 0;
	}
}

@media (max-width: 1024px) {

	a.bb-close-model {
		top: 10px;
		right: 10px;
	}

	.document.document-theatre {
		display: block;
	}

	#buddypress .bb-document-theater {
		width: calc(100% - 60px);
		min-width: initial;
		margin: 0 30px;
		height: 100%;
		max-width: 100%;

		.bb-media-model-inner {
			height: 100%;
			display: block;

			.bb-media-info-section.document {
				-webkit-box-flex: 0;
				-ms-flex: none;
				flex: none;
				width: 100%;
				margin-left: 0;
				max-height: calc(50vh - 40px);
				border-radius: 0 0 4px 4px;

				.activity-list.bp-list {
					min-height: calc(50vh - 40px);
					max-height: calc(50vh - 40px);
				}

				li.activity_update {
					min-height: calc(50vh - 80px);
					max-height: calc(50vh - 80px);
				}
			}
		}

		.bb-media-section {
			width: 100%;
			height: auto;
			min-height: 50vh;
			max-height: 50vh;
			background-color: #fff;
			-webkit-box-align: center;
			-ms-flex-align: center;
			align-items: center;
			border-radius: 4px 4px 0 8px;

			&.bb-video-preview .document-preview,
			&.bb-video-preview .document-preview .video-js {
				min-height: 50vh;
				max-height: 50vh;
			}

			.img-section {
				max-height: 50vh;
				height: auto;
				display: block;
				text-align: center;
				padding: 5px 0;

				.img-block-wrap {
					height: 48vh;
					padding-bottom: 0;
					padding-top: 0;

					img {
						max-height: calc(50vh - 80px);
					}
				}
			}

			&.bb-media-no-preview .img-section > p {
				font-size: 16px;
				padding: 0 10px;
				word-break: break-word;
			}

			.theater-command {
				opacity: 1;
				visibility: visible;

				&.bb-next-document {
					left: auto;
					right: -30px;
					width: 30px;
					padding: 0;
				}

				&.bb-prev-document {
					left: -30px;
					width: 30px;
					padding: 0;
				}

			}

			.CodeMirror {
				height: calc(50vh - 40px);
				border-radius: 4px 4px 0 0;
			}

			.document-preview {

				.document-text {
					margin-top: 40px;
				}

				h3 {
					right: 0;
				}
			}

		}
	}
}

@media (max-width: 1000px) {

	.bb-album-cover-wrap .bb-album-content-wrap {
		font-size: 11px;
		padding: 15px;
	}

	.bb-album-cover-wrap h4 {
		font-size: 18px;
	}

	ul.bb-albums-list .bb-more-photos {
		font-size: 10px;
	}

	ul.bb-albums-list .bb-more-photos strong {
		font-size: 20px;
	}

	.buddypress-wrap .bp-list.bb-photo-list li {
		flex: 0 0 25%;
	}
}

@media (max-width: 580px) {

	ul.bb-albums-list li {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 50%;
		flex: 0 0 50%;
		max-width: 50%;
		min-width: 0;
	}

	.bb-album-list-item .bs-cover-wrap {
		padding-top: 95.52%;
	}

	.buddypress-wrap .bp-list.bb-photo-list li {
		flex: 0 0 50%;
	}

	.bb-album-content-wrap .bb-album_stats {
		font-size: 12px;

		[class*=" bb-icon-"] {
			font-size: 20px;

			&.bb-icon-video-alt {
				font-size: 17px;
			}

		}

		.bb-album_stats_spacer {
			margin: 0 5px;
		}

	}
}

@media (max-width: 480px) {

	#buddypress {

		.bb-media-container.member-media {

			#bp-media-single-album {

				.bb-single-album-header {

					input[type="text"] {
						display: block;
						width: 100%;
						max-width: 100%;
						margin-bottom: 10px;
					}

				}

			}

			.bb-album-actions {
				flex-wrap: wrap;
				justify-content: space-around;

				.button {
					margin: 0 0 10px 0;
				}

				select {
					width: 100%;
					direction: ltr;
				}

			}

		}

	}

}

.bb-custom-check {
	position: absolute; // take it out of document flow
	opacity: 0; // hide it

	+ label {
		color: #fff;
		position: relative;
		cursor: pointer;
		padding: 0;
	}

	// Box checked
	&:checked + label {
		color: #fff;
	}
}

#media_uploader.dropzone .bb-dz-preview-wrap .dz-image img {
	cursor: default;
}

.bb-dropzone-wrap.bb-has-items {

	.dz-default {
		display: none !important;
	}
}

.bb-dropzone-wrap.bb-has-items {
	margin: -8px;

	.dz-preview {
		margin: 8px;
		width: calc(20% - 16px);
	}
}

.bb-model-footer {

	.button {
		min-width: 100px;

		&.pull-right {
			float: right;
		}
	}

}

.bbm-uploader-model-wrap {

	.bb-model-header {

		h4 {
			font-size: 22px;
		}

		span:not(.bp-reported-type) {
			font-size: 1rem;
			color: #939597;
			letter-spacing: -0.24px;
			line-height: 24px;
		}

		.dashicons {
			font-size: 22px;
			margin: 0;
		}
	}
}

.bb-media-actions-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;

	.groups.single-item & {

		.bb-media-actions-wrap {
			margin-bottom: 0;
		}
	}

	@media (max-width: 540px) {

		.bb-photos-actions {
			flex: 1;
		}
	}
}

.bb-media-actions-wrap {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	margin: 15px 0;

	h2 {
		margin: 0;
	}

	.bb-media-actions-wrap {
		width: 100%;
	}

}

.bb-add-media {
	display: inline-block;
}

.emojionearea.emojionearea-standalone {
	vertical-align: top;
}

.webui-popover.webui-popover {
	z-index: 999991;
}

.text-center {
	text-align: center;
}

.modal-container .bp-existing-media-wrap {
	margin-bottom: 20px;
	max-height: 68vh;
	overflow: auto;

	.media-list li .media-action-wrap,
	.media-list li .video-action-wrap {
		display: none;
	}

	.media-list > li {

		.bp-tooltip[data-bp-tooltip-pos="up"]:after {
			transform: translate(-10px, 0);
		}

		.bp-tooltip[data-bp-tooltip-pos="up"]:before {
			transform: translate(-5px, 0);
		}

		.bb-open-media-theatre,
		.bb-open-video-theatre {
			pointer-events: none;
		}
	}
}

.bb-model-close-button .bb-icon-close {
	font-size: 28px !important;
}

.bb-field-wrap,
.bb-dropdown-wrap {
	margin-bottom: 20px;
}

.bb-field-wrap .bb-allowed-file-types {
	margin-top: 10px;
	font-size: 14px;
	color: #888;
}

.bb-model-footer.flex .bb-dropdown-wrap {
	margin-bottom: 0;
}

.bp-upload-tab {
	margin-right: 15px;
}

#buddypress ul.bp-list.loading,
#bbpress-forums ul.bp-list.loading {
	background: #fff;
	padding: 20px;
	text-align: center;
}

.bb-media-model-inner {

	.post-gif {

		.gif-media-search-dropdown.open {
			width: 300px;
		}

		@media (min-width: 544px) {

			.gif-media-search-dropdown.open {
				left: -40px;

				&:before {
					left: 60px;
				}

			}

			&:nth-child(3) .gif-media-search-dropdown.open {
				left: -70px;

				&:before {
					left: 90px;
				}
			}

			&:nth-child(3) .gif-media-search-dropdown.open {
				left: -110px;

				&:before {
					left: 130px;
				}
			}

			&:nth-child(4) .gif-media-search-dropdown.open {
				left: -140px;

				&:before {
					left: 160px;
				}
			}

			&:nth-child(5) .gif-media-search-dropdown.open {
				left: -170px;

				&:before {
					left: 190px;
				}
			}
		}

	}

	.comment-item & ul .gif-media-search-dropdown.open {
		left: -80px;
	}

	.gif-media-search-dropdown:before {
		left: 70px;
	}

	.comment-item & ul .gif-media-search-dropdown:before {
		left: 100px;
	}

	.emojionearea .emojionearea-picker.emojionearea-picker-position-bottom {
		left: -110px;
	}

	.emojionearea .emojionearea-picker.emojionearea-picker-position-bottom .emojionearea-wrapper:after {
		left: 112px;
	}

	.ac-reply-toolbar.post-media-disabled.post-gif-disabled {

		.emojionearea-picker.emojionearea-picker-position-bottom {
			left: -28px;
		}

		.emojionearea-picker.emojionearea-picker-position-bottom .emojionearea-wrapper:after {
			left: 30px;
		}
	}

	.ac-reply-content .ac-reply-toolbar.post-media-disabled.post-gif-disabled {

		.emojionearea-picker.emojionearea-picker-position-bottom {
			left: -50px;
		}

		.emojionearea-picker.emojionearea-picker-position-bottom .emojionearea-wrapper:after {
			left: 52px;
		}
	}

	.ac-reply-toolbar.post-media-disabled:not(.post-gif-disabled) {

		.emojionearea-picker.emojionearea-picker-position-bottom {
			left: -80px;
		}

		.emojionearea-picker.emojionearea-picker-position-bottom .emojionearea-wrapper:after {
			left: 82px;
		}

		.gif-media-search-dropdown.open {
			left: -30px;
		}

		.gif-media-search-dropdown:before {
			left: 50px;
		}
	}

	.ac-reply-toolbar.post-gif-disabled:not(.post-media-disabled) {

		.emojionearea-picker.emojionearea-picker-position-bottom {
			left: -80px;
		}

		.emojionearea-picker.emojionearea-picker-position-bottom .emojionearea-wrapper:after {
			left: 82px;
		}
	}
}

img.emoji,
img.emojioneemoji {
	display: inline-block;
	margin: 3px;
	width: 24px;
	height: 24px;
}

.emojionearea-filters {

	img.emoji,
	img.emojioneemoji {
		max-width: 20px;
		max-height: 20px;
		width: 20px;
		height: auto;
	}

	.emojionearea-filter-flags {

		img.emojioneemoji {
			max-width: 14px;
		}
	}
}

.bb-media-only-privacy {
	position: absolute;
	top: 20px;
	right: 20px;
	z-index: 111;

	/*For Document Popup*/
	.bb-document-section & {
		top: 0;
		right: 385px;
		background-color: #fff;
		padding: 6px 0 0 10px;

		@media (max-width: 767px) {
			right: 6px;
		}

	}

	.privacy {
		border: 1px solid;
		border-radius: 4px;
		padding: 2px 1px 2px 6px;
	}

	.bb-media-privacy-wrap:before {
		display: none;
	}

	.privacy-wrap {
		display: inline-block;
	}

	.document-privacy,
	.media-privacy {
		display: none;
		margin: 0;
		position: absolute;
		background: #fff;
		box-shadow: 0 2px 7px 1px rgba(0, 0, 0, 0.05), 0 6px 32px 0 rgba(18, 43, 70, 0.1);
		border-radius: 4px;
		padding: 5px 0;
		min-width: 200px;
		right: 0;
		top: 35px;
		z-index: 11;

		&:before {
			content: " ";
			position: absolute;
			width: 0;
			height: 0;
			top: 0;
			right: 25px;
			border: 6px solid #000;
			border-color: #fff #fff transparent transparent;
			-webkit-transform-origin: 0 0;
			-ms-transform-origin: 0 0;
			transform-origin: 0 0;
			-webkit-transform: rotate(-45deg);
			-ms-transform: rotate(-45deg);
			transform: rotate(-45deg);
			box-shadow: 2px -3px 3px 0 rgba(0, 0, 0, 0.02);
			z-index: 1002;
		}

		li {
			cursor: pointer;
			line-height: 1;
			margin: 0;
			padding: 10px 14px;
			position: relative;
			display: flex;
			align-items: center;
			margin-bottom: 1px;

			&:hover {
				background: #f5f5f5;
			}
		}

		.selected {
			background: #f5f5f5;
		}
	}

	.document-privacy.bb-open,
	.media-privacy.bb-open {
		display: block;
	}
}

.document-privacy,
.media-privacy {

	> li {

		&:before {
			content: "\f11f";
			font-family: dashicons;
			font-size: 16px;
			vertical-align: middle;
			display: inline-block;
			margin-right: 10px;
			width: 20px;
			text-align: center;
		}

		&.onlyme:before {
			content: "\f160";
		}

		&.loggedin:before {
			content: "\f307";
		}

		&.friends:before {
			content: "\f110";
		}
	}
}

.dropzone {

	.dz-preview.dz-file-preview .dz-image {
		padding-top: 0;
	}

	.dz-error-title {
		display: none;
	}

	.dz-preview.dz-file-preview .dz-details {
		display: block !important;
	}

	.dz-preview.dz-file-preview .dz-details {
		border-radius: 4px;
		position: relative;
		overflow: hidden;
		padding: 5px 10px;
		display: flex;
		background-color: #f8f8f8;
		border: 1px solid #e7e9eb;
		text-align: center;
		height: 100%;
		min-height: 130px;
		width: 130px;
		flex-flow: column;
		align-items: center;
		justify-content: center;
	}

	.dz-preview.dz-file-preview .dz-details .dz-size {
		margin-bottom: 0;
		font-size: 14px;
		font-weight: 400;
		line-height: 1;
		text-transform: lowercase;

		strong {
			font-weight: 500;
		}
	}

	.dz-preview.dz-file-preview .dz-details .dz-filename {
		display: block;
		width: 100%;
	}

	.dz-preview.dz-file-preview .dz-details .dz-filename span {
		margin-top: 0;
	}

}

.dropzone.media-dropzone {

	.dz-preview:not(.dz-image-preview) {

		.dz-progress-ring-wrap {
			background-color: rgba(0, 0, 0, 0.05);

			[class*=" bb-icon-"] {
				color: rgba(18, 43, 70, 0.5);
			}
		}

		.dz-image {
			background-color: #f8f8f8;
		}
	}

	.dz-preview {

		.dz-image {
			position: relative;

			img {
				width: auto;
				margin: 0 auto;
				position: absolute;
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%);
			}
		}
	}
}

// Dropzone Error
.dropzone .dz-preview.dz-error {

	.dz-error-title {
		display: block;
		font-size: 13px;
		font-weight: 500;
		color: #ef3e46;
		text-align: center;
		position: absolute;
		top: 10px;
		left: 0;
		right: 0;
		z-index: 1;
	}

	.dz-details {

		&:before {
			content: "\ee06";
			font-family: "bb-icons"; /* stylelint-disable-line */
			font-size: 24px;
			font-weight: 400;
			height: 50px;
			width: 50px;
			display: inline-block;
			background-color: rgba(0, 0, 0, 0.05);
			border-radius: 50%;
			line-height: 50px;
			color: rgba(18, 43, 70, 0.5);
		}

		.dz-size {
			display: none;
		}

		.dz-filename {
			font-size: 13px;
			color: rgba(18, 43, 70, 0.5);
			line-height: 1.2;
			margin-top: 10px;

			span {
				margin-top: 3px;
			}

		}
	}

	.dz-error-mark {
		display: none;
	}

	.dz-error-message {
		top: 96%;
		left: 50%;
		right: inherit;
		max-width: 350px;
		min-width: 100%;
		transform: translateX(-50%);
	}

	.dz-remove {
		background-color: transparent;
		box-shadow: none;
		color: #92949d;
	}

}


//Media Dropzone when error
.dropzone.media-dropzone .dz-preview.dz-error {

	.dz-image {
		border-color: #ef3e46;

		img {
			display: none;
		}

	}

	.dz-details {
		display: block;
		position: absolute;
		bottom: 12px;
		left: 10px;
		right: 10px;
		text-align: center;
	}

}

//Document Dropzone when error
.dropzone.document-dropzone .dz-preview.dz-error {

	.dz-error-title {
		top: inherit;
		bottom: 8px;
	}

	&.dz-preview .dz-details {
		border-color: #ef3e46;

		.dz-size,
		.dz-icon {
			display: none;
		}

		&:before {
			content: "\e980";
			font-family: "bb-icons";/* stylelint-disable-line */
			margin-top: 15px;
			font-weight: 400;
		}

		.dz-filename {
			margin-top: 10px;
		}

	}

}

//Video Dropzone when error
.dropzone.video-dropzone .dz-preview.dz-error {

	&.dz-complete.dz-file-preview {
		background-color: #f8f8f8;
	}

	&.dz-file-preview .dz-remove,
	&.dz-complete.dz-file-preview .dz-remove {
		background-color: transparent;
	}

	&.dz-preview .dz-details {
		border-color: #ef3e46;
		background-color: transparent;

		&:before {
			display: none;
		}

		.dz-size,
		.dz-icon {
			display: none;
		}

		&:before {
			content: "\ef65";
			font-family: "bb-icons";/* stylelint-disable-line */
			font-size: 16px;
			font-weight: 400;
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
		}

		.dz-filename {
			font-size: 13px;
			color: rgba(18, 43, 70, 0.5);
			line-height: 1;
			opacity: 1;
			visibility: visible;
			position: absolute;
			bottom: 15px;
			left: 10px;
			right: 10px;
			width: auto;
		}

	}

	.dz-video-thumbnail {
		display: none;
	}

}

#buddypress .bp-media-document-uploader {

	.media-uploader-wrapper {
		margin-bottom: 5px;
	}

	#bp-media-document-prev,
	#bp-media-document-next {
		display: none;
	}

	#bp-media-document-next {
		text-align: center;

		> i {
			margin-right: 5px;
			font-size: 15px;
		}

	}

	#bp-media-document-prev {
		font-size: 15px;
		float: right;
		margin: 8px 20px 0 0;
		color: #a5a7ab;
	}

	.document-uploader-footer {
		display: table;
		width: 100%;

		.bb-dropdown-wrap {
			float: left;
			margin-bottom: 0;
		}

		.bp-document-open-create-popup-folder {
			display: block;
			margin-bottom: 15px;
			clear: both;
			text-transform: capitalize;

			> i {
				margin-right: 5px;
				font-size: 15px;
			}

		}

		#bp-media-document-submit {
			float: right;
		}
	}

	@media screen and (max-width: 480px) {

		.document-uploader-footer {
			-ms-flex-direction: column;
			flex-direction: column;

			.bb-dropdown-wrap {
				width: 100%;
				margin-bottom: 15px;

				select {
					width: 100%;
				}

			}

			#bp-media-document-submit {
				width: 100%;
			}

			#bp-media-document-prev {
				text-align: center;
				margin: 10px 0 0 0;
				width: 100%;
			}

		}

		body.groups & {

			.modal-container .bb-field-steps.bb-field-steps-1.controls-added #bp-dropzone-content {
				margin-bottom: 0;

				#bp-media-document-next {
					display: inline-block !important;
					margin-top: 0;
				}

			}

		}

	}

}

@media screen and (max-width: 480px) {

	#buddypress #bp-media-single-folder,
	#buddypress #bp-media-single-album {

		.modal-container .bb-field-steps.bb-field-steps-1.controls-added #bp-dropzone-content {
			margin-bottom: 0;

			#bp-media-document-next {
				display: inline-block !important;
				margin-top: 0;
			}

		}
	}
}

#buddypress .bp-media-photo-uploader {

	.media-uploader-wrapper {
		margin-bottom: 5px;
	}

	#bp-media-prev,
	#bp-media-photo-next {
		display: none;
	}

	#bp-media-photo-next {
		text-align: center;

		> i {
			margin-right: 5px;
			font-size: 15px;
		}

	}

	#bp-media-prev {
		font-size: 15px;
		float: right;
		margin: 6px 20px 0 0;
		color: #a5a7ab;
	}

	.media-uploader-footer {
		display: table;
		width: 100%;

		.bb-dropdown-wrap {
			float: left;
			margin-bottom: 0;
		}

		.bp-media-open-create-popup-folder {
			display: block;
			margin-bottom: 15px;
			clear: both;
			text-transform: capitalize;

			> i {
				font-size: 15px;
				margin-right: 5px;
			}

		}

		#bp-media-submit {
			float: right;
		}

	}

	@media screen and (max-width: 480px) {

		.media-uploader-footer {
			-ms-flex-direction: column;
			flex-direction: column;

			.bb-dropdown-wrap {
				width: 100%;
				margin-bottom: 15px;

				select {
					width: 100%;
				}

			}

			#bp-media-submit {
				width: 100%;
			}

			#bp-media-prev {
				text-align: center;
				margin: 10px 0 0 0;
				width: 100%;
			}

		}

		body.groups & {

			.modal-container .bb-field-steps.bb-field-steps-1.controls-added #bp-dropzone-content {
				margin-bottom: 0;

				#bp-media-photo-next {
					display: inline-block !important;
					margin-top: 0;
				}

			}

		}

	}

}

#media-folder-document-data-table td.svg-document-icon {
	padding-right: 0;
}

#media-folder-document-data-table td {
	vertical-align: middle;
}

#media-folder-document-data-table td img {
	max-width: 40px;
}

div.dropzone .dz-preview {
	min-width: 130px;

	.dz-success-mark,
	.dz-progress,
	.dz-error-mark {
		top: 50%;
	}

	.dz-filename {
		font-size: 15px;
	}

}

.dz-preview .dz-image {
	height: 130px;

	img {
		max-width: 130px;
		object-fit: cover;
	}
}

#media-stream .document-data-table-head {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	border-bottom: 1px solid #e7e9ec;
	padding: 0 0 8px;

	.data-head-sort-label {
		display: none;
	}

	.data-head {
		font-size: 12px;
		color: #939597;

		span {
			cursor: pointer;
			user-select: none;
			text-transform: uppercase;
		}

		i {
			font-size: 13px;
			-webkit-transform: rotate(180deg) translateY(1px);
			-ms-transform: rotate(180deg) translateY(1px);
			transform: rotate(180deg) translateY(1px);
			transition: all ease 0.3s;
		}

		&.asce {

			i {
				-webkit-transform: rotate(0deg) translateY(-1px);
				-ms-transform: rotate(0deg) translateY(-1px);
				transform: rotate(0deg) translateY(-1px);
			}

		}
	}

	.data-head-name {
		-ms-flex-preferred-size: calc(60% - 50px);
		flex-basis: calc(60% - 50px);
	}

	.data-head-modified {
		-ms-flex-preferred-size: 20%;
		flex-basis: 20%;
	}

	.data-head-visibility {
		-ms-flex-preferred-size: 20%;
		flex-basis: 20%;
	}

	@media screen and (max-width: 540px) {
		-webkit-box-pack: center;
		-ms-flex-pack: center;
		justify-content: center;
		padding: 10px 0;

		.data-head-sort-label {
			position: absolute;
			left: 25px;
			font-size: 14px;
			display: inline-block;
		}

		.data-head-name,
		.data-head-modified,
		.data-head-origin,
		.data-head-visibility {
			-ms-flex-preferred-size: auto !important;
			flex-basis: auto !important;
			width: auto !important;
			padding-right: 15px;
		}

		.data-head-visibility {
			padding-right: 0;
		}

	}

	@media screen and (max-width: 360px) {

		&:before {
			font-size: 12px;
		}

		.data-head-name,
		.data-head-modified,
		.data-head-visibility,
		.data-head-origin {
			padding-right: 7px;
			font-size: 10px;

			i {
				font-size: 6px;
				-webkit-transform: rotate(180deg) translateY(2px);
				-ms-transform: rotate(180deg) translateY(2px);
				transform: rotate(180deg) translateY(2px);
			}
		}
	}
}

#media-folder-document-data-table {

	.pager {
		margin: 20px auto;
		width: 230px;

		a.button.loading:after {
			content: "\ef30";
			font-family: "bb-icons"; /* stylelint-disable-line */
			font-size: 18px;
			font-weight: 400;
			line-height: 1;
			margin-left: 10px;
			text-align: center;
			display: inline-block;
			-webkit-animation: spin 3s infinite linear;
			animation: spin 3s infinite linear;
			vertical-align: middle;
		}
	}

}

.activity-item.activity_update .activity-comments {

	.document-detail-wrap .document-extension-description {
		max-width: calc(100% - 55px);
		vertical-align: middle;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.document-helper-text {
		height: 0;
		overflow: hidden;
		width: 0;
	}

	.bb-activity-media-elem.document-activity {

		&:hover .document-helper-text {
			height: auto;
			overflow: initial;
			width: auto;
		}

		.document-helper-text {
			max-width: calc(100% - 55px);
			vertical-align: middle;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}

	}

	.acomment-content {
		overflow: initial;
	}
}

.document.document-theatre:hover a.theater-command {
	opacity: 1;
	visibility: visible;
}

.bb-document-theater {
	max-width: 1750px;
	width: 90%;
	min-width: auto;

	.bb-media-model-inner {
		overflow: visible;

		.bb-media-info-section.document {

			.avatar {
				border-radius: 50%;
			}

			.activity-comments li .acomment-content {

				.document-preview-wrap,
				.document-text-wrap,
				.document-audio-wrap,
				.more_text_view,
				.document-helper-text-click {
					display: none;
				}

				.document-helper-text-inner {
					text-transform: capitalize;
				}

			}
		}
	}

	.bb-media-section {
		height: 90vh;
		overflow: visible;
		align-items: flex-end;
		background-color: #fff;
		border-radius: 4px 4px 4px 5px;
		position: initial;

		.document-preview {
			width: 100%;
		}

		&.bb-video-preview .document-preview {
			height: 100%;
			background-color: #000;

			.video-js {
				height: 100% !important;
				padding: 0;
				border-radius: 4px 0 0 4px;
			}

		}

		.theater-command {
			justify-content: center;

			&.bb-prev-document {
				left: -45px;
				width: 45px;
				padding: 0;

				[dir="rtl"] & {
					left: auto;
					right: -45px;
				}
			}

			&.bb-next-document {
				left: auto;
				right: -45px;
				width: 45px;
				padding: 0;

				[dir="rtl"] & {
					left: -45px;
					right: auto;
				}
			}
		}

		&.bb-media-no-preview {
			background-color: #fff;
			align-items: center;
			border-radius: 4px;

			.bb-icon-loader {
				font-size: 20px;
			}

			.img-section {
				justify-content: center;
				display: flex;
				align-items: center;
				flex-direction: column;

				> i {
					font-size: 60px;
					margin-bottom: 10px;
					color: #9ca8b4;
				}

				> p {
					font-size: 18px;
					max-width: 100%;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
					padding: 5px;
					margin-bottom: 0;
				}

				a.download-button {
					display: block;
					line-height: 1;
					color: $text-link-hover;
				}

			}
		}

		.img-section {
			overflow: auto;
			height: 90vh;
			background-color: #fff;
			display: -webkit-box;
			display: -ms-flexbox;
			display: flex;
			-webkit-box-align: center;
			-ms-flex-align: center;
			align-items: center;
			-webkit-box-pack: center;
			-ms-flex-pack: center;
			justify-content: center;
			border-radius: 4px 4px 4px 4px;

			.img-block-wrap {
				height: 90vh;
				width: 100%;
				display: -webkit-box;
				display: -ms-flexbox;
				display: flex;
				-webkit-box-align: center;
				-ms-flex-align: center;
				align-items: center;
				-webkit-box-pack: center;
				-ms-flex-pack: center;
				justify-content: center;
				-webkit-box-orient: vertical;
				-webkit-box-direction: normal;
				-ms-flex-direction: column;
				flex-direction: column;
				background-color: #f5f5f5;
				overflow: auto;
				z-index: 1;
				padding-top: 40px;
			}

			img {
				padding: 20px;
				max-height: calc(90vh - 40px);
			}

			a.download-button {
				line-height: normal;
			}

		}

		.document-text {
			width: 100%;
			border-radius: 0 0 0 4px;
		}

		.document-audio,
		.document-video {
			display: flex;
			height: 100%;
			width: 100%;
			padding: 0 15px;
			align-items: center;
			justify-content: center;
			background-color: #fff;
			border-radius: 4px;

			audio {
				outline: 0;
				min-width: 80%;
			}
		}

		h3 {
			background-color: #fff;
			display: block;
			margin-bottom: 0;
			position: absolute;
			top: 0;
			left: 0;
			padding: 6px;
			right: 375px;
			text-align: center;
			border-bottom: 1px solid #ecedee;
			font-size: 16px;
			font-weight: 400;
			border-radius: 4px 4px 0 0;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			z-index: 11;
		}

	}

	.bb-media-info-section.document {
		position: relative;
		z-index: 11;
		background-color: #fff;
		border-left: 1px solid #ecedee;
		margin-left: -1px;
		border-radius: 0 4px 4px 0;
		overflow: hidden;
		max-height: 90vh;
	}

	.document-text-file-data-hidden {
		display: none;
	}

	.CodeMirror { /* stylelint-disable-line */
		height: calc(90vh - 40px);
		border-top: 1px solid #ecedee;
		border-radius: 0 0 0 4px;
	}

	@media screen and (min-width: 1025px) {

		.bb-media-section {
			border-right: 1px solid #ecedee;
		}
	}

	.bb-media-info-section .bb-icon-loader {
		font-size: 20px;
	}

}

.search-document-list .media-folder_items,
#media-folder-document-data-table .media-folder_items {
	margin: 0;
	border-bottom: 1px solid #e7e9ec;
	padding: 15px 0;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;

	&:last-child {
		border-bottom: 0;
	}

	&.is-visible {
		z-index: 1;

		@media screen and (max-width: 980px) {

			.document_more_option_open & {
				z-index: 9;
			}
		}

		.media-folder_actions .media-folder_action__list {
			display: block;
			z-index: 1122;
		}
	}

	.media-folder_icon {
		width: 52px;
		padding-left: 10px;

		> a i {
			font-size: 30px;
			color: #9ca8b4;
			margin-top: 5px;
		}
	}

	.media-folder_visibility,
	.media-folder_modified {
		width: 20%;
	}

	.media-folder_details {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		width: calc(60% - 98px);
		-webkit-box-orient: vertical;
		-webkit-box-direction: normal;
		-ms-flex-flow: column;
		flex-flow: column;

		.media-folder_name {
			color: #122b46;
			font-size: 14px;
			font-weight: 500;
			letter-spacing: -0.24px;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;

			> span {
				max-width: calc(80% - 30px);
				white-space: nowrap;
				display: block;
				overflow: hidden;
				text-overflow: ellipsis;
				float: left;
			}

		}

		.media-folder_name_edit_wrap {
			display: none;
			padding-right: 5px;
			position: relative;

			&.submitting {
				padding-right: 30px;

				.bb-icon-loader {
					position: absolute;
					right: 15px;
					top: 9px;
				}
			}

			> a {
				margin: 5px 0;
			}

			.error-box {
				display: none;
			}
		}

		.media-folder_name_edit {
			height: 32px;
			width: 90%;
			margin: 0 8px 0 0;

			&.error + .error-box {
				display: block;
				line-height: 1.3;
				margin-top: 5px;
				color: #f00;
			}
		}

	}

	.media-folder_details__bottom {
		display: block;
		font-size: 14px;
		line-height: 1.3;
		color: #4d5c6d;

		#bb-folder-privacy {
			max-width: 100%;
		}

		.media-folder_date {
			color: #9b9c9f;
			background-color: transparent;
			display: inline-block;
			font-size: 0.8125rem;
			font-weight: 400;
			letter-spacing: -0.15px;
			line-height: 1.1875;
			vertical-align: middle;
		}

		.media-folder_author {
			display: block;
			font-size: 13px;
			color: #939597;

			a {
				color: #939597;

				&:hover {
					color: $text-link-hover;
				}
			}
		}

		.hide {
			display: none;
		}

		.bp-tooltip {
			display: inline-block;
			vertical-align: middle;
		}
	}

	.media-folder_actions {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		-webkit-box-pack: center;
		-ms-flex-pack: center;
		justify-content: center;
		width: 55px;
		position: relative;

		.media-folder_action__anchor {

			i {
				font-size: 19px;
				color: #939597;
			}

		}

		.media-folder_action__list {
			position: absolute;
			top: 30px;
			right: 0;
			background: #fff;
			box-shadow: 0 2px 7px 1px rgba(0, 0, 0, 0.05), 0 6px 32px 0 rgba(18, 43, 70, 0.1);
			border-radius: 4px;
			width: 198px;
			display: none;

			@media screen and (max-width: 980px) {

				&.bb_more_dropdown.open {
					position: fixed;
					top: 50%;
					bottom: inherit;
					right: inherit;
					width: 220px;
					z-index: 999;
				}
			}

			&:after {
				content: " ";
				position: absolute;
				width: 0;
				height: 0;
				top: 0;
				margin: 0 auto;
				right: 24px;
				box-sizing: border-box;
				border: 6px solid #000;
				border-color: #fff #fff transparent transparent;
				-webkit-transform-origin: 0 0;
				-ms-transform-origin: 0 0;
				transform-origin: 0 0;
				-webkit-transform: rotate(-45deg);
				-ms-transform: rotate(-45deg);
				transform: rotate(-45deg);
				box-shadow: 2px -3px 3px 0 rgba(0, 0, 0, 0.02);
				z-index: 101;
				opacity: 1;
				visibility: visible;
			}

			ul {
				list-style: none;
				margin: 5px 0;
				padding: 0;

				li {

					a {
						padding: 10px 14px;
						display: flex;
						align-items: center;
						font-size: 14px;
						line-height: 1;
						color: #7f868f;

						&:hover {
							background-color: #f5f5f5;
							color: $blue;
						}

						&:before {
							font-family: "bb-icons"; /* stylelint-disable-line */
							font-size: 20px;
							display: inline-block;
							margin-right: 10px;
							width: 20px;
							text-align: center;
						}
					}

					&.rename_file a:before {
						content: "\ee5a";
					}

					&.move_file {

						&.disabled-move a {
							pointer-events: none;
							opacity: 0.7;
						}

						&.disabled-move[data-balloon]:after {
							content: attr(data-balloon);
							white-space: normal;
							max-width: 200px;
							width: 100%;
						}

						a:before {
							content: "\ee9f";
							font-family: "bb-icons";/* stylelint-disable-line */
						}

					}

					&.delete_file a:before {
						content: "\ef48";
						font-family: "bb-icons";/* stylelint-disable-line */
					}

					&.report_file a {
						background-color: transparent;
						text-align: left;
						box-shadow: none;
						border-radius: 0;
						min-height: auto;
						font-weight: 400;

						&:before {
							content: "\ee9c";
							font-family: "bb-icons";/* stylelint-disable-line */
							font-size: 15px;
							vertical-align: middle;
						}

						&:hover {
							background-color: #f5f5f5;
						}

						&.reported-content {
							opacity: 0.8 !important;
							//pointer-events: none;
						}

						&.report-content,
						&.reported-content {
							border: 0;
							-webkit-font-smoothing: auto;
						}

					}

					&.download_file a:before {
						font-family: "bb-icons";/* stylelint-disable-line */
						content: "\ee57";
					}

					&.copy_download_file_url a:before {
						font-family: "bb-icons";/* stylelint-disable-line */
						content: "\ee3b";
					}

					&.privacy_file a:before {
						font-family: "bb-icons";/* stylelint-disable-line */
						content: "\eecc";
					}

				}
			}
		}
	}

	@media screen and (max-width: 540px) {
		-webkit-box-orient: vertical;
		-webkit-box-direction: normal;
		-ms-flex-direction: column;
		flex-direction: column;
		-webkit-box-align: start;
		-ms-flex-align: start;
		align-items: flex-start;
		position: relative;

		.media-folder_icon {
			flex-basis: auto;
			padding-left: 0;
			margin-bottom: 5px;

			img {
				width: 100%;
			}

		}

		.media-folder_visibility,
		.media-folder_details,
		.media-folder_group {
			-ms-flex-preferred-size: 100% !important;
			flex-basis: 100% !important;
			width: 100% !important;
		}

		.media-folder_modified {
			margin-bottom: 5px;
			width: 100%;
		}

		.media-folder_actions {
			position: absolute;
			right: 0;
			top: 19px;

			.media-folder_action__list {
				right: 14px;

				&:after {
					left: inherit;
					right: 9px;
				}

			}
		}
	}

	&:last-child {

		.media-folder_action__list {
			top: initial;
			bottom: 33px;

			&:after {
				top: inherit;
				bottom: -12px;
				right: 7px;
				-webkit-transform: rotate(137deg);
				-ms-transform: rotate(137deg);
				transform: rotate(137deg);
			}

			@media screen and (max-width: 540px) {
				bottom: 30px;
				right: -1px;
			}
		}
	}
}

.search-document-list .media-folder_items {
	display: block;

	.media-folder_icon {
		float: left;
		margin-right: 15px;
		padding-left: 0;
		width: auto;

		> a i {
			align-items: center;
			background-color: #f2f4f5;
			border-radius: 50%;
			color: #9b9c9f;
			display: flex;
			font-size: 24px;
			font-weight: 300;
			height: 48px;
			justify-content: center;
			margin-top: 0;
			width: 48px;
		}

	}

	.media-folder_details {

		.media-folder_name {
			font-size: 14px;
			font-weight: 500;
			letter-spacing: -0.24px;
			line-height: 1.4;
		}

	}

	.media-folder_visibility,
	.media-folder_modified {
		display: inline-block;
		vertical-align: middle;
		width: auto;
	}

	.middot {
		font-size: 20px;
		margin: 0 5px;
	}

	.media-folder_details__bottom {
		color: #9b9c9f;

		.media-folder_author {
			display: inline-block;
			vertical-align: middle;

			a {
				display: inline-block;
			}
		}

		.media-folder_date {
			color: #9b9c9f;
			background-color: transparent;
			font-size: 0.8125rem;
			font-weight: 400;
			letter-spacing: -0.15px;
			line-height: 1.1875;
		}

	}

}

li:hover .search-document-list .media-folder_items .media-folder_icon > a i {
	background-color: #fff;
}

//Album Search result ajax and page
.search-video-list,
.search-media-list {

	.item {
		width: 100%;
	}

	.media-album_items {
		display: block;
		width: 100%;
	}

	.media-album_thumb {
		float: left;
		padding: 0 15px 0 0;
		width: auto;

		img {
			border-radius: 50px !important;
			width: 48px;
			height: 48px;
			object-fit: cover;
		}

		.item-avatar {
			margin-right: 0;
		}
	}

	.media-album_details {
		width: calc(100% - 63px);
		float: right;
		line-height: 1;
		margin-top: 5px;

		.media-album_name {
			color: #122b46;
			display: block;
			font-size: 14px;
			font-weight: 500;
			letter-spacing: -0.24px;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}

		.media-photo_count {
			color: #939597;
			font-size: 13px;
			display: block;
			line-height: 1;
			margin-top: 4px;
		}
	}

	.media-photo_count {
		display: inline-block;
		font-size: 13px;
		line-height: 1.3;
		color: #4d5c6d;
	}

	.media-album_modified {
		display: inline-block;
		width: auto;

		.media-album_details__bottom {
			display: block;
			font-size: 13px;
			line-height: 1.3;
			color: #4d5c6d;

			.media-album_date {
				background-color: transparent;
				color: #9b9c9f;
				display: inline-block;
				font-size: 0.8125rem;
				font-weight: 400;
				letter-spacing: -0.15px;
				line-height: 1.1875;
				vertical-align: middle;
			}

			.media-album_author {
				display: inline-block;
				vertical-align: middle;

				a {
					display: inline-block;
				}
			}
		}
	}

	.media-album_group,
	.media-album_visibility {
		color: #9b9c9f;
		display: inline-block;
		vertical-align: middle;
		font-size: 0.8125rem;
		font-weight: 400;
		letter-spacing: -0.15px;
		line-height: 1.1875;
		width: auto;

		.bp-tooltip {
			display: inline-block;
		}
	}

	.media-album_group_name a {
		color: #4d5c6d;
	}

	.media-album_status {
		display: block;
		font-size: 13px;
		color: #939597;
	}

	.middot {
		display: inline-block;
		margin: 0 6px;
		font-size: 17px;
		line-height: 0;
		vertical-align: middle;
	}

}

//Document Search result ajax and page
.search-document-list {

	.item {
		width: 100%;
	}

	.media-folder_items {
		width: 100%;
		padding: 0;

		.media-folder_details {
			display: inline-block;
			float: right;
			width: calc(100% - 64px);
		}

		.media-folder_group {
			display: inline-block;
			width: auto;

			a {
				color: #9b9c9f;
			}
		}
	}
}

// Search result page
body.search-results .search_results {

	.search-video-list,
	.search-media-list {

		.media-album_thumb {

			img {
				border-radius: 3px !important;
				display: inline-block;
				width: 90px;
				height: 90px;
				max-width: 90px;
				max-height: 90px;
				object-fit: cover;
				overflow: hidden;
			}
		}

		.media-album_details {
			margin-top: 22px;
			width: calc(100% - 106px);
		}

	}

	.search-document-list .media-folder_items {

		.media-folder_icon > a i {
			font-size: 28px;
			border-radius: 3px;
			height: 90px;
			width: 90px;
		}

		.media-folder_details {
			margin-top: 20px;
			width: calc(100% - 106px);
			line-height: 1;
		}
	}

	.search-video-list .media-album_thumb,
	.search-media-list .media-album_thumb,
	.search-document-list .media-folder_items {

		.middot {
			display: inline-block;
			margin: 0 6px;
			font-size: 17px;
			line-height: 0;
			vertical-align: middle;
		}
	}
}

@media screen and (max-width: 540px) {

	.bp-search-ac {

		.search-document-list .media-folder_items {

			.media-folder_details {
				width: calc(100% - 64px) !important;
			}

			.media-folder_visibility {
				flex-basis: initial !important;
				width: auto !important;
			}
		}
	}

}

body.search-results.bb-template-v2 .search_results {

	.media-album_thumb img {
		border-radius: 15px !important;
	}

	.search-document-list .media-folder_items .media-folder_icon > a i {
		border-radius: 15px;
	}

}

.document-type-navs.main-navs {
	border-bottom: 1px solid #e7e9ec;
}

#media-stream.document-parent.group-column {

	.document-data-table-head {

		.data-head-name {
			-ms-flex-preferred-size: calc(40% - 50px);
			flex-basis: calc(40% - 50px);
		}

		.data-head-origin {
			-ms-flex-preferred-size: 20%;
			flex-basis: 20%;
		}

	}

	#media-folder-document-data-table {

		.media-folder_items {

			.media-folder_details {
				width: calc(40% - 100px);
			}

			.media-folder_group {
				width: 20%;

				a {
					color: #4d5c6d;

					&:hover {
						color: $text-link-hover;
					}
				}
			}

			.media-folder_status {
				display: block;
				font-size: 13px;
				color: #939597;
			}

		}
	}

}

.bp-media-header-wrap {

	h2 {
		float: left;
	}

	.bb-media-actions-wrap {
		float: right;
		margin-left: 10px;

		.bb-media-actions a i {
			margin-right: 6px;
		}

	}

	@media screen and (max-width: 1440px) {

		h2 {
			width: 100%;
		}

	}

	@media screen and (max-width: 1220px) {

		.bb-media-actions-wrap,
		.media-search-form {
			width: 100%;
			margin-bottom: 10px !important;

			input[type="text"] {
				width: 100% !important;
			}

			.bb-media-actions,
			a.button {
				width: 100%;
			}

		}
	}

	@media screen and (max-width: 782px) {

		h2 {
			float: none;
		}

		.bb-media-actions-wrap {
			float: right;
		}

	}

	@media screen and (max-width: 640px) {

		.bb-media-actions-wrap {
			float: none;
			margin-bottom: 10px;
			-webkit-box-orient: vertical;
			-webkit-box-direction: normal;
			-ms-flex-direction: column;
			flex-direction: column;

			.bb-media-actions a {
				width: 100%;
			}

		}

	}

	.media-search-form {
		background-color: #fff;
		border: 1px solid #dedfe2;
		border-radius: 100px;
		margin-bottom: 0;
		position: relative;
		display: inline-block;
		vertical-align: middle;

		input[type="text"] {
			background: transparent;
			border-radius: 100px;
			border: 0;
			height: 34px;
			width: 215px;
			font-size: 14px;
			letter-spacing: -0.24px;
			padding: 0 30px 0 35px;
		}

		&:after {
			content: "\ef10";
			font-family: "bb-icons"; /* stylelint-disable-line */
			display: inline-block;
			color: #4d5c6d;
			opacity: 0.4;
			position: absolute;
			left: 10px;
			top: 8px;
			line-height: 1;
			font-size: 16px;
		}
	}
}

.bp-document-listing {
	display: flex;

	.media-search-form {
		float: right;

		.bp-dir-search-form {
			border: 0;

			.nouveau-search-submit {
				width: auto;
				padding: 4px;
				height: auto;
				z-index: 112;
				opacity: 0;
			}

			#group-document-search {
				background-image: none;
				padding: 6px 30px 6px 32px;
				height: auto;
				font-size: 14px;
			}
		}
	}

	.bp-media-header-wrap {
		width: 100%;

		.bb-title {
			width: auto;

			&.loading {
				position: relative;
				padding-right: 30px;

				&:after {
					content: "\ef30";
					font-weight: 400;
					font-family: "bb-icons"; /* stylelint-disable-line */
					font-size: 20px;
					line-height: 1;
					position: absolute;
					right: 0;
					top: 7px;
					text-align: center;
					display: inline-block;
					vertical-align: middle;
					-webkit-animation: spin 2s infinite linear;
					animation: spin 2s infinite linear;
				}
			}
		}
	}

}

.buddypress .buddypress-wrap .search-form-has-reset,
.search-form-has-reset {
	position: relative;

	.search-form_reset {
		font-size: 13px;
		color: $light-text;
		background-color: transparent;
		border: 0;
		padding: 0;
		opacity: 0.4;
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		right: 12px;
		display: none;

		&:hover {
			opacity: 1;
		}
	}

	input[type="search"],
	#bbp_search {
		padding-right: 30px;
	}

}

body.document #bp-media-single-folder .album-single-view .bp-media-header-wrap .bp-media-header-wrap-inner {
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-ms-flex-direction: row;
	flex-direction: row;

	.bb-media-actions {
		width: 75%;
		-webkit-flex-basis: 75%;
		-ms-flex-preferred-size: 75%;
		flex-basis: 75%;
		text-align: right;

		.media-search-form .nouveau-search-submit {
			width: auto;
			padding: 4px;
			height: auto;
			z-index: 112;
		}
	}

	.bb-single-album-header {
		width: 25%;
		-webkit-flex-basis: 25%;
		-ms-flex-preferred-size: 25%;
		flex-basis: 25%;
	}

}

#bp-media-single-folder .bb-single-album-header .bb-title.loading {
	position: relative;
	padding-right: 30px;

	&:after {
		content: "\ef30";
		font-weight: 400;
		font-family: "bb-icons"; /* stylelint-disable-line */
		font-size: 20px;
		line-height: 1;
		margin-left: 10px;
		text-align: center;
		display: inline-block;
		vertical-align: middle;
		-webkit-animation: spin 2s infinite linear;
		animation: spin 2s infinite linear;
	}
}

@media screen and (max-width: 1120px) {

	.bp-media-header-wrap {

		.media-search-form {
			width: 100%;
			margin: 5px 0;

			input[type="text"] {
				width: 100%;
			}

		}

		.album-actions-wrap,
		.bb-media-actions-wrap {
			margin: 5px 0 !important;
		}

	}
}

#bp-media-single-folder {

	.album-single-view .bp-media-header-wrap {

		.bb-single-album-header {
			padding: 0;
			width: 30%;
			-webkit-flex-basis: 30%;
			-ms-flex-preferred-size: 30%;
			flex-basis: 30%;

			.bb-title {
				max-width: 100%;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				text-align: left;
				padding-top: 5px;
			}

		}

		.bb-media-actions {
			width: 70%;
			-webkit-flex-basis: 70%;
			-ms-flex-preferred-size: 70%;
			flex-basis: 70%;
			text-align: right;

			> a {
				margin-left: 10px;

				> i {
					margin-right: 6px;
				}

			}

			> select {
				margin-left: 10px;
				height: 34px;
				font-size: 14px;
			}

			*:not(.button) {
				text-align: left;
			}

			.dropzone * {
				text-align: center;
			}

			.bp-dir-search-form {

				.nouveau-search-submit {
					opacity: 0;
				}

				#group-document-search {
					background-image: none;
					padding: 6px 30px 6px 32px;
					height: auto;
					font-size: 14px;
				}
			}

		}

		@media screen and (max-width: 1480px) {

			.bp-media-header-wrap-inner {
				webkit-box-orient: vertical; /* stylelint-disable-line */
				-webkit-box-direction: normal;
				-ms-flex-direction: column;
				flex-direction: column;
				position: relative;
			}

			.bb-single-album-header {
				margin-bottom: 10px;
			}

			.bb-single-album-header,
			.bb-media-actions {
				width: 100%;
				-webkit-flex-basis: 30%;
				-ms-flex-preferred-size: 30%;
				flex-basis: 100%;
			}

		}

		@media screen and (max-width: 1220px) {

			.bb-media-actions .media-folder_items {
				position: absolute;
				right: 0;
				top: 3px;
			}

			.bb-media-actions > a {
				width: 100%;
				margin: 0 0 10px;
			}

			.bp-media-header-wrap-inner {

				body.document & {
					-webkit-box-orient: vertical;
					-webkit-box-direction: normal;
					-ms-flex-direction: column;
					flex-direction: column;

					.bb-single-album-header,
					.bb-media-actions {
						width: 100%;
						-webkit-flex-basis: 100%;
						-ms-flex-preferred-size: 100%;
						flex-basis: 100%;
					}

				}
			}

		}
	}

	.bp-media-header-wrap {
		display: inline-block;
		width: 100%;
		width: calc(100% + 40px);
		margin: 0 -20px;
		padding: 0 20px 15px;

		.bp-media-header-wrap-inner {
			display: -webkit-box;
			display: -ms-flexbox;
			display: flex;
		}

		.document-breadcrumb {
			list-style: none;
			margin: 10px 0 0;
			text-align: left;
			padding: 0;

			li {
				display: inline-block;
				font-weight: 600;
				vertical-align: middle;
				color: #939597;

				&:after {
					content: "\f345";
					font-family: dashicons; /* stylelint-disable-line */
					vertical-align: middle;
					font-size: 15px;
					margin-right: 6px;
					margin-left: 6px;
					color: inherit;
					font-weight: 400;
					float: right;

					[dir="rtl"] & {
						content: "\f341";
					}
				}

				&:last-child {

					a {
						color: #393e41;
						font-weight: 600;
					}

					&:after {
						display: none;
					}
				}

				a {
					color: #939597;
					font-weight: 400;
				}

			}
		}

		.media-folder_items {
			display: inline-block;
			position: relative;
			text-align: left;

			.media-folder_action__anchor {
				display: inline-block;
				position: relative;
				vertical-align: middle;

				i {
					font-size: 19px;
					margin-top: 5px;
				}

			}

			.media-folder_action__list {
				position: absolute;
				top: 33px;
				right: -20px;
				background: #fff;
				box-shadow: 0 2px 7px 1px rgba(0, 0, 0, 0.05), 0 6px 32px 0 rgba(18, 43, 70, 0.1);
				border-radius: 4px;
				width: 168px;
				z-index: 1;
				display: none;

				@media screen and (max-width: 980px) {

					&.bb_more_dropdown.open {
						position: fixed;
						top: 50%;
						bottom: inherit;
						right: inherit;
						width: 220px;
						z-index: 999;
					}
				}

				&:after {
					content: " ";
					position: absolute;
					width: 0;
					height: 0;
					top: 0;
					margin: 0 auto;
					right: 27px;
					box-sizing: border-box;
					border: 6px solid #000;
					border-color: #fff #fff transparent transparent;
					-webkit-transform-origin: 0 0;
					-ms-transform-origin: 0 0;
					transform-origin: 0 0;
					-webkit-transform: rotate(-45deg);
					-ms-transform: rotate(-45deg);
					transform: rotate(-45deg);
					box-shadow: 2px -3px 3px 0 rgba(0, 0, 0, 0.02);
					z-index: 101;
					opacity: 1;
					visibility: visible;
				}

				ul {
					list-style: none;
					margin: 5px 0;
					padding: 0;

					li {

						.media-edit-folder {
							display: inline-block;
							width: 100%;

							input {
								width: calc(100% - 20px);
								margin: 0 10px;
								padding: 3px 7px;
								height: 34px;
								font-size: 14px;
							}

							#bp-save-folder-title {
								float: right;
								margin: 5px 10px 0 0;
								width: calc(50% - 10px);
								text-align: center;
							}

							#bp-cancel-edit-album-title {
								float: left;
								margin: 5px 0 0 10px;
								text-align: center;
								width: calc(50% - 10px);
							}
						}

						a {
							padding: 10px 14px;
							display: flex;
							align-items: center;
							font-size: 14px;
							line-height: 1;
							color: #7f868f;

							&:hover {
								background-color: #f5f5f5;
								color: $blue;
							}

							i:before {
								font-family: "bb-icons"; /* stylelint-disable-line */
								font-size: 20px;
								display: inline-block;
								margin-right: 10px;
								width: 20px;
								text-align: center !important;
							}
						}

					}
				}
			}

			&.is-visible {

				.media-folder_action__list {
					display: block;
					z-index: 112;
				}

			}
		}
	}

	@media screen and (max-width: 1120px) {

		.album-single-view {

			.bp-media-header-wrap {

				.bp-media-header-wrap-inner {
					-webkit-box-orient: vertical;
					-webkit-box-direction: normal;
					-webkit-flex-direction: column;
					-ms-flex-direction: column;
					flex-direction: column;
				}

				.bb-single-album-header {
					width: 100%;
					-webkit-flex-basis: 100%;
					-ms-flex-preferred-size: 100%;
					flex-basis: 100%;
				}

				.bb-media-actions {
					float: left;
					width: 100%;
					-webkit-flex-basis: 100%;
					-ms-flex-preferred-size: 100%;
					flex-basis: 100%;
					padding-right: 25px;

					> select {
						float: right;
					}

					.media-folder_items {
						position: absolute;
						right: 0;
						top: 3px;
					}

					> a {
						margin-left: 0;
						margin-right: 10px;
					}

				}
			}
		}
	}

	@media screen and (max-width: 640px) {

		.album-single-view {

			.bp-media-header-wrap {

				.bb-media-actions {
					padding-right: 0;

					> a,
					> select {
						width: 100%;
						margin: 5px 0;
					}

				}

				.bb-single-album-header .bb-title {
					padding-right: 25px;
				}

			}
		}
	}
}

#bp-media-create-folder,
#bp-media-edit-child-folder,
#bp-media-create-child-folder {

	#bb-folder-location {
		display: none;
	}

	.bb-dropdown-wrap select {
		width: 100%;
	}

	.bb-model-footer {
		text-align: right !important;
	}

}

.modal-container,
#bp-media-create-folder {

	.error-box {
		display: none;
		line-height: 1.3;
		margin-top: 5px;
	}

	#bb-album-child-title.error + .error-box,
	#bb-album-title.error + .error-box {
		display: block;
	}
}

#bp-video-uploader .modal-container,
.modal-container.has-folderlocationUI {

	.bb-model-footer a {

		&.saving,
		&.loading {
			pointer-events: none;

			&:after {
				content: "\ef30";
				font-family: "bb-icons"; /* stylelint-disable-line */
				font-size: 18px;
				font-weight: 400;
				line-height: 1;
				margin-left: 10px;
				text-align: center;
				display: inline-block;
				-webkit-animation: spin 3s infinite linear;
				animation: spin 3s infinite linear;
			}

		}

		&.is-disabled {
			pointer-events: none;
			opacity: 0.5;
		}
	}
}

.bp-video-move-file,
.bp-media-move-file,
.bp-media-move-folder {

	&.move-folder-popup-group .location-folder-list-wrap-main .location-folder-list-wrap .breadcrumbs-append-ul-li .breadcrumb .item {
		padding: 2px 0 22px;
	}

	.bb-model-footer {
		display: inline-block;
		width: 100%;
		text-align: right;

		a {
			display: inline-block;
			vertical-align: middle;
			margin: 0 20px 0 0;

			&:last-child {
				margin-right: 0;
			}

			&.bp-document-open-create-popup-folder,
			&.bp-video-open-create-popup-album,
			&.bp-media-open-create-popup-folder {
				float: left;
				margin-top: 7px;
				text-transform: capitalize;
			}

		}
	}

	.close-create-popup-album {
		display: inline-block;
		vertical-align: middle;
		margin: 0 20px 0 0;
	}

	.bb-model-header p {
		font-size: 17px;
		font-weight: 500;
		margin: 0;
	}

	@media screen and (max-width: 520px) {

		.bb-model-footer a.bp-media-open-create-popup-folder,
		.bb-model-footer a.bp-document-open-create-popup-folder {
			display: block;
			float: none;
			margin: 0 0 15px 0;
			text-align: left;
		}

	}

	.ac-media-close-button,
	.close-create-popup-album {
		color: #a5a7ab;
	}

}

.bb-model-footer {

	a {

		&.ac-document-close-button,
		&.ac-folder-close-button,
		&.ac-video-close-button {
			color: #a5a7ab;
		}
	}
}

.bp-video-uploader,
.bp-media-photo-uploader {

	.bb-model-header p {
		font-size: 17px;
		font-weight: 500;
		margin: 0;
	}

	.close-create-popup-album {
		display: inline-block;
		vertical-align: middle;
		margin: 0 20px 0 0;
		color: #a5a7ab;
	}

}


.bp-media-document-uploader .bb-model-header > p {
	font-weight: 500;
	margin: 0;
}

.popup-on-fly-create-album .bb-field-wrap .bb-dropdown-wrap select,
.popup-on-fly-create-folder .bb-field-wrap .bb-dropdown-wrap select {
	width: 100%;
}

.db-modal-buttons {
	text-align: right !important;
	margin-top: 20px;

	.close-create-popup-folder {
		color: #a5a7ab;
		margin-right: 20px;
		font-size: 14px;
	}

	.button {
		min-width: 130px;

		&.loading {
			pointer-events: none;

			&:after {
				content: "\ef30";
				font-family: "bb-icons"; /* stylelint-disable-line */
				font-weight: 400;
				font-size: 18px;
				line-height: 1;
				margin-left: 10px;
				text-align: center;
				display: inline-block;
				-webkit-animation: spin 3s infinite linear;
				animation: spin 3s infinite linear;
			}

		}
	}
}

.has-folderlocationUI .bb-field-wrap-search {
	position: relative;

	&:after {
		content: "\ef10";
		font-family: "bb-icons"; /* stylelint-disable-line */
		display: inline-block;
		color: #4d5c6d;
		opacity: 0.4;
		position: absolute;
		left: 12px;
		bottom: 12px;
		line-height: 1;
		font-size: 16px;
	}

	.ac_document_search_folder {
		border-radius: 30px;
		padding-left: 35px;
	}

}

.location-album-list-wrap-main,
.location-folder-list-wrap-main {

	.bb-album-destination,
	.bb-folder-destination {
		cursor: pointer;
	}

	.no-folder-exists,
	.no-album-exists {
		margin: 20px 0 0 0;
		display: block;
	}

	.ac_document_search_folder_list,
	.location-folder-list-wrap,
	.location-album-list-wrap {

		h4 {
			margin: 15px 0 0 0 !important;
			font-size: 17px;
			font-weight: 500;
		}

		&.has-error {
			border-color: #f00;
		}

		.breadcrumbs-append-ul-li .item {
			padding: 8px 0;
			display: inline-block;
			vertical-align: middle;
			font-size: 12px;
			white-space: nowrap;

			span:before {
				content: "\e827";
				font-family: "bb-icons"; /* stylelint-disable-line */
				font-size: 20px;
				vertical-align: middle;
				display: inline-block;
				line-height: 1;

				[dir="rtl"] & {
					content: "\e8ab";
					float: left;
					margin-top: 4px;
				}
			}

			span {
				display: inline-block !important;
				max-width: 210px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				vertical-align: middle;

				@media screen and (max-width: 520px) {
					max-width: 120px;
				}

				&:not(.more_options):hover {
					text-decoration: underline;
					cursor: pointer;
				}

			}

			span:first-child:before {
				display: none;
			}

			span.hidden {
				display: none !important;
			}
		}

		.location-folder-back {
			display: none;
			cursor: pointer;
			vertical-align: middle;

			i {
				display: inline-block;
				vertical-align: middle;
				font-size: 22px;
				height: 26px;
				line-height: 26px;
				width: 15px;
				user-select: none;
				color: #939597;
			}
		}

		.location-album-list,
		.location-folder-list {
			overflow: auto;
			max-height: 230px;
			min-height: 230px;
			list-style: none;
			margin: 0;
			background-color: #fff;
			border: 1px solid #e7e9ec;
			border-radius: 4px;

			&.is-loading {
				display: flex;
				align-items: center;
				justify-content: center;

				.bb-icon-loader {
					font-size: 20px;
				}
			}

			&.has-error {
				border-color: #f00;
			}

			li {
				position: relative;
				margin: 0;
				padding: 0;

				&:first-child {
					margin-top: 0;
				}

				&.is-disabled {
					cursor: auto;
					opacity: 0.5;
					pointer-events: none;
				}

				span {
					padding: 8px 15px;
					border-bottom: 1px solid #e7e9ec;
					display: block;
					cursor: pointer;
					font-size: 14px;
					width: 100%;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;

					&:before {
						content: "\ee9f";
						font-family: "bb-icons"; /* stylelint-disable-line */
						color: #939597;
						margin-right: 12px;
						font-size: 16px;

						[dir="rtl"] & {
							float: left;
							margin-left: 0;
						}
					}

					&:hover {
						background-color: #fbfbfc;
					}

					&.selected {
						background-color: #f7fafe;
					}

					&.disabled {
						pointer-events: none;
					}
				}

				.sub-menu-anchor {
					position: absolute;
					right: 5px;
					top: 50%;
					font-size: 34px;
					line-height: 1;
					cursor: pointer;
					transform: translateY(-50%);
					display: inline-block;
					height: auto;
					width: auto;
					transition: all ease 0.3s;

					[dir="rtl"] & {
						right: initial;
						left: 5px;

						&:before {
							content: "\e8ab";
						}
					}
				}

				ul {
					display: none;
					margin: 0;
					padding: 0;
					list-style: none;
				}

				&:last-child {

					> span {
						border-bottom: 0;
					}

				}
			}

			&::-webkit-scrollbar {
				width: 5px;
			}

			&::-webkit-scrollbar-thumb {
				background-color: #a4acb4;
				border-radius: 10px;
			}

		}
	}

	&.is-mobile {

		.location-folder-list-wrap .location-folder-list li .sub-menu-anchor {
			visibility: visible;
			opacity: 1;
		}

	}
}

.location-album-list-wrap-main .location-album-list-wrap .location-album-list li span:before {
	content: "\eeb8";
	font-family: "bb-icons";/* stylelint-disable-line */
	font-size: 18px;
}

.modal-container .bb-field-steps {
	display: none;

	&.bb-field-steps-1 {
		display: block;

		&.controls-added #bp-dropzone-content {

			body.groups &,
			#bp-media-single-folder &,
			#bp-media-single-album & {
				margin-bottom: -55px;

				#bp-media-photo-next,
				#bp-media-document-next {
					display: inline-block !important;
					margin-top: 15px;
				}

			}

		}

	}

	#bp-media-create-child-folder-submit,
	#bp-media-create-folder-submit,
	.bb-field-steps-next,
	#bp-media-edit-child-folder-submit {
		min-width: 100px;
		float: right;
	}

	.bb-field-steps-previous {
		float: left;
	}

	@media screen and (max-width: 420px) {

		.bb-field-steps-previous,
		#bp-media-create-folder-submit {
			float: none;
			width: 100% !important;
			margin-bottom: 10px;
		}

	}
}

@media screen and (max-width: 420px) {

	.bp-media-document-uploader {

		.bb-model-footer {
			-ms-flex-direction: column;
			flex-direction: column;

			> a {
				width: 100% !important;
				margin-bottom: 10px;
			}

		}
	}
}

body.buddyboss-theme {

	&.directory.media .site-main,
	&.directory.document .site-main,
	&.directory.video .site-main {
		position: relative;

		.media-options,
		.document-options,
		.video-options {
			position: absolute;
			right: 0;
			top: 0;
			margin: 0;

			@media screen and (max-width: 1024px) {
				position: static;
				margin: 0;
			}
		}
	}
}

.directory.media .media-options,
.directory.document .document-options,
.directory.video .video-options {

	.subnav-filters .subnav-search .bp-dir-search-form {

		&:before {
			top: 7px;
		}

	}

}

.directory.media .media-options #dir-media-search,
.directory.document .document-options #dir-document-search,
.directory.video .video-options #dir-video-search {
	border: 0;
	background-color: transparent;
	padding-left: 32px;
	font-size: 14px;
	height: 39px;
	outline: 0;
}

.buddypress-wrap {

	.bp-invites-search-form,
	form.bp-dir-search-form,
	form.bp-messages-search-form {

		&:before {
			content: "\ef10";
			color: inherit;
			font-family: bb-icons;
			font-size: 17px;
			opacity: 0.4;
			position: absolute;
			left: 9px;
			top: 4px;
		}

		input[type="search"]::-webkit-search-cancel-button {
			display: none;
		}

	}
}

#dir-activity-search-form {

	#dir-activity-search-submit {
		opacity: 0;
	}

	#dir-activity-search {
		border: 0;
		background-color: transparent;
		outline: 0;
		padding-left: 35px;
		font-size: 14px;
		height: 34px;
	}
}

.bb-subnav-filters-container-main {
	display: flex;
	align-items: center;

	&:not(:last-child) {
		margin-right: 10px;
	}
}

.activity-head-bar .bb-subnav-filters-container {
	position: relative;
	z-index: 11;

	&.bb-subnav-filters-search {
		display: flex;
		align-items: center;
		min-height: 40px;
		margin-right: auto;

		.subnav-filters-opener {
			border-radius: 6px;
			border: 1px solid transparent;
			background: #f2f4f5;
			height: 30px;
			width: 30px;
			padding: 0;
			display: flex;
			align-items: center;
			justify-content: center;
			cursor: pointer;

			> i {
				color: #5a5a5a;
			}
		}
	}

	.subnav-filters-modal {
		opacity: 0;
		visibility: hidden;
		position: absolute;
		top: 40px;
		left: 0;
		border-radius: 8px;
		background: #fff;
		box-shadow: 0 3px 8px 0 rgba(0, 0, 0, 0.06), 0 6px 24px 0 rgba(0, 0, 0, 0.12);
		padding: 9px 7px;

		.subnav-search {
			float: none;
		}

		form.bp-dir-search-form {
			display: flex;
			border: 0;
			float: none;

			&:focus-within {

				.buddypress-wrap & {
					box-shadow: none;
				}
			}

			input[type="search"] {

				#buddypress & {
					box-shadow: none;
					width: 260px;
				}
			}
		}
	}

	&.bb-subnav-filters-search .subnav-filters-modal {
		display: none;
		position: static;
		border: 1px solid #b7b7b7;
		opacity: 1;
		visibility: visible;
		box-shadow: none;
		padding: 2px 4px;
		margin-bottom: 0;
	}

	&.active {

		&.bb-subnav-filters-search {

			.subnav-filters-modal {
				display: block;
			}

			.subnav-filters-opener {
				display: none;
			}

			&.loading {

				.search-form_reset {
					line-height: 1;
					opacity: 1;
					pointer-events: none;

					[class^="bb-icon-"]:before {
						content: "\ef30";
						font-weight: 300;
						font-size: 18px;
						line-height: 1;
						animation: spin 2s infinite linear;
						display: inline-block;
					}
				}
			}
		}

		.subnav-filters-modal {
			opacity: 1;
			visibility: visible;
		}
	}

	&.bb-subnav-filters-search {

		&.loading {

			.search-form_reset {
				line-height: 1;
				opacity: 1;
				pointer-events: none;

				[class^="bb-icon-"]:before {
					content: "\ef30";
					font-weight: 300;
					font-size: 18px;
					line-height: 1;
					animation: spin 2s infinite linear;
					display: inline-block;
				}
			}

			&.active .search-form_reset {
				display: flex !important;
				box-shadow: none;
			}
		}
	}

	&.bb-subnav-filters-filtering {

		.subnav-filters-opener {
			display: flex;
			align-items: center;
			padding: 0;
			background-color: transparent;
			border: 0;
			color: var(--bb-body-text-color);
			cursor: pointer;

			&:focus,
			&:hover {
				background-color: transparent;
				color: var(--bb-body-text-color);
				box-shadow: none;
			}

			span {
				font-size: 14px;
				font-weight: 500;
			}

			&[aria-controls="bb-subnav-filter-by"] {

				span {
					text-transform: lowercase;
				}
			}
		}

		.subnav-filters-modal {
			left: auto;
			right: -20px;
			padding: 10px;
			width: 230px;

			ul {
				list-style: none;
				margin: 0;
				padding: 0;
			}

			li {

				a {
					display: block;
					color: #5a5a5a;
					font-size: 13px;
					font-weight: 500;
					line-height: 1.2;
					padding: 10px 16px;
					border-radius: 6px;
					position: relative;
					text-decoration: none;

					&:hover {
						background-color: #f2f4f5;
					}
				}

				&.selected a:after {
					content: "\e876";
					color: #385dff;
					font-family: bb-icons;
					font-size: 16px;
					font-weight: 400;
					position: absolute;
					right: 10px;
					top: 50%;
					transform: translateY(-50%);
				}
			}
		}

		&.bb-subnav-filters-labels-only {

			> .subnav-filters-opener {
				pointer-events: none;

				i.bb-icon-angle-down {
					display: none;
				}
			}
		}
	}
}

.activity-head-bar .activity-type-navs + .bb-subnav-filters-search {
	margin-left: 20px;
}

.has-sidebar #buddypress .bb-profile-grid:has(.widget-area) {

	.activity-head-bar {

		.bb-subnav-filters-container .subnav-filters-modal form.bp-dir-search-form input[type="search"] {
			width: 200px;
		}

		@media screen and (min-width: 1131px) and (max-width: 1460px) {
			flex-wrap: wrap;

			.bb-subnav-filters-container {
				width: 100%;
				margin: 10px 0;
			}
		}
	}

}

@media screen and (min-width: 800px) and (max-width: 1220px) {

	body.activity-sidebar-right .activity-head-bar {
		flex-wrap: wrap;
		justify-content: flex-end;

		.bb-subnav-filters-container.bb-subnav-filters-search {
			width: 100%;
			margin-bottom: 20px;
		}
	}
}

.activity-head-bar {
	display: flex;
	align-items: center;
	margin: 20px 0;

	> .activity-type-navs {
		flex: 0 0 auto;
	}

	> .subnav-filters {
		margin: 0 auto 0 0;

		.buddypress-wrap .bb-profile-grid & {
			margin-bottom: 0;
		}
	}

	.bb-subnav-filters-label {
		font-size: 14px;
		margin-right: 4px;
		line-height: 1;
	}

	.bb-icon-loader {
		display: none;
	}

	&:has(li.loading):not(:has(.bb-subnav-filters-search.loading)) {

		.bb-icon-loader {
			display: inline-block;
			margin: 0 15px 0 auto;
			font-size: 22px;
			line-height: 1;
		}
	}
}

.buddypress-wrap .activity-head-bar {

	.subnav-filters .group-search.loading {

		.search-form_reset {
			display: flex !important;
			box-shadow: none;

			[class^="bb-icon-"] {
				line-height: 1;
				opacity: 1;
				pointer-events: none;

				&:before {
					content: "\ef30";
					font-weight: 300;
					font-size: 18px;
					line-height: 1;
					animation: spin 2s infinite linear;
					display: inline-block;
				}
			}
		}

		&:not(#specificity):after {
			display: none;
		}
	}
}

@media screen and (max-width: 680px) {

	.activity-head-bar {
		justify-content: center;
		flex-wrap: wrap;

		&:has(li.loading):not(:has(.bb-subnav-filters-search.loading)) .bb-icon-loader {
			margin: 0 10px 0 0;
		}
	}

	.activity-head-bar .bb-subnav-filters-container {

		&.bb-subnav-filters-search {
			margin-bottom: 10px;
			width: 100%;
			min-height: auto;
			margin-right: 0;

			.group-search.activity-search {
				margin: 0;
				float: none;
			}

			.subnav-filters-opener {
				display: none;
			}

			.subnav-filters-modal {
				display: block;
				width: 100%;

				form.bp-dir-search-form input[type="search"] {

					#buddypress & {
						width: 100%;
					}
				}

				.subnav-search {
					margin: 0;

					.activity-search.bp-search {
						margin: 0;
						float: none;
					}
				}
			}
		}

		&.bb-subnav-filters-filtering #bb-subnav-filter-show {
			right: -70px;
		}
	}

}

.directory.media,
.directory.document,
.directory.video {

	.bp-subnavs li.selected a:hover span {
		color: #fff;
	}

	.entry-header .entry-title {
		text-align: left;
	}

}

.directory.media .media-options,
.directory.document .document-options,
.directory.video .video-options {

	> .bb-subnav-filters-search {
		display: inline-block;
	}

	a.button > i {
		margin-right: 7px;
		font-size: 18px;
	}

	> * {
		margin-left: 6px;
	}

	@media screen and (max-width: 1024px) {

		> * {
			margin-bottom: 15px;
			width: 100% !important;
			display: inline-block;
			box-sizing: border-box;
		}

		.subnav-filters .subnav-search {
			width: 100%;

			.bp-search {
				width: 100%;

				.bp-dir-search-form {
					width: 100%;
				}
			}
		}

	}

	.subnav-filters {
		display: inline-block;
		vertical-align: middle;

		.subnav-search {
			margin: 0;
			position: initial;

			.bp-dir-search-form {
				background-color: #fff;
				border: 1px solid #dedfe2;
				box-shadow: none;
				border-radius: 100px;
				margin: 0;
				position: relative;

				button[type="submit"] {
					position: absolute;
					opacity: 0;
					visibility: hidden;
					width: 0;
					height: 0;
				}
			}
		}
	}
}

#media-stream.document-parent {
	background-color: #fff;
	border-radius: 4px;
	border: 1px solid #e7e9ec;
	margin-top: 20px;

	.document-data-table-head {
		padding: 10px 20px 8px;
	}

	#media-folder-document-data-table {

		.media-folder_items {
			margin: 0 20px;
		}

	}

	> #bp-ajax-loader,
	> .bp-feedback.bp-messages {
		margin: -1px;
	}
}

.message-members-list.member-popup .modal-container,
.moderation-popup .modal-container {
	max-height: 80vh;
	overflow: auto;

	.bb-model-header {
		margin-bottom: 25px;

		h4 {
			font-size: 17px;
			font-weight: 500;
		}

		.mfp-close {

			@include box-shadow-none;
			right: 20px;
			left: inherit;
		}

	}

	.bb-model-footer {
		text-align: right;

		.button {
			width: auto;

			&.report-submit.loading {
				pointer-events: none;
				opacity: 0.9;

				&:after {
					font: normal normal 400 14px/1 bb-icons;
					speak: none;
					display: inline-block;
					margin: 0 0 0 6px;
					text-decoration: inherit;
					text-transform: none;
					-webkit-font-smoothing: antialiased;
					-moz-osx-font-smoothing: grayscale;
					content: "\ef30";
					animation: spin 2s infinite linear;
				}

			}

			&.bb-cancel-report-content {
				color: #a5a7ab;
				background-color: transparent;
				box-shadow: none;
				margin: 0 20px 0 0;
				padding: 0;
				min-width: inherit;
			}
		}

	}

	.bb-model-header ~ .bp-feedback {
		margin: -15px -20px 15px;
	}

	#bb-block-member,
	#bb-report-content {
		margin-bottom: 0;
	}

	.form-item {
		margin-bottom: 10px;
		text-align: left;

		> span {
			font-size: 14px;
			padding-left: 30px;
			display: inline-block;
			line-height: 1.4;
		}

		label {
			margin-bottom: 0;

			> input[type="radio"] {
				margin-right: 10px;
				vertical-align: middle;
				height: 15px;
				width: 15px;
			}

			> span {
				font-size: 16px;
				font-weight: 500;
				vertical-align: middle;
			}

			> textarea {
				font-size: 14px;
				margin-bottom: 0;
			}

		}

	}

	.bp-report-form-err {
		display: flex;
		color: #ef3e46;
		flex-flow: row nowrap;
		align-items: stretch;
		padding: 10px 0;
		background: #fff;
		-webkit-box-shadow: 0 1px 1px 1px rgba(0, 0, 0, 0.1);
		-moz-box-shadow: 0 1px 1px 1px rgba(0, 0, 0, 0.1);
		box-shadow: 0 1px 1px 1px rgba(0, 0, 0, 0.1);
		margin: 10px 0 20px;
		position: relative;
		border: 1px solid #ccc;
		border-radius: 3px;

		&:empty {
			display: none;
		}

		&:before {
			content: "\f534";
			background-color: #d33;
			color: #fff;
			display: flex;
			align-items: center;
			font-family: dashicons;
			left: 0;
			margin: -10px 10px -10px 0;
			position: relative;
			padding: 0 0.5em;
			border-radius: 3px 0 0 3px;
		}

	}

}

.moderation_notice.is_hidden {
	display: none;
}

.moderation-popup .bb-report-type-wrp {
	font-size: 15px;
	text-align: left;

	p {
		margin-bottom: 10px;

		&:last-child {
			margin-bottom: 10px;
		}

	}

	ul {
		margin-bottom: 10px;
	}

	.bb-model-footer {
		margin-top: 30px;

		[dir="rtl"] & {
			text-align: left;
		}

	}

	.bbm-notice {
		margin: 10px;
	}

}

.message-members-list.member-popup,
.mass-user-block-list.moderation-popup {

	.bb-model-header {
		position: relative;

		h4 {
			font-size: 20px;
			font-weight: 600;
		}

		.mfp-close {
			top: 7px;
			right: 20px;
			box-shadow: none;

			&:before {
				content: "\e828";
				color: #939597;
				font-family: "bb-icons"; /* stylelint-disable-line */
				font-size: 22px;
				font-weight: 400;
				letter-spacing: -0.24px;
				line-height: 24px;
			}

		}

	}

	.bb-report-type-wrp .user-item-wrp {
		display: flex;
		align-items: center;
		margin: 15px 0;

		&:last-child {
			margin-bottom: 0;
		}

		.user-avatar {
			max-height: 32px;
			max-width: 32px;
			overflow: hidden;
			border-radius: 100%;
		}

		.user-name {
			min-width: calc(100% - 195px);
			margin: 0 auto 0 8px;
			font-size: 14px;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}

		.user-actions {
			margin-left: 8px;
			text-align: right;

			.button {
				min-width: 95px;
			}

		}

	}

}

.message-members-list.member-popup {

	.user-actions {
		display: none;
	}
}

/* Access Control - Empty a tags */
.acomment-avatar,
.comment-author,
.acomment-meta,
.bp-avatar-wrap,
.notification-avatar,
.bs-group-member,
.member-name,
.message-metadata,
.thread-avatar,
.item-avatar,
.item-meta h3 {

	> a[href=""] {
		pointer-events: none;
	}
}

/*comment option*/
#comments .comment-body {
	position: relative;

	.bb_more_options {
		position: absolute;
		top: 0;
		right: 0;

		a {
			text-decoration: none;
		}

	}

}

/*More option popup General styles*/
#comments .bb_more_dropdown,
#buddypress .bb_more_dropdown,
#bbpress-forums .bb_more_dropdown {

	.bb_more_dropdown__title {
		display: none;
		background-color: #f2f4f5;
		padding: 10px 15px;
		position: relative;
	}

	.bb_more_dropdown__title__text {
		color: #1e2132;
		display: inline-block;
		font-size: 16px;
		font-weight: 600;
		line-height: 24px;
	}

	.bb_more_dropdown__close_button {
		color: #1e2132;
		cursor: pointer;
		font-size: 24px;
		line-height: 1;
		opacity: 0.4;
		position: absolute;
		right: 15px;

		> i {
			font-size: 24px;
			margin: 0;
			line-height: 1;
		}
	}

	+ .bb_more_dropdown_overlay {
		display: none;
	}

	@media screen and (max-width: 980px) {

		&.open {
			border: 1px solid #e1e3e5;
			bottom: inherit;
			position: fixed;
			top: 50%;
			left: 50%;
			max-height: calc(100% - 100px);
			height: fit-content;
			max-width: 220px;
			overflow: auto;
			padding: 0;
			transform: translate(-50%, -50%);
			z-index: 992;

			&:before,
			&:after {
				display: none;
			}

			.bb_more_dropdown__title {
				display: flex;
				align-items: center;
				border-bottom: 1px solid #e1e3e5;
				border-radius: 4px 4px 0 0;
				text-align: left;
			}

			+ .bb_more_dropdown_overlay {
				background-color: rgba(0, 0, 0, 0.1);
				bottom: 0;
				display: block;
				position: fixed;
				left: 0;
				right: 0;
				top: 0;
				z-index: 991;
			}
		}
	}
}

@media screen and (max-width: 980px) {

	#bbpress-forums .bb_more_dropdown.open {
		position: fixed !important;
		top: 50% !important;
		z-index: 992 !important;
	}

	#comments .bb_more_dropdown .report-content {
		margin-right: 0;
	}

	div:has(> .bb_more_dropdown.open) {

		> [data-bp-tooltip],
		> [data-balloon] {

			&:before,
			&:after {
				display: none;
			}
		}

		.bb-activity-more-options-action[data-balloon][data-balloon-pos="up"]:not(.specificity):before {
			display: none;
		}
	}
}
