// BP primary & secondary navigation - directory and single screens
// @version 3.0.0

// all devices & generic styles sitewide
.bp-navs {
	background: transparent;
	clear: both;
	overflow: hidden;

	ul {
		margin: 0;
		padding: 0;

		li {
			list-style: none;
			margin: 0;
		}

		li.last {

			select {
				max-width: 185px;
			}
		}

		li {

			a {
				border: 0;
				display: flex;
				padding: 5px 10px;
				text-decoration: none;
			}

			.count {
				background: $light-grey;
				border: 1px solid $bp-border-dark;
				border-radius: 50%;
				color: $meta-text-dark;
				display: inline;

				@include font-size(12);
				margin-left: 2px;
				padding-top: 2px;
				padding-bottom: 2px;
				text-align: center;
				vertical-align: middle;
				min-width: 25px;
				align-self: center;
			}
		}

		// these selected/current should apply to all navs
		// if not pull out & move to end.
		li.selected,
		li.current {

			a {
				color: $black;
				opacity: 1;
			}
		}

	} // close ul

	&.bp-invites-filters,
	&.bp-messages-filters {

		ul {

			li {

				a {
					border: 1px solid $bp-border-dark;
					display: inline-block;
				}
			}
		}
	}

} // close .bp-navs

.main-navs.dir-navs {
	margin-bottom: $marg-lrg;
}

// Primary, default current/selected states
.buddypress-wrap {

	.bp-navs {

		li.selected,
		li.current,
		li a:hover {

			a {

				.count {
					background-color: $grey;
				}
			}
		}

		li:not(.current),
		li:not(.selected), {

			a:focus,
			a:hover {
				background: $grey;
				color: $black;
			}
		}
	}

	@include medium-up() {

		.main-navs:not(.dir-navs) {

			li.selected,
			li.current {

				a {
					background: $white;
					color: $black;
					font-weight: 600;
				}
			}
		}

		.main-navs.vertical { // single screen navs

			li.selected,
			li.current {

				a {
					background: $dark-grey;
					color: $off-white;
					text-decoration: none;
				}
			}
		}

		&.bp-dir-hori-nav:not(.bp-vertical-navs) {

			nav {
				border-bottom: 1px solid $bp-border-color;
				border-top: 1px solid $bp-border-color;
				margin-bottom: 20px;

				@include box-shadow( 0 2px 12px 0 $off-white);
			}
		}
	} // close @media
} // close .buddypress-wrap - current & selected states.

.bp-subnavs {

	li.selected,
	li.current {

		a,
		a:focus,
		a:hover {
			background: $dark-grey;
			color: $off-white;
		}
	}

	li.selected,
	li.current {

		a {
			background: $white;
			color: $black;
			font-weight: 600;
		}
	}
}

// visual styling of default single navs - adds background/padding to
// the parent elements if vertical nav not selected
.buddypress-wrap:not(.bp-single-vert-nav) {

	@include medium-max {

		.bp-navs {

			li {
				background: $light-grey;
			}
		}
	}

	.main-navs {

		> ul > li {

			> a {
				padding: $pad-sml calc(0.5em + 2px);
			}
		}
	}

	.user-subnav#subsubnav,
	.group-subnav#subsubnav {
		background: none;
	}
}

// Specifically default subnav elements
.buddypress-wrap {

	.bp-navs.bp-invites-nav#subnav,
	.bp-navs.bp-invites-filters#subsubnav,
	.bp-navs.bp-messages-filters#subsubnav {

		ul {

			li.last {
				margin-top: 0;
			}
		}
	}

} // close .buddypress-wrap

.bp-subnavs {

	margin: $marg-sml 0;
	overflow: hidden;

	ul {

		li {
			margin-top: 0;

			&.selected,
			&.current {

				:focus,
				:hover {
					background: none;
					color: $black;
				}
			}

		}
	}
}

// Single screens object navs
// Adjusts visual styling for small screens only

@include medium-max {

	.buddypress-wrap {

		.single-screen-navs {
			border: 1px solid $bp-border-color;

			li {
				border-bottom: 1px solid $bp-border-color;

				&:last-child {
					border-bottom: none;
				}
			}
		}
	}

	.bp-subnavs {

		li {

			a {

				@include font-size(14);
			}

			&.selected,
			&.current {

				a,
				a:focus,
				a:hover {
					background: $dark-grey;
					color: $white;
				}
			}
		}
	}
}

.buddypress_object_nav,
.buddypress-wrap {

	.bp-navs {

		li.selected,
		li.current {

			a {

				.count {
					background-color: $white;
				}
			}
		} // close li

		li.dynamic,
		li.dynamic.selected,
		li.dynamic.current {

			a {

				.count {
					background-color: $highlight;
					border: 0;
					color: $off-white;
				}
			}
		}

		li.dynamic {

			a:hover {

				.count {
					background-color: $highlight;
					border: 0;
					color: $white;
				}
			}
		}

		li {

			a {

				.count:empty {
					display: none;
				}
			}
		}
	} // bp-navs

	// Create steps current position tabs highlight
	.bp-navs.group-create-links {

		ul {

			li:not(.current) {
				color: $light-text;

				a {
					color: $light-text;

					&:focus,
					&:hover {
						background: none;
						color: $black;
					}
				}

				a[disabled] {

					&:focus,
					&:hover {
						color: $light-text;
					}
				}
			}

			li.current {

				a {
					text-align: center;
				}
			}
		}
	}
}

.buddypress-wrap {

	// position our nav elements at larger screen widths

	@include medium-up() {

		.bp-navs {

			li { // set the list links of all navs to shrinkwrap/width auto
				float: left;
			}
		}

		.subnav {
			// float: left;
		}

		ul.subnav {
			// width: auto;
		}

		// user screen last filter
		#subsubnav {

			.activity-search {
				float: left;
			}

			.filter {
				float: right;
			}
		}

	} // close @media

} // close .buddypress-wrap


// Just buddypress_object_nav rules
.buddypress_object_nav {

	.bp-navs {

		li {

			a {

				.count {
					display: inline-block;
					float: right;
				}
			}
		}

	}
}

// Directory screens vertical nav rules

@include medium-up() {

	// the top level class activating vert layout
	.bp-dir-vert-nav {

		.bp-navs.dir-navs {
			background: none;

			a {

				.count {
					float: right;

				}
			}
		}
	}
}

// Directory Count Styling
.bb-item-count {
	font-size: 16px;
	font-weight: 500;
	line-height: 1.3;

	.item-body-inner > & {
		margin-bottom: 20px;

		+ .subnav-filters {
			float: right;
		}
	}

	.item-body > & {
		margin-bottom: 20px;

		&.bb-group-members-count {
			margin-top: 20px;

			+ .members-search {
				float: left;
			}
		}
	}

	body.directory .main-navs + & {
		margin-bottom: 10px;

		~ #media-stream .bp-feedback {
			margin-top: 15px;
		}
	}
}

.item-body,
.item-body-inner {

	&:has(.bb-item-count + .subnav-filters) .bb-item-count {
		float: left;
		margin: 10px 0 0;
	}

	&:has(.group-search + .bb-item-count) .group-search {
		margin-top: 10px;
	}
}

.bp-search + .bb-item-count {
	margin-top: 35px !important;

	+ .subnav-filters {
		margin-top: 25px;
	}
}
