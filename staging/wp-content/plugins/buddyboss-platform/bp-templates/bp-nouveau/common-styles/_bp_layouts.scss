// BP Layouts
// Layouts provides classes to handle specific module
// layouts on a user selectable basis.
// The sheet also adds layout properties such as border-box
// @version 3.0.0

#item-body,
.single-screen-navs {

	@include box-model();
}


// ==== The Grid Layout Classes ====

// BP Lists (loops) may be suited to a grid layout e.g members loops
// These classes added to the elements will provide these styles.

.grid {
	// the parent element (usually the ul)

	> li,
	> li .generic-button a {

		@include box-model( border-box );
	}

	> li {
		border-bottom: 0;
		padding-bottom: 10px;
		padding-top: 0;

		.list-wrap {
			background: $off-white;
			border: 1px solid $bp-border-color;
			padding-bottom: 15px;
			position: relative;
			overflow: hidden;
			padding-top: 14px;
			height: 100%;

			.list-title {
				padding: $pad-sml $pad-sml 0;
			}

			.update {
				color: $meta-text;
				padding: $pad-sml $pad-lrg;
			}
		}

		.item-avatar {
			text-align: center;

			.avatar {
				border-radius: 50%;
				display: inline-block;
				width: 50%;
			}
		}
	} // close > li

	// min-height rules to line up grid box elements
	// to prevent breaking grid layout through uneven heights
	// one column or single column mobile can have varying heights

	@include small-up() {

		&.members-list {

			.list-wrap {

				.item-block {
					margin: 0 auto;
				}
			}
		}

		&.members-group-list {

			.list-wrap {

				.item-block {
					margin: 0 auto;
				}
			}
		}

		&.groups-list {

			.list-wrap {

				.group-desc {
					margin: $marg-med auto 0;
					min-height: 5em;
					overflow: hidden;
				}

				.last-activity,
				.group-details,
				.item-desc {
					margin-bottom: 0;

					p {
						margin-bottom: 0;
					}
				}
			}
		}

		&.blogs-list {

			.list-wrap {

				.item-block {
					margin: 0 auto;
				}
			}
		}
	} // close @media small-up

} // close .grid

/* Build the two column class small up */

@include small-up() {

	// Set the column  or item numbers to span horizontally
	.grid {

		> li.item-entry {
			float: left;
			margin: 0;
		}
	}
}

// This block styles the loop items for a grid layout
// The block might be better located in the generic bp-lists section

// Style the .bp-list li elements if a 'grid' class is set on the ul

// if grid is set & this is a member, group or blog loop
// we want to re-style some of the elements in the item
.buddypress-wrap {

	.grid.bp-list {
		display: flex;
		flex-flow: row wrap;
		padding-top: $pad-med;
		margin-left: -5px;
		margin-right: -5px;
		width: auto;

		> li {
			border-bottom: none;
			flex: 0 0 100%;
			max-width: 100%;
			min-width: 0;
			padding: 0 5px;
			margin-bottom: $marg-sml;

			@include medium-small-up() {
				flex: 0 0 50%;
				max-width: 50%;
			}

			@include large-up() {
				flex: 0 0 33.33%;
				max-width: 33.33%;
			}

			.item-avatar {
				margin: 0;
				text-align: center;
				width: auto;

				img.avatar {
					display: inline-block;
					height: auto;
					width: 50%;
					image-rendering: -webkit-optimize-contrast; // To solve blurry image issue in chrome
				}
			}

			.item-meta,
			.list-title {
				float: none;
				text-align: center;
			}

			.list-title {
				font-size: inherit;
				line-height: 1.1;
			}

			.item {

				@include responsive-font( 22 );
				left: 0;
				margin: 0 auto;
				text-align: center;
				//width: 96%;

				.item-block,
				.group-desc {
					float: none;
					//width: 96%;
				}

				.item-block {
					margin-bottom: $marg-sml;
				}

				.last-activity {
					margin-top: 5px;
					font-weight: 400;
				}

				.group-desc {
					clear: none;
				}

				.user-update {
					clear: both;
					text-align: left;
				}

				.activity-read-more a {
					display: inline;
				}
			}

			.action {

				.generic-button {
					float: none;
					margin: 0;
					text-align: center;
					width: 100%;

					a,
					button {
						width: 100%;
					}
				}
			}

			.item-avatar,
			.avatar,
			.item {
				float: none;
			}

		} // close > li
	} // close .grid.bp-list

	#members-list.bp-list.members-group-list {

		li {

			&.item-entry-header {
				margin: 20px 0;
			}
		}
	}

	#members-list.grid.bp-list.members-group-list {

		li {

			&.item-entry-header {
				max-width: 100%;
				flex: 100%;
				min-width: 0;
			}
		}
	}

} // close .buddypress-wrap

// ==== Vertical Navigation Classes ====

// These classes added to the BP user navigation elements
// will provide rules to modify the layout to present the
// parent object navigation in a vertical column & sub navs
// horizontally across the item-body

.bp-single-vert-nav {

	.main-navs {

		ul {

			li {
				width: 100%;

				a {
					justify-content: space-between;
				}
			}
		}
	}
}

@include medium-up() {

	.bp-single-vert-nav { // described for the #buddypress element

		.bp-wrap {
			display: -webkit-box;
			display: -ms-flexbox;
			display: flex;
		}

		.item-body {
			-webkit-box-flex: 1;
			-ms-flex: 1;
			flex: 1;
			min-width: 0;
			padding-left: 20px;
		}

		.subnav {
			background: $light-grey;
			width: auto;

			li {

				@include font-size(16);
				margin: 0 0 $marg-sml;

				a {
					border-right: 1px solid $bp-border-dark;
					padding: 0 $pad-sml;
				}

				a:focus,
				a:hover {
					background: none;
				}
			}

			li.current {

				a {
					background: none;
					color: $black;
				}
			}

			li:last-child {

				a {
					border-right: none;
				}
			}

		} // close .subnav
	}

	.bb-single-main-nav--vertical {
		border-right: 1px solid $med-light-grey;
		border-bottom: 0;
		float: left;
		width: 25%;
		overflow: visible;

		ul {
			margin: 0;
		}

		li {
			float: none;
			margin-right: 0;

			&.selected a {
				background: $grey;
				color: $black;
			}

			&:focus,
			&:hover {
				background: $grey;
			}

			span {
				border-radius: 10%;
				float: right;
				margin-right: 2px;
			}

			&:hover span {
				border-color: $light-grey;
			}
		} // li
	}

	// Set the directory screens main navs as vertical aligned
	// medium breakpoint up only

	.bp-dir-vert-nav { // described for the #buddypress element

		.dir-navs {
			float: left;
			left: 1px;
			position: relative;
			width: 20%;

			ul {

				li {
					float: none;
					overflow: hidden;
					width: auto;

					&.selected {
						border: 1px solid #eee;

						a {
							background: $dark-grey;
							color: $white;

							span {
								background: $light-grey;
								border-color: $grey;
								color: $highlight;
							}
						}
					} // close .selected
				}

				li {

					a:hover,
					a:focus {
						background: $grey;
						color: $black;

						span {
							border: 1px solid $dark-grey;
						}
					}
				}

			}
		} // close .dir-navs

		.screen-content {
			border-left: 1px solid $med-light-grey;
			margin-left: 20%;
			overflow: hidden;
			padding: 0 0 $pad-lrg $pad-med;

			.subnav-filters {
				margin-top: 0;
			}
		}
	}

} // close @media

// Adminbar Styles
.admin-bar.bp-hide-adminbar {
	margin-top: -32px;

	@media screen and (max-width: 782px) {
		margin-top: -46px;
	}

	#wpadminbar {
		display: none;
	}

	.bb-mobile-panel-wrapper {
		top: 0;
		height: 100%;
	}

	a.bb-close-panel i {
		top: 32px;
	}
}

@media screen and (max-width: 782px) {

	#wpadminbar .menupop .menupop > .ab-item {

		.wp-admin-bar-arrow:before {
			position: initial;
		}
	}
}
