// BP Single Screens Item Header General Styles.
// @version 3.0.0

@include clearfix-element(".single-headers");

.single-headers {
	margin-bottom: $marg-med;

	// Breakpoint element positioning

	#item-header-avatar { // todo: this ought to be a class!

		a {
			border: 0;
			display: block;
			position: relative;
			text-align: center;
			text-decoration: none;
		}
	}

	// End breakpoint positioning

	.group-status,
	.activity {
		display: inline;
	}

	.group-status {

		@include font-size(14);
		padding-right: $pad-med;
	}

	.activity {
		display: inline-block;

		@include font-size(14);
		padding: 0;
	}

	div#message,
	#sitewide-notice {

		p {
			background-color: #ffd;
			border: 1px solid #cb2;
			color: #440;
			font-weight: 400;
			margin-top: 3px;
			text-decoration: none;
		}
	}

	// @mention user name
	h2 {
		line-height: 1.2;
		margin: 0 0 $marg-xsml;

		a {
			color: $light-text;
			text-decoration: none;
		}

		span.highlight {
			display: inline-block;
			font-size: 60%;
			font-weight: 400;
			line-height: 1.7;
			vertical-align: middle;

			span {
				background: #a1dcfa;
				color: $white;
				cursor: pointer;
				font-size: 80%;
				font-weight: 700;
				margin-bottom: 2px;
				padding: 1px 4px;
				position: relative;
				right: -2px;
				top: -2px;
				vertical-align: middle;
			}

		}

	} // close h2

	.item-header-wrap {
		display: flex;
		flex-flow: row wrap;
	}

	img.avatar {
		//float: left;
		margin: 0 15px 19px 0;
	}

	.item-meta {
		color: inherit;

		@include font-size(14);
		margin: $marg-med 0 $marg-xsml;
		padding-bottom: $pad-sml;

		&.last-activity {
			margin-bottom: 0;
			margin-top: 1px;
		}

	}

	ul {
		list-style: none;
		margin: 0;

		// if these are list constructs
		> .generic-button {
			display: inline-block;
			text-align: center;
			margin: 5px 10px 10px 0;
		}

	}

	.group-description {

		ul,
		ol {
			list-style-position: inside !important;
			margin: 0 0 1rem;
		}

		ul {
			list-style: initial;
		}

		ol {
			list-style: decimal;
		}
	}

	.member-header-actions {

		> .generic-button {
			display: inline-block;
			text-align: center;
			margin: 5px 10px 10px 0;
		}
	}

	div.generic-button {
		text-align: center;
	}

	@include medium-up() {

		div.generic-button,
		a.button,
		li.generic-button {
			float: left;
		}
	}

	div#message.info {
		line-height: 0.8;
	}

} // close .single-headers

body.no-js  .single-item-header .js-self-profile-button {
	display: none;
}

#create-group-form #header-cover-image {
	margin-bottom: $marg-med;
}

.group-create #create-group-form #header-cover-image {
	display: none;
}

@media screen and (min-width: 783px) {

	#item-header-avatar {
		padding-right: $pad-med;
	}
}

@media screen and (max-width: 782px) {

	.single-headers .item-header-wrap {
		flex-flow: column;
	}

	#item-header-content {
		text-align: center;
	}
}
