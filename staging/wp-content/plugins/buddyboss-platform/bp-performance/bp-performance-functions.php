<?php
/**
 * BuddyBoss Performance Functions.
 *
 * Functions are where all the magic happens in Buddy<PERSON>ress. They will
 * handle the actual saving or manipulation of information. Usually they will
 * hand off to a database class for data access, then return
 * true or false on success or failure.
 *
 * @package BuddyBoss\Performance\Functions
 * @since   BuddyBoss 1.6.0
 */

// Exit if accessed directly.
defined( 'ABSPATH' ) || exit;
