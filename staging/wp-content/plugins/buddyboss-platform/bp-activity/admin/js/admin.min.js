(a=>{var r={init:function(){a(document).on("click",".row-actions a.reply",r.open),a(document).on("click","#bp-activities-container a.cancel",r.close),a(document).on("click","#bp-activities-container a.save",r.send),a(document).on("keyup","#bp-activities:visible",function(i){27===i.which&&r.close()})},open:function(){var i=a("#bp-activities-container").hide();return a(this).parents("tr").after(i),i.fadeIn("300"),a("#bp-activities").focus(),!1},close:function(){return a("#bp-activities-container").fadeOut("200",function(){a("#bp-activities").val("").blur(),a("#bp-replysubmit .error").html("").hide(),a("#bp-replysubmit .waiting").hide()}),!1},send:function(){a("#bp-replysubmit .error").hide(),a("#bp-replysubmit .waiting").show();var i={};return i["_ajax_nonce-bp-activity-admin-reply"]=a('#bp-activities-container input[name="_ajax_nonce-bp-activity-admin-reply"]').val(),i.action="bp-activity-admin-reply",i.content=a("#bp-activities").val(),i.parent_id=a("#bp-activities-container").prev().data("parent_id"),i.root_id=a("#bp-activities-container").prev().data("root_id"),a.ajax({data:i,type:"POST",url:ajaxurl,error:function(i){r.error(i)},success:function(i){r.show(i)}}),!1},error:function(i){var t=i.statusText;a("#bp-replysubmit .waiting").hide(),(t=i.responseText?i.responseText.replace(/<.[^<>]*?>/g,""):t)&&a("#bp-replysubmit .error").html(t).show()},show:function(i){var t,e,n;return"string"==typeof i?(r.error({responseText:i}),!1):(n=wpAjax.parseAjaxResponse(i)).errors?(r.error({responseText:wpAjax.broken}),!1):(n=n.responses[0],void a("#bp-activities-container").fadeOut("200",function(){a("#bp-activities").val("").blur(),a("#bp-replysubmit .error").html("").hide(),a("#bp-replysubmit .waiting").hide(),a("#bp-activities-container").before(n.data),e=a("#activity-"+n.id),t=e.closest(".widefat").css("backgroundColor"),e.animate({backgroundColor:"#CEB"},300).animate({backgroundColor:t},300)}))}};a(document).ready(function(){r.init(),a("#bp_activity_action h3, #bp_activity_content h3").unbind("click"),"undefined"!=typeof postboxes&&postboxes.add_postbox_toggles(bp_activity_admin_vars.page)})})(jQuery);