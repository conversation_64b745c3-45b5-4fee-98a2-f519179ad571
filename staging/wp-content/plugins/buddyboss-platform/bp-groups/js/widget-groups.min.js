function groups_widget_click_handler(){jQuery(".widget div#groups-list-options a").on("click",function(){var t=this;return jQuery(t).addClass("loading"),jQuery(".widget div#groups-list-options a").removeClass("selected"),jQuery(this).addClass("selected"),jQuery.post(ajaxurl,{action:"widget_groups_list",cookie:encodeURIComponent(document.cookie),_wpnonce:jQuery("input#_wpnonce-groups").val(),max_groups:jQuery("input#groups_widget_max").val(),filter:jQuery(this).attr("id")},function(e){jQuery(t).removeClass("loading"),groups_widget_response(e)}),!1})}function groups_widget_response(t){"-1"!==(t=(t=t.substr(0,t.length-1)).split("[[SPLIT]]"))[0]?jQuery(".widget ul#groups-list").fadeOut(200,function(){jQuery(".widget ul#groups-list").html(t[1]),jQuery(".widget ul#groups-list").fadeIn(200)}):jQuery(".widget ul#groups-list").fadeOut(200,function(){var e="<p>"+t[1]+"</p>";jQuery(".widget ul#groups-list").html(e),jQuery(".widget ul#groups-list").fadeIn(200)})}jQuery(document).ready(function(){groups_widget_click_handler(),"undefined"!=typeof wp&&wp.customize&&wp.customize.selectiveRefresh&&wp.customize.selectiveRefresh.bind("partial-content-rendered",function(){groups_widget_click_handler()})});