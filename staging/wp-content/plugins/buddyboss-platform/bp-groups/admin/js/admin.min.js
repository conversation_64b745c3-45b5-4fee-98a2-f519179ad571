(r=>{var e="undefined"!=typeof group_id?"&group_id="+group_id:"";r(document).ready(function(){window.warn_on_leave=!1,r(".bp-suggest-user").autocomplete({source:ajaxurl+"?action=bp_group_admin_member_autocomplete"+e,delay:500,minLength:2,position:"undefined"!=typeof isRtl&&isRtl?{my:"right top",at:"right bottom",offset:"0, -1"}:{offset:"0, -1"},open:function(){r(this).addClass("open")},close:function(){r(this).removeClass("open"),r(this).val("")},select:function(e,o){o=o,r("#bp-groups-new-members-list").append('<li data-login="'+o.item.value+'"><a href="#" class="bp-groups-remove-new-member">x</a> '+o.item.label+"</li>")}}),r("#bp-groups-new-members").prop("placeholder",BP_Group_Admin.add_member_placeholder),r("#bp_group_add_members").on("click",".bp-groups-remove-new-member",function(e){e.preventDefault(),r(e.target.parentNode).remove()}),r(document).on("change",'input#bp-groups-name, input#bp-groups-description, select.bp-groups-role, #bp-groups-settings-section-status input[type="radio"]',function(){window.warn_on_leave=!0}),r("input#save").on("click",function(e){var o=[];if(r("#bp-groups-new-members-list li").each(function(){o.push(r(this).data("login"))}),o.length&&r("#bp-groups-new-members").val("").val(o.join(", ")),0<r("#bbp_group_forum_id").length){var n=parseInt(r("#bbp_group_forum_id").val());if(void 0!==BP_Group_Admin.group_connected_forum_id&&void 0!==BP_Group_Admin.warn_on_attach_forum&&!isNaN(n)&&parseInt(BP_Group_Admin.group_connected_forum_id)!==n&&!confirm(BP_Group_Admin.warn_on_attach_forum))return e.preventDefault(),!1}window.warn_on_leave=!1}),r("#delete-groups-submit").on("click",function(e){e.preventDefault();var o=[],e=this.getAttribute("href");document.querySelectorAll("input[name=delete_group_forum]:checked").forEach(function(e){o.push(e.value)}),0<o.length&&(e+="&gfid="+encodeURIComponent(o.join(","))),location.replace(e)}),window.onbeforeunload=function(){if(window.warn_on_leave)return BP_Group_Admin.warn_on_leave},r("#bbpress_group_admin_ui_meta_box #bbp-edit-group-forum").change(function(){this.checked?r("#bb_group_forum_list").show():r("#bb_group_forum_list").hide()})})})(jQuery);