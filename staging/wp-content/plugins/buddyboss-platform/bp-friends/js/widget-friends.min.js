function friend_widget_click_handler(){jQuery(".widget div#friends-list-options a").on("click",function(){var i=this;return jQuery(i).addClass("loading"),jQuery(".widget div#friends-list-options a").removeClass("selected"),jQuery(this).addClass("selected"),jQuery.post(ajaxurl,{action:"widget_friends",cookie:encodeURIComponent(document.cookie),_wpnonce:jQuery("input#_wpnonce-friends").val(),"max-friends":jQuery("input#friends_widget_max").val(),filter:jQuery(this).attr("id")},function(e){jQuery(i).removeClass("loading"),friend_widget_response(e)}),!1})}function friend_widget_response(i){"-1"!==(i=(i=i.substr(0,i.length-1)).split("[[SPLIT]]"))[0]?jQuery(".widget ul#friends-list").fadeOut(200,function(){jQuery(".widget ul#friends-list").html(i[1]),jQuery(".widget ul#friends-list").fadeIn(200)}):jQuery(".widget ul#friends-list").fadeOut(200,function(){var e="<p>"+i[1]+"</p>";jQuery(".widget ul#friends-list").html(e),jQuery(".widget ul#friends-list").fadeIn(200)})}jQuery(document).ready(function(){friend_widget_click_handler(),"undefined"!=typeof wp&&wp.customize&&wp.customize.selectiveRefresh&&wp.customize.selectiveRefresh.bind("partial-content-rendered",function(){friend_widget_click_handler()})});