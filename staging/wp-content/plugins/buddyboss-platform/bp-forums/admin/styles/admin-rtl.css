body.post-type-forum #minor-publishing,
body.post-type-forum #save-post {
	display: none;
}

#bbp-converter-stop,
#bbp-converter-progress {
	display: none;
}

#bbp-converter-spinner {
	float: none;
}

#bbp-converter-state-message {
	display: none;
	position: relative;
	padding: 5px;
	border: 1px solid #c3c4c7;
	box-shadow: 0 1px 1px rgb(0 0 0 / 4%);
}

#bbp-converter-state-message .bbp-progress-bar {
	position: absolute;
	right: 0;
	height: 1px;
	width: 0;
	background-color: #00b9eb;
	transition-property: width;
	transition-timing-function: ease-out;
	transition-duration: 1s;
}

#bbp-converter-state-message #bbp-converter-step-percentage {
	top: -1px;
}

#bbp-converter-state-message #bbp-converter-total-percentage {
	top: 0;
}

#bbp-converter-state-message #bbp-converter-label {
	color: #1d2327;
	font-weight: 600;
	display: block;
	line-height: 1.4;
	font-size: 14px;
}

#bbp-converter-status {
	font-weight: 400;
	font-size: 12px;
	color: #aaa;
	display: block;
	margin-top: 2px;
}

div.bbp-converter-updated,
div.bbp-converter-warning {
	border-radius: 3px 3px 3px 3px;
	border-style: solid;
	border-width: 1px;
	padding: 5px 5px 5px 5px;
}

div.bbp-converter-updated {
	height: 300px;
	overflow: auto;
	display: none;
	background-color: #FFFFE0;
	border-color: #E6DB55;
	font-family: monospace;
}

div.bbp-converter-updated p {
	margin: 0.2em 0;
	padding: 2px;
	float: right;
	clear: right;
}

div.bbp-converter-updated .output {
	margin: 0 5px;
}

div.bbp-converter-updated .mini-step {
	padding: 3px 5px;
	vertical-align: middle;
	font-size: 8px;
	font-weight: 600;
	border-radius: 6px;
	background-color: #aaa;
	color: #fff;
}

._bbp_converter_db_pass_wrap {
	position: relative;
	display: inline-block;
}

._bbp_converter_db_pass_wrap #_bbp_converter_db_pass {
	padding-left: 25px;
}

._bbp_converter_db_pass_wrap #_bbp_converter_db_pass[type="text"] + .bbp-db-pass-toggle:before {
	content: '\e8fc';
}

._bbp_converter_db_pass_wrap .bbp-db-pass-toggle {
	position: absolute;
	top: 50%;
	left: 10px;
	transform: translateY(-50%);
	cursor: pointer;
}