(H=>{H.fn.extend({autocompletebp:function(e,t){var a="string"==typeof e;return(t=H.extend({},H.Autocompleter.defaults,{url:a?e:null,data:a?null:e,delay:a?H.Autocompleter.defaults.delay:10,max:t&&!t.scroll?10:150},t)).highlight=t.highlight||function(e){return e},this.each(function(){new H.Autocompleter(this,t)})},result:function(e){return this.bind("result",e)},search:function(e){return this.trigger("search",[e])},flushCache:function(){return this.trigger("flushCache")},setOptions:function(e){return this.trigger("setOptions",[e])},unautocomplete:function(){return this.trigger("unautocomplete")}}),H.Autocompleter=function(i,l){var t,n,a=38,o=40,r=46,s=9,u=13,c=27,f=188,h=33,d=34,p=H(i).attr("autocomplete","off").addClass(l.inputClass),m="",g=H.Autocompleter.Cache(l),v=0,e={mouseDownOnSelect:!1},C=H.Autocompleter.Select(l,i,b,e);function b(){var e,t,a=C.selected();return!!a&&(e=a.result,m=e,l.multiple&&(1<(t=x(p.val())).length&&(e=t.slice(0,t.length-1).join(l.multipleSeparator)+l.multipleSeparator+e),e+=l.multipleSeparator),p.val(e),S(),p.trigger("result",[a.data,a.value]),!0)}function w(e,t){var a;n==r?C.hide():(a=p.val(),!t&&a==m||((a=y(m=a)).length>=l.minChars?(p.addClass(l.loadingClass),jQuery("#send-to-input").addClass("loading"),k(a=l.matchCase?a:a.toLowerCase(),T,S)):(L(),C.hide())))}function x(e){var a;return e?(e=e.split(H.trim(l.multipleSeparator)),a=[],H.each(e,function(e,t){H.trim(t)&&(a[e]=H.trim(t))}),a):[""]}function y(e){var t;return l.multiple?(t=x(e))[t.length-1]:e}function S(){C.hide(),clearTimeout(t),L(),l.mustMatch&&p.search(function(e){e||p.val("")})}function T(e,t){var a;t&&t.length&&v?(L(),C.display(t,e),a=t[0].value.split(";"),t.value=a[0],a=e,e=t.value,l.autoFill&&y(p.val()).toLowerCase()==a.toLowerCase()&&8!=n&&(p.val(p.val()+e.substring(y(m).length)),H.Autocompleter.Selection(i,m.length,m.length+e.length)),C.show()):S()}function k(t,a,e){l.matchCase||(t=t.toLowerCase());var n,o=g.load(t);o&&o.length?a(t,o):"string"==typeof l.url&&0<l.url.length?(n={},H.each(l.extraParams,function(e,t){n[e]="function"==typeof t?t():t}),H.ajax({mode:"abort",port:"autocomplete"+i.name,dataType:l.dataType,url:l.url,data:H.extend({q:y(t),limit:l.max,action:"messages_autocomplete_results",cookie:(()=>{var e,t,a,n,o=document.cookie.split(";"),i={};for(e=0;e<o.length;e++)n=o[e],t=n.indexOf("="),a=jq.trim(unescape(n.slice(0,t))),n=unescape(n.slice(t+1)),0===a.indexOf("bp-")&&(i[a]=n);return encodeURIComponent(jq.param(i))})()},n),success:function(e){e=l.parse&&l.parse(e)||(e=>{for(var t=[],a=e.split("\n"),n=0;n<a.length;n++){var o=H.trim(a[n]);o&&(o=o.split("|"),t[t.length]={data:o,value:o[0],result:l.formatResult&&l.formatResult(o,o[0])||o[0]})}return t})(e);g.add(t,e),a(t,e)}})):e(t)}function L(){p.removeClass(l.loadingClass),jQuery("#send-to-input").removeClass("loading")}p.keydown(function(e){switch(n=e.keyCode,e.keyCode){case a:e.preventDefault(),C.visible()?C.prev():w(0,!0);break;case o:e.preventDefault(),C.visible()?C.next():w(0,!0);break;case h:e.preventDefault(),C.visible()?C.pageUp():w(0,!0);break;case d:e.preventDefault(),C.visible()?C.pageDown():w(0,!0);break;case l.multiple&&","==H.trim(l.multipleSeparator)&&f:case s:case u:b()&&(l.multiple||p.blur(),e.preventDefault(),p.focus());break;case c:C.hide();break;default:clearTimeout(t),t=setTimeout(w,l.delay)}}).keypress(function(){}).focus(function(){v++}).blur(function(){v=0,e.mouseDownOnSelect||(clearTimeout(t),t=setTimeout(S,200))}).click(function(){1<v++&&!C.visible()&&w(0,!0)}).bind("search",function(){var o=1<arguments.length?arguments[1]:null;function a(e,t){var a;if(t&&t.length)for(var n=0;n<t.length;n++)if(t[n].result.toLowerCase()==e.toLowerCase()){a=t[n];break}"function"==typeof o?o(a):p.trigger("result",a&&[a.data,a.value])}H.each(x(p.val()),function(e,t){k(t,a,a)})}).bind("flushCache",function(){g.flush()}).bind("setOptions",function(){H.extend(l,arguments[1]),"data"in arguments[1]&&g.populate()}).bind("unautocomplete",function(){C.unbind(),p.unbind()})},H.Autocompleter.defaults={inputClass:"ac_input",resultsClass:"ac_results",loadingClass:"ac_loading",minChars:1,delay:400,matchCase:!1,matchSubset:!0,matchContains:!1,cacheLength:10,max:100,mustMatch:!1,extraParams:{},selectFirst:!0,formatItem:function(e){return e[0]},autoFill:!1,width:0,multiple:!1,multipleSeparator:", ",highlight:function(e,t){return e.replace(new RegExp("(?![^&;]+;)(?!<[^<>]*)("+t.replace(/([\^\$\(\)\[\]\{\}\*\.\+\?\|\\])/gi,"\\$1")+")(?![^<>]*>)(?![^&;]+;)","gi"),"<strong>$1</strong>")},scroll:!0,scrollHeight:250,attachTo:"body"},H.Autocompleter.Cache=function(r){var i={},l=0;function s(e,t){e=(e=r.matchCase?e:e.toLowerCase()).indexOf(t);return-1!=e&&(0==e||r.matchContains)}function u(e,t){l>r.cacheLength&&a(),i[e]||l++,i[e]=t}function e(){if(!r.data)return!1;var e={},t=0;r.url||(r.cacheLength=1),e[""]=[];for(var a=0,n=r.data.length;a<n;a++){var o,i=r.data[a],l=r.formatItem(i="string"==typeof i?[i]:i,a+1,r.data.length);!1!==l&&(e[o=l.charAt(0).toLowerCase()]||(e[o]=[]),i={value:l,data:i,result:r.formatResult&&r.formatResult(i)||l},e[o].push(i),t++<r.max)&&e[""].push(i)}H.each(e,function(e,t){r.cacheLength++,u(e,t)})}function a(){i={},l=0}return setTimeout(e,25),{flush:a,add:u,populate:e,load:function(a){if(r.cacheLength&&l){if(!r.url&&r.matchContains){var e,n=[];for(e in i)0<e.length&&(t=i[e],H.each(t,function(e,t){s(t.value,a)&&n.push(t)}));return n}if(i[a])return i[a];if(r.matchSubset)for(var t,o=a.length-1;o>=r.minChars;o--)if(t=i[a.substr(0,o)])return n=[],H.each(t,function(e,t){s(t.value,a)&&(n[n.length]=t)}),n}return null}}},H.Autocompleter.Select=function(o,a,n,i){var l,r,s,u,c,f="ac_over",h=-1,d=!0;function p(e){for(var t=e.target;t&&"LI"!=t.tagName;)t=t.parentNode;return t||[]}function e(e){l.slice(h,h+1).removeClass(),(h+=e)<0?h=l.size()-1:h>=l.size()&&(h=0);var t,e=l.slice(h,h+1).addClass(f);o.scroll&&(t=0,l.slice(0,h).each(function(){t+=this.offsetHeight}),t+e[0].offsetHeight-c.scrollTop()>c[0].clientHeight?c.scrollTop(t+e[0].offsetHeight-c.innerHeight()):t<c.scrollTop()&&c.scrollTop(t))}function m(){c.empty();e=r.length;for(var e,t,a=o.max&&o.max<e?o.max:e,n=0;n<a;n++)r[n]&&!1!==(t=o.formatItem(r[n].data,n+1,a,r[n].value,s))&&(t=H("<li>").html(o.highlight(t,s)).addClass(n%2==0?"ac_event":"ac_odd").appendTo(c)[0],H.data(t,"ac_data",r[n]));l=c.find("li"),o.selectFirst&&(l.slice(0,1).addClass(f),h=0),c.bgiframe()}return{display:function(e,t){d&&(u=H("<div/>").hide().addClass(o.resultsClass).css("position","absolute").appendTo(o.attachTo),c=H("<ul>").appendTo(u).mouseover(function(e){p(e).nodeName&&"LI"==p(e).nodeName.toUpperCase()&&(h=H("li",c).removeClass(f).index(p(e)),H(p(e)).addClass(f))}).click(function(e){return H(p(e)).addClass(f),n(),a.focus(),!1}).mousedown(function(){i.mouseDownOnSelect=!0}).mouseup(function(){i.mouseDownOnSelect=!1}),0<o.width&&u.css("width",o.width),d=!1),r=e,s=t,m()},next:function(){e(1)},prev:function(){e(-1)},pageUp:function(){e(0!=h&&h-8<0?-h:-8)},pageDown:function(){h!=l.size()-1&&h+8>l.size()?e(l.size()-1-h):e(8)},hide:function(){u&&u.hide(),h=-1},visible:function(){return u&&u.is(":visible")},current:function(){return this.visible()&&(l.filter("."+f)[0]||o.selectFirst&&l[0])},show:function(){var e,t=H(a).offset();u.css({width:"string"==typeof o.width||0<o.width?o.width:H(a).width(),top:t.top+a.offsetHeight,left:t.left}).show(),o.scroll&&(c.scrollTop(0),c.css({maxHeight:o.scrollHeight,overflow:"auto"}),H.browser.msie)&&void 0===document.body.style.maxHeight&&(e=0,l.each(function(){e+=this.offsetHeight}),t=e>o.scrollHeight,c.css("height",t?o.scrollHeight:e),t||l.width(c.width()-parseInt(l.css("padding-left"))-parseInt(l.css("padding-right"))))},selected:function(){var e=l&&l.filter("."+f).removeClass(f);return e&&e.length&&H.data(e[0],"ac_data")},unbind:function(){u&&u.remove()}}},H.Autocompleter.Selection=function(e,t,a){var n;e.createTextRange?((n=e.createTextRange()).collapse(!0),n.moveStart("character",t),n.moveEnd("character",a),n.select()):e.setSelectionRange?e.setSelectionRange(t,a):e.selectionStart&&(e.selectionStart=t,e.selectionEnd=a),e.focus()}})(jQuery);