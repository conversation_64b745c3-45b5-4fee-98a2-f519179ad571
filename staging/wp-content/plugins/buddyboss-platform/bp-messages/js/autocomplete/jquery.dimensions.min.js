(n=>{n.dimensions={version:"@VERSION"},n.each(["Height","Width"],function(t,o){n.fn["inner"+o]=function(){var t,i;if(this[0])return t="Height"==o?"Top":"Left",i="Height"==o?"Bottom":"Right",this[o.toLowerCase()]()+s(this,"padding"+t)+s(this,"padding"+i)},n.fn["outer"+o]=function(t){var i,e;if(this[0])return i="Height"==o?"Top":"Left",e="Height"==o?"Bottom":"Right",t=n.extend({margin:!1},t||{}),this[o.toLowerCase()]()+s(this,"border"+i+"Width")+s(this,"border"+e+"Width")+s(this,"padding"+i)+s(this,"padding"+e)+(t.margin?s(this,"margin"+i)+s(this,"margin"+e):0)}}),n.each(["Left","Top"],function(t,i){n.fn["scroll"+i]=function(t){if(this[0])return null!=t?this.each(function(){this==window||this==document?window.scrollTo("Left"==i?t:n(window).scrollLeft(),"Top"==i?t:n(window).scrollTop()):this["scroll"+i]=t}):this[0]==window||this[0]==document?self["Left"==i?"pageXOffset":"pageYOffset"]||n.boxModel&&document.documentElement["scroll"+i]||document.body["scroll"+i]:this[0]["scroll"+i]}}),n.fn.extend({position:function(){var t,i,e,o=this[0];return o&&(e=this.offsetParent(),t=this.offset(),i=e.offset(),t.top-=s(o,"marginTop"),t.left-=s(o,"marginLeft"),i.top+=s(e,"borderTopWidth"),i.left+=s(e,"borderLeftWidth"),e={top:t.top-i.top,left:t.left-i.left}),e},offsetParent:function(){for(var t=this[0].offsetParent;t&&!/^body|html$/i.test(t.tagName)&&"static"==n.css(t,"position");)t=t.offsetParent;return n(t)}});var s=function(t,i){return parseInt(n.css(t.jquery?t[0]:t,i))||0}})(jQuery);