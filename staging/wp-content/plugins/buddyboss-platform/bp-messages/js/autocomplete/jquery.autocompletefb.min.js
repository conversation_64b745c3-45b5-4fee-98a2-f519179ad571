jQuery.fn.autoCompletefb=function(e){var r=this,i={ul:r,urlLookup:[""],acOptions:{},foundClass:".friend-tab",inputClass:".send-to-input"},t=(e&&jQuery.extend(i,e),{params:i,removeFind:function(e){return t.removeUsername(e),jQuery(e).unbind("click").parent().remove(),jQuery(i.inputClass,r).focus(),r.acfb},removeUsername:function(e){e=e.parentNode.id.substr(e.parentNode.id.indexOf("-")+1);jQuery("#send-to-usernames").removeClass(e)}});return jQuery(i.foundClass+" img.p").click(function(){t.removeFind(this)}),jQuery(i.inputClass,r).autocompletebp(i.urlLookup,i.acOptions),jQuery(i.inputClass,r).result(function(e,s,n){var n=i.foundClass.replace(/\./,""),u=(s=String(s).split(" ("))[1].substr(0,s[1].length-1);0===jQuery(i.inputClass).siblings("#un-"+u).length&&(n='<li class="'+n+'" id="un-'+u+'"><span><a href="'+jQuery("#link-"+u).attr("href")+'">'+s[0]+'</a></span> <span class="p">X</span></li>',s=jQuery(i.inputClass,r).before(n),jQuery("#send-to-usernames").addClass(u),jQuery(".p",s[0].previousSibling).click(function(){t.removeFind(this)})),jQuery(i.inputClass,r).val("")}),jQuery(i.inputClass,r).focus(),t};