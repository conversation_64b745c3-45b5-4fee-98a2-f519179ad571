<?php
/**
 * Debug script do sprawdzenia danych regionów w bazie
 * 
 * UWAGA: Usuń po debugowaniu!
 */

// Ładowanie WordPress
require_once('../../../../../wp-load.php');

// Sprawdzenie uprawnień
if (!current_user_can('manage_options')) {
    wp_die('Brak uprawnień');
}

?>
<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Danych Regionów</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { background: #f9f9f9; padding: 15px; margin: 15px 0; border-radius: 5px; }
        .data-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .data-table th { background: #007cba; color: white; }
        .unique-values { display: flex; flex-wrap: wrap; gap: 5px; }
        .value-tag { background: #e7f3ff; padding: 3px 8px; border-radius: 3px; font-size: 12px; }
        pre { background: #f4f4f4; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔍 Debug Danych Regionów w Bazie</h1>
    
    <div class="section">
        <h2>1. Sprawdzenie tabel GeoDirectory</h2>
        <?php
        global $wpdb;
        
        // Znajdź tabele GeoDirectory
        $gd_tables = $wpdb->get_results("SHOW TABLES LIKE '{$wpdb->prefix}geodir_%'");
        
        echo "<h3>Dostępne tabele GeoDirectory:</h3>";
        foreach ($gd_tables as $table) {
            $table_name = array_values((array)$table)[0];
            echo "<div class='value-tag'>$table_name</div>";
        }
        ?>
    </div>
    
    <div class="section">
        <h2>2. Sprawdzenie struktury tabeli gd_place</h2>
        <?php
        $place_table = $wpdb->prefix . 'geodir_gd_place_detail';
        
        // Sprawdź czy tabela istnieje
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$place_table'");
        
        if ($table_exists) {
            echo "<p>✅ Tabela <strong>$place_table</strong> istnieje</p>";
            
            // Pokaż strukturę tabeli
            $columns = $wpdb->get_results("DESCRIBE $place_table");
            
            echo "<h3>Kolumny tabeli:</h3>";
            echo "<table class='data-table'>";
            echo "<tr><th>Kolumna</th><th>Typ</th><th>Null</th><th>Domyślna</th></tr>";
            
            $region_columns = [];
            foreach ($columns as $column) {
                echo "<tr>";
                echo "<td>{$column->Field}</td>";
                echo "<td>{$column->Type}</td>";
                echo "<td>{$column->Null}</td>";
                echo "<td>{$column->Default}</td>";
                echo "</tr>";
                
                // Zbierz kolumny związane z regionami
                if (stripos($column->Field, 'region') !== false || 
                    stripos($column->Field, 'wojewod') !== false || 
                    stripos($column->Field, 'powiat') !== false ||
                    stripos($column->Field, 'city') !== false) {
                    $region_columns[] = $column->Field;
                }
            }
            echo "</table>";
            
            echo "<h3>Kolumny związane z regionami:</h3>";
            echo "<div class='unique-values'>";
            foreach ($region_columns as $col) {
                echo "<div class='value-tag'>$col</div>";
            }
            echo "</div>";
            
        } else {
            echo "<p>❌ Tabela <strong>$place_table</strong> nie istnieje</p>";
            
            // Sprawdź alternatywne nazwy tabel
            $alt_tables = [
                $wpdb->prefix . 'geodir_gd_place',
                $wpdb->prefix . 'gd_place_detail',
                $wpdb->prefix . 'geodir_place_detail'
            ];
            
            echo "<h3>Sprawdzanie alternatywnych nazw tabel:</h3>";
            foreach ($alt_tables as $alt_table) {
                $exists = $wpdb->get_var("SHOW TABLES LIKE '$alt_table'");
                if ($exists) {
                    echo "<p>✅ Znaleziono: <strong>$alt_table</strong></p>";
                    $place_table = $alt_table;
                    break;
                } else {
                    echo "<p>❌ Nie ma: $alt_table</p>";
                }
            }
        }
        ?>
    </div>
    
    <div class="section">
        <h2>3. Przykładowe dane regionów</h2>
        <?php
        if ($table_exists || isset($place_table)) {
            // Pobierz przykładowe dane
            $sample_data = $wpdb->get_results("
                SELECT post_id, region, city, wojewodztwa_uslugi, powiaty_uslugi 
                FROM $place_table 
                WHERE (wojewodztwa_uslugi IS NOT NULL AND wojewodztwa_uslugi != '') 
                   OR (region IS NOT NULL AND region != '')
                LIMIT 20
            ");
            
            if ($sample_data) {
                echo "<table class='data-table'>";
                echo "<tr><th>Post ID</th><th>Region</th><th>City</th><th>Województwa Usługi</th><th>Powiaty Usługi</th></tr>";
                
                foreach ($sample_data as $row) {
                    echo "<tr>";
                    echo "<td>{$row->post_id}</td>";
                    echo "<td>" . esc_html($row->region) . "</td>";
                    echo "<td>" . esc_html($row->city) . "</td>";
                    echo "<td>" . esc_html($row->wojewodztwa_uslugi) . "</td>";
                    echo "<td>" . esc_html($row->powiaty_uslugi) . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p>❌ Brak danych do wyświetlenia</p>";
            }
        }
        ?>
    </div>
    
    <div class="section">
        <h2>4. Unikalne wartości województw</h2>
        <?php
        if ($table_exists || isset($place_table)) {
            // Pobierz unikalne wartości z różnych kolumn
            $wojewodztwa_uslugi = $wpdb->get_col("
                SELECT DISTINCT wojewodztwa_uslugi 
                FROM $place_table 
                WHERE wojewodztwa_uslugi IS NOT NULL 
                AND wojewodztwa_uslugi != '' 
                AND wojewodztwa_uslugi != 'NULL'
                LIMIT 50
            ");
            
            $regions = $wpdb->get_col("
                SELECT DISTINCT region 
                FROM $place_table 
                WHERE region IS NOT NULL 
                AND region != '' 
                AND region != 'NULL'
                LIMIT 50
            ");
            
            echo "<h3>Wartości z kolumny 'wojewodztwa_uslugi':</h3>";
            echo "<div class='unique-values'>";
            foreach ($wojewodztwa_uslugi as $woj) {
                echo "<div class='value-tag'>" . esc_html($woj) . "</div>";
            }
            echo "</div>";
            
            echo "<h3>Wartości z kolumny 'region':</h3>";
            echo "<div class='unique-values'>";
            foreach ($regions as $reg) {
                echo "<div class='value-tag'>" . esc_html($reg) . "</div>";
            }
            echo "</div>";
        }
        ?>
    </div>
    
    <div class="section">
        <h2>5. Analiza formatów nazw</h2>
        <?php
        if ($table_exists || isset($place_table)) {
            // Analiza formatów
            $all_regions = array_merge($wojewodztwa_uslugi ?: [], $regions ?: []);
            $all_regions = array_unique($all_regions);
            
            $formats = [
                'z_wojewodztwo' => [],
                'bez_wojewodztwo' => [],
                'angielskie' => [],
                'inne' => []
            ];
            
            foreach ($all_regions as $region) {
                if (empty($region)) continue;
                
                if (stripos($region, 'województwo') !== false) {
                    $formats['z_wojewodztwo'][] = $region;
                } elseif (preg_match('/^[a-zA-Z\s-]+$/', $region) && !preg_match('/[ąćęłńóśźż]/i', $region)) {
                    $formats['angielskie'][] = $region;
                } elseif (preg_match('/^[a-ząćęłńóśźż\s-]+$/i', $region)) {
                    $formats['bez_wojewodztwo'][] = $region;
                } else {
                    $formats['inne'][] = $region;
                }
            }
            
            foreach ($formats as $format => $values) {
                if (!empty($values)) {
                    echo "<h3>Format: $format</h3>";
                    echo "<div class='unique-values'>";
                    foreach ($values as $value) {
                        echo "<div class='value-tag'>" . esc_html($value) . "</div>";
                    }
                    echo "</div>";
                }
            }
        }
        ?>
    </div>
    
    <div class="section">
        <h2>6. Porównanie z selektorem</h2>
        <?php
        // Załaduj dane z JSON
        $json_file = get_stylesheet_directory() . '/assets/json/powiaty.json';
        if (file_exists($json_file)) {
            $json_data = json_decode(file_get_contents($json_file), true);
            
            echo "<h3>Województwa w selektorze:</h3>";
            echo "<div class='unique-values'>";
            foreach (array_keys($json_data) as $woj) {
                echo "<div class='value-tag'>" . esc_html($woj) . "</div>";
            }
            echo "</div>";
            
            // Porównanie
            echo "<h3>Analiza dopasowań:</h3>";
            $selector_wojewodztwa = array_keys($json_data);
            $db_wojewodztwa = array_unique(array_merge($wojewodztwa_uslugi ?: [], $regions ?: []));
            
            echo "<table class='data-table'>";
            echo "<tr><th>Selektor</th><th>Dopasowania w bazie</th><th>Status</th></tr>";
            
            foreach ($selector_wojewodztwa as $sel_woj) {
                $matches = [];
                foreach ($db_wojewodztwa as $db_woj) {
                    if (stripos($db_woj, $sel_woj) !== false || stripos($sel_woj, $db_woj) !== false) {
                        $matches[] = $db_woj;
                    }
                }
                
                echo "<tr>";
                echo "<td><strong>" . esc_html($sel_woj) . "</strong></td>";
                echo "<td>" . implode(', ', array_map('esc_html', $matches)) . "</td>";
                echo "<td>" . (empty($matches) ? '❌ Brak' : '✅ OK') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        ?>
    </div>
    
    <div class="section">
        <h2>7. Testowe zapytanie SQL</h2>
        <?php
        if ($table_exists || isset($place_table)) {
            $test_wojewodztwo = 'małopolskie';
            
            echo "<h3>Test dla województwa: <strong>$test_wojewodztwo</strong></h3>";
            
            // Różne warianty zapytań
            $queries = [
                "Dokładne dopasowanie wojewodztwa_uslugi" => "SELECT COUNT(*) FROM $place_table WHERE wojewodztwa_uslugi = '$test_wojewodztwo'",
                "LIKE wojewodztwa_uslugi" => "SELECT COUNT(*) FROM $place_table WHERE wojewodztwa_uslugi LIKE '%$test_wojewodztwo%'",
                "LIKE region" => "SELECT COUNT(*) FROM $place_table WHERE region LIKE '%$test_wojewodztwo%'",
                "Kombinowane" => "SELECT COUNT(*) FROM $place_table WHERE wojewodztwa_uslugi LIKE '%$test_wojewodztwo%' OR region LIKE '%$test_wojewodztwo%'"
            ];
            
            echo "<table class='data-table'>";
            echo "<tr><th>Typ zapytania</th><th>Wynik</th><th>SQL</th></tr>";
            
            foreach ($queries as $name => $sql) {
                $result = $wpdb->get_var($sql);
                echo "<tr>";
                echo "<td><strong>$name</strong></td>";
                echo "<td><strong>$result</strong> rekordów</td>";
                echo "<td><code>" . esc_html($sql) . "</code></td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        ?>
    </div>
    
    <div class="section">
        <h2>8. Rekomendacje</h2>
        <div style="background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107;">
            <h3>🔧 Zalecane poprawki:</h3>
            <ol>
                <li><strong>Mapowanie nazw:</strong> Utworzyć mapowanie między nazwami w selektorze a nazwami w bazie</li>
                <li><strong>Fuzzy matching:</strong> Używać LIKE z % dla elastycznego dopasowywania</li>
                <li><strong>Normalizacja:</strong> Usuwać "województwo" z nazw przed porównaniem</li>
                <li><strong>Wielokolumnowe wyszukiwanie:</strong> Sprawdzać zarówno 'wojewodztwa_uslugi' jak i 'region'</li>
                <li><strong>Cache wyników:</strong> Zapisywać mapowania w cache dla wydajności</li>
            </ol>
        </div>
    </div>
    
    <button onclick="location.reload()">🔄 Odśwież dane</button>
    <button onclick="window.close()">❌ Zamknij</button>
    
</body>
</html>
