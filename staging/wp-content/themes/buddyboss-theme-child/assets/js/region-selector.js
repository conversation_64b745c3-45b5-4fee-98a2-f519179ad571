/**
 * JavaScript dla systemu wyboru regionu
 * 
 * @package BuddyBoss Child
 * @version 1.0.0
 */

(function($) {
    'use strict';
    
    // Sprawdzenie czy LupikRegion jest dostępny
    if (typeof LupikRegion === 'undefined') {
        console.error('LupikRegion object not found');
        return;
    }
    
    let regionsData = {};
    let isDetecting = false;
    
    /**
     * Inicjalizacja systemu
     */
    function init() {
        loadRegionsData();
        bindEvents();
        
        // Jeśli to pierwsza wizyta, pokaż modal po 2 sekundach
        if (LupikRegion.is_first_visit) {
            setTimeout(showModal, 2000);
        }
        
        updateRegionButton();
    }
    
    /**
     * Ładowanie danych regionów z JSON
     */
    function loadRegionsData() {
        $.getJSON(LupikRegion.regions_url)
            .done(function(data) {
                regionsData = data;
                populateWojewodztwaSelect();
            })
            .fail(function() {
                console.error('Nie udało się załadować danych regionów');
                showStatus('error', 'Błąd ładowania danych regionów');
            });
    }
    
    /**
     * Wypełnienie selecta województw
     */
    function populateWojewodztwaSelect() {
        const $select = $('#lupik-wojewodztwo');
        $select.empty().append('<option value="">Wybierz województwo</option>');
        
        Object.keys(regionsData).sort().forEach(function(wojewodztwo) {
            $select.append(`<option value="${wojewodztwo}">${wojewodztwo}</option>`);
        });
    }
    
    /**
     * Wypełnienie selecta powiatów
     */
    function populatePowiatySelect(wojewodztwo) {
        const $select = $('#lupik-powiat');
        $select.empty().append('<option value="">Wybierz powiat (opcjonalnie)</option>');
        
        if (wojewodztwo && regionsData[wojewodztwo]) {
            regionsData[wojewodztwo].sort().forEach(function(powiat) {
                $select.append(`<option value="${powiat}">${powiat}</option>`);
            });
            $select.prop('disabled', false);
        } else {
            $select.prop('disabled', true);
        }
    }
    
    /**
     * Bindowanie eventów
     */
    function bindEvents() {
        // Zmiana województwa
        $(document).on('change', '#lupik-wojewodztwo', function() {
            const wojewodztwo = $(this).val();
            populatePowiatySelect(wojewodztwo);
            updateSaveButton();
        });
        
        // Zmiana powiatu
        $(document).on('change', '#lupik-powiat', function() {
            updateSaveButton();
        });
        
        // Automatyczne wykrywanie
        $(document).on('click', '#lupik-auto-detect', function() {
            if (!isDetecting) {
                detectLocation();
            }
        });
        
        // Zapisywanie regionu
        $(document).on('click', '#lupik-save-region', function() {
            saveRegion();
        });
        
        // Pomijanie wyboru
        $(document).on('click', '#lupik-skip-selection', function() {
            skipSelection();
        });
        
        // Przycisk zmiany regionu
        $(document).on('click', '#lupik-change-region', function() {
            showModal();
        });
        
        // Zamykanie modala
        $(document).on('click', '.lupik-modal-overlay', function(e) {
            if (e.target === this) {
                hideModal();
            }
        });
        
        // ESC key
        $(document).on('keydown', function(e) {
            if (e.keyCode === 27 && $('#lupik-region-modal').is(':visible')) {
                hideModal();
            }
        });
    }
    
    /**
     * Wykrywanie lokalizacji użytkownika
     */
    function detectLocation() {
        if (!navigator.geolocation) {
            showStatus('error', LupikRegion.strings.error_location);
            return;
        }
        
        isDetecting = true;
        showStatus('loading', LupikRegion.strings.detecting);
        $('#lupik-auto-detect').prop('disabled', true);
        
        navigator.geolocation.getCurrentPosition(
            function(position) {
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;
                
                // Wysłanie współrzędnych do serwera
                $.ajax({
                    url: LupikRegion.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'get_user_location',
                        nonce: LupikRegion.nonce,
                        lat: lat,
                        lng: lng
                    },
                    success: function(response) {
                        if (response.success) {
                            const region = response.data;
                            $('#lupik-wojewodztwo').val(region.wojewodztwo).trigger('change');
                            if (region.powiat) {
                                setTimeout(function() {
                                    $('#lupik-powiat').val(region.powiat);
                                    updateSaveButton();
                                }, 100);
                            }
                            showStatus('success', 'Lokalizacja wykryta pomyślnie!');
                        } else {
                            showStatus('error', response.data || LupikRegion.strings.error_location);
                        }
                    },
                    error: function() {
                        showStatus('error', LupikRegion.strings.error_location);
                    },
                    complete: function() {
                        isDetecting = false;
                        $('#lupik-auto-detect').prop('disabled', false);
                        setTimeout(hideStatus, 3000);
                    }
                });
            },
            function(error) {
                let errorMsg = LupikRegion.strings.error_location;
                switch(error.code) {
                    case error.PERMISSION_DENIED:
                        errorMsg = "Dostęp do lokalizacji został odrzucony";
                        break;
                    case error.POSITION_UNAVAILABLE:
                        errorMsg = "Lokalizacja niedostępna";
                        break;
                    case error.TIMEOUT:
                        errorMsg = "Przekroczono czas oczekiwania";
                        break;
                }
                
                showStatus('error', errorMsg);
                isDetecting = false;
                $('#lupik-auto-detect').prop('disabled', false);
                setTimeout(hideStatus, 5000);
            },
            {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 300000
            }
        );
    }
    
    /**
     * Zapisywanie wybranego regionu
     */
    function saveRegion() {
        const wojewodztwo = $('#lupik-wojewodztwo').val();
        const powiat = $('#lupik-powiat').val();
        
        if (!wojewodztwo) {
            showStatus('error', 'Wybierz województwo');
            return;
        }
        
        showStatus('loading', 'Zapisywanie...');
        
        $.ajax({
            url: LupikRegion.ajax_url,
            type: 'POST',
            data: {
                action: 'save_user_region',
                nonce: LupikRegion.nonce,
                wojewodztwo: wojewodztwo,
                powiat: powiat
            },
            success: function(response) {
                if (response.success) {
                    showStatus('success', LupikRegion.strings.success_saved);
                    updateRegionButton(response.data.region);

                    // Powiadom o zmianie regionu
                    $(document).trigger('lupik_region_changed', [response.data.region]);

                    setTimeout(function() {
                        hideModal();
                        // Opcjonalnie: przeładuj stronę aby zastosować filtry
                        // location.reload();
                    }, 1500);
                } else {
                    showStatus('error', response.data || 'Błąd zapisywania');
                }
            },
            error: function() {
                showStatus('error', 'Błąd połączenia');
            }
        });
    }
    
    /**
     * Pomijanie wyboru regionu
     */
    function skipSelection() {
        // Zapisz "Cała Polska" jako wybór
        $.ajax({
            url: LupikRegion.ajax_url,
            type: 'POST',
            data: {
                action: 'save_user_region',
                nonce: LupikRegion.nonce,
                wojewodztwo: 'Cała Polska',
                powiat: ''
            },
            success: function() {
                var region = {wojewodztwo: 'Cała Polska', powiat: ''};
                updateRegionButton(region);

                // Powiadom o zmianie regionu
                $(document).trigger('lupik_region_changed', [region]);

                hideModal();
            }
        });
    }
    
    /**
     * Aktualizacja przycisku regionu
     */
    function updateRegionButton(region) {
        region = region || LupikRegion.current_region;
        let text = 'Cała Polska';
        
        if (region && region.wojewodztwo && region.wojewodztwo !== 'Cała Polska') {
            text = region.wojewodztwo;
            if (region.powiat) {
                text += ', ' + region.powiat;
            }
        }
        
        $('.lupik-current-region').text(text);
    }
    
    /**
     * Aktualizacja stanu przycisku zapisz
     */
    function updateSaveButton() {
        const wojewodztwo = $('#lupik-wojewodztwo').val();
        $('#lupik-save-region').prop('disabled', !wojewodztwo);
    }
    
    /**
     * Pokazanie statusu
     */
    function showStatus(type, message) {
        const $status = $('#lupik-status');
        const $text = $('.lupik-status-text');
        const $spinner = $('.lupik-spinner');
        
        $text.text(message);
        
        if (type === 'loading') {
            $spinner.show();
            $status.removeClass('lupik-status-error lupik-status-success');
        } else if (type === 'error') {
            $spinner.hide();
            $status.removeClass('lupik-status-success').addClass('lupik-status-error');
        } else if (type === 'success') {
            $spinner.hide();
            $status.removeClass('lupik-status-error').addClass('lupik-status-success');
        }
        
        $status.show();
    }
    
    /**
     * Ukrycie statusu
     */
    function hideStatus() {
        $('#lupik-status').hide();
    }
    
    /**
     * Pokazanie modala
     */
    function showModal() {
        $('#lupik-region-modal').fadeIn(300);
        $('body').addClass('lupik-modal-open');
    }
    
    /**
     * Ukrycie modala
     */
    function hideModal() {
        $('#lupik-region-modal').fadeOut(300);
        $('body').removeClass('lupik-modal-open');
        hideStatus();
    }
    
    // Inicjalizacja po załadowaniu DOM
    $(document).ready(init);
    
})(jQuery);
