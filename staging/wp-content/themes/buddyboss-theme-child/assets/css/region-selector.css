/**
 * Style dla systemu wyboru regionu
 * 
 * @package BuddyBoss Child
 * @version 1.0.0
 */

/* ===== MODAL OVERLAY ===== */
.lupik-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 99999;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(3px);
}

/* ===== MODAL CONTAINER ===== */
.lupik-modal-container {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    animation: lupikModalSlideIn 0.3s ease-out;
}

@keyframes lupikModalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* ===== MODAL HEADER ===== */
.lupik-modal-header {
    padding: 30px 30px 20px;
    text-align: center;
    border-bottom: 1px solid #e9ecef;
}

.lupik-modal-title {
    margin: 0 0 10px;
    font-size: 24px;
    font-weight: 600;
    color: #2c3e50;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.lupik-modal-title .lupik-icon {
    font-size: 22px;
}

.lupik-modal-subtitle {
    margin: 0;
    color: #6c757d;
    font-size: 16px;
    line-height: 1.4;
}

/* ===== MODAL BODY ===== */
.lupik-modal-body {
    padding: 30px;
}

/* ===== DETECTION SECTION ===== */
.lupik-detection-section {
    text-align: center;
    margin-bottom: 20px;
}

.lupik-detection-info {
    margin: 10px 0 0;
    font-size: 14px;
    color: #6c757d;
    line-height: 1.4;
}

/* ===== DIVIDER ===== */
.lupik-divider {
    text-align: center;
    margin: 25px 0;
    position: relative;
}

.lupik-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e9ecef;
}

.lupik-divider span {
    background: #ffffff;
    padding: 0 15px;
    color: #6c757d;
    font-size: 14px;
    position: relative;
    z-index: 1;
}

/* ===== MANUAL SECTION ===== */
.lupik-manual-section h3 {
    margin: 0 0 20px;
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

/* ===== FORM GROUPS ===== */
.lupik-form-group {
    margin-bottom: 20px;
}

.lupik-form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #495057;
    font-size: 14px;
}

.lupik-select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 16px;
    background: #ffffff;
    transition: all 0.3s ease;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}

.lupik-select:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.1);
}

.lupik-select:disabled {
    background-color: #f8f9fa;
    color: #6c757d;
    cursor: not-allowed;
}

/* ===== BUTTONS ===== */
.lupik-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    line-height: 1;
}

.lupik-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.lupik-btn-primary {
    background: #007cba;
    color: #ffffff;
}

.lupik-btn-primary:hover:not(:disabled) {
    background: #005a87;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 124, 186, 0.3);
}

.lupik-btn-success {
    background: #28a745;
    color: #ffffff;
}

.lupik-btn-success:hover:not(:disabled) {
    background: #218838;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.lupik-btn-secondary {
    background: #6c757d;
    color: #ffffff;
}

.lupik-btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

/* ===== STATUS/LOADING ===== */
.lupik-status {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin: 20px 0;
}

.lupik-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #e9ecef;
    border-radius: 50%;
    border-top-color: #007cba;
    animation: lupikSpin 1s ease-in-out infinite;
    margin-right: 10px;
}

@keyframes lupikSpin {
    to {
        transform: rotate(360deg);
    }
}

.lupik-status-text {
    color: #495057;
    font-size: 14px;
}

.lupik-status-error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.lupik-status-success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

/* ===== BODY MODAL OPEN ===== */
body.lupik-modal-open {
    overflow: hidden;
}

/* ===== WIDGET STYLES ===== */
.lupik-region-widget-container {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.lupik-current-region-display {
    margin-bottom: 15px;
    padding: 10px;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.lupik-region-label {
    display: block;
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 5px;
    font-weight: 500;
}

.lupik-region-value {
    font-size: 14px;
    color: #495057;
    font-weight: 600;
}

/* Full widget style */
.lupik-style-full .lupik-form-row {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.lupik-style-full .lupik-form-col {
    flex: 1;
}

.lupik-style-full .lupik-form-col label {
    display: block;
    margin-bottom: 5px;
    font-size: 12px;
    font-weight: 500;
    color: #495057;
}

.lupik-style-full .lupik-form-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.lupik-style-full .lupik-btn {
    flex: 1;
    min-width: 120px;
    font-size: 14px;
    padding: 8px 12px;
}

/* Compact widget style */
.lupik-style-compact .lupik-region-compact-selector {
    text-align: center;
}

.lupik-style-compact .lupik-btn {
    width: 100%;
    justify-content: center;
}

/* Button only style */
.lupik-style-button .lupik-region-button-selector {
    text-align: center;
}

.lupik-style-button .lupik-btn {
    width: 100%;
    justify-content: center;
    font-size: 14px;
}

/* Widget select styling */
.lupik-widget-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    background: #ffffff;
}

.lupik-widget-select:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.1);
}

/* Button variants */
.lupik-btn-outline {
    background: transparent;
    color: #007cba;
    border: 2px solid #007cba;
}

.lupik-btn-outline:hover {
    background: #007cba;
    color: #ffffff;
}

/* Shortcode title */
.lupik-region-shortcode-title {
    margin: 0 0 15px;
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

/* ===== MODAL FOOTER ===== */
.lupik-modal-footer {
    padding: 20px 30px 30px;
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

/* ===== REGION BUTTON (FLOATING) ===== */
.lupik-region-button {
    position: fixed;
    top: 120px;
    right: 20px;
    z-index: 9998;
    background: #007cba;
    color: white;
    border: none;
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 4px 20px rgba(0, 124, 186, 0.3);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    max-width: 200px;
}

/* Ukryj floating button gdy menu region jest dostępne */
.lupik-region-menu-item ~ .lupik-region-button,
body.has-region-menu .lupik-region-button {
    display: none;
}

.lupik-region-button:hover {
    background: #005a87;
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(0, 124, 186, 0.4);
}

.lupik-region-button .lupik-icon {
    font-size: 16px;
}

/* ===== GENERAL ICON STYLES ===== */
.lupik-icon {
    display: inline-block;
    line-height: 1;
}

.lupik-current-region {
    font-size: 12px;
    opacity: 0.9;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .lupik-modal-container {
        width: 95%;
        margin: 20px;
    }
    
    .lupik-modal-header,
    .lupik-modal-body,
    .lupik-modal-footer {
        padding: 20px;
    }
    
    .lupik-modal-title {
        font-size: 20px;
    }
    
    .lupik-modal-footer {
        flex-direction: column;
    }
    
    .lupik-btn {
        width: 100%;
        justify-content: center;
    }
    
    .lupik-region-button {
        right: 10px;
        top: 100px;
        padding: 10px 12px;
        font-size: 12px;
        max-width: 150px;
    }
    
    .lupik-current-region {
        font-size: 11px;
    }
}

@media (max-width: 480px) {
    .lupik-modal-container {
        width: 100%;
        height: 100%;
        border-radius: 0;
        max-height: none;
    }
    
    .lupik-region-button {
        position: relative;
        right: auto;
        top: auto;
        margin: 10px;
        max-width: none;
    }
}
