<?php
/**
 * Test naprawy błędu regionów
 * 
 * UWAGA: Usuń po testach!
 */

// Ładowanie WordPress
require_once('../../../../../wp-load.php');

// Sprawdzenie uprawnień
if (!current_user_can('manage_options')) {
    wp_die('Brak uprawnień');
}

?>
<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Naprawy Błędu Regionów</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { background: #f9f9f9; padding: 15px; margin: 15px 0; border-radius: 5px; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button { background: #007cba; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #005a87; }
        .code { background: #f4f4f4; padding: 10px; border-radius: 3px; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <h1>🔧 Test Naprawy Błędu Regionów</h1>
    
    <div class="section">
        <h2>1. Status aktywnych hooków</h2>
        <?php
        $integration = Lupik_Region_Search_Integration::get_instance();
        
        // Sprawdź które hooki są aktywne
        $active_hooks = [
            'pre_get_posts' => has_action('pre_get_posts', array($integration, 'modify_search_query')),
            'geodir_posts_where' => has_filter('geodir_posts_where', array($integration, 'filter_posts_by_region')),
            'geodir_before_search_form' => has_action('geodir_before_search_form', array($integration, 'add_region_fields_to_search')),
            'wp_footer' => has_action('wp_footer', array($integration, 'add_search_integration_script')),
            'geodir_map_default_location' => has_filter('geodir_map_default_location', array($integration, 'set_default_map_location'))
        ];
        
        foreach ($active_hooks as $hook => $is_active) {
            $class = $is_active ? 'warning' : 'success';
            $status = $is_active ? '⚠️ AKTYWNY' : '✅ WYŁĄCZONY';
            $note = $is_active ? ' (może powodować konflikty)' : ' (bezpieczny)';
            
            echo "<div class='test-result $class'>$status - $hook$note</div>";
        }
        ?>
    </div>
    
    <div class="section">
        <h2>2. Test aktualnego regionu użytkownika</h2>
        <?php
        $region_selector = Lupik_Region_Selector::get_instance();
        $current_region = $region_selector->get_user_region();
        
        if ($current_region) {
            echo "<div class='test-result info'>";
            echo "<strong>Aktualny region:</strong><br>";
            echo "Województwo: " . esc_html($current_region['wojewodztwo']) . "<br>";
            if (!empty($current_region['powiat'])) {
                echo "Powiat: " . esc_html($current_region['powiat']);
            }
            echo "</div>";
        } else {
            echo "<div class='test-result warning'>⚠️ Brak zapisanego regionu</div>";
        }
        ?>
    </div>
    
    <div class="section">
        <h2>3. Test JavaScript w konsoli</h2>
        <div class="test-result info">
            <strong>Instrukcje:</strong><br>
            1. Otwórz konsolę przeglądarki (F12)<br>
            2. Przejdź na stronę z wyszukiwaniem GeoDirectory<br>
            3. Sprawdź czy nie ma błędów JavaScript<br>
            4. Sprawdź czy formularz wyszukiwania ma ukryte pola regionu
        </div>
        
        <button onclick="testJavaScript()">🧪 Test JavaScript</button>
        <button onclick="checkSearchForm()">🔍 Sprawdź formularz wyszukiwania</button>
    </div>
    
    <div class="section">
        <h2>4. Test wyszukiwania bez konfliktów</h2>
        <div class="test-result info">
            <strong>Co zostało wyłączone:</strong><br>
            • Automatyczne filtrowanie query (pre_get_posts)<br>
            • Modyfikacja WHERE clause (geodir_posts_where)<br>
            • Filtrowanie widget'ów i RSS<br><br>
            
            <strong>Co pozostało aktywne:</strong><br>
            • Ukryte pola w formularzu wyszukiwania<br>
            • JavaScript do automatycznego ustawiania regionu<br>
            • Integracja z mapą
        </div>
        
        <button onclick="window.open('/miejsca/', '_blank')">🏢 Test strony firm</button>
        <button onclick="window.open('/miejsca/?geodir_search=1', '_blank')">🔍 Test wyszukiwania</button>
    </div>
    
    <div class="section">
        <h2>5. Sprawdzenie błędów w logach</h2>
        <?php
        // Sprawdź ostatnie błędy PHP
        $error_log = ini_get('error_log');
        if ($error_log && file_exists($error_log)) {
            $recent_errors = array_slice(file($error_log), -10);
            $lupik_errors = array_filter($recent_errors, function($line) {
                return strpos($line, 'Lupik') !== false || strpos($line, 'selW.forEach') !== false;
            });
            
            if (!empty($lupik_errors)) {
                echo "<div class='test-result error'>";
                echo "<strong>Ostatnie błędy związane z Lupik:</strong><br>";
                foreach ($lupik_errors as $error) {
                    echo "<div class='code'>" . esc_html(trim($error)) . "</div>";
                }
                echo "</div>";
            } else {
                echo "<div class='test-result success'>✅ Brak błędów Lupik w logach</div>";
            }
        } else {
            echo "<div class='test-result info'>ℹ️ Nie można sprawdzić logów błędów</div>";
        }
        ?>
    </div>
    
    <div class="section">
        <h2>6. Instrukcje testowania</h2>
        <div class="test-result info">
            <strong>Kroki testowania:</strong><br>
            1. <strong>Wybierz region</strong> - użyj selektora regionu<br>
            2. <strong>Przejdź na stronę firm</strong> - kliknij "Test strony firm"<br>
            3. <strong>Sprawdź czy nie ma błędów JavaScript</strong> - otwórz konsolę (F12)<br>
            4. <strong>Sprawdź wyszukiwanie</strong> - użyj formularza wyszukiwania<br>
            5. <strong>Sprawdź czy firmy się wyświetlają</strong> - powinny być widoczne<br><br>
            
            <strong>Oczekiwane rezultaty:</strong><br>
            ✅ Brak błędu "selW.forEach is not a function"<br>
            ✅ Strona się ładuje bez błędów<br>
            ✅ Formularz wyszukiwania działa<br>
            ✅ Firmy są widoczne (może być mniej niż wcześniej - to normalne)
        </div>
    </div>
    
    <div class="section">
        <h2>7. Akcje</h2>
        <button onclick="location.reload()">🔄 Odśwież test</button>
        <button onclick="clearRegion()">🗑️ Wyczyść region</button>
        <button onclick="setTestRegion()">🎯 Ustaw testowy region</button>
        <button onclick="window.open('debug-region-data.php', '_blank')">📊 Debug bazy danych</button>
    </div>
    
    <script>
    function testJavaScript() {
        console.log('=== LUPIK REGION TEST ===');
        console.log('LupikRegion:', typeof LupikRegion !== 'undefined' ? LupikRegion : 'Not found');
        console.log('jQuery:', typeof jQuery !== 'undefined' ? 'Available' : 'Not found');
        console.log('Search forms:', jQuery('form[class*="geodir"]').length);
        console.log('Region selects:', jQuery('select[name*="wojewodztwa"]').length);
        alert('Sprawdź konsolę przeglądarki (F12) - informacje debugowania');
    }
    
    function checkSearchForm() {
        if (typeof jQuery !== 'undefined') {
            var forms = jQuery('form[class*="geodir"]');
            var hiddenFields = jQuery('input[name*="region"]');
            
            console.log('Search forms found:', forms.length);
            console.log('Hidden region fields:', hiddenFields.length);
            
            if (forms.length > 0) {
                alert('Znaleziono ' + forms.length + ' formularzy wyszukiwania i ' + hiddenFields.length + ' ukrytych pól regionu');
            } else {
                alert('Nie znaleziono formularzy wyszukiwania na tej stronie');
            }
        } else {
            alert('jQuery nie jest dostępne');
        }
    }
    
    function clearRegion() {
        if (confirm('Czy wyczyścić zapisany region użytkownika?')) {
            fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=save_user_region&nonce=<?php echo wp_create_nonce('lupik_region_nonce'); ?>&wojewodztwo=Cała Polska&powiat='
            }).then(() => {
                alert('Region wyczyszczony');
                location.reload();
            });
        }
    }
    
    function setTestRegion() {
        if (confirm('Czy ustawić testowy region (małopolskie)?')) {
            fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=save_user_region&nonce=<?php echo wp_create_nonce('lupik_region_nonce'); ?>&wojewodztwo=małopolskie&powiat='
            }).then(() => {
                alert('Ustawiono testowy region: małopolskie');
                location.reload();
            });
        }
    }
    </script>
    
    <?php wp_head(); ?>
    <?php wp_footer(); ?>
</body>
</html>
