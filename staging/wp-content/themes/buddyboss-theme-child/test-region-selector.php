<?php
/**
 * Plik testowy dla systemu wyboru regionu
 * 
 * UWAGA: Ten plik służy tylko do testowania!
 * Usuń go po zakończeniu testów lub zmień nazwę na .txt
 * 
 * Aby użyć: otwórz w przeglądarce: /wp-content/themes/buddyboss-theme-child/test-region-selector.php
 */

// Ładowanie WordPress
require_once('../../../../../wp-load.php');

// Sprawdzenie czy użytkownik ma uprawnienia (opcjonalnie)
if (!current_user_can('manage_options')) {
    wp_die('Brak uprawnień do testowania');
}

?>
<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Systemu Wyboru Regionu</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #005a87; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; }
    </style>
</head>
<body>
    <h1>🧪 Test Systemu Wyboru Regionu Lupik</h1>
    
    <div class="test-section">
        <h2>1. Test podstawowych klas i plików</h2>
        <?php
        $tests = [
            'Klasa Lupik_Region_Selector' => class_exists('Lupik_Region_Selector'),
            'Klasa Lupik_Region_Search_Integration' => class_exists('Lupik_Region_Search_Integration'),
            'Klasa Lupik_Region_Widget' => class_exists('Lupik_Region_Widget'),
            'Plik CSS' => file_exists(get_stylesheet_directory() . '/assets/css/region-selector.css'),
            'Plik JS' => file_exists(get_stylesheet_directory() . '/assets/js/region-selector.js'),
            'Plik JSON powiatów' => file_exists(get_stylesheet_directory() . '/assets/json/powiaty.json'),
            'Template modala' => file_exists(get_stylesheet_directory() . '/templates/region-selector-modal.php')
        ];
        
        foreach ($tests as $test => $result) {
            $class = $result ? 'success' : 'error';
            $status = $result ? '✅ PASS' : '❌ FAIL';
            echo "<div class='test-result $class'>$status - $test</div>";
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>2. Test instancji klas</h2>
        <?php
        try {
            $region_selector = Lupik_Region_Selector::get_instance();
            echo "<div class='test-result success'>✅ PASS - Instancja Lupik_Region_Selector utworzona</div>";
            
            $integration = Lupik_Region_Search_Integration::get_instance();
            echo "<div class='test-result success'>✅ PASS - Instancja Lupik_Region_Search_Integration utworzona</div>";
            
        } catch (Exception $e) {
            echo "<div class='test-result error'>❌ FAIL - Błąd tworzenia instancji: " . $e->getMessage() . "</div>";
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>3. Test aktualnego regionu użytkownika</h2>
        <?php
        $region_selector = Lupik_Region_Selector::get_instance();
        $current_region = $region_selector->get_user_region();
        $is_first_visit = $region_selector->is_first_visit();
        
        echo "<div class='test-result info'>";
        echo "<strong>Pierwsza wizyta:</strong> " . ($is_first_visit ? 'TAK' : 'NIE') . "<br>";
        echo "<strong>Aktualny region:</strong> ";
        if ($current_region) {
            echo $current_region['wojewodztwo'];
            if (!empty($current_region['powiat'])) {
                echo ', ' . $current_region['powiat'];
            }
            echo "<br><strong>Timestamp:</strong> " . date('Y-m-d H:i:s', $current_region['timestamp']);
        } else {
            echo "Brak zapisanego regionu";
        }
        echo "</div>";
        ?>
    </div>
    
    <div class="test-section">
        <h2>4. Test danych JSON powiatów</h2>
        <?php
        $json_file = get_stylesheet_directory() . '/assets/json/powiaty.json';
        if (file_exists($json_file)) {
            $json_data = json_decode(file_get_contents($json_file), true);
            if ($json_data) {
                echo "<div class='test-result success'>✅ PASS - Plik JSON załadowany poprawnie</div>";
                echo "<div class='test-result info'>";
                echo "<strong>Liczba województw:</strong> " . count($json_data) . "<br>";
                echo "<strong>Przykładowe województwa:</strong> " . implode(', ', array_slice(array_keys($json_data), 0, 5)) . "...<br>";
                
                $total_powiaty = 0;
                foreach ($json_data as $powiaty) {
                    $total_powiaty += count($powiaty);
                }
                echo "<strong>Łączna liczba powiatów:</strong> " . $total_powiaty;
                echo "</div>";
            } else {
                echo "<div class='test-result error'>❌ FAIL - Błąd parsowania JSON</div>";
            }
        } else {
            echo "<div class='test-result error'>❌ FAIL - Plik JSON nie istnieje</div>";
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>5. Test hooków WordPress</h2>
        <?php
        $hooks_to_check = [
            'wp_enqueue_scripts' => 'Ładowanie skryptów',
            'wp_ajax_save_user_region' => 'AJAX zapisywanie regionu',
            'wp_ajax_nopriv_save_user_region' => 'AJAX zapisywanie regionu (niezalogowani)',
            'wp_ajax_get_user_location' => 'AJAX geolokalizacja',
            'wp_ajax_nopriv_get_user_location' => 'AJAX geolokalizacja (niezalogowani)',
            'wp_footer' => 'Renderowanie modala',
            'pre_get_posts' => 'Modyfikacja query wyszukiwania',
            'geodir_before_search_form' => 'Dodanie pól regionu do wyszukiwania'
        ];
        
        foreach ($hooks_to_check as $hook => $description) {
            $has_hook = has_action($hook);
            $class = $has_hook ? 'success' : 'warning';
            $status = $has_hook ? '✅ PASS' : '⚠️ INFO';
            echo "<div class='test-result $class'>$status - $description ($hook)</div>";
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>6. Test shortcode'a</h2>
        <?php
        if (shortcode_exists('lupik_region_selector')) {
            echo "<div class='test-result success'>✅ PASS - Shortcode [lupik_region_selector] zarejestrowany</div>";
            
            echo "<h3>Przykład użycia shortcode'a:</h3>";
            echo "<div class='code'>[lupik_region_selector style=\"compact\" show_current=\"true\" title=\"Wybierz region\"]</div>";
            
            echo "<h3>Renderowany shortcode:</h3>";
            echo "<div style='border: 1px solid #ddd; padding: 15px; background: white;'>";
            echo do_shortcode('[lupik_region_selector style="compact" show_current="true" title="Test regionu"]');
            echo "</div>";
        } else {
            echo "<div class='test-result error'>❌ FAIL - Shortcode nie jest zarejestrowany</div>";
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>7. Test widget'a</h2>
        <?php
        global $wp_widget_factory;
        if (isset($wp_widget_factory->widgets['Lupik_Region_Widget'])) {
            echo "<div class='test-result success'>✅ PASS - Widget Lupik_Region_Widget zarejestrowany</div>";
        } else {
            echo "<div class='test-result error'>❌ FAIL - Widget nie jest zarejestrowany</div>";
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>8. Test funkcji JavaScript</h2>
        <div id="js-test-results"></div>
        <button onclick="testJavaScript()">Uruchom test JavaScript</button>
        <button onclick="clearCookies()">Wyczyść cookies regionu</button>
        <button onclick="testGeolocation()">Test geolokalizacji</button>
    </div>
    
    <div class="test-section">
        <h2>9. Informacje debugowania</h2>
        <div class="test-result info">
            <strong>WordPress Version:</strong> <?php echo get_bloginfo('version'); ?><br>
            <strong>PHP Version:</strong> <?php echo PHP_VERSION; ?><br>
            <strong>Theme:</strong> <?php echo get_stylesheet(); ?><br>
            <strong>Child Theme Directory:</strong> <?php echo get_stylesheet_directory(); ?><br>
            <strong>Current User:</strong> <?php echo wp_get_current_user()->user_login; ?><br>
            <strong>Is HTTPS:</strong> <?php echo is_ssl() ? 'TAK' : 'NIE'; ?><br>
            <strong>Cookie Domain:</strong> <?php echo COOKIE_DOMAIN; ?><br>
            <strong>Cookie Path:</strong> <?php echo COOKIEPATH; ?>
        </div>
    </div>
    
    <div class="test-section">
        <h2>10. Akcje testowe</h2>
        <button onclick="showModal()">Pokaż modal wyboru regionu</button>
        <button onclick="location.reload()">Odśwież stronę</button>
        <button onclick="openInIncognito()">Instrukcje testu incognito</button>
    </div>

    <!-- Ładowanie skryptów WordPress -->
    <?php wp_head(); ?>
    
    <script>
    function testJavaScript() {
        const results = document.getElementById('js-test-results');
        results.innerHTML = '';
        
        // Test dostępności obiektów
        const tests = [
            { name: 'jQuery', test: () => typeof jQuery !== 'undefined' },
            { name: 'LupikRegion object', test: () => typeof LupikRegion !== 'undefined' },
            { name: 'Modal element', test: () => document.getElementById('lupik-region-modal') !== null },
            { name: 'Region button', test: () => document.getElementById('lupik-change-region') !== null },
            { name: 'Geolocation API', test: () => 'geolocation' in navigator }
        ];
        
        tests.forEach(test => {
            const result = test.test();
            const status = result ? '✅ PASS' : '❌ FAIL';
            const className = result ? 'success' : 'error';
            results.innerHTML += `<div class="test-result ${className}">${status} - ${test.name}</div>`;
        });
    }
    
    function clearCookies() {
        document.cookie = 'lupik_user_region=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
        alert('Cookies regionu zostały wyczyszczone. Odśwież stronę aby zobaczyć modal.');
    }
    
    function testGeolocation() {
        if ('geolocation' in navigator) {
            navigator.geolocation.getCurrentPosition(
                position => {
                    alert(`Geolokalizacja działa!\nSzerokosc: ${position.coords.latitude}\nDługość: ${position.coords.longitude}`);
                },
                error => {
                    alert(`Błąd geolokalizacji: ${error.message}`);
                }
            );
        } else {
            alert('Geolokalizacja nie jest dostępna w tej przeglądarce');
        }
    }
    
    function showModal() {
        if (typeof jQuery !== 'undefined' && jQuery('#lupik-region-modal').length) {
            jQuery('#lupik-region-modal').fadeIn(300);
        } else {
            alert('Modal nie jest dostępny. Sprawdź czy skrypty są załadowane.');
        }
    }
    
    function openInIncognito() {
        alert('Aby przetestować modal dla nowych użytkowników:\n\n1. Otwórz stronę główną w trybie incognito\n2. Modal powinien pojawić się po 2 sekundach\n3. Przetestuj wszystkie opcje\n4. Sprawdź czy region jest zapisywany');
    }
    
    // Auto-test JavaScript po załadowaniu
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(testJavaScript, 1000);
    });
    </script>
    
    <?php wp_footer(); ?>
</body>
</html>
