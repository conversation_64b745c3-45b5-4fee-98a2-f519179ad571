<?php
/**
 * Test integracji regionu z menu
 * 
 * UWAGA: Ten plik służy tylko do testowania!
 * Usuń go po zakończeniu testów
 */

// Ładowanie WordPress
require_once('../../../../../wp-load.php');

// Sprawdzenie uprawnień
if (!current_user_can('manage_options')) {
    wp_die('Brak uprawnień do testowania');
}

?>
<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Integracji Menu z Regionem</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #005a87; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; }
        .test-links {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 15px 0;
        }
        .test-links a {
            padding: 8px 15px;
            background: #007cba;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
        }
        .test-links a:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <h1>🧪 Test Integracji Menu z Regionem</h1>
    
    <div class="test-section">
        <h2>1. Test klas integracji menu</h2>
        <?php
        $tests = [
            'Klasa Lupik_Region_Menu_Integration' => class_exists('Lupik_Region_Menu_Integration'),
            'Plik integracji menu' => file_exists(get_stylesheet_directory() . '/includes/region-menu-integration.php'),
            'Hook wp_nav_menu_items' => has_filter('wp_nav_menu_items'),
            'Hook body_class' => has_filter('body_class')
        ];
        
        foreach ($tests as $test => $result) {
            $class = $result ? 'success' : 'error';
            $status = $result ? '✅ PASS' : '❌ FAIL';
            echo "<div class='test-result $class'>$status - $test</div>";
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>2. Test menu locations</h2>
        <?php
        $menu_locations = get_nav_menu_locations();
        echo "<div class='test-result info'>";
        echo "<strong>Dostępne lokalizacje menu:</strong><br>";
        foreach ($menu_locations as $location => $menu_id) {
            $menu = wp_get_nav_menu_object($menu_id);
            $menu_name = $menu ? $menu->name : 'Brak menu';
            echo "• <strong>$location:</strong> $menu_name (ID: $menu_id)<br>";
        }
        echo "</div>";
        
        // Sprawdź czy header-menu istnieje
        $has_header_menu = isset($menu_locations['header-menu']) && $menu_locations['header-menu'] > 0;
        $class = $has_header_menu ? 'success' : 'warning';
        $status = $has_header_menu ? '✅ PASS' : '⚠️ WARNING';
        echo "<div class='test-result $class'>$status - Menu główne (header-menu) " . ($has_header_menu ? 'jest przypisane' : 'nie jest przypisane') . "</div>";
        ?>
    </div>
    
    <div class="test-section">
        <h2>3. Test aktualnego regionu</h2>
        <?php
        if (class_exists('Lupik_Region_Selector')) {
            $region_selector = Lupik_Region_Selector::get_instance();
            $current_region = $region_selector->get_user_region();
            
            echo "<div class='test-result info'>";
            echo "<strong>Aktualny region użytkownika:</strong><br>";
            if ($current_region) {
                echo "• Województwo: " . esc_html($current_region['wojewodztwo']) . "<br>";
                echo "• Powiat: " . (empty($current_region['powiat']) ? 'Brak' : esc_html($current_region['powiat'])) . "<br>";
                echo "• Zapisano: " . date('Y-m-d H:i:s', $current_region['timestamp']) . "<br>";
            } else {
                echo "• Brak zapisanego regionu<br>";
            }
            echo "</div>";
        } else {
            echo "<div class='test-result error'>❌ FAIL - Klasa Lupik_Region_Selector nie istnieje</div>";
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>4. Test renderowania menu</h2>
        <?php
        if (isset($menu_locations['header-menu']) && $menu_locations['header-menu'] > 0) {
            echo "<h3>Przykład renderowania menu głównego:</h3>";
            echo "<div style='border: 1px solid #ddd; padding: 15px; background: white; max-height: 300px; overflow-y: auto;'>";
            
            // Renderuj menu z naszą integracją
            wp_nav_menu(array(
                'theme_location' => 'header-menu',
                'container' => 'nav',
                'container_class' => 'test-menu-container',
                'menu_class' => 'test-menu-list',
                'echo' => true
            ));
            
            echo "</div>";
            
            echo "<div class='test-result info'>";
            echo "<strong>Sprawdź czy:</strong><br>";
            echo "• Element regionu jest widoczny w menu powyżej<br>";
            echo "• Ma ikonę 📍 i tekst regionu<br>";
            echo "• Ma strzałkę dropdown ▼<br>";
            echo "• Po kliknięciu rozwija się dropdown<br>";
            echo "</div>";
        } else {
            echo "<div class='test-result warning'>⚠️ WARNING - Brak przypisanego menu głównego. Przypisz menu w Wygląd → Menu</div>";
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>5. Test na różnych stronach</h2>
        <p>Kliknij poniższe linki aby przetestować działanie na różnych stronach:</p>
        
        <div class="test-links">
            <a href="<?php echo home_url('/'); ?>" target="_blank">Strona główna</a>
            <a href="<?php echo home_url('/sample-page/'); ?>" target="_blank">Przykładowa strona</a>
            <a href="<?php echo admin_url('admin.php?page=geodir_settings'); ?>" target="_blank">GeoDirectory</a>
            <a href="<?php echo home_url('/members/'); ?>" target="_blank">Członkowie</a>
            <a href="<?php echo home_url('/activity/'); ?>" target="_blank">Aktywność</a>
            <a href="<?php echo home_url('/groups/'); ?>" target="_blank">Grupy</a>
        </div>
        
        <div class="test-result info">
            <strong>Na każdej stronie sprawdź:</strong><br>
            • Czy element regionu jest widoczny w menu głównym<br>
            • Czy floating button jest ukryty (gdy menu jest aktywne)<br>
            • Czy dropdown działa poprawnie<br>
            • Czy modal otwiera się po kliknięciu "Zmień region"<br>
            • Czy szybki wybór regionu działa<br>
            • Czy automatyczne wykrywanie działa<br>
        </div>
    </div>
    
    <div class="test-section">
        <h2>6. Test JavaScript funkcjonalności</h2>
        <div id="js-menu-test-results"></div>
        <button onclick="testMenuJavaScript()">Test JavaScript menu</button>
        <button onclick="testMenuInteractions()">Test interakcji menu</button>
        <button onclick="simulateRegionChange()">Symuluj zmianę regionu</button>
    </div>
    
    <div class="test-section">
        <h2>7. Test responsywności</h2>
        <div class="test-result info">
            <strong>Instrukcje testowania responsywności:</strong><br>
            1. Zmień rozmiar okna przeglądarki<br>
            2. Przetestuj na urządzeniach mobilnych<br>
            3. Sprawdź czy dropdown menu regionu dostosowuje się do ekranu<br>
            4. Sprawdź czy przyciski są klikalne na dotyk<br>
            5. Sprawdź czy tekst regionu nie wychodzi poza kontener<br>
        </div>
        
        <button onclick="simulateMobile()">Symuluj widok mobilny</button>
        <button onclick="simulateDesktop()">Przywróć widok desktop</button>
    </div>
    
    <div class="test-section">
        <h2>8. Akcje debugowania</h2>
        <button onclick="showRegionModal()">Pokaż modal regionu</button>
        <button onclick="clearRegionCookie()">Wyczyść cookie regionu</button>
        <button onclick="showConsoleInfo()">Pokaż info w konsoli</button>
        <button onclick="location.reload()">Odśwież stronę</button>
    </div>

    <!-- Ładowanie skryptów WordPress -->
    <?php wp_head(); ?>
    
    <script>
    function testMenuJavaScript() {
        const results = document.getElementById('js-menu-test-results');
        results.innerHTML = '';
        
        const tests = [
            { name: 'Element menu regionu', test: () => document.querySelector('.lupik-region-menu-item') !== null },
            { name: 'Link menu regionu', test: () => document.querySelector('.lupik-region-menu-link') !== null },
            { name: 'Dropdown menu', test: () => document.querySelector('.lupik-region-dropdown') !== null },
            { name: 'Przyciski akcji', test: () => document.querySelectorAll('.lupik-region-actions .lupik-btn').length >= 2 },
            { name: 'Szybki wybór regionów', test: () => document.querySelectorAll('.lupik-quick-region').length >= 5 },
            { name: 'Klasa body has-region-menu', test: () => document.body.classList.contains('has-region-menu') }
        ];
        
        tests.forEach(test => {
            const result = test.test();
            const status = result ? '✅ PASS' : '❌ FAIL';
            const className = result ? 'success' : 'error';
            results.innerHTML += `<div class="test-result ${className}">${status} - ${test.name}</div>`;
        });
    }
    
    function testMenuInteractions() {
        const menuLink = document.querySelector('.lupik-region-menu-link');
        if (menuLink) {
            // Symuluj kliknięcie
            menuLink.click();
            
            setTimeout(() => {
                const isActive = document.querySelector('.lupik-region-menu-item.active') !== null;
                const status = isActive ? '✅ PASS' : '❌ FAIL';
                const className = isActive ? 'success' : 'error';
                
                document.getElementById('js-menu-test-results').innerHTML += 
                    `<div class="test-result ${className}">${status} - Dropdown toggle działa</div>`;
            }, 100);
        } else {
            document.getElementById('js-menu-test-results').innerHTML += 
                '<div class="test-result error">❌ FAIL - Brak elementu menu do testowania</div>';
        }
    }
    
    function simulateRegionChange() {
        if (typeof jQuery !== 'undefined') {
            const newRegion = {
                wojewodztwo: 'śląskie',
                powiat: 'katowicki'
            };
            
            jQuery(document).trigger('lupik_region_changed', [newRegion]);
            
            setTimeout(() => {
                const regionText = document.querySelector('.lupik-region-text');
                const success = regionText && regionText.textContent.includes('śląskie');
                const status = success ? '✅ PASS' : '❌ FAIL';
                const className = success ? 'success' : 'error';
                
                document.getElementById('js-menu-test-results').innerHTML += 
                    `<div class="test-result ${className}">${status} - Aktualizacja tekstu regionu w menu</div>`;
            }, 100);
        }
    }
    
    function simulateMobile() {
        document.body.style.maxWidth = '480px';
        document.body.style.margin = '0';
        document.body.style.padding = '10px';
        alert('Symulacja widoku mobilnego włączona. Sprawdź responsywność menu regionu.');
    }
    
    function simulateDesktop() {
        document.body.style.maxWidth = '1200px';
        document.body.style.margin = '0 auto';
        document.body.style.padding = '20px';
        alert('Widok desktop przywrócony.');
    }
    
    function showRegionModal() {
        if (typeof jQuery !== 'undefined' && jQuery('#lupik-region-modal').length) {
            jQuery('#lupik-region-modal').fadeIn(300);
        } else {
            alert('Modal regionu nie jest dostępny');
        }
    }
    
    function clearRegionCookie() {
        document.cookie = 'lupik_user_region=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
        alert('Cookie regionu wyczyszczone. Odśwież stronę.');
    }
    
    function showConsoleInfo() {
        console.log('=== LUPIK REGION DEBUG INFO ===');
        console.log('LupikRegion object:', typeof LupikRegion !== 'undefined' ? LupikRegion : 'Not available');
        console.log('Menu element:', document.querySelector('.lupik-region-menu-item'));
        console.log('Modal element:', document.querySelector('#lupik-region-modal'));
        console.log('Body classes:', document.body.className);
        console.log('Current region cookie:', document.cookie.split(';').find(c => c.trim().startsWith('lupik_user_region=')));
        alert('Informacje debugowania wyświetlone w konsoli przeglądarki (F12)');
    }
    
    // Auto-test po załadowaniu
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(testMenuJavaScript, 1000);
    });
    </script>
    
    <?php wp_footer(); ?>
</body>
</html>
