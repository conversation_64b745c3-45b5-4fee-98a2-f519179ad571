<?php
/**
 * @package BuddyBoss Child
 * The parent theme functions are located at /buddyboss-theme/inc/theme/functions.php
 * Add your own functions at the bottom of this file.
 */


/****************************** THEME SETUP ******************************/

/**
 * Sets up theme for translation
 *
 * @since BuddyBoss Child 1.0.0
 */
function buddyboss_theme_child_languages()
{
  /**
   * Makes child theme available for translation.
   * Translations can be added into the /languages/ directory.
   */

  // Translate text from the PARENT theme.
  load_theme_textdomain( 'buddyboss-theme', get_stylesheet_directory() . '/languages' );

  // Translate text from the CHILD theme only.
  // Change 'buddyboss-theme' instances in all child theme files to 'buddyboss-theme-child'.
  // load_theme_textdomain( 'buddyboss-theme-child', get_stylesheet_directory() . '/languages' );

}
add_action( 'after_setup_theme', 'buddyboss_theme_child_languages' );

/**
 * Enqueues scripts and styles for child theme front-end.
 *
 * @since Boss Child Theme  1.0.0
 */

require_once get_stylesheet_directory() . '/geodirectory_mod.php';
require_once get_stylesheet_directory() . '/geodirectory_categories_menu.php';

// System geolokalizacji regionu - modularny
require_once get_stylesheet_directory() . '/includes/region-mapping.php';
require_once get_stylesheet_directory() . '/includes/region-selector.php';
require_once get_stylesheet_directory() . '/includes/region-search-integration.php';
require_once get_stylesheet_directory() . '/includes/region-widget.php';
require_once get_stylesheet_directory() . '/includes/region-menu-integration.php';

function buddyboss_theme_child_scripts_styles() {

/* ----------  CSS  ---------- */
    // 1) główny arkusz child-theme
    wp_enqueue_style(
        'buddyboss-child-css',
        get_stylesheet_directory_uri() . '/assets/css/custom.css'
    );

    // 2) styl profilu GeoDirectory
    wp_enqueue_style(
        'gd-profile-css',
        get_stylesheet_directory_uri() . '/assets/css/gd-profile.css',
        array( 'buddyboss-child-css' ),
        '1.0'
    );

    // 3) **NOWY plik dla podstron**
    wp_enqueue_style(
        'podstrony-css',
        get_stylesheet_directory_uri() . '/assets/css/podstrony.css',
        array( 'buddyboss-child-css' ), // ładuj po custom.css
        '1.0'
    );


    /* ----------  JS  ---------- */
    wp_enqueue_script(
        'buddyboss-child-js',
        get_stylesheet_directory_uri() . '/assets/js/custom.js',
        array(),
        null,
        true
    );
}
add_action( 'wp_enqueue_scripts', 'buddyboss_theme_child_scripts_styles', 9999 );



/****************************** CUSTOM FUNCTIONS ******************************/

// Add your own custom functions here


/////////////////
////////dodanie ładowania powiatów
/////////////////


///frontend
add_action( 'wp_footer', function () {

    $uri = $_SERVER['REQUEST_URI'];

    //  Ładuj skrypt, jeżeli ścieżka zaczyna się od /add-listing/places/
    if ( strpos( $uri, '/add-listing/miejsca/' ) === false ) {
        return;                                    //  -> nie jesteśmy w edycji front-end
    }

    /* ─── wyciągnij ID wpisu ze ścieżki ───
       np. /add-listing/places/51/  ⇒  51
    */
    $listing_id = 0;
    if ( preg_match( '#/add-listing/miejsca/(\d+)/?#', $uri, $m ) ) {
        $listing_id = absint( $m[1] );
    }

    // pobierz zapisane powiaty, jeśli mamy ID
    $saved_pow = $listing_id ? geodir_get_post_meta( $listing_id, 'powiaty_uslugi', true ) : '';

    /* ─── wstrzyknij zmienną + skrypt (ver=1.6) ─── */
    printf(
        '<script>var PowiatyData={jsonUrl:"%s",preselected:"%s"};</script>',
        esc_url( get_stylesheet_directory_uri() . '/assets/json/powiaty.json' ),
        esc_js( $saved_pow )
    );

    echo '<script src="' . esc_url(
            get_stylesheet_directory_uri() . '/assets/js/powiaty.js?ver=1.9'
         ) . '"></script>';
}, 20 );




////admin
function lupik_admin_powiaty_script( $hook ) {

    $screen = get_current_screen();
    if ( empty( $screen ) || $screen->post_type !== 'gd_place' ) {
        return;
    }

    // ⬇⬇⬇  KLUCZOWA ZMIANA
    $saved_powiaty = geodir_get_post_meta( get_the_ID(), 'powiaty_uslugi', true );

    wp_register_script( 'powiaty-data', '' );
    wp_add_inline_script(
        'powiaty-data',
        'var PowiatyData = {jsonUrl:"' . esc_js( get_stylesheet_directory_uri() . '/assets/json/powiaty.json' ) . '", preselected:"' . esc_js( $saved_powiaty ) . '"};',
        'before'
    );
    wp_enqueue_script( 'powiaty-data' );

    wp_enqueue_script(
        'powiaty-select2-admin',
        get_stylesheet_directory_uri() . '/assets/js/powiaty.js',
        [ 'jquery', 'select2', 'powiaty-data' ],
        '1.9',
        true
    );
}
add_action( 'admin_enqueue_scripts', 'lupik_admin_powiaty_script', 20 );



//////////////////////
/////////////// koniec wybierania powiatów
//////////////////////


/////////////////pole wyszukiwania powiatow
add_action( 'wp_enqueue_scripts', function () {

    wp_enqueue_script(
        'powiaty-search',
        get_stylesheet_directory_uri() . '/assets/js/powiaty-search.js',
        [ 'jquery' ],
        '1.5',               // podbijaj przy zmianie
        true
    );

    wp_add_inline_script(
        'powiaty-search',
        'var PowiatyData = {jsonUrl:"' .
        esc_js( get_stylesheet_directory_uri() . '/assets/json/powiaty.json' ) .
        '"};',
        'before'
    );
}, 999 );    // ← bardzo późno, po skryptach GeoDirectory


/////poprawne pokazywanie powiatow - własny shortcode
/* -------------------------------------------------------------------
 *  Shortcode [powiaty_raw]  →  zwraca wartości meta "powiaty_uslugi"
 * ------------------------------------------------------------------*/
add_shortcode( 'powiaty_raw', function () {

    $raw = geodir_get_post_meta( get_the_ID(), 'powiaty_uslugi', true );
    if ( empty( $raw ) ) return '';

   
    return esc_html( str_replace( ',', ', ', $raw ) );
} );

/////przypisywanie szablonu do typu profilu
add_filter( 'geodir_details_page_id', function ( $page_id, $post_type ) {

	global $gd_post;
	if ( empty( $gd_post ) || empty( $gd_post->package_id ) ) {
		return $page_id;
	}

	$pkg = (int) $gd_post->package_id;

	switch ( $pkg ) {
		case 1:   $page_id = 36;    break;          // free
		case 2:   $page_id = 8401; break; 
		case 3:   $page_id = 8402; break;          // premium
	}

	// ukryj tytuł tylko raz
	static $css_added = false;
	if ( ! $css_added && in_array( $pkg, [2,3], true ) ) {
		$css_added = true;
		add_action( 'wp_head', function () {
			echo '<style>.entry-header{display:none!important}</style>';
		} );
	}

	return $page_id;
}, 10, 2 );




