<?php
/**
 * Template dla modala wyboru regionu
 * 
 * @package BuddyBoss Child
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<!-- Modal wyboru regionu -->
<div id="lupik-region-modal" class="lupik-modal-overlay" style="display: none;">
    <div class="lupik-modal-container">
        <div class="lupik-modal-header">
            <h2 class="lupik-modal-title">
                <span class="lupik-icon">📍</span>
                <?php _e('Wybierz swój region', 'buddyboss-theme'); ?>
            </h2>
            <p class="lupik-modal-subtitle">
                <?php _e('Aby otrzymywać spersonalizowane treści i oferty z Twojej okolicy', 'buddyboss-theme'); ?>
            </p>
        </div>
        
        <div class="lupik-modal-body">
            <!-- Opcja automatycznego wykrywania -->
            <div class="lupik-detection-section">
                <button type="button" id="lupik-auto-detect" class="lupik-btn lupik-btn-primary">
                    <span class="lupik-icon">🎯</span>
                    <?php _e('Wykryj automatycznie', 'buddyboss-theme'); ?>
                </button>
                <p class="lupik-detection-info">
                    <?php _e('Użyjemy Twojej lokalizacji GPS do automatycznego określenia regionu', 'buddyboss-theme'); ?>
                </p>
            </div>
            
            <div class="lupik-divider">
                <span><?php _e('lub', 'buddyboss-theme'); ?></span>
            </div>
            
            <!-- Opcja ręcznego wyboru -->
            <div class="lupik-manual-section">
                <h3><?php _e('Wybierz ręcznie', 'buddyboss-theme'); ?></h3>
                
                <div class="lupik-form-group">
                    <label for="lupik-wojewodztwo"><?php _e('Województwo', 'buddyboss-theme'); ?></label>
                    <select id="lupik-wojewodztwo" class="lupik-select">
                        <option value=""><?php _e('Wybierz województwo', 'buddyboss-theme'); ?></option>
                        <!-- Opcje będą dodane przez JavaScript -->
                    </select>
                </div>
                
                <div class="lupik-form-group">
                    <label for="lupik-powiat"><?php _e('Powiat (opcjonalnie)', 'buddyboss-theme'); ?></label>
                    <select id="lupik-powiat" class="lupik-select" disabled>
                        <option value=""><?php _e('Najpierw wybierz województwo', 'buddyboss-theme'); ?></option>
                    </select>
                </div>
            </div>
            
            <!-- Status/Loading -->
            <div id="lupik-status" class="lupik-status" style="display: none;">
                <div class="lupik-spinner"></div>
                <span class="lupik-status-text"></span>
            </div>
        </div>
        
        <div class="lupik-modal-footer">
            <button type="button" id="lupik-save-region" class="lupik-btn lupik-btn-success" disabled>
                <span class="lupik-icon">✅</span>
                <?php _e('Zapisz wybór', 'buddyboss-theme'); ?>
            </button>
            <button type="button" id="lupik-skip-selection" class="lupik-btn lupik-btn-secondary">
                <?php _e('Pomiń (pokaż wszystko)', 'buddyboss-theme'); ?>
            </button>
        </div>
    </div>
</div>

<!-- Przycisk zmiany regionu (zawsze widoczny) -->
<button type="button" id="lupik-change-region" class="lupik-region-button" title="<?php _e('Zmień region', 'buddyboss-theme'); ?>">
    <span class="lupik-icon">📍</span>
    <span class="lupik-current-region">
        <?php
        $region_selector = Lupik_Region_Selector::get_instance();
        $current_region = $region_selector->get_user_region();
        if ($current_region && $current_region['wojewodztwo'] !== 'Cała Polska') {
            echo esc_html($current_region['wojewodztwo']);
            if (!empty($current_region['powiat'])) {
                echo ', ' . esc_html($current_region['powiat']);
            }
        } else {
            _e('Cała Polska', 'buddyboss-theme');
        }
        ?>
    </span>
</button>

<script type="text/javascript">
// Inicjalizacja modala po załadowaniu DOM
jQuery(document).ready(function($) {
    // Pokazuj modal tylko jeśli to pierwsza wizyta
    if (typeof LupikRegion !== 'undefined' && LupikRegion.is_first_visit) {
        // Opóźnienie 2 sekundy dla lepszego UX
        setTimeout(function() {
            $('#lupik-region-modal').fadeIn(300);
        }, 2000);
    }
    
    // Obsługa przycisku zmiany regionu
    $('#lupik-change-region').on('click', function() {
        $('#lupik-region-modal').fadeIn(300);
    });
    
    // Zamykanie modala po kliknięciu w tło
    $('#lupik-region-modal').on('click', function(e) {
        if (e.target === this) {
            $(this).fadeOut(300);
        }
    });
});
</script>
