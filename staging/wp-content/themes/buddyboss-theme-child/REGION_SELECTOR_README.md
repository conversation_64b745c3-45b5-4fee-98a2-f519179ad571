# System Wyboru Regionu Lupik

## Opis
Modularny system wyboru domyślnego regionu dla użytkowników strony Lupik.pl. System umożliwia:
- Automatyczne wykrywanie lokalizacji użytkownika (geolokalizacja)
- Ręczny wybór województwa i powiatu
- Zapisywanie wyboru w cookies (30 dni)
- Integrację z wyszukiwaniem GeoDirectory
- Filtrowanie treści RSS na podstawie regionu
- Łatwe zarządzanie przez widget'y i shortcode'y

## Struktura plików

### Główne pliki systemu:
```
staging/wp-content/themes/buddyboss-theme-child/
├── includes/
│   ├── region-selector.php              # Główna klasa systemu
│   ├── region-search-integration.php    # Integracja z GeoDirectory
│   └── region-widget.php               # Widget i shortcode
├── templates/
│   └── region-selector-modal.php       # Template modala
├── assets/
│   ├── css/
│   │   └── region-selector.css         # Style CSS
│   └── js/
│       └── region-selector.js          # JavaScript
└── REGION_SELECTOR_README.md           # Ta dokumentacja
```

## Funkcjonalności

### 1. Modal wyboru regionu
- Automatycznie wyświetla się nowym użytkownikom po 2 sekundach
- Opcja automatycznego wykrywania lokalizacji GPS
- Ręczny wybór województwa i powiatu
- Możliwość pominięcia (wybór "Cała Polska")

### 2. Przycisk zmiany regionu
- Zawsze widoczny floating button w prawym górnym rogu
- Pokazuje aktualnie wybrany region
- Umożliwia ponowne otwarcie modala

### 3. Widget wyboru regionu
- Dostępny w panelu administracyjnym: Wygląd → Widget'y
- Trzy style: kompaktowy, pełny, tylko przycisk
- Możliwość pokazania aktualnego regionu

### 4. Shortcode
```
[lupik_region_selector style="compact" show_current="true" title="Wybierz region"]
```

Parametry:
- `style`: "compact", "full", "button"
- `show_current`: "true", "false"
- `title`: opcjonalny tytuł

### 5. Integracja z wyszukiwaniem
- Automatyczne filtrowanie wyników GeoDirectory
- Ustawianie domyślnej lokalizacji mapy
- Filtrowanie RSS feeds
- Automatyczne ustawianie regionu w formularzach wyszukiwania

## Instalacja i konfiguracja

### 1. Automatyczna instalacja
System jest już zintegrowany z child theme. Pliki są automatycznie ładowane przez `functions.php`.

### 2. Ręczne włączenie/wyłączenie

**Opcja 1: Przez wp-config.php (zalecane)**
Dodaj do `wp-config.php`:
```php
define('LUPIK_REGION_SELECTOR_DISABLED', true);
```

**Opcja 2: Przez functions.php**
Zakomentuj w `functions.php`:
```php
// require_once get_stylesheet_directory() . '/includes/region-selector.php';
// require_once get_stylesheet_directory() . '/includes/region-search-integration.php';
// require_once get_stylesheet_directory() . '/includes/region-widget.php';
```

### 3. Konfiguracja cookies
W pliku `region-selector.php` można zmienić:
```php
const COOKIE_NAME = 'lupik_user_region';        # Nazwa cookie
const COOKIE_LIFETIME = 30 * DAY_IN_SECONDS;    # Czas życia (30 dni)
```

## Testowanie

### 1. Test podstawowej funkcjonalności
1. Otwórz stronę w trybie incognito
2. Sprawdź czy modal się pojawia po 2 sekundach
3. Przetestuj automatyczne wykrywanie lokalizacji
4. Przetestuj ręczny wybór regionu
5. Sprawdź czy region jest zapisywany (odśwież stronę)

### 2. Test integracji z wyszukiwaniem
1. Wybierz konkretny region
2. Przejdź do wyszukiwania firm
3. Sprawdź czy formularz ma ustawiony region
4. Wykonaj wyszukiwanie i sprawdź wyniki

### 3. Test widget'a
1. Idź do Wygląd → Widget'y
2. Dodaj widget "Wybór Regionu Lupik"
3. Przetestuj różne style
4. Sprawdź funkcjonalność na frontend'zie

### 4. Test shortcode'a
Dodaj do strony/wpisu:
```
[lupik_region_selector style="full" show_current="true" title="Twój region"]
```

### 5. Test responsywności
- Sprawdź na urządzeniach mobilnych
- Przetestuj różne rozdzielczości
- Sprawdź czy modal jest dostępny

## API i eventy JavaScript

### Dostępne eventy:
```javascript
// Powiadomienie o zmianie regionu
$(document).on('lupik_region_changed', function(e, region) {
    console.log('Nowy region:', region);
});

// Ręczne wywołanie zmiany regionu
$(document).trigger('lupik_region_changed', [{
    wojewodztwo: 'mazowieckie',
    powiat: 'warszawski'
}]);
```

### Dostępne funkcje PHP:
```php
// Pobranie aktualnego regionu użytkownika
$region_selector = Lupik_Region_Selector::get_instance();
$region = $region_selector->get_user_region();

// Sprawdzenie czy to pierwsza wizyta
$is_first_visit = $region_selector->is_first_visit();

// Zapisanie regionu
$region_selector->save_user_region('mazowieckie', 'warszawski');
```

## Rozwiązywanie problemów

### 1. Modal się nie pojawia
- Sprawdź czy JavaScript jest załadowany
- Sprawdź konsolę przeglądarki pod kątem błędów
- Upewnij się że to pierwsza wizyta (wyczyść cookies)

### 2. Geolokalizacja nie działa
- Sprawdź czy strona używa HTTPS
- Sprawdź czy użytkownik zezwolił na lokalizację
- Przetestuj na różnych przeglądarkach

### 3. Integracja z wyszukiwaniem nie działa
- Sprawdź czy GeoDirectory jest aktywne
- Sprawdź czy pola `wojewodztwa_uslugi` i `powiaty_uslugi` istnieją
- Sprawdź logi PHP pod kątem błędów

### 4. Style się nie ładują
- Sprawdź czy plik CSS jest dostępny
- Wyczyść cache przeglądarki
- Sprawdź czy nie ma konfliktów z innymi stylami

## Customizacja

### 1. Zmiana stylów
Edytuj plik `assets/css/region-selector.css` lub dodaj własne style w `custom.css`.

### 2. Zmiana tekstów
Wszystkie teksty używają funkcji `__()` i są gotowe do tłumaczenia.

### 3. Dodanie nowych województw/powiatów
Edytuj plik `assets/json/powiaty.json`.

### 4. Zmiana API geolokalizacji
W pliku `region-selector.php` zmień metodę `reverse_geocode()`.

## Bezpieczeństwo

- Wszystkie dane wejściowe są sanityzowane
- AJAX requesty używają nonce verification
- Cookies są ustawiane z flagami bezpieczeństwa
- Brak bezpośredniego dostępu do plików PHP

## Wydajność

- Dane regionów są cache'owane w JavaScript
- Minimalna liczba zapytań AJAX
- Lazy loading dla nieużywanych funkcji
- Optymalizowane zapytania do bazy danych

## Wsparcie

W przypadku problemów:
1. Sprawdź logi PHP i JavaScript
2. Przetestuj na czystej instalacji
3. Sprawdź kompatybilność z innymi pluginami
4. Skontaktuj się z deweloperem: Łukasz Nowak
