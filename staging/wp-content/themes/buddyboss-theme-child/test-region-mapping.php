<?php
/**
 * Test inteligentnego mapowania regionów
 * 
 * UWAGA: Usuń po testach!
 */

// Ładowanie WordPress
require_once('../../../../../wp-load.php');

// Sprawdzenie uprawnień
if (!current_user_can('manage_options')) {
    wp_die('Brak uprawnień');
}

?>
<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Mapowania Regionów</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { background: #f9f9f9; padding: 15px; margin: 15px 0; border-radius: 5px; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .data-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .data-table th { background: #007cba; color: white; }
        button { background: #007cba; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #005a87; }
        .code { background: #f4f4f4; padding: 10px; border-radius: 3px; font-family: monospace; font-size: 12px; }
        .variants { display: flex; flex-wrap: wrap; gap: 5px; margin: 10px 0; }
        .variant-tag { background: #e7f3ff; padding: 3px 8px; border-radius: 3px; font-size: 12px; }
    </style>
</head>
<body>
    <h1>🧪 Test Inteligentnego Mapowania Regionów</h1>
    
    <div class="section">
        <h2>1. Test klasy mapowania</h2>
        <?php
        $tests = [
            'Klasa Lupik_Region_Mapping' => class_exists('Lupik_Region_Mapping'),
            'Plik mapowania' => file_exists(get_stylesheet_directory() . '/includes/region-mapping.php'),
            'Instancja klasy' => method_exists('Lupik_Region_Mapping', 'get_instance')
        ];
        
        foreach ($tests as $test => $result) {
            $class = $result ? 'success' : 'error';
            $status = $result ? '✅ PASS' : '❌ FAIL';
            echo "<div class='test-result $class'>$status - $test</div>";
        }
        
        if (class_exists('Lupik_Region_Mapping')) {
            $mapping = Lupik_Region_Mapping::get_instance();
            echo "<div class='test-result success'>✅ Instancja utworzona pomyślnie</div>";
        }
        ?>
    </div>
    
    <div class="section">
        <h2>2. Test normalizacji nazw</h2>
        <?php
        if (class_exists('Lupik_Region_Mapping')) {
            $mapping = Lupik_Region_Mapping::get_instance();
            
            $test_cases = [
                'województwo małopolskie' => 'małopolskie',
                'Lesser Poland Voivodeship' => 'lesser poland',
                'Śląskie' => 'śląskie',
                'Silesian Voivodeship' => 'silesian',
                'powiat krakowski' => 'krakowski',
                'Kraków County' => 'kraków'
            ];
            
            echo "<table class='data-table'>";
            echo "<tr><th>Oryginał</th><th>Znormalizowane</th><th>Oczekiwane</th><th>Status</th></tr>";
            
            foreach ($test_cases as $original => $expected) {
                if (strpos($original, 'powiat') !== false || strpos($original, 'County') !== false) {
                    $normalized = $mapping->normalize_powiat($original);
                } else {
                    $normalized = $mapping->normalize_wojewodztwo($original);
                }
                
                $status = ($normalized === $expected) ? '✅' : '❌';
                $class = ($normalized === $expected) ? 'success' : 'error';
                
                echo "<tr class='$class'>";
                echo "<td>" . esc_html($original) . "</td>";
                echo "<td><strong>" . esc_html($normalized) . "</strong></td>";
                echo "<td>" . esc_html($expected) . "</td>";
                echo "<td>$status</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        ?>
    </div>
    
    <div class="section">
        <h2>3. Test mapowania województw</h2>
        <?php
        if (class_exists('Lupik_Region_Mapping')) {
            $mapping = Lupik_Region_Mapping::get_instance();
            
            $test_wojewodztwa = ['małopolskie', 'śląskie', 'mazowieckie', 'wielkopolskie'];
            
            echo "<table class='data-table'>";
            echo "<tr><th>Województwo</th><th>Warianty do wyszukiwania</th></tr>";
            
            foreach ($test_wojewodztwa as $woj) {
                $variants = $mapping->find_wojewodztwo_match($woj);
                
                echo "<tr>";
                echo "<td><strong>" . esc_html($woj) . "</strong></td>";
                echo "<td>";
                echo "<div class='variants'>";
                foreach ($variants as $variant) {
                    echo "<span class='variant-tag'>" . esc_html($variant) . "</span>";
                }
                echo "</div>";
                echo "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        ?>
    </div>
    
    <div class="section">
        <h2>4. Test generowania warunków SQL</h2>
        <?php
        if (class_exists('Lupik_Region_Mapping')) {
            $mapping = Lupik_Region_Mapping::get_instance();
            
            // Test dla różnych województw
            $test_cases = [
                ['wojewodztwo' => 'małopolskie', 'powiat' => ''],
                ['wojewodztwo' => 'śląskie', 'powiat' => 'katowicki'],
                ['wojewodztwo' => 'mazowieckie', 'powiat' => 'warszawski']
            ];
            
            echo "<table class='data-table'>";
            echo "<tr><th>Test</th><th>Warunki SQL</th></tr>";
            
            foreach ($test_cases as $i => $case) {
                $table = 'test_table';
                $wojewodztwo_sql = $mapping->generate_wojewodztwo_sql_conditions($table, $case['wojewodztwo']);
                
                echo "<tr>";
                echo "<td><strong>Test " . ($i + 1) . "</strong><br>";
                echo "Województwo: " . esc_html($case['wojewodztwo']) . "<br>";
                if (!empty($case['powiat'])) {
                    echo "Powiat: " . esc_html($case['powiat']);
                }
                echo "</td>";
                echo "<td><div class='code'>" . esc_html($wojewodztwo_sql) . "</div></td>";
                echo "</tr>";
                
                if (!empty($case['powiat'])) {
                    $powiat_sql = $mapping->generate_powiat_sql_conditions($table, $case['powiat']);
                    echo "<tr>";
                    echo "<td><em>Warunki powiatu</em></td>";
                    echo "<td><div class='code'>" . esc_html($powiat_sql) . "</div></td>";
                    echo "</tr>";
                }
            }
            echo "</table>";
        }
        ?>
    </div>
    
    <div class="section">
        <h2>5. Test rzeczywistych danych z bazy</h2>
        <?php
        if (class_exists('Lupik_Region_Mapping')) {
            $mapping = Lupik_Region_Mapping::get_instance();
            
            // Test z rzeczywistymi danymi
            $test_wojewodztwa = ['małopolskie', 'śląskie', 'mazowieckie'];
            
            echo "<table class='data-table'>";
            echo "<tr><th>Województwo</th><th>Liczba wyników</th><th>SQL</th><th>Warianty</th></tr>";
            
            foreach ($test_wojewodztwa as $woj) {
                $result = $mapping->test_matching($woj);
                
                $class = ($result['count'] > 0) ? 'success' : 'warning';
                
                echo "<tr class='$class'>";
                echo "<td><strong>" . esc_html($woj) . "</strong></td>";
                echo "<td><strong>" . $result['count'] . "</strong> firm</td>";
                echo "<td><div class='code' style='max-width: 300px; overflow-x: auto;'>" . esc_html($result['sql']) . "</div></td>";
                echo "<td>";
                echo "<div class='variants'>";
                foreach ($result['wojewodztwo_variants'] as $variant) {
                    echo "<span class='variant-tag'>" . esc_html($variant) . "</span>";
                }
                echo "</div>";
                echo "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        ?>
    </div>
    
    <div class="section">
        <h2>6. Test z powiatami</h2>
        <?php
        if (class_exists('Lupik_Region_Mapping')) {
            $mapping = Lupik_Region_Mapping::get_instance();
            
            $test_cases = [
                ['wojewodztwo' => 'małopolskie', 'powiat' => 'krakowski'],
                ['wojewodztwo' => 'śląskie', 'powiat' => 'katowicki'],
                ['wojewodztwo' => 'mazowieckie', 'powiat' => 'warszawski']
            ];
            
            echo "<table class='data-table'>";
            echo "<tr><th>Region</th><th>Liczba wyników</th><th>Status</th></tr>";
            
            foreach ($test_cases as $case) {
                $result = $mapping->test_matching($case['wojewodztwo'], $case['powiat']);
                
                $class = ($result['count'] > 0) ? 'success' : 'warning';
                $status = ($result['count'] > 0) ? '✅ Znaleziono' : '⚠️ Brak wyników';
                
                echo "<tr class='$class'>";
                echo "<td><strong>" . esc_html($case['wojewodztwo']) . "</strong>";
                if (!empty($case['powiat'])) {
                    echo "<br><em>" . esc_html($case['powiat']) . "</em>";
                }
                echo "</td>";
                echo "<td><strong>" . $result['count'] . "</strong> firm</td>";
                echo "<td>$status</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        ?>
    </div>
    
    <div class="section">
        <h2>7. Test pobierania danych z bazy</h2>
        <?php
        if (class_exists('Lupik_Region_Mapping')) {
            $mapping = Lupik_Region_Mapping::get_instance();
            
            echo "<h3>Województwa w bazie danych:</h3>";
            $db_wojewodztwa = $mapping->get_unique_wojewodztwa_from_db();
            
            if (!empty($db_wojewodztwa)) {
                echo "<div class='variants'>";
                foreach ($db_wojewodztwa as $woj) {
                    echo "<span class='variant-tag'>" . esc_html($woj) . "</span>";
                }
                echo "</div>";
                
                echo "<div class='test-result info'>Znaleziono " . count($db_wojewodztwa) . " unikalnych województw w bazie</div>";
            } else {
                echo "<div class='test-result warning'>⚠️ Brak danych województw w bazie lub błąd połączenia</div>";
            }
            
            // Test powiatów dla pierwszego województwa
            if (!empty($db_wojewodztwa)) {
                $first_woj = $db_wojewodztwa[0];
                echo "<h3>Powiaty dla województwa: <strong>$first_woj</strong></h3>";
                
                $powiaty = $mapping->get_unique_powiaty_from_db($first_woj);
                
                if (!empty($powiaty)) {
                    echo "<div class='variants'>";
                    foreach ($powiaty as $powiat) {
                        echo "<span class='variant-tag'>" . esc_html($powiat) . "</span>";
                    }
                    echo "</div>";
                    
                    echo "<div class='test-result info'>Znaleziono " . count($powiaty) . " powiatów</div>";
                } else {
                    echo "<div class='test-result warning'>⚠️ Brak powiatów dla tego województwa</div>";
                }
            }
        }
        ?>
    </div>
    
    <div class="section">
        <h2>8. Akcje testowe</h2>
        <button onclick="location.reload()">🔄 Odśwież testy</button>
        <button onclick="window.open('debug-region-data.php', '_blank')">🔍 Debug danych bazy</button>
        <button onclick="testCurrentRegion()">🎯 Test aktualnego regionu</button>
        <button onclick="showConsoleDebug()">📊 Debug w konsoli</button>
    </div>
    
    <script>
    function testCurrentRegion() {
        // Sprawdź aktualny region użytkownika
        if (typeof LupikRegion !== 'undefined' && LupikRegion.current_region) {
            const region = LupikRegion.current_region;
            alert('Aktualny region użytkownika:\n' + 
                  'Województwo: ' + (region.wojewodztwo || 'Brak') + '\n' +
                  'Powiat: ' + (region.powiat || 'Brak'));
        } else {
            alert('Brak zapisanego regionu użytkownika');
        }
    }
    
    function showConsoleDebug() {
        console.log('=== LUPIK REGION MAPPING DEBUG ===');
        console.log('LupikRegion object:', typeof LupikRegion !== 'undefined' ? LupikRegion : 'Not available');
        console.log('Current URL:', window.location.href);
        console.log('User agent:', navigator.userAgent);
        alert('Informacje debugowania w konsoli przeglądarki (F12)');
    }
    </script>
    
    <?php wp_head(); ?>
    <?php wp_footer(); ?>
</body>
</html>
