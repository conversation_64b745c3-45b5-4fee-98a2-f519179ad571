<?php
/**
 * Inteligentne mapowanie regionów między selektorem a bazą danych
 * 
 * @package BuddyBoss Child
 * <AUTHOR>
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Klasa do mapowania regionów
 */
class Lupik_Region_Mapping {
    
    /**
     * Instancja klasy
     */
    private static $instance = null;
    
    /**
     * Cache mapowań
     */
    private static $mapping_cache = null;
    
    /**
     * Singleton pattern
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Konstruktor
     */
    private function __construct() {
        // Inicjalizacja
    }
    
    /**
     * Normalizacja nazwy województwa
     */
    public function normalize_wojewodztwo($name) {
        if (empty($name)) {
            return '';
        }
        
        // Konwersja na małe litery
        $normalized = mb_strtolower(trim($name), 'UTF-8');
        
        // Usunięcie prefiksów
        $prefixes_to_remove = [
            'województwo ',
            'woj. ',
            'woj ',
            'voivodeship',
            'province'
        ];
        
        foreach ($prefixes_to_remove as $prefix) {
            if (strpos($normalized, $prefix) === 0) {
                $normalized = substr($normalized, strlen($prefix));
                break;
            }
        }
        
        // Usunięcie sufiksów
        $suffixes_to_remove = [
            ' voivodeship',
            ' province'
        ];
        
        foreach ($suffixes_to_remove as $suffix) {
            if (substr($normalized, -strlen($suffix)) === $suffix) {
                $normalized = substr($normalized, 0, -strlen($suffix));
                break;
            }
        }
        
        return trim($normalized);
    }
    
    /**
     * Normalizacja nazwy powiatu
     */
    public function normalize_powiat($name) {
        if (empty($name)) {
            return '';
        }
        
        // Konwersja na małe litery
        $normalized = mb_strtolower(trim($name), 'UTF-8');
        
        // Usunięcie prefiksów i sufiksów
        $to_remove = [
            'powiat ',
            'pow. ',
            'pow ',
            ' county',
            'county ',
            'gmina ',
            'gm. '
        ];
        
        foreach ($to_remove as $remove) {
            $normalized = str_replace($remove, '', $normalized);
        }
        
        return trim($normalized);
    }
    
    /**
     * Mapowanie nazw województw między różnymi formatami
     */
    public function get_wojewodztwo_mapping() {
        if (self::$mapping_cache !== null) {
            return self::$mapping_cache;
        }
        
        // Mapowanie nazw polskich na angielskie i odwrotnie
        $mapping = [
            // Polskie -> Angielskie
            'dolnośląskie' => ['lower silesian', 'lower silesian voivodeship', 'dolnośląskie'],
            'kujawsko-pomorskie' => ['kuyavian-pomeranian', 'kuyavian-pomeranian voivodeship', 'kujawsko-pomorskie'],
            'lubelskie' => ['lublin', 'lublin voivodeship', 'lubelskie'],
            'lubuskie' => ['lubusz', 'lubusz voivodeship', 'lubuskie'],
            'łódzkie' => ['łódź', 'łódź voivodeship', 'lodz', 'lodzkie', 'łódzkie'],
            'małopolskie' => ['lesser poland', 'lesser poland voivodeship', 'małopolskie', 'malopolskie'],
            'mazowieckie' => ['masovian', 'masovian voivodeship', 'mazowieckie'],
            'opolskie' => ['opole', 'opole voivodeship', 'opolskie'],
            'podkarpackie' => ['subcarpathian', 'subcarpathian voivodeship', 'podkarpackie'],
            'podlaskie' => ['podlaskie', 'podlaskie voivodeship'],
            'pomorskie' => ['pomeranian', 'pomeranian voivodeship', 'pomorskie'],
            'śląskie' => ['silesian', 'silesian voivodeship', 'śląskie', 'slaskie'],
            'świętokrzyskie' => ['holy cross', 'holy cross voivodeship', 'świętokrzyskie', 'swietokrzyskie'],
            'warmińsko-mazurskie' => ['warmian-masurian', 'warmian-masurian voivodeship', 'warmińsko-mazurskie', 'warminsko-mazurskie'],
            'wielkopolskie' => ['greater poland', 'greater poland voivodeship', 'wielkopolskie'],
            'zachodniopomorskie' => ['west pomeranian', 'west pomeranian voivodeship', 'zachodniopomorskie']
        ];
        
        // Dodaj mapowanie odwrotne (angielskie -> polskie)
        $reverse_mapping = [];
        foreach ($mapping as $polish => $variants) {
            foreach ($variants as $variant) {
                $reverse_mapping[$this->normalize_wojewodztwo($variant)] = $polish;
            }
            // Dodaj też polską nazwę
            $reverse_mapping[$this->normalize_wojewodztwo($polish)] = $polish;
        }
        
        self::$mapping_cache = $reverse_mapping;
        return self::$mapping_cache;
    }
    
    /**
     * Znajdź dopasowanie województwa w bazie danych
     */
    public function find_wojewodztwo_match($selector_name) {
        $normalized_selector = $this->normalize_wojewodztwo($selector_name);
        
        if (empty($normalized_selector)) {
            return [];
        }
        
        $mapping = $this->get_wojewodztwo_mapping();
        
        // Sprawdź bezpośrednie mapowanie
        if (isset($mapping[$normalized_selector])) {
            $canonical_name = $mapping[$normalized_selector];
        } else {
            $canonical_name = $normalized_selector;
        }
        
        // Generuj wszystkie możliwe warianty do wyszukiwania
        $search_variants = [
            $canonical_name,
            $normalized_selector,
            'województwo ' . $canonical_name,
            $canonical_name . ' voivodeship',
            ucfirst($canonical_name),
            ucwords($canonical_name)
        ];
        
        // Dodaj warianty z mapowania
        foreach ($mapping as $variant => $polish) {
            if ($polish === $canonical_name) {
                $search_variants[] = $variant;
                $search_variants[] = 'województwo ' . $variant;
                $search_variants[] = $variant . ' voivodeship';
            }
        }
        
        return array_unique($search_variants);
    }
    
    /**
     * Generuj warunki SQL dla województwa
     */
    public function generate_wojewodztwo_sql_conditions($table, $selector_name) {
        global $wpdb;
        
        $variants = $this->find_wojewodztwo_match($selector_name);
        
        if (empty($variants)) {
            return '';
        }
        
        $conditions = [];
        
        // Kolumny do sprawdzenia
        $columns_to_check = ['wojewodztwa_uslugi', 'region'];
        
        foreach ($columns_to_check as $column) {
            foreach ($variants as $variant) {
                // Dokładne dopasowanie
                $conditions[] = $wpdb->prepare("$table.$column = %s", $variant);
                
                // LIKE dopasowanie
                $conditions[] = $wpdb->prepare("$table.$column LIKE %s", '%' . $wpdb->esc_like($variant) . '%');
            }
        }
        
        // Dodaj warunek dla "Cała Polska" i firm bez określonego województwa
        $conditions[] = "$table.wojewodztwa_uslugi = 'Cała Polska'";
        $conditions[] = "$table.wojewodztwa_uslugi IS NULL";
        $conditions[] = "$table.wojewodztwa_uslugi = ''";
        $conditions[] = "$table.wojewodztwa_uslugi = 'NULL'";

        // Dodaj też sprawdzenie kolumny 'region' dla pustych wartości
        $conditions[] = "$table.region IS NULL";
        $conditions[] = "$table.region = ''";
        $conditions[] = "$table.region = 'NULL'";

        return '(' . implode(' OR ', $conditions) . ')';
    }
    
    /**
     * Generuj warunki SQL dla powiatu
     * WAŻNE: Firmy bez powiatu (tylko województwo) powinny być widoczne zawsze
     */
    public function generate_powiat_sql_conditions($table, $selector_name) {
        global $wpdb;

        if (empty($selector_name)) {
            // Jeśli nie wybrano powiatu, pokaż wszystkie firmy w województwie
            return "($table.powiaty_uslugi IS NULL OR $table.powiaty_uslugi = '' OR $table.powiaty_uslugi = 'NULL')";
        }

        $normalized = $this->normalize_powiat($selector_name);

        $variants = [
            $normalized,
            $selector_name,
            'powiat ' . $normalized,
            ucfirst($normalized),
            ucwords($normalized)
        ];

        $conditions = [];

        // Kolumny do sprawdzenia
        $columns_to_check = ['powiaty_uslugi'];

        foreach ($columns_to_check as $column) {
            foreach ($variants as $variant) {
                // Dokładne dopasowanie
                $conditions[] = $wpdb->prepare("$table.$column = %s", $variant);

                // LIKE dopasowanie (dla powiatów rozdzielonych przecinkami)
                $conditions[] = $wpdb->prepare("$table.$column LIKE %s", '%' . $wpdb->esc_like($variant) . '%');
            }
        }

        // KLUCZOWE: Zawsze dodaj warunki dla firm BEZ POWIATU
        // Te firmy powinny być widoczne przy każdym wyborze powiatu w województwie
        $conditions[] = "$table.powiaty_uslugi IS NULL";
        $conditions[] = "$table.powiaty_uslugi = ''";
        $conditions[] = "$table.powiaty_uslugi = 'NULL'";

        return '(' . implode(' OR ', $conditions) . ')';
    }
    
    /**
     * Pobierz unikalne województwa z bazy danych
     */
    public function get_unique_wojewodztwa_from_db() {
        global $wpdb;
        
        // Sprawdź czy tabela istnieje
        $table = geodir_db_cpt_table('gd_place');
        if (!$table) {
            return [];
        }
        
        // Pobierz unikalne wartości
        $results = $wpdb->get_col("
            SELECT DISTINCT wojewodztwa_uslugi 
            FROM $table 
            WHERE wojewodztwa_uslugi IS NOT NULL 
            AND wojewodztwa_uslugi != '' 
            AND wojewodztwa_uslugi != 'NULL'
            UNION
            SELECT DISTINCT region 
            FROM $table 
            WHERE region IS NOT NULL 
            AND region != '' 
            AND region != 'NULL'
            AND region NOT LIKE '%,%'
        ");
        
        // Normalizuj i usuń duplikaty
        $normalized = [];
        foreach ($results as $result) {
            $norm = $this->normalize_wojewodztwo($result);
            if (!empty($norm)) {
                $normalized[] = $norm;
            }
        }
        
        return array_unique($normalized);
    }
    
    /**
     * Pobierz unikalne powiaty dla województwa z bazy danych
     */
    public function get_unique_powiaty_from_db($wojewodztwo) {
        global $wpdb;
        
        // Sprawdź czy tabela istnieje
        $table = geodir_db_cpt_table('gd_place');
        if (!$table) {
            return [];
        }
        
        // Generuj warunki dla województwa
        $wojewodztwo_conditions = $this->generate_wojewodztwo_sql_conditions($table, $wojewodztwo);
        
        if (empty($wojewodztwo_conditions)) {
            return [];
        }
        
        // Pobierz powiaty dla tego województwa
        $results = $wpdb->get_col("
            SELECT DISTINCT powiaty_uslugi 
            FROM $table 
            WHERE $wojewodztwo_conditions
            AND powiaty_uslugi IS NOT NULL 
            AND powiaty_uslugi != '' 
            AND powiaty_uslugi != 'NULL'
        ");
        
        // Normalizuj i usuń duplikaty
        $normalized = [];
        foreach ($results as $result) {
            // Powiaty mogą być rozdzielone przecinkami
            $powiaty = explode(',', $result);
            foreach ($powiaty as $powiat) {
                $norm = $this->normalize_powiat(trim($powiat));
                if (!empty($norm)) {
                    $normalized[] = $norm;
                }
            }
        }
        
        return array_unique($normalized);
    }
    
    /**
     * Test dopasowywania z szczegółami
     */
    public function test_matching($selector_wojewodztwo, $selector_powiat = '') {
        global $wpdb;

        $table = geodir_db_cpt_table('gd_place');
        if (!$table) {
            return ['error' => 'Tabela nie istnieje'];
        }

        $wojewodztwo_conditions = $this->generate_wojewodztwo_sql_conditions($table, $selector_wojewodztwo);

        $where_clause = "WHERE $wojewodztwo_conditions";

        if (!empty($selector_powiat)) {
            $powiat_conditions = $this->generate_powiat_sql_conditions($table, $selector_powiat);
            $where_clause .= " AND $powiat_conditions";
        }

        $count = $wpdb->get_var("SELECT COUNT(*) FROM $table $where_clause");

        // Dodatkowe testy dla firm bez powiatu
        $no_powiat_count = 0;
        if (!empty($selector_powiat)) {
            $no_powiat_where = "WHERE $wojewodztwo_conditions AND (powiaty_uslugi IS NULL OR powiaty_uslugi = '' OR powiaty_uslugi = 'NULL')";
            $no_powiat_count = $wpdb->get_var("SELECT COUNT(*) FROM $table $no_powiat_where");
        }

        return [
            'count' => $count,
            'no_powiat_count' => $no_powiat_count,
            'sql' => "SELECT COUNT(*) FROM $table $where_clause",
            'wojewodztwo_variants' => $this->find_wojewodztwo_match($selector_wojewodztwo),
            'includes_no_powiat' => !empty($selector_powiat) ? "Tak ($no_powiat_count firm bez powiatu)" : 'N/A'
        ];
    }
}

// Inicjalizacja
Lupik_Region_Mapping::get_instance();
