<?php
/**
 * System wyboru domyślnego regionu użytkownika
 * 
 * @package BuddyBoss Child
 * <AUTHOR>
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Sprawdź czy system jest włączony
if (defined('LUPIK_REGION_SELECTOR_DISABLED') && LUPIK_REGION_SELECTOR_DISABLED) {
    return;
}

/**
 * Główna klasa systemu wyboru regionu
 */
class Lupik_Region_Selector {
    
    /**
     * Nazwa cookie dla zapisanego regionu
     */
    const COOKIE_NAME = 'lupik_user_region';
    
    /**
     * Czas życia cookie (30 dni)
     */
    const COOKIE_LIFETIME = 30 * DAY_IN_SECONDS;
    
    /**
     * Instancja klasy
     */
    private static $instance = null;
    
    /**
     * Dane regionów z JSON
     */
    private $regions_data = null;
    
    /**
     * Singleton pattern
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Konstruktor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Inicjalizacja hooków WordPress
     */
    private function init_hooks() {
        // Ładowanie skryptów i stylów
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        
        // AJAX endpoints
        add_action('wp_ajax_save_user_region', array($this, 'ajax_save_region'));
        add_action('wp_ajax_nopriv_save_user_region', array($this, 'ajax_save_region'));
        add_action('wp_ajax_get_user_location', array($this, 'ajax_get_location'));
        add_action('wp_ajax_nopriv_get_user_location', array($this, 'ajax_get_location'));
        
        // Wyświetlanie modala na stronie głównej
        add_action('wp_footer', array($this, 'render_region_modal'));
        
        // Dodanie przycisku zmiany regionu do menu/nagłówka
        add_action('wp_head', array($this, 'add_region_change_button'));
    }
    
    /**
     * Ładowanie skryptów i stylów
     */
    public function enqueue_scripts() {
        // CSS dla modala i przycisku
        wp_enqueue_style(
            'lupik-region-selector',
            get_stylesheet_directory_uri() . '/assets/css/region-selector.css',
            array(),
            '1.0.0'
        );
        
        // JavaScript dla funkcjonalności
        wp_enqueue_script(
            'lupik-region-selector',
            get_stylesheet_directory_uri() . '/assets/js/region-selector.js',
            array('jquery'),
            '1.0.0',
            true
        );
        
        // Przekazanie danych do JS
        wp_localize_script('lupik-region-selector', 'LupikRegion', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('lupik_region_nonce'),
            'regions_url' => get_stylesheet_directory_uri() . '/assets/json/powiaty.json',
            'current_region' => $this->get_user_region(),
            'is_first_visit' => $this->is_first_visit(),
            'is_front_page' => is_front_page(),
            'strings' => array(
                'modal_title' => __('Wybierz swój region', 'buddyboss-theme'),
                'modal_subtitle' => __('Aby otrzymywać spersonalizowane treści i oferty z Twojej okolicy', 'buddyboss-theme'),
                'auto_detect' => __('Wykryj automatycznie', 'buddyboss-theme'),
                'manual_select' => __('Wybierz ręcznie', 'buddyboss-theme'),
                'wojewodztwo' => __('Województwo', 'buddyboss-theme'),
                'powiat' => __('Powiat', 'buddyboss-theme'),
                'save_button' => __('Zapisz wybór', 'buddyboss-theme'),
                'skip_button' => __('Pomiń (pokaż wszystko)', 'buddyboss-theme'),
                'change_region' => __('Zmień region', 'buddyboss-theme'),
                'detecting' => __('Wykrywanie lokalizacji...', 'buddyboss-theme'),
                'error_location' => __('Nie udało się wykryć lokalizacji', 'buddyboss-theme'),
                'success_saved' => __('Region został zapisany', 'buddyboss-theme')
            )
        ));
    }
    
    /**
     * Sprawdza czy to pierwsza wizyta użytkownika
     */
    public function is_first_visit() {
        return !isset($_COOKIE[self::COOKIE_NAME]);
    }
    
    /**
     * Pobiera zapisany region użytkownika
     */
    public function get_user_region() {
        if (isset($_COOKIE[self::COOKIE_NAME])) {
            return json_decode(stripslashes($_COOKIE[self::COOKIE_NAME]), true);
        }
        return null;
    }
    
    /**
     * Zapisuje region użytkownika
     */
    public function save_user_region($wojewodztwo, $powiat = '') {
        $region_data = array(
            'wojewodztwo' => sanitize_text_field($wojewodztwo),
            'powiat' => sanitize_text_field($powiat),
            'timestamp' => current_time('timestamp')
        );
        
        $cookie_value = json_encode($region_data);
        setcookie(
            self::COOKIE_NAME,
            $cookie_value,
            time() + self::COOKIE_LIFETIME,
            COOKIEPATH,
            COOKIE_DOMAIN,
            is_ssl(),
            true
        );
        
        return true;
    }
    
    /**
     * AJAX handler dla zapisywania regionu
     */
    public function ajax_save_region() {
        // Sprawdzenie nonce
        if (!wp_verify_nonce($_POST['nonce'], 'lupik_region_nonce')) {
            wp_die('Security check failed');
        }
        
        $wojewodztwo = sanitize_text_field($_POST['wojewodztwo']);
        $powiat = sanitize_text_field($_POST['powiat']);
        
        if (empty($wojewodztwo)) {
            wp_send_json_error('Województwo jest wymagane');
        }
        
        $this->save_user_region($wojewodztwo, $powiat);
        
        wp_send_json_success(array(
            'message' => 'Region został zapisany',
            'region' => array(
                'wojewodztwo' => $wojewodztwo,
                'powiat' => $powiat
            )
        ));
    }
    
    /**
     * AJAX handler dla geolokalizacji
     */
    public function ajax_get_location() {
        // Sprawdzenie nonce
        if (!wp_verify_nonce($_POST['nonce'], 'lupik_region_nonce')) {
            wp_die('Security check failed');
        }
        
        $lat = floatval($_POST['lat']);
        $lng = floatval($_POST['lng']);
        
        if (empty($lat) || empty($lng)) {
            wp_send_json_error('Nieprawidłowe współrzędne');
        }
        
        // Wywołanie API do reverse geocoding
        $region = $this->reverse_geocode($lat, $lng);
        
        if ($region) {
            wp_send_json_success($region);
        } else {
            wp_send_json_error('Nie udało się określić regionu');
        }
    }
    
    /**
     * Reverse geocoding - określenie regionu na podstawie współrzędnych
     */
    private function reverse_geocode($lat, $lng) {
        // Używamy Nominatim API (OpenStreetMap)
        $url = "https://nominatim.openstreetmap.org/reverse?format=json&lat={$lat}&lon={$lng}&zoom=10&addressdetails=1";
        
        $response = wp_remote_get($url, array(
            'timeout' => 10,
            'headers' => array(
                'User-Agent' => 'Lupik.pl Region Selector'
            )
        ));
        
        if (is_wp_error($response)) {
            return false;
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if (!$data || !isset($data['address'])) {
            return false;
        }
        
        $address = $data['address'];
        
        // Mapowanie nazw województw z API na nasze nazwy
        $wojewodztwo = $this->map_wojewodztwo($address);
        $powiat = $this->map_powiat($address);
        
        if ($wojewodztwo) {
            return array(
                'wojewodztwo' => $wojewodztwo,
                'powiat' => $powiat
            );
        }
        
        return false;
    }
    
    /**
     * Mapowanie nazwy województwa z API
     */
    private function map_wojewodztwo($address) {
        $state = isset($address['state']) ? $address['state'] : '';
        
        // Mapowanie nazw województw
        $mapping = array(
            'Lower Silesian Voivodeship' => 'dolnośląskie',
            'Kuyavian-Pomeranian Voivodeship' => 'kujawsko-pomorskie',
            'Lublin Voivodeship' => 'lubelskie',
            'Lubusz Voivodeship' => 'lubuskie',
            'Łódź Voivodeship' => 'łódzkie',
            'Lesser Poland Voivodeship' => 'małopolskie',
            'Masovian Voivodeship' => 'mazowieckie',
            'Opole Voivodeship' => 'opolskie',
            'Subcarpathian Voivodeship' => 'podkarpackie',
            'Podlaskie Voivodeship' => 'podlaskie',
            'Pomeranian Voivodeship' => 'pomorskie',
            'Silesian Voivodeship' => 'śląskie',
            'Holy Cross Voivodeship' => 'świętokrzyskie',
            'Warmian-Masurian Voivodeship' => 'warmińsko-mazurskie',
            'Greater Poland Voivodeship' => 'wielkopolskie',
            'West Pomeranian Voivodeship' => 'zachodniopomorskie'
        );
        
        return isset($mapping[$state]) ? $mapping[$state] : null;
    }
    
    /**
     * Mapowanie nazwy powiatu z API
     */
    private function map_powiat($address) {
        // Próbujemy różne pola dla powiatu
        $county_fields = array('county', 'state_district', 'municipality');
        
        foreach ($county_fields as $field) {
            if (isset($address[$field])) {
                $county = $address[$field];
                // Usuwamy "powiat" z nazwy jeśli występuje
                $county = str_replace(' County', '', $county);
                $county = str_replace('powiat ', '', $county);
                return $county;
            }
        }
        
        return '';
    }
    
    /**
     * Renderowanie modala wyboru regionu
     */
    public function render_region_modal() {
        // Modal i przycisk renderujemy zawsze, ale auto-show tylko na stronie głównej dla nowych użytkowników
        include get_stylesheet_directory() . '/templates/region-selector-modal.php';
    }
    
    /**
     * Dodanie przycisku zmiany regionu
     */
    public function add_region_change_button() {
        // Dodajemy CSS dla przycisku w nagłówku
        echo '<style>
        .lupik-region-button {
            position: fixed;
            top: 100px;
            right: 20px;
            z-index: 9999;
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .lupik-region-button:hover {
            background: #005a87;
        }
        </style>';
    }
}

// Inicjalizacja systemu
Lupik_Region_Selector::get_instance();
