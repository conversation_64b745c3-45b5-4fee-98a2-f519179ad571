<?php
/**
 * Integracja systemu regionów z menu głównym
 * 
 * @package BuddyBoss Child
 * <AUTHOR>
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Sprawdź czy system jest włączony
if (defined('LUPIK_REGION_SELECTOR_DISABLED') && LUPIK_REGION_SELECTOR_DISABLED) {
    return;
}

/**
 * Klasa integracji regionów z menu
 */
class Lupik_Region_Menu_Integration {
    
    /**
     * Instancja klasy
     */
    private static $instance = null;
    
    /**
     * Singleton pattern
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Konstruktor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Inicjalizacja hooków WordPress
     */
    private function init_hooks() {
        // Dodanie elementu regionu do menu głównego
        add_filter('wp_nav_menu_items', array($this, 'add_region_to_menu'), 10, 2);
        
        // Dodanie CSS dla elementu menu
        add_action('wp_head', array($this, 'add_menu_styles'));
        
        // Dodanie JavaScript dla menu
        add_action('wp_footer', array($this, 'add_menu_scripts'));

        // Dodanie klasy do body
        add_filter('body_class', array($this, 'add_body_class'));
    }
    
    /**
     * Dodanie elementu regionu do menu głównego
     */
    public function add_region_to_menu($items, $args) {
        // Dodaj tylko do głównego menu dla zalogowanych użytkowników
        if ($args->theme_location !== 'header-menu') {
            return $items;
        }
        
        $region_selector = Lupik_Region_Selector::get_instance();
        $current_region = $region_selector->get_user_region();
        
        // Określ tekst regionu
        $region_text = 'Cała Polska';
        $region_class = 'lupik-region-all';
        
        if ($current_region && $current_region['wojewodztwo'] !== 'Cała Polska') {
            $region_text = $current_region['wojewodztwo'];
            $region_class = 'lupik-region-specific';
            
            if (!empty($current_region['powiat'])) {
                $region_text .= ', ' . $current_region['powiat'];
            }
        }
        
        // Utwórz element menu
        $region_menu_item = '
        <li class="menu-item lupik-region-menu-item ' . $region_class . '">
            <a href="#" class="lupik-region-menu-link" data-action="open-region-modal">
                <span class="lupik-region-icon">📍</span>
                <span class="lupik-region-text">' . esc_html($region_text) . '</span>
                <span class="lupik-region-arrow">▼</span>
            </a>
            <div class="lupik-region-dropdown">
                <div class="lupik-region-dropdown-content">
                    <div class="lupik-region-current">
                        <strong>' . __('Aktualny region:', 'buddyboss-theme') . '</strong>
                        <span class="lupik-current-region-display">' . esc_html($region_text) . '</span>
                    </div>
                    <div class="lupik-region-actions">
                        <button type="button" class="lupik-btn lupik-btn-primary lupik-change-region-menu">
                            ' . __('Zmień region', 'buddyboss-theme') . '
                        </button>
                        <button type="button" class="lupik-btn lupik-btn-secondary lupik-detect-location-menu">
                            ' . __('Wykryj automatycznie', 'buddyboss-theme') . '
                        </button>
                    </div>
                    <div class="lupik-region-quick-select">
                        <div class="lupik-quick-title">' . __('Popularne regiony:', 'buddyboss-theme') . '</div>
                        <div class="lupik-quick-regions">
                            <button type="button" class="lupik-quick-region" data-region="Cała Polska">Cała Polska</button>
                            <button type="button" class="lupik-quick-region" data-region="mazowieckie">Mazowieckie</button>
                            <button type="button" class="lupik-quick-region" data-region="śląskie">Śląskie</button>
                            <button type="button" class="lupik-quick-region" data-region="wielkopolskie">Wielkopolskie</button>
                            <button type="button" class="lupik-quick-region" data-region="małopolskie">Małopolskie</button>
                            <button type="button" class="lupik-quick-region" data-region="dolnośląskie">Dolnośląskie</button>
                        </div>
                    </div>
                </div>
            </div>
        </li>';
        
        // Dodaj element na końcu menu
        $items .= $region_menu_item;
        
        return $items;
    }

    /**
     * Dodanie klasy do body gdy menu region jest aktywne
     */
    public function add_body_class($classes) {
        $classes[] = 'has-region-menu';
        return $classes;
    }
    
    /**
     * Dodanie stylów CSS dla elementu menu
     */
    public function add_menu_styles() {
        ?>
        <style>
        /* Region Menu Item */
        .lupik-region-menu-item {
            position: relative;
        }
        
        .lupik-region-menu-link {
            display: flex !important;
            align-items: center;
            gap: 8px;
            padding: 10px 15px !important;
            color: #333 !important;
            text-decoration: none !important;
            border-radius: 6px;
            transition: all 0.3s ease;
            background: rgba(0, 124, 186, 0.1) !important;
            border: 1px solid rgba(0, 124, 186, 0.2) !important;
        }
        
        .lupik-region-menu-link:hover {
            background: rgba(0, 124, 186, 0.2) !important;
            color: #007cba !important;
        }
        
        .lupik-region-icon {
            font-size: 16px;
            line-height: 1;
        }
        
        .lupik-region-text {
            font-size: 14px;
            font-weight: 500;
            max-width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .lupik-region-arrow {
            font-size: 12px;
            transition: transform 0.3s ease;
        }
        
        .lupik-region-menu-item.active .lupik-region-arrow {
            transform: rotate(180deg);
        }
        
        /* Dropdown */
        .lupik-region-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            min-width: 300px;
            background: #ffffff;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
        }
        
        .lupik-region-menu-item.active .lupik-region-dropdown {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .lupik-region-dropdown-content {
            padding: 20px;
        }
        
        .lupik-region-current {
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .lupik-region-current strong {
            display: block;
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 5px;
        }
        
        .lupik-current-region-display {
            font-size: 14px;
            color: #495057;
            font-weight: 600;
        }
        
        .lupik-region-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .lupik-region-actions .lupik-btn {
            flex: 1;
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .lupik-btn-primary {
            background: #007cba;
            color: #ffffff;
        }
        
        .lupik-btn-primary:hover {
            background: #005a87;
        }
        
        .lupik-btn-secondary {
            background: #6c757d;
            color: #ffffff;
        }
        
        .lupik-btn-secondary:hover {
            background: #5a6268;
        }
        
        .lupik-region-quick-select {
            border-top: 1px solid #e9ecef;
            padding-top: 15px;
        }
        
        .lupik-quick-title {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 10px;
            font-weight: 500;
        }
        
        .lupik-quick-regions {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }
        
        .lupik-quick-region {
            padding: 4px 8px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-size: 11px;
            color: #495057;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .lupik-quick-region:hover {
            background: #007cba;
            color: #ffffff;
            border-color: #007cba;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .lupik-region-dropdown {
                right: -20px;
                left: -20px;
                min-width: auto;
            }
            
            .lupik-region-text {
                max-width: 100px;
            }
            
            .lupik-region-actions {
                flex-direction: column;
            }
        }
        
        /* Ukryj domyślny floating button gdy menu jest aktywne */
        .lupik-region-menu-item.active ~ .lupik-region-button {
            opacity: 0.5;
        }
        </style>
        <?php
    }
    
    /**
     * Dodanie JavaScript dla funkcjonalności menu
     */
    public function add_menu_scripts() {
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Toggle dropdown
            $('.lupik-region-menu-link').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                var $menuItem = $(this).closest('.lupik-region-menu-item');
                var isActive = $menuItem.hasClass('active');
                
                // Zamknij wszystkie inne dropdown'y
                $('.lupik-region-menu-item').removeClass('active');
                
                // Toggle aktualny
                if (!isActive) {
                    $menuItem.addClass('active');
                }
            });
            
            // Zamknij dropdown po kliknięciu poza nim
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.lupik-region-menu-item').length) {
                    $('.lupik-region-menu-item').removeClass('active');
                }
            });
            
            // Otwórz modal
            $('.lupik-change-region-menu').on('click', function(e) {
                e.preventDefault();
                $('.lupik-region-menu-item').removeClass('active');
                $('#lupik-region-modal').fadeIn(300);
            });
            
            // Automatyczne wykrywanie
            $('.lupik-detect-location-menu').on('click', function(e) {
                e.preventDefault();
                var $btn = $(this);
                
                if (navigator.geolocation) {
                    $btn.prop('disabled', true).text('Wykrywanie...');
                    
                    navigator.geolocation.getCurrentPosition(function(position) {
                        if (typeof LupikRegion !== 'undefined') {
                            $.ajax({
                                url: LupikRegion.ajax_url,
                                type: 'POST',
                                data: {
                                    action: 'get_user_location',
                                    nonce: LupikRegion.nonce,
                                    lat: position.coords.latitude,
                                    lng: position.coords.longitude
                                },
                                success: function(response) {
                                    if (response.success) {
                                        // Zapisz region
                                        $.ajax({
                                            url: LupikRegion.ajax_url,
                                            type: 'POST',
                                            data: {
                                                action: 'save_user_region',
                                                nonce: LupikRegion.nonce,
                                                wojewodztwo: response.data.wojewodztwo,
                                                powiat: response.data.powiat || ''
                                            },
                                            success: function(saveResponse) {
                                                if (saveResponse.success) {
                                                    location.reload();
                                                }
                                            }
                                        });
                                    }
                                },
                                complete: function() {
                                    $btn.prop('disabled', false).text('Wykryj automatycznie');
                                }
                            });
                        }
                    }, function() {
                        alert('Nie udało się wykryć lokalizacji');
                        $btn.prop('disabled', false).text('Wykryj automatycznie');
                    });
                }
            });
            
            // Szybki wybór regionu
            $('.lupik-quick-region').on('click', function(e) {
                e.preventDefault();
                var region = $(this).data('region');
                
                if (typeof LupikRegion !== 'undefined') {
                    $.ajax({
                        url: LupikRegion.ajax_url,
                        type: 'POST',
                        data: {
                            action: 'save_user_region',
                            nonce: LupikRegion.nonce,
                            wojewodztwo: region,
                            powiat: ''
                        },
                        success: function(response) {
                            if (response.success) {
                                location.reload();
                            }
                        }
                    });
                }
            });
            
            // Aktualizuj menu po zmianie regionu
            $(document).on('lupik_region_changed', function(e, region) {
                var newText = region.wojewodztwo;
                if (region.powiat) {
                    newText += ', ' + region.powiat;
                }
                
                $('.lupik-region-text').text(newText);
                $('.lupik-current-region-display').text(newText);
                
                // Zmień klasę CSS
                var $menuItem = $('.lupik-region-menu-item');
                if (region.wojewodztwo === 'Cała Polska') {
                    $menuItem.removeClass('lupik-region-specific').addClass('lupik-region-all');
                } else {
                    $menuItem.removeClass('lupik-region-all').addClass('lupik-region-specific');
                }
            });
        });
        </script>
        <?php
    }
}

// Inicjalizacja integracji z menu
Lupik_Region_Menu_Integration::get_instance();
