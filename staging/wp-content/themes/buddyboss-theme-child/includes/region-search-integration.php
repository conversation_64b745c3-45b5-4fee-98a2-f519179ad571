<?php
/**
 * Integracja systemu regionów z wyszukiwaniem GeoDirectory
 * 
 * @package BuddyBoss Child
 * <AUTHOR>
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Klasa integracji regionów z wyszukiwaniem
 */
class Lupik_Region_Search_Integration {
    
    /**
     * Instancja klasy
     */
    private static $instance = null;
    
    /**
     * Singleton pattern
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Konstruktor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Inicjalizacja hooków WordPress
     */
    private function init_hooks() {
        // Modyfikacja query dla wyszukiwania na podstawie regionu
        add_action('pre_get_posts', array($this, 'modify_search_query'), 20);
        
        // Dodanie ukrytych pól do formularza wyszukiwania
        add_action('geodir_before_search_form', array($this, 'add_region_fields_to_search'));
        
        // Modyfikacja WHERE clause dla zapytań GeoDirectory
        add_filter('geodir_posts_where', array($this, 'filter_posts_by_region'), 10, 2);
        
        // Dodanie JavaScript dla automatycznego ustawiania regionu w wyszukiwaniu
        add_action('wp_footer', array($this, 'add_search_integration_script'));
        
        // Modyfikacja shortcode'ów GeoDirectory
        add_filter('geodir_widget_listings_output', array($this, 'filter_listings_by_region'), 10, 2);
        
        // Integracja z mapą
        add_filter('geodir_map_default_location', array($this, 'set_default_map_location'));
        
        // Filtrowanie RSS feeds na podstawie regionu
        add_filter('rss_aggregator_feed_items_query', array($this, 'filter_rss_by_region'), 10, 2);
    }
    
    /**
     * Pobiera aktualny region użytkownika
     */
    private function get_current_user_region() {
        $region_selector = Lupik_Region_Selector::get_instance();
        return $region_selector->get_user_region();
    }
    
    /**
     * Modyfikacja głównego query wyszukiwania
     */
    public function modify_search_query($query) {
        // Tylko dla głównych zapytań GeoDirectory
        if (!$query->is_main_query() || is_admin()) {
            return;
        }

        // Sprawdź czy to strona wyszukiwania GeoDirectory lub GD post type
        $is_gd_search = isset($_GET['geodir_search']) || geodir_is_page('search');
        $is_gd_post_type = isset($query->query_vars['post_type']) && geodir_is_gd_post_type($query->query_vars['post_type']);

        if (!$is_gd_search && !$is_gd_post_type) {
            return;
        }
        
        $region = $this->get_current_user_region();
        if (!$region || $region['wojewodztwo'] === 'Cała Polska') {
            return;
        }
        
        // Użyj inteligentnego mapowania regionów
        $region_mapping = Lupik_Region_Mapping::get_instance();

        // Dodaj hook do modyfikacji WHERE clause zamiast meta_query
        add_filter('posts_where', array($this, 'modify_posts_where_for_region'), 10, 2);
    }
    
    /**
     * Dodanie ukrytych pól regionu do formularza wyszukiwania
     */
    public function add_region_fields_to_search($instance) {
        $region = $this->get_current_user_region();
        
        if ($region && $region['wojewodztwo'] !== 'Cała Polska') {
            echo '<input type="hidden" name="user_region_wojewodztwo" value="' . esc_attr($region['wojewodztwo']) . '" />';
            
            if (!empty($region['powiat'])) {
                echo '<input type="hidden" name="user_region_powiat" value="' . esc_attr($region['powiat']) . '" />';
            }
        }
    }
    
    /**
     * Filtrowanie postów na podstawie regionu
     */
    public function filter_posts_by_region($where, $query) {
        global $wpdb;

        // Zabezpieczenie przed błędami
        try {
            // Sprawdź czy mamy region użytkownika
            $region = $this->get_current_user_region();
            if (!$region || $region['wojewodztwo'] === 'Cała Polska') {
                return $where;
            }

            // Pobierz post type z query
            $post_type = '';
            if (is_object($query) && isset($query->query_vars['post_type'])) {
                $post_type = $query->query_vars['post_type'];
            } elseif (is_string($query)) {
                $post_type = $query;
            }

            // Jeśli nie ma post_type lub nie jest to GeoDirectory post type, zwróć bez zmian
            if (empty($post_type) || !function_exists('geodir_is_gd_post_type') || !geodir_is_gd_post_type($post_type)) {
                return $where;
            }

            // Sprawdź czy funkcja geodir_db_cpt_table istnieje
            if (!function_exists('geodir_db_cpt_table')) {
                return $where;
            }

            // Pobierz tabelę dla danego post type
            $table = geodir_db_cpt_table($post_type);
            if (!$table) {
                return $where;
            }
        } catch (Exception $e) {
            // W przypadku błędu, zwróć oryginalny WHERE bez modyfikacji
            error_log('Lupik Region Filter Error: ' . $e->getMessage());
            return $where;
        }
        
        // Użyj inteligentnego mapowania regionów
        $region_mapping = Lupik_Region_Mapping::get_instance();

        // Generuj warunki dla województwa
        $wojewodztwo_condition = $region_mapping->generate_wojewodztwo_sql_conditions($table, $region['wojewodztwo']);
        if (!empty($wojewodztwo_condition)) {
            $where .= " AND $wojewodztwo_condition";
        }

        // Jeśli jest wybrany powiat, dodaj warunki dla powiatu
        if (!empty($region['powiat'])) {
            $powiat_condition = $region_mapping->generate_powiat_sql_conditions($table, $region['powiat']);
            if (!empty($powiat_condition)) {
                $where .= " AND $powiat_condition";
            }
        }
        
        return $where;
    }

    /**
     * Modyfikacja WHERE clause dla głównych zapytań
     */
    public function modify_posts_where_for_region($where, $query) {
        // Tylko dla głównych zapytań
        if (!$query->is_main_query() || is_admin()) {
            return $where;
        }

        // Sprawdź czy to zapytanie GeoDirectory
        $post_type = $query->get('post_type');
        if (empty($post_type) || !geodir_is_gd_post_type($post_type)) {
            return $where;
        }

        // Pobierz region użytkownika
        $region = $this->get_current_user_region();
        if (!$region || $region['wojewodztwo'] === 'Cała Polska') {
            return $where;
        }

        global $wpdb;

        // Pobierz tabelę GeoDirectory
        $table = geodir_db_cpt_table($post_type);
        if (!$table) {
            return $where;
        }

        try {
            $region_mapping = Lupik_Region_Mapping::get_instance();

            // Generuj warunki dla województwa
            $wojewodztwo_condition = $region_mapping->generate_wojewodztwo_sql_conditions($table, $region['wojewodztwo']);
            if (!empty($wojewodztwo_condition)) {
                $where .= " AND $wojewodztwo_condition";
            }

            // Jeśli jest wybrany powiat, dodaj warunki dla powiatu
            if (!empty($region['powiat'])) {
                $powiat_condition = $region_mapping->generate_powiat_sql_conditions($table, $region['powiat']);
                if (!empty($powiat_condition)) {
                    $where .= " AND $powiat_condition";
                }
            }

        } catch (Exception $e) {
            error_log('Lupik Region Posts Where Error: ' . $e->getMessage());
        }

        return $where;
    }
    
    /**
     * Filtrowanie widget'ów listings na podstawie regionu
     */
    public function filter_listings_by_region($output, $args) {
        $region = $this->get_current_user_region();
        
        if (!$region || $region['wojewodztwo'] === 'Cała Polska') {
            return $output;
        }
        
        // Dodaj meta_query do argumentów widget'a
        if (!isset($args['meta_query'])) {
            $args['meta_query'] = array();
        }
        
        $args['meta_query'][] = array(
            'key' => 'wojewodztwa_uslugi',
            'value' => $region['wojewodztwo'],
            'compare' => 'LIKE'
        );
        
        if (!empty($region['powiat'])) {
            $args['meta_query'][] = array(
                'key' => 'powiaty_uslugi',
                'value' => $region['powiat'],
                'compare' => 'LIKE'
            );
        }
        
        return $output;
    }
    
    /**
     * Ustawienie domyślnej lokalizacji mapy na podstawie regionu
     */
    public function set_default_map_location($location) {
        $region = $this->get_current_user_region();
        
        if (!$region || $region['wojewodztwo'] === 'Cała Polska') {
            return $location;
        }
        
        // Mapowanie województw na współrzędne (przybliżone centra)
        $wojewodztwa_coords = array(
            'dolnośląskie' => array('lat' => 51.1079, 'lng' => 17.0385),
            'kujawsko-pomorskie' => array('lat' => 53.0138, 'lng' => 18.5984),
            'lubelskie' => array('lat' => 51.2465, 'lng' => 22.5684),
            'lubuskie' => array('lat' => 51.9356, 'lng' => 15.5062),
            'łódzkie' => array('lat' => 51.7592, 'lng' => 19.4560),
            'małopolskie' => array('lat' => 49.8419, 'lng' => 20.0117),
            'mazowieckie' => array('lat' => 52.2297, 'lng' => 21.0122),
            'opolskie' => array('lat' => 50.6751, 'lng' => 17.9213),
            'podkarpackie' => array('lat' => 49.9348, 'lng' => 22.0677),
            'podlaskie' => array('lat' => 53.1325, 'lng' => 23.1688),
            'pomorskie' => array('lat' => 54.3520, 'lng' => 18.6466),
            'śląskie' => array('lat' => 50.2649, 'lng' => 19.0238),
            'świętokrzyskie' => array('lat' => 50.8661, 'lng' => 20.6286),
            'warmińsko-mazurskie' => array('lat' => 53.7784, 'lng' => 20.4801),
            'wielkopolskie' => array('lat' => 52.4064, 'lng' => 16.9252),
            'zachodniopomorskie' => array('lat' => 53.4285, 'lng' => 14.5528)
        );
        
        if (isset($wojewodztwa_coords[$region['wojewodztwo']])) {
            $coords = $wojewodztwa_coords[$region['wojewodztwo']];
            $location = array(
                'latitude' => $coords['lat'],
                'longitude' => $coords['lng'],
                'zoom' => !empty($region['powiat']) ? 10 : 8
            );
        }
        
        return $location;
    }
    
    /**
     * Filtrowanie RSS feeds na podstawie regionu
     */
    public function filter_rss_by_region($query_args, $feed_id) {
        $region = $this->get_current_user_region();
        
        if (!$region || $region['wojewodztwo'] === 'Cała Polska') {
            return $query_args;
        }
        
        // Dodaj meta_query dla RSS items
        if (!isset($query_args['meta_query'])) {
            $query_args['meta_query'] = array();
        }
        
        $query_args['meta_query'][] = array(
            'relation' => 'OR',
            array(
                'key' => 'rss_region_wojewodztwo',
                'value' => $region['wojewodztwo'],
                'compare' => '='
            ),
            array(
                'key' => 'rss_region_wojewodztwo',
                'value' => 'Cała Polska',
                'compare' => '='
            ),
            array(
                'key' => 'rss_region_wojewodztwo',
                'compare' => 'NOT EXISTS'
            )
        );
        
        return $query_args;
    }
    
    /**
     * Dodanie JavaScript dla integracji z wyszukiwaniem
     */
    public function add_search_integration_script() {
        $region = $this->get_current_user_region();
        
        if (!$region) {
            return;
        }
        
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Automatyczne ustawienie regionu w formularzach wyszukiwania
            function setRegionInSearchForms() {
                var region = <?php echo json_encode($region); ?>;
                
                if (region && region.wojewodztwo !== 'Cała Polska') {
                    // Ustaw województwo w select'ach wyszukiwania
                    $('select[name$="wojewodztwa_uslugi[]"]').each(function() {
                        var $select = $(this);
                        if ($select.find('option[value="' + region.wojewodztwo + '"]').length) {
                            $select.val([region.wojewodztwo]).trigger('change');
                        }
                    });
                    
                    // Jeśli jest powiat, ustaw go po chwili (po załadowaniu opcji)
                    if (region.powiat) {
                        setTimeout(function() {
                            $('select[name$="powiaty_uslugi[]"]').each(function() {
                                var $select = $(this);
                                if ($select.find('option[value="' + region.powiat + '"]').length) {
                                    $select.val([region.powiat]).trigger('change');
                                }
                            });
                        }, 500);
                    }
                }
            }
            
            // Uruchom przy załadowaniu strony
            setRegionInSearchForms();
            
            // Uruchom ponownie po AJAX reload'ach GeoDirectory
            $(document).on('geodir_setup_search_form', setRegionInSearchForms);
            
            // Obsługa zmiany regionu przez użytkownika
            $(document).on('lupik_region_changed', function(e, newRegion) {
                setRegionInSearchForms();
                
                // Opcjonalnie: automatyczne odświeżenie wyników wyszukiwania
                if ($('.geodir_submit_search').length) {
                    setTimeout(function() {
                        $('.geodir_submit_search').first().trigger('click');
                    }, 1000);
                }
            });
        });
        </script>
        <?php
    }
}

// Inicjalizacja integracji
Lupik_Region_Search_Integration::get_instance();
