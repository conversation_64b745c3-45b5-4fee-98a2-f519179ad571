<?php
/**
 * Widget i shortcode dla zmiany regionu
 * 
 * @package BuddyBoss Child
 * <AUTHOR>
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Widget wyboru regionu
 */
class Lupik_Region_Widget extends WP_Widget {
    
    /**
     * Konstruktor widget'a
     */
    public function __construct() {
        parent::__construct(
            'lupik_region_widget',
            __('Wybór Regionu Lupik', 'buddyboss-theme'),
            array(
                'description' => __('Widget umożliwiający użytkownikom zmianę regionu', 'buddyboss-theme'),
                'classname' => 'lupik-region-widget'
            )
        );
    }
    
    /**
     * Wyświetlanie widget'a
     */
    public function widget($args, $instance) {
        $title = !empty($instance['title']) ? $instance['title'] : __('Twój region', 'buddyboss-theme');
        $show_current = isset($instance['show_current']) ? $instance['show_current'] : true;
        $style = !empty($instance['style']) ? $instance['style'] : 'compact';
        
        echo $args['before_widget'];
        
        if ($title) {
            echo $args['before_title'] . apply_filters('widget_title', $title) . $args['after_title'];
        }
        
        $this->render_region_selector($style, $show_current);
        
        echo $args['after_widget'];
    }
    
    /**
     * Formularz konfiguracji widget'a
     */
    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : __('Twój region', 'buddyboss-theme');
        $show_current = isset($instance['show_current']) ? $instance['show_current'] : true;
        $style = !empty($instance['style']) ? $instance['style'] : 'compact';
        ?>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('title')); ?>"><?php _e('Tytuł:', 'buddyboss-theme'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('title')); ?>" name="<?php echo esc_attr($this->get_field_name('title')); ?>" type="text" value="<?php echo esc_attr($title); ?>">
        </p>
        
        <p>
            <input class="checkbox" type="checkbox" <?php checked($show_current); ?> id="<?php echo esc_attr($this->get_field_id('show_current')); ?>" name="<?php echo esc_attr($this->get_field_name('show_current')); ?>" />
            <label for="<?php echo esc_attr($this->get_field_id('show_current')); ?>"><?php _e('Pokaż aktualny region', 'buddyboss-theme'); ?></label>
        </p>
        
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('style')); ?>"><?php _e('Styl:', 'buddyboss-theme'); ?></label>
            <select class="widefat" id="<?php echo esc_attr($this->get_field_id('style')); ?>" name="<?php echo esc_attr($this->get_field_name('style')); ?>">
                <option value="compact" <?php selected($style, 'compact'); ?>><?php _e('Kompaktowy', 'buddyboss-theme'); ?></option>
                <option value="full" <?php selected($style, 'full'); ?>><?php _e('Pełny', 'buddyboss-theme'); ?></option>
                <option value="button" <?php selected($style, 'button'); ?>><?php _e('Tylko przycisk', 'buddyboss-theme'); ?></option>
            </select>
        </p>
        <?php
    }
    
    /**
     * Aktualizacja ustawień widget'a
     */
    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['title'] = (!empty($new_instance['title'])) ? sanitize_text_field($new_instance['title']) : '';
        $instance['show_current'] = !empty($new_instance['show_current']);
        $instance['style'] = (!empty($new_instance['style'])) ? sanitize_text_field($new_instance['style']) : 'compact';
        
        return $instance;
    }
    
    /**
     * Renderowanie selektora regionu
     */
    private function render_region_selector($style = 'compact', $show_current = true) {
        $region_selector = Lupik_Region_Selector::get_instance();
        $current_region = $region_selector->get_user_region();
        
        $region_text = 'Cała Polska';
        if ($current_region && $current_region['wojewodztwo'] !== 'Cała Polska') {
            $region_text = $current_region['wojewodztwo'];
            if (!empty($current_region['powiat'])) {
                $region_text .= ', ' . $current_region['powiat'];
            }
        }
        
        ?>
        <div class="lupik-region-widget-container lupik-style-<?php echo esc_attr($style); ?>">
            
            <?php if ($show_current && $style !== 'button'): ?>
            <div class="lupik-current-region-display">
                <span class="lupik-region-label"><?php _e('Aktualny region:', 'buddyboss-theme'); ?></span>
                <span class="lupik-region-value"><?php echo esc_html($region_text); ?></span>
            </div>
            <?php endif; ?>
            
            <?php if ($style === 'full'): ?>
            <!-- Pełny selektor z dropdown'ami -->
            <div class="lupik-region-full-selector">
                <div class="lupik-form-row">
                    <div class="lupik-form-col">
                        <label for="lupik-widget-wojewodztwo"><?php _e('Województwo:', 'buddyboss-theme'); ?></label>
                        <select id="lupik-widget-wojewodztwo" class="lupik-select lupik-widget-select">
                            <option value=""><?php _e('Wybierz województwo', 'buddyboss-theme'); ?></option>
                            <option value="Cała Polska" <?php selected($current_region['wojewodztwo'] ?? '', 'Cała Polska'); ?>><?php _e('Cała Polska', 'buddyboss-theme'); ?></option>
                        </select>
                    </div>
                    <div class="lupik-form-col">
                        <label for="lupik-widget-powiat"><?php _e('Powiat:', 'buddyboss-theme'); ?></label>
                        <select id="lupik-widget-powiat" class="lupik-select lupik-widget-select" disabled>
                            <option value=""><?php _e('Wybierz powiat', 'buddyboss-theme'); ?></option>
                        </select>
                    </div>
                </div>
                <div class="lupik-form-actions">
                    <button type="button" class="lupik-btn lupik-btn-primary lupik-save-widget-region" disabled>
                        <?php _e('Zapisz', 'buddyboss-theme'); ?>
                    </button>
                    <button type="button" class="lupik-btn lupik-btn-secondary lupik-detect-widget-location">
                        <?php _e('Wykryj automatycznie', 'buddyboss-theme'); ?>
                    </button>
                </div>
            </div>
            
            <?php elseif ($style === 'compact'): ?>
            <!-- Kompaktowy selektor -->
            <div class="lupik-region-compact-selector">
                <button type="button" class="lupik-btn lupik-btn-outline lupik-change-region-compact">
                    <span class="lupik-icon">📍</span>
                    <?php _e('Zmień region', 'buddyboss-theme'); ?>
                </button>
            </div>
            
            <?php else: ?>
            <!-- Tylko przycisk -->
            <div class="lupik-region-button-selector">
                <button type="button" class="lupik-btn lupik-btn-primary lupik-change-region-button">
                    <span class="lupik-icon">📍</span>
                    <?php echo esc_html($region_text); ?>
                </button>
            </div>
            <?php endif; ?>
            
        </div>
        
        <?php if ($style === 'full'): ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Załaduj dane regionów dla widget'a
            if (typeof LupikRegion !== 'undefined' && LupikRegion.regions_url) {
                $.getJSON(LupikRegion.regions_url, function(data) {
                    // Wypełnij województwa
                    var $wojewSelect = $('#lupik-widget-wojewodztwo');
                    Object.keys(data).sort().forEach(function(wojewodztwo) {
                        $wojewSelect.append('<option value="' + wojewodztwo + '">' + wojewodztwo + '</option>');
                    });
                    
                    // Ustaw aktualny region
                    <?php if ($current_region && $current_region['wojewodztwo'] !== 'Cała Polska'): ?>
                    $wojewSelect.val('<?php echo esc_js($current_region['wojewodztwo']); ?>').trigger('change');
                    <?php endif; ?>
                    
                    // Obsługa zmiany województwa
                    $wojewSelect.on('change', function() {
                        var wojewodztwo = $(this).val();
                        var $powiatSelect = $('#lupik-widget-powiat');
                        
                        $powiatSelect.empty().append('<option value="">Wybierz powiat</option>');
                        
                        if (wojewodztwo && wojewodztwo !== 'Cała Polska' && data[wojewodztwo]) {
                            data[wojewodztwo].sort().forEach(function(powiat) {
                                $powiatSelect.append('<option value="' + powiat + '">' + powiat + '</option>');
                            });
                            $powiatSelect.prop('disabled', false);
                        } else {
                            $powiatSelect.prop('disabled', true);
                        }
                        
                        $('.lupik-save-widget-region').prop('disabled', !wojewodztwo);
                    });
                    
                    // Ustaw powiat jeśli jest
                    <?php if ($current_region && !empty($current_region['powiat'])): ?>
                    setTimeout(function() {
                        $('#lupik-widget-powiat').val('<?php echo esc_js($current_region['powiat']); ?>');
                    }, 100);
                    <?php endif; ?>
                });
            }
            
            // Zapisywanie regionu z widget'a
            $('.lupik-save-widget-region').on('click', function() {
                var wojewodztwo = $('#lupik-widget-wojewodztwo').val();
                var powiat = $('#lupik-widget-powiat').val();
                
                if (!wojewodztwo) return;
                
                $.ajax({
                    url: LupikRegion.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'save_user_region',
                        nonce: LupikRegion.nonce,
                        wojewodztwo: wojewodztwo,
                        powiat: powiat
                    },
                    success: function(response) {
                        if (response.success) {
                            // Aktualizuj wyświetlanie
                            var newText = wojewodztwo;
                            if (powiat) newText += ', ' + powiat;
                            $('.lupik-region-value').text(newText);
                            
                            // Powiadom o zmianie
                            $(document).trigger('lupik_region_changed', [response.data.region]);
                            
                            // Pokaż komunikat
                            alert('Region został zapisany!');
                        }
                    }
                });
            });
            
            // Automatyczne wykrywanie
            $('.lupik-detect-widget-location').on('click', function() {
                if (navigator.geolocation) {
                    $(this).prop('disabled', true).text('Wykrywanie...');
                    
                    navigator.geolocation.getCurrentPosition(function(position) {
                        $.ajax({
                            url: LupikRegion.ajax_url,
                            type: 'POST',
                            data: {
                                action: 'get_user_location',
                                nonce: LupikRegion.nonce,
                                lat: position.coords.latitude,
                                lng: position.coords.longitude
                            },
                            success: function(response) {
                                if (response.success) {
                                    $('#lupik-widget-wojewodztwo').val(response.data.wojewodztwo).trigger('change');
                                    if (response.data.powiat) {
                                        setTimeout(function() {
                                            $('#lupik-widget-powiat').val(response.data.powiat);
                                        }, 100);
                                    }
                                }
                            },
                            complete: function() {
                                $('.lupik-detect-widget-location').prop('disabled', false).text('Wykryj automatycznie');
                            }
                        });
                    });
                }
            });
        });
        </script>
        <?php endif; ?>
        
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Obsługa przycisków otwierających modal
            $('.lupik-change-region-compact, .lupik-change-region-button').on('click', function() {
                $('#lupik-region-modal').fadeIn(300);
            });
        });
        </script>
        <?php
    }
}

/**
 * Shortcode dla wyboru regionu
 */
function lupik_region_selector_shortcode($atts) {
    $atts = shortcode_atts(array(
        'style' => 'compact',
        'show_current' => 'true',
        'title' => ''
    ), $atts, 'lupik_region_selector');
    
    $show_current = ($atts['show_current'] === 'true');
    
    ob_start();
    
    if ($atts['title']) {
        echo '<h3 class="lupik-region-shortcode-title">' . esc_html($atts['title']) . '</h3>';
    }
    
    $widget = new Lupik_Region_Widget();
    $widget_method = new ReflectionMethod($widget, 'render_region_selector');
    $widget_method->setAccessible(true);
    $widget_method->invoke($widget, $atts['style'], $show_current);
    
    return ob_get_clean();
}

// Rejestracja widget'a i shortcode'a
add_action('widgets_init', function() {
    register_widget('Lupik_Region_Widget');
});

add_shortcode('lupik_region_selector', 'lupik_region_selector_shortcode');
